import{jsx as e,jsxs as t,Fragment as n}from"react/jsx-runtime";import{createContext as o,useContext as s,memo as i,useState as r,useRef as a,useEffect as l,PureComponent as p,useMemo as u,Children as h,isValidElement as d,cloneElement as g,createRef as c}from"react";import*as m from"react-dom";import{createPortal as v}from"react-dom";function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function y(e){var t=function(e,t){if("object"!=f(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=f(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f(t)?t:t+""}function b(e,t,n){return(t=y(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function L(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var w,M;var C=L(function(){if(M)return w;M=1;var e=process.env.NODE_ENV;return w=function(t,n,o,s,i,r,a,l){if("production"!==e&&void 0===n)throw new Error("invariant requires an error message argument");if(!t){var p;if(void 0===n)p=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[o,s,i,r,a,l],h=0;(p=new Error(n.replace(/%s/g,(function(){return u[h++]})))).name="Invariant Violation"}throw p.framesToPop=1,p}}}()),x=o(null);function k(){C(!!s,"useGoogleMap is React hook and requires React version 16.8+");var e=s(x);return C(!!e,"useGoogleMap needs a GoogleMap available up in the tree"),e}function P(e,t,n,o){var s,i,r={};return s=e,i=(e,s)=>{var i=n[s];i!==t[s]&&(r[s]=i,e(o,i))},Object.keys(s).forEach((e=>i(s[e],e))),r}function O(e,t,n){var o,s,i,r=(o=n,s=function(n,o,s){return"function"==typeof e[s]&&n.push(google.maps.event.addListener(t,o,e[s])),n},i=[],Object.keys(o).reduce((function(e,t){return s(e,o[t],t)}),i));return r}function E(e){google.maps.event.removeListener(e)}function S(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach(E)}function D(e){var{updaterMap:t,eventMap:n,prevProps:o,nextProps:s,instance:i}=e,r=O(s,i,n);return P(t,o,s,i),r}var I={onDblClick:"dblclick",onDragEnd:"dragend",onDragStart:"dragstart",onMapTypeIdChanged:"maptypeid_changed",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseDown:"mousedown",onMouseUp:"mouseup",onRightClick:"rightclick",onTilesLoaded:"tilesloaded",onBoundsChanged:"bounds_changed",onCenterChanged:"center_changed",onClick:"click",onDrag:"drag",onHeadingChanged:"heading_changed",onIdle:"idle",onProjectionChanged:"projection_changed",onResize:"resize",onTiltChanged:"tilt_changed",onZoomChanged:"zoom_changed"},j={extraMapTypes(e,t){t.forEach((function(t,n){e.mapTypes.set(String(n),t)}))},center(e,t){e.setCenter(t)},clickableIcons(e,t){e.setClickableIcons(t)},heading(e,t){e.setHeading(t)},mapTypeId(e,t){e.setMapTypeId(t)},options(e,t){e.setOptions(t)},streetView(e,t){e.setStreetView(t)},tilt(e,t){e.setTilt(t)},zoom(e,t){e.setZoom(t)}};i((function(t){var{children:n,options:o,id:s,mapContainerStyle:i,mapContainerClassName:p,center:u,onClick:h,onDblClick:d,onDrag:g,onDragEnd:c,onDragStart:m,onMouseMove:v,onMouseOut:f,onMouseOver:y,onMouseDown:b,onMouseUp:L,onRightClick:w,onCenterChanged:M,onLoad:C,onUnmount:k}=t,[P,O]=r(null),E=a(null),[S,D]=r(null),[I,j]=r(null),[B,_]=r(null),[T,U]=r(null),[z,R]=r(null),[A,Z]=r(null),[V,W]=r(null),[N,H]=r(null),[G,F]=r(null),[K,Y]=r(null),[q,J]=r(null),[X,$]=r(null);return l((()=>{o&&null!==P&&P.setOptions(o)}),[P,o]),l((()=>{null!==P&&void 0!==u&&P.setCenter(u)}),[P,u]),l((()=>{P&&d&&(null!==I&&google.maps.event.removeListener(I),j(google.maps.event.addListener(P,"dblclick",d)))}),[d]),l((()=>{P&&c&&(null!==B&&google.maps.event.removeListener(B),_(google.maps.event.addListener(P,"dragend",c)))}),[c]),l((()=>{P&&m&&(null!==T&&google.maps.event.removeListener(T),U(google.maps.event.addListener(P,"dragstart",m)))}),[m]),l((()=>{P&&b&&(null!==z&&google.maps.event.removeListener(z),R(google.maps.event.addListener(P,"mousedown",b)))}),[b]),l((()=>{P&&v&&(null!==A&&google.maps.event.removeListener(A),Z(google.maps.event.addListener(P,"mousemove",v)))}),[v]),l((()=>{P&&f&&(null!==V&&google.maps.event.removeListener(V),W(google.maps.event.addListener(P,"mouseout",f)))}),[f]),l((()=>{P&&y&&(null!==N&&google.maps.event.removeListener(N),H(google.maps.event.addListener(P,"mouseover",y)))}),[y]),l((()=>{P&&L&&(null!==G&&google.maps.event.removeListener(G),F(google.maps.event.addListener(P,"mouseup",L)))}),[L]),l((()=>{P&&w&&(null!==K&&google.maps.event.removeListener(K),Y(google.maps.event.addListener(P,"rightclick",w)))}),[w]),l((()=>{P&&h&&(null!==q&&google.maps.event.removeListener(q),J(google.maps.event.addListener(P,"click",h)))}),[h]),l((()=>{P&&g&&(null!==X&&google.maps.event.removeListener(X),$(google.maps.event.addListener(P,"drag",g)))}),[g]),l((()=>{P&&M&&(null!==S&&google.maps.event.removeListener(S),D(google.maps.event.addListener(P,"center_changed",M)))}),[h]),l((()=>{var e=null===E.current?null:new google.maps.Map(E.current,o);return O(e),null!==e&&C&&C(e),()=>{null!==e&&k&&k(e)}}),[]),e("div",{id:s,ref:E,style:i,className:p,children:e(x.Provider,{value:P,children:null!==P?n:null})})}));class B extends p{constructor(){super(...arguments),b(this,"state",{map:null}),b(this,"registeredEvents",[]),b(this,"mapRef",null),b(this,"getInstance",(()=>null===this.mapRef?null:new google.maps.Map(this.mapRef,this.props.options))),b(this,"panTo",(e=>{var t=this.getInstance();t&&t.panTo(e)})),b(this,"setMapCallback",(()=>{null!==this.state.map&&this.props.onLoad&&this.props.onLoad(this.state.map)})),b(this,"getRef",(e=>{this.mapRef=e}))}componentDidMount(){var e=this.getInstance();this.registeredEvents=D({updaterMap:j,eventMap:I,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{map:e}}),this.setMapCallback)}componentDidUpdate(e){null!==this.state.map&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:j,eventMap:I,prevProps:e,nextProps:this.props,instance:this.state.map}))}componentWillUnmount(){null!==this.state.map&&(this.props.onUnmount&&this.props.onUnmount(this.state.map),S(this.registeredEvents))}render(){return e("div",{id:this.props.id,ref:this.getRef,style:this.props.mapContainerStyle,className:this.props.mapContainerClassName,children:e(x.Provider,{value:this.state.map,children:null!==this.state.map?this.props.children:null})})}}function _(e,t,n,o,s,i,r){try{var a=e[i](r),l=a.value}catch(e){return void n(e)}a.done?t(l):Promise.resolve(l).then(o,s)}function T(e){return function(){var t=this,n=arguments;return new Promise((function(o,s){var i=e.apply(t,n);function r(e){_(i,o,s,r,a,"next",e)}function a(e){_(i,o,s,r,a,"throw",e)}r(void 0)}))}}function U(e){var{googleMapsApiKey:t,googleMapsClientId:n,version:o="weekly",language:s,region:i,libraries:r,channel:a,mapIds:l,authReferrerPolicy:p,apiUrl:u="https://maps.googleapis.com"}=e,h=[];return C(t&&n||!(t&&n),"You need to specify either googleMapsApiKey or googleMapsClientId for @react-google-maps/api load script to work. You cannot use both at the same time."),t?h.push("key=".concat(t)):n&&h.push("client=".concat(n)),o&&h.push("v=".concat(o)),s&&h.push("language=".concat(s)),i&&h.push("region=".concat(i)),r&&r.length&&h.push("libraries=".concat(r.sort().join(","))),a&&h.push("channel=".concat(a)),l&&l.length&&h.push("map_ids=".concat(l.join(","))),p&&h.push("auth_referrer_policy=".concat(p)),h.push("loading=async"),h.push("callback=initMap"),"".concat(u,"/maps/api/js?").concat(h.join("&"))}var z="undefined"!=typeof document;function R(e){var{url:t,id:n,nonce:o}=e;return z?new Promise((function(e,s){var i=document.getElementById(n),r=window;if(i){var a=i.getAttribute("data-state");if(i.src===t&&"error"!==a){if("ready"===a)return e(n);var l=r.initMap,p=i.onerror;return r.initMap=function(){l&&l(),e(n)},void(i.onerror=function(e){p&&p(e),s(e)})}i.remove()}var u=document.createElement("script");u.type="text/javascript",u.src=t,u.id=n,u.async=!0,u.nonce=o||"",u.onerror=function(e){u.setAttribute("data-state","error"),s(e)},r.initMap=function(){u.setAttribute("data-state","ready"),e(n)},document.head.appendChild(u)})).catch((e=>{throw console.error("injectScript error: ",e),e})):Promise.reject(new Error("document is undefined"))}function A(e){var t=e.href;return!(!t||0!==t.indexOf("https://fonts.googleapis.com/css?family=Roboto")&&0!==t.indexOf("https://fonts.googleapis.com/css?family=Google+Sans+Text"))||("style"===e.tagName.toLowerCase()&&e.styleSheet&&e.styleSheet.cssText&&0===e.styleSheet.cssText.replace("\r\n","").indexOf(".gm-style")?(e.styleSheet.cssText="",!0):"style"===e.tagName.toLowerCase()&&e.innerHTML&&0===e.innerHTML.replace("\r\n","").indexOf(".gm-style")?(e.innerHTML="",!0):"style"===e.tagName.toLowerCase()&&!e.styleSheet&&!e.innerHTML)}function Z(){var e=document.getElementsByTagName("head")[0];if(e){var t=e.insertBefore.bind(e);e.insertBefore=function(n,o){return A(n)||Reflect.apply(t,e,[n,o]),n};var n=e.appendChild.bind(e);e.appendChild=function(t){return A(t)||Reflect.apply(n,e,[t]),t}}}var V=!1;function W(){return e("div",{children:"Loading..."})}var N,H={id:"script-loader",version:"weekly"};class G extends p{constructor(){super(...arguments),b(this,"check",null),b(this,"state",{loaded:!1}),b(this,"cleanupCallback",(()=>{delete window.google.maps,this.injectScript()})),b(this,"isCleaningUp",T((function*(){return new Promise((function(e){if(V){if(z)var t=window.setInterval((function(){V||(window.clearInterval(t),e())}),1)}else e()}))}))),b(this,"cleanup",(()=>{V=!0;var e=document.getElementById(this.props.id);e&&e.parentNode&&e.parentNode.removeChild(e),Array.prototype.slice.call(document.getElementsByTagName("script")).filter((function(e){return"string"==typeof e.src&&e.src.includes("maps.googleapis")})).forEach((function(e){e.parentNode&&e.parentNode.removeChild(e)})),Array.prototype.slice.call(document.getElementsByTagName("link")).filter((function(e){return"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Google+Sans"===e.href})).forEach((function(e){e.parentNode&&e.parentNode.removeChild(e)})),Array.prototype.slice.call(document.getElementsByTagName("style")).filter((function(e){return void 0!==e.innerText&&e.innerText.length>0&&e.innerText.includes(".gm-")})).forEach((function(e){e.parentNode&&e.parentNode.removeChild(e)}))})),b(this,"injectScript",(()=>{this.props.preventGoogleFontsLoading&&Z(),C(!!this.props.id,'LoadScript requires "id" prop to be a string: %s',this.props.id),R({id:this.props.id,nonce:this.props.nonce,url:U(this.props)}).then((()=>{this.props.onLoad&&this.props.onLoad(),this.setState((function(){return{loaded:!0}}))})).catch((e=>{this.props.onError&&this.props.onError(e),console.error("\n          There has been an Error with loading Google Maps API script, please check that you provided correct google API key (".concat(this.props.googleMapsApiKey||"-",") or Client ID (").concat(this.props.googleMapsClientId||"-",") to <LoadScript />\n          Otherwise it is a Network issue.\n        "))}))})),b(this,"getRef",(e=>{this.check=e}))}componentDidMount(){if(z){if(window.google&&window.google.maps&&!V)return void console.error("google api is already presented");this.isCleaningUp().then(this.injectScript).catch((function(e){console.error("Error at injecting script after cleaning up: ",e)}))}}componentDidUpdate(e){this.props.libraries!==e.libraries&&console.warn("Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables"),z&&e.language!==this.props.language&&(this.cleanup(),this.setState((function(){return{loaded:!1}}),this.cleanupCallback))}componentWillUnmount(){if(z){this.cleanup();window.setTimeout((()=>{this.check||(delete window.google,V=!1)}),1),this.props.onUnmount&&this.props.onUnmount()}}render(){return t(n,{children:[e("div",{ref:this.getRef}),this.state.loaded?this.props.children:this.props.loadingElement||e(W,{})]})}}function F(e,t){if(null==e)return{};var n,o,s=function(e,t){if(null==e)return{};var n={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.includes(o))continue;n[o]=e[o]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}function K(e){var{id:t=H.id,version:n=H.version,nonce:o,googleMapsApiKey:s,googleMapsClientId:i,language:p,region:u,libraries:h,preventGoogleFontsLoading:d,channel:g,mapIds:c,authReferrerPolicy:m,apiUrl:v="https://maps.googleapis.com"}=e,f=a(!1),[y,b]=r(!1),[L,w]=r(void 0);l((function(){return f.current=!0,()=>{f.current=!1}}),[]),l((function(){z&&d&&Z()}),[d]),l((function(){y&&C(!!window.google,"useLoadScript was marked as loaded, but window.google is not present. Something went wrong.")}),[y]);var M=U({version:n,googleMapsApiKey:s,googleMapsClientId:i,language:p,region:u,libraries:h,channel:g,mapIds:c,authReferrerPolicy:m,apiUrl:v});l((function(){function e(){f.current&&(b(!0),N=M)}z&&(window.google&&window.google.maps&&N===M?e():R({id:t,url:M,nonce:o}).then(e).catch((function(e){f.current&&w(e),console.warn("\n        There has been an Error with loading Google Maps API script, please check that you provided correct google API key (".concat(s||"-",") or Client ID (").concat(i||"-",")\n        Otherwise it is a Network issue.\n      ")),console.error(e)})))}),[t,M,o]);var x=a(void 0);return l((function(){x.current&&h!==x.current&&console.warn("Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables"),x.current=h}),[h]),{isLoaded:y,loadError:L,url:M}}b(G,"defaultProps",H);var Y=["loadingElement","onLoad","onError","onUnmount","children"],q=e(W,{});var J=i((function(e){var{loadingElement:t,onLoad:n,onError:o,onUnmount:s,children:i}=e,r=F(e,Y),{isLoaded:a,loadError:p}=K(r);return l((function(){a&&"function"==typeof n&&n()}),[a,n]),l((function(){p&&"function"==typeof o&&o(p)}),[p,o]),l((function(){return()=>{s&&s()}}),[s]),a?i:t||q}));function X(e,t,n,o){return new(n||(n=Promise))((function(t,s){function i(e){try{a(o.next(e))}catch(e){s(e)}}function r(e){try{a(o.throw(e))}catch(e){s(e)}}function a(e){var o;e.done?t(e.value):(o=e.value,o instanceof n?o:new n((function(e){e(o)}))).then(i,r)}a((o=o.apply(e,[])).next())}))}function $(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}"function"==typeof SuppressedError&&SuppressedError;var Q,ee=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var o,s,i;if(Array.isArray(t)){if((o=t.length)!=n.length)return!1;for(s=o;0!=s--;)if(!e(t[s],n[s]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((o=(i=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(s=o;0!=s--;)if(!Object.prototype.hasOwnProperty.call(n,i[s]))return!1;for(s=o;0!=s--;){var r=i[s];if(!e(t[r],n[r]))return!1}return!0}return t!=t&&n!=n},te=$(ee),ne="__googleMapsScriptId";!function(e){e[e.INITIALIZED=0]="INITIALIZED",e[e.LOADING=1]="LOADING",e[e.SUCCESS=2]="SUCCESS",e[e.FAILURE=3]="FAILURE"}(Q||(Q={}));class oe{constructor(e){var{apiKey:t,authReferrerPolicy:n,channel:o,client:s,id:i=ne,language:r,libraries:a=[],mapIds:l,nonce:p,region:u,retries:h=3,url:d="https://maps.googleapis.com/maps/api/js",version:g}=e;if(this.callbacks=[],this.done=!1,this.loading=!1,this.errors=[],this.apiKey=t,this.authReferrerPolicy=n,this.channel=o,this.client=s,this.id=i||ne,this.language=r,this.libraries=a,this.mapIds=l,this.nonce=p,this.region=u,this.retries=h,this.url=d,this.version=g,oe.instance){if(!te(this.options,oe.instance.options))throw new Error("Loader must not be called again with different options. ".concat(JSON.stringify(this.options)," !== ").concat(JSON.stringify(oe.instance.options)));return oe.instance}oe.instance=this}get options(){return{version:this.version,apiKey:this.apiKey,channel:this.channel,client:this.client,id:this.id,libraries:this.libraries,language:this.language,region:this.region,mapIds:this.mapIds,nonce:this.nonce,url:this.url,authReferrerPolicy:this.authReferrerPolicy}}get status(){return this.errors.length?Q.FAILURE:this.done?Q.SUCCESS:this.loading?Q.LOADING:Q.INITIALIZED}get failed(){return this.done&&!this.loading&&this.errors.length>=this.retries+1}createUrl(){var e=this.url;return e+="?callback=__googleMapsCallback&loading=async",this.apiKey&&(e+="&key=".concat(this.apiKey)),this.channel&&(e+="&channel=".concat(this.channel)),this.client&&(e+="&client=".concat(this.client)),this.libraries.length>0&&(e+="&libraries=".concat(this.libraries.join(","))),this.language&&(e+="&language=".concat(this.language)),this.region&&(e+="&region=".concat(this.region)),this.version&&(e+="&v=".concat(this.version)),this.mapIds&&(e+="&map_ids=".concat(this.mapIds.join(","))),this.authReferrerPolicy&&(e+="&auth_referrer_policy=".concat(this.authReferrerPolicy)),e}deleteScript(){var e=document.getElementById(this.id);e&&e.remove()}load(){return this.loadPromise()}loadPromise(){return new Promise(((e,t)=>{this.loadCallback((n=>{n?t(n.error):e(window.google)}))}))}importLibrary(e){return this.execute(),google.maps.importLibrary(e)}loadCallback(e){this.callbacks.push(e),this.execute()}setScript(){var e,t;if(document.getElementById(this.id))this.callback();else{var n={key:this.apiKey,channel:this.channel,client:this.client,libraries:this.libraries.length&&this.libraries,v:this.version,mapIds:this.mapIds,language:this.language,region:this.region,authReferrerPolicy:this.authReferrerPolicy};Object.keys(n).forEach((e=>!n[e]&&delete n[e])),(null===(t=null===(e=null===window||void 0===window?void 0:window.google)||void 0===e?void 0:e.maps)||void 0===t?void 0:t.importLibrary)||(e=>{var t,n,o,s="The Google Maps JavaScript API",i="google",r="importLibrary",a="__ib__",l=document,p=window,u=(p=p[i]||(p[i]={})).maps||(p.maps={}),h=new Set,d=new URLSearchParams,g=()=>t||(t=new Promise(((r,p)=>X(this,0,void 0,(function*(){var g;for(o in yield n=l.createElement("script"),n.id=this.id,d.set("libraries",[...h]+""),e)d.set(o.replace(/[A-Z]/g,(e=>"_"+e[0].toLowerCase())),e[o]);d.set("callback",i+".maps."+a),n.src=this.url+"?"+d,u[a]=r,n.onerror=()=>t=p(Error(s+" could not load.")),n.nonce=this.nonce||(null===(g=l.querySelector("script[nonce]"))||void 0===g?void 0:g.nonce)||"",l.head.append(n)})))));u[r]?console.warn(s+" only loads once. Ignoring:",e):u[r]=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return h.add(e)&&g().then((()=>u[r](e,...n)))}})(n);var o=this.libraries.map((e=>this.importLibrary(e)));o.length||o.push(this.importLibrary("core")),Promise.all(o).then((()=>this.callback()),(e=>{var t=new ErrorEvent("error",{error:e});this.loadErrorCallback(t)}))}}reset(){this.deleteScript(),this.done=!1,this.loading=!1,this.errors=[],this.onerrorEvent=null}resetIfRetryingFailed(){this.failed&&this.reset()}loadErrorCallback(e){if(this.errors.push(e),this.errors.length<=this.retries){var t=this.errors.length*Math.pow(2,this.errors.length);console.error("Failed to load Google Maps script, retrying in ".concat(t," ms.")),setTimeout((()=>{this.deleteScript(),this.setScript()}),t)}else this.onerrorEvent=e,this.callback()}callback(){this.done=!0,this.loading=!1,this.callbacks.forEach((e=>{e(this.onerrorEvent)})),this.callbacks=[]}execute(){if(this.resetIfRetryingFailed(),!this.loading)if(this.done)this.callback();else{if(window.google&&window.google.maps&&window.google.maps.version)return console.warn("Google Maps already loaded outside @googlemaps/js-api-loader. This may result in undesirable behavior as options and script parameters may not match."),void this.callback();this.loading=!0,this.setScript()}}}var se=["maps"];function ie(e){var{id:t=H.id,version:n=H.version,nonce:o,googleMapsApiKey:s,language:i,region:p,libraries:h=se,preventGoogleFontsLoading:d,mapIds:g,authReferrerPolicy:c}=e,m=a(!1),[v,f]=r(!1),[y,b]=r(void 0);l((function(){return m.current=!0,()=>{m.current=!1}}),[]);var L=u((()=>new oe({id:t,apiKey:s,version:n,libraries:h,language:i||"en",region:p||"US",mapIds:g||[],nonce:o||"",authReferrerPolicy:c||"origin"})),[t,s,n,h,i,p,g,o,c]);l((function(){v||L.load().then((()=>{m.current&&f(!0)})).catch((e=>{b(e)}))}),[]),l((()=>{z&&d&&Z()}),[d]);var w=a();return l((()=>{w.current&&h!==w.current&&console.warn("Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables"),w.current=h}),[h]),{isLoaded:v,loadError:y}}function re(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function ae(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?re(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):re(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var le={},pe={options(e,t){e.setOptions(t)}};var ue=i((function(e){var{options:t,onLoad:n,onUnmount:o}=e,i=s(x),[a,p]=r(null);return l((()=>{null!==a&&a.setMap(i)}),[i]),l((()=>{t&&null!==a&&a.setOptions(t)}),[a,t]),l((()=>{var e=new google.maps.TrafficLayer(ae(ae({},t),{},{map:i}));return p(e),n&&n(e),()=>{null!==a&&(o&&o(a),a.setMap(null))}}),[]),null}));class he extends p{constructor(){super(...arguments),b(this,"state",{trafficLayer:null}),b(this,"setTrafficLayerCallback",(()=>{null!==this.state.trafficLayer&&this.props.onLoad&&this.props.onLoad(this.state.trafficLayer)})),b(this,"registeredEvents",[])}componentDidMount(){var e=new google.maps.TrafficLayer(ae(ae({},this.props.options),{},{map:this.context}));this.registeredEvents=D({updaterMap:pe,eventMap:le,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{trafficLayer:e}}),this.setTrafficLayerCallback)}componentDidUpdate(e){null!==this.state.trafficLayer&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:pe,eventMap:le,prevProps:e,nextProps:this.props,instance:this.state.trafficLayer}))}componentWillUnmount(){null!==this.state.trafficLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.trafficLayer),S(this.registeredEvents),this.state.trafficLayer.setMap(null))}render(){return null}}b(he,"contextType",x);var de=i((function(e){var{onLoad:t,onUnmount:n}=e,o=s(x),[i,a]=r(null);return l((()=>{null!==i&&i.setMap(o)}),[o]),l((()=>{var e=new google.maps.BicyclingLayer;return a(e),e.setMap(o),t&&t(e),()=>{null!==e&&(n&&n(e),e.setMap(null))}}),[]),null}));class ge extends p{constructor(){super(...arguments),b(this,"state",{bicyclingLayer:null}),b(this,"setBicyclingLayerCallback",(()=>{null!==this.state.bicyclingLayer&&(this.state.bicyclingLayer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.bicyclingLayer))}))}componentDidMount(){var e=new google.maps.BicyclingLayer;this.setState((()=>({bicyclingLayer:e})),this.setBicyclingLayerCallback)}componentWillUnmount(){null!==this.state.bicyclingLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.bicyclingLayer),this.state.bicyclingLayer.setMap(null))}render(){return null}}b(ge,"contextType",x);var ce=i((function(e){var{onLoad:t,onUnmount:n}=e,o=s(x),[i,a]=r(null);return l((()=>{null!==i&&i.setMap(o)}),[o]),l((()=>{var e=new google.maps.TransitLayer;return a(e),e.setMap(o),t&&t(e),()=>{null!==i&&(n&&n(i),i.setMap(null))}}),[]),null}));class me extends p{constructor(){super(...arguments),b(this,"state",{transitLayer:null}),b(this,"setTransitLayerCallback",(()=>{null!==this.state.transitLayer&&(this.state.transitLayer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.transitLayer))}))}componentDidMount(){var e=new google.maps.TransitLayer;this.setState((function(){return{transitLayer:e}}),this.setTransitLayerCallback)}componentWillUnmount(){null!==this.state.transitLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.transitLayer),this.state.transitLayer.setMap(null))}render(){return null}}function ve(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function fe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ve(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ve(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}b(me,"contextType",x);var ye={onCircleComplete:"circlecomplete",onMarkerComplete:"markercomplete",onOverlayComplete:"overlaycomplete",onPolygonComplete:"polygoncomplete",onPolylineComplete:"polylinecomplete",onRectangleComplete:"rectanglecomplete"},be={drawingMode(e,t){e.setDrawingMode(t)},options(e,t){e.setOptions(t)}};var Le=i((function(e){var{options:t,drawingMode:n,onCircleComplete:o,onMarkerComplete:i,onOverlayComplete:a,onPolygonComplete:p,onPolylineComplete:u,onRectangleComplete:h,onLoad:d,onUnmount:g}=e,c=s(x),[m,v]=r(null),[f,y]=r(null),[b,L]=r(null),[w,M]=r(null),[k,P]=r(null),[O,E]=r(null),[S,D]=r(null);return l((()=>{null!==m&&m.setMap(c)}),[c]),l((()=>{t&&null!==m&&m.setOptions(t)}),[m,t]),l((()=>{null!==m&&m.setDrawingMode(null!=n?n:null)}),[m,n]),l((()=>{m&&o&&(null!==f&&google.maps.event.removeListener(f),y(google.maps.event.addListener(m,"circlecomplete",o)))}),[m,o]),l((()=>{m&&i&&(null!==b&&google.maps.event.removeListener(b),L(google.maps.event.addListener(m,"markercomplete",i)))}),[m,i]),l((()=>{m&&a&&(null!==w&&google.maps.event.removeListener(w),M(google.maps.event.addListener(m,"overlaycomplete",a)))}),[m,a]),l((()=>{m&&p&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(m,"polygoncomplete",p)))}),[m,p]),l((()=>{m&&u&&(null!==O&&google.maps.event.removeListener(O),E(google.maps.event.addListener(m,"polylinecomplete",u)))}),[m,u]),l((()=>{m&&h&&(null!==S&&google.maps.event.removeListener(S),D(google.maps.event.addListener(m,"rectanglecomplete",h)))}),[m,h]),l((()=>{C(!!google.maps.drawing,"Did you include prop libraries={['drawing']} in the URL? %s",google.maps.drawing);var e=new google.maps.drawing.DrawingManager(fe(fe({},t),{},{map:c}));return n&&e.setDrawingMode(n),o&&y(google.maps.event.addListener(e,"circlecomplete",o)),i&&L(google.maps.event.addListener(e,"markercomplete",i)),a&&M(google.maps.event.addListener(e,"overlaycomplete",a)),p&&P(google.maps.event.addListener(e,"polygoncomplete",p)),u&&E(google.maps.event.addListener(e,"polylinecomplete",u)),h&&D(google.maps.event.addListener(e,"rectanglecomplete",h)),v(e),d&&d(e),()=>{null!==m&&(f&&google.maps.event.removeListener(f),b&&google.maps.event.removeListener(b),w&&google.maps.event.removeListener(w),k&&google.maps.event.removeListener(k),O&&google.maps.event.removeListener(O),S&&google.maps.event.removeListener(S),g&&g(m),m.setMap(null))}}),[]),null}));class we extends p{constructor(e){super(e),b(this,"registeredEvents",[]),b(this,"state",{drawingManager:null}),b(this,"setDrawingManagerCallback",(()=>{null!==this.state.drawingManager&&this.props.onLoad&&this.props.onLoad(this.state.drawingManager)})),C(!!google.maps.drawing,"Did you include prop libraries={['drawing']} in the URL? %s",google.maps.drawing)}componentDidMount(){var e=new google.maps.drawing.DrawingManager(fe(fe({},this.props.options),{},{map:this.context}));this.registeredEvents=D({updaterMap:be,eventMap:ye,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{drawingManager:e}}),this.setDrawingManagerCallback)}componentDidUpdate(e){null!==this.state.drawingManager&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:be,eventMap:ye,prevProps:e,nextProps:this.props,instance:this.state.drawingManager}))}componentWillUnmount(){null!==this.state.drawingManager&&(this.props.onUnmount&&this.props.onUnmount(this.state.drawingManager),S(this.registeredEvents),this.state.drawingManager.setMap(null))}render(){return null}}function Me(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Ce(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Me(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Me(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}b(we,"contextType",x);var xe={onAnimationChanged:"animation_changed",onClick:"click",onClickableChanged:"clickable_changed",onCursorChanged:"cursor_changed",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDraggableChanged:"draggable_changed",onDragStart:"dragstart",onFlatChanged:"flat_changed",onIconChanged:"icon_changed",onMouseDown:"mousedown",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onPositionChanged:"position_changed",onRightClick:"rightclick",onShapeChanged:"shape_changed",onTitleChanged:"title_changed",onVisibleChanged:"visible_changed",onZindexChanged:"zindex_changed"},ke={animation(e,t){e.setAnimation(t)},clickable(e,t){e.setClickable(t)},cursor(e,t){e.setCursor(t)},draggable(e,t){e.setDraggable(t)},icon(e,t){e.setIcon(t)},label(e,t){e.setLabel(t)},map(e,t){e.setMap(t)},opacity(e,t){e.setOpacity(t)},options(e,t){e.setOptions(t)},position(e,t){e.setPosition(t)},shape(e,t){e.setShape(t)},title(e,t){e.setTitle(t)},visible(e,t){e.setVisible(t)},zIndex(e,t){e.setZIndex(t)}},Pe={};var Oe=i((function(t){var{position:o,options:i,clusterer:a,noClustererRedraw:p,children:c,draggable:m,visible:v,animation:f,clickable:y,cursor:b,icon:L,label:w,opacity:M,shape:C,title:k,zIndex:P,onClick:O,onDblClick:E,onDrag:S,onDragEnd:D,onDragStart:I,onMouseOut:j,onMouseOver:B,onMouseUp:_,onMouseDown:T,onRightClick:U,onClickableChanged:z,onCursorChanged:R,onAnimationChanged:A,onDraggableChanged:Z,onFlatChanged:V,onIconChanged:W,onPositionChanged:N,onShapeChanged:H,onTitleChanged:G,onVisibleChanged:F,onZindexChanged:K,onLoad:Y,onUnmount:q}=t,J=s(x),[X,$]=r(null),[Q,ee]=r(null),[te,ne]=r(null),[oe,se]=r(null),[ie,re]=r(null),[ae,le]=r(null),[pe,ue]=r(null),[he,de]=r(null),[ge,ce]=r(null),[me,ve]=r(null),[fe,ye]=r(null),[be,Le]=r(null),[we,Me]=r(null),[xe,ke]=r(null),[Oe,Ee]=r(null),[Se,De]=r(null),[Ie,je]=r(null),[Be,_e]=r(null),[Te,Ue]=r(null),[ze,Re]=r(null),[Ae,Ze]=r(null),[Ve,We]=r(null);l((()=>{null!==X&&X.setMap(J)}),[J]),l((()=>{void 0!==i&&null!==X&&X.setOptions(i)}),[X,i]),l((()=>{void 0!==m&&null!==X&&X.setDraggable(m)}),[X,m]),l((()=>{o&&null!==X&&X.setPosition(o)}),[X,o]),l((()=>{void 0!==v&&null!==X&&X.setVisible(v)}),[X,v]),l((()=>{null==X||X.setAnimation(f)}),[X,f]),l((()=>{X&&void 0!==y&&X.setClickable(y)}),[X,y]),l((()=>{X&&void 0!==b&&X.setCursor(b)}),[X,b]),l((()=>{X&&void 0!==L&&X.setIcon(L)}),[X,L]),l((()=>{X&&void 0!==w&&X.setLabel(w)}),[X,w]),l((()=>{X&&void 0!==M&&X.setOpacity(M)}),[X,M]),l((()=>{X&&void 0!==C&&X.setShape(C)}),[X,C]),l((()=>{X&&void 0!==k&&X.setTitle(k)}),[X,k]),l((()=>{X&&void 0!==P&&X.setZIndex(P)}),[X,P]),l((()=>{X&&E&&(null!==Q&&google.maps.event.removeListener(Q),ee(google.maps.event.addListener(X,"dblclick",E)))}),[E]),l((()=>{X&&D&&(null!==te&&google.maps.event.removeListener(te),ne(google.maps.event.addListener(X,"dragend",D)))}),[D]),l((()=>{X&&I&&(null!==oe&&google.maps.event.removeListener(oe),se(google.maps.event.addListener(X,"dragstart",I)))}),[I]),l((()=>{X&&T&&(null!==ie&&google.maps.event.removeListener(ie),re(google.maps.event.addListener(X,"mousedown",T)))}),[T]),l((()=>{X&&j&&(null!==ae&&google.maps.event.removeListener(ae),le(google.maps.event.addListener(X,"mouseout",j)))}),[j]),l((()=>{X&&B&&(null!==pe&&google.maps.event.removeListener(pe),ue(google.maps.event.addListener(X,"mouseover",B)))}),[B]),l((()=>{X&&_&&(null!==he&&google.maps.event.removeListener(he),de(google.maps.event.addListener(X,"mouseup",_)))}),[_]),l((()=>{X&&U&&(null!==ge&&google.maps.event.removeListener(ge),ce(google.maps.event.addListener(X,"rightclick",U)))}),[U]),l((()=>{X&&O&&(null!==me&&google.maps.event.removeListener(me),ve(google.maps.event.addListener(X,"click",O)))}),[O]),l((()=>{X&&S&&(null!==fe&&google.maps.event.removeListener(fe),ye(google.maps.event.addListener(X,"drag",S)))}),[S]),l((()=>{X&&z&&(null!==be&&google.maps.event.removeListener(be),Le(google.maps.event.addListener(X,"clickable_changed",z)))}),[z]),l((()=>{X&&R&&(null!==we&&google.maps.event.removeListener(we),Me(google.maps.event.addListener(X,"cursor_changed",R)))}),[R]),l((()=>{X&&A&&(null!==xe&&google.maps.event.removeListener(xe),ke(google.maps.event.addListener(X,"animation_changed",A)))}),[A]),l((()=>{X&&Z&&(null!==Oe&&google.maps.event.removeListener(Oe),Ee(google.maps.event.addListener(X,"draggable_changed",Z)))}),[Z]),l((()=>{X&&V&&(null!==Se&&google.maps.event.removeListener(Se),De(google.maps.event.addListener(X,"flat_changed",V)))}),[V]),l((()=>{X&&W&&(null!==Ie&&google.maps.event.removeListener(Ie),je(google.maps.event.addListener(X,"icon_changed",W)))}),[W]),l((()=>{X&&N&&(null!==Be&&google.maps.event.removeListener(Be),_e(google.maps.event.addListener(X,"position_changed",N)))}),[N]),l((()=>{X&&H&&(null!==Te&&google.maps.event.removeListener(Te),Ue(google.maps.event.addListener(X,"shape_changed",H)))}),[H]),l((()=>{X&&G&&(null!==ze&&google.maps.event.removeListener(ze),Re(google.maps.event.addListener(X,"title_changed",G)))}),[G]),l((()=>{X&&F&&(null!==Ae&&google.maps.event.removeListener(Ae),Ze(google.maps.event.addListener(X,"visible_changed",F)))}),[F]),l((()=>{X&&K&&(null!==Ve&&google.maps.event.removeListener(Ve),We(google.maps.event.addListener(X,"zindex_changed",K)))}),[K]),l((()=>{var e=Ce(Ce(Ce({},i||Pe),a?Pe:{map:J}),{},{position:o}),t=new google.maps.Marker(e);return a?a.addMarker(t,!!p):t.setMap(J),o&&t.setPosition(o),void 0!==v&&t.setVisible(v),void 0!==m&&t.setDraggable(m),void 0!==y&&t.setClickable(y),"string"==typeof b&&t.setCursor(b),L&&t.setIcon(L),void 0!==w&&t.setLabel(w),void 0!==M&&t.setOpacity(M),C&&t.setShape(C),"string"==typeof k&&t.setTitle(k),"number"==typeof P&&t.setZIndex(P),E&&ee(google.maps.event.addListener(t,"dblclick",E)),D&&ne(google.maps.event.addListener(t,"dragend",D)),I&&se(google.maps.event.addListener(t,"dragstart",I)),T&&re(google.maps.event.addListener(t,"mousedown",T)),j&&le(google.maps.event.addListener(t,"mouseout",j)),B&&ue(google.maps.event.addListener(t,"mouseover",B)),_&&de(google.maps.event.addListener(t,"mouseup",_)),U&&ce(google.maps.event.addListener(t,"rightclick",U)),O&&ve(google.maps.event.addListener(t,"click",O)),S&&ye(google.maps.event.addListener(t,"drag",S)),z&&Le(google.maps.event.addListener(t,"clickable_changed",z)),R&&Me(google.maps.event.addListener(t,"cursor_changed",R)),A&&ke(google.maps.event.addListener(t,"animation_changed",A)),Z&&Ee(google.maps.event.addListener(t,"draggable_changed",Z)),V&&De(google.maps.event.addListener(t,"flat_changed",V)),W&&je(google.maps.event.addListener(t,"icon_changed",W)),N&&_e(google.maps.event.addListener(t,"position_changed",N)),H&&Ue(google.maps.event.addListener(t,"shape_changed",H)),G&&Re(google.maps.event.addListener(t,"title_changed",G)),F&&Ze(google.maps.event.addListener(t,"visible_changed",F)),K&&We(google.maps.event.addListener(t,"zindex_changed",K)),$(t),Y&&Y(t),()=>{null!==Q&&google.maps.event.removeListener(Q),null!==te&&google.maps.event.removeListener(te),null!==oe&&google.maps.event.removeListener(oe),null!==ie&&google.maps.event.removeListener(ie),null!==ae&&google.maps.event.removeListener(ae),null!==pe&&google.maps.event.removeListener(pe),null!==he&&google.maps.event.removeListener(he),null!==ge&&google.maps.event.removeListener(ge),null!==me&&google.maps.event.removeListener(me),null!==be&&google.maps.event.removeListener(be),null!==we&&google.maps.event.removeListener(we),null!==xe&&google.maps.event.removeListener(xe),null!==Oe&&google.maps.event.removeListener(Oe),null!==Se&&google.maps.event.removeListener(Se),null!==Ie&&google.maps.event.removeListener(Ie),null!==Be&&google.maps.event.removeListener(Be),null!==ze&&google.maps.event.removeListener(ze),null!==Ae&&google.maps.event.removeListener(Ae),null!==Ve&&google.maps.event.removeListener(Ve),q&&q(t),a?a.removeMarker(t,!!p):t&&t.setMap(null)}}),[]);var Ne=u((()=>c?h.map(c,(e=>d(e)?g(e,{anchor:X}):e)):null),[c,X]);return e(n,{children:Ne})||null}));class Ee extends p{constructor(){super(...arguments),b(this,"registeredEvents",[])}componentDidMount(){var e=this;return T((function*(){var t=Ce(Ce(Ce({},e.props.options||Pe),e.props.clusterer?Pe:{map:e.context}),{},{position:e.props.position});e.marker=new google.maps.Marker(t),e.props.clusterer?e.props.clusterer.addMarker(e.marker,!!e.props.noClustererRedraw):e.marker.setMap(e.context),e.registeredEvents=D({updaterMap:ke,eventMap:xe,prevProps:{},nextProps:e.props,instance:e.marker}),e.props.onLoad&&e.props.onLoad(e.marker)}))()}componentDidUpdate(e){this.marker&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:ke,eventMap:xe,prevProps:e,nextProps:this.props,instance:this.marker}))}componentWillUnmount(){this.marker&&(this.props.onUnmount&&this.props.onUnmount(this.marker),S(this.registeredEvents),this.props.clusterer?this.props.clusterer.removeMarker(this.marker,!!this.props.noClustererRedraw):this.marker&&this.marker.setMap(null))}render(){return(this.props.children?h.map(this.props.children,(e=>d(e)?g(e,{anchor:this.marker}):e)):null)||null}}b(Ee,"contextType",x);var Se=function(){function e(t,n){t.getClusterer().extend(e,google.maps.OverlayView),this.cluster=t,this.clusterClassName=this.cluster.getClusterer().getClusterClass(),this.className=this.clusterClassName,this.styles=n,this.center=void 0,this.div=null,this.sums=null,this.visible=!1,this.boundsChangedListener=null,this.url="",this.height=0,this.width=0,this.anchorText=[0,0],this.anchorIcon=[0,0],this.textColor="black",this.textSize=11,this.textDecoration="none",this.fontWeight="bold",this.fontStyle="normal",this.fontFamily="Arial,sans-serif",this.backgroundPosition="0 0",this.cMouseDownInCluster=null,this.cDraggingMapByCluster=null,this.timeOut=null,this.setMap(t.getMap()),this.onBoundsChanged=this.onBoundsChanged.bind(this),this.onMouseDown=this.onMouseDown.bind(this),this.onClick=this.onClick.bind(this),this.onMouseOver=this.onMouseOver.bind(this),this.onMouseOut=this.onMouseOut.bind(this),this.onAdd=this.onAdd.bind(this),this.onRemove=this.onRemove.bind(this),this.draw=this.draw.bind(this),this.hide=this.hide.bind(this),this.show=this.show.bind(this),this.useStyle=this.useStyle.bind(this),this.setCenter=this.setCenter.bind(this),this.getPosFromLatLng=this.getPosFromLatLng.bind(this)}return e.prototype.onBoundsChanged=function(){this.cDraggingMapByCluster=this.cMouseDownInCluster},e.prototype.onMouseDown=function(){this.cMouseDownInCluster=!0,this.cDraggingMapByCluster=!1},e.prototype.onClick=function(e){if(this.cMouseDownInCluster=!1,!this.cDraggingMapByCluster){var t=this.cluster.getClusterer();if(google.maps.event.trigger(t,"click",this.cluster),google.maps.event.trigger(t,"clusterclick",this.cluster),t.getZoomOnClick()){var n=t.getMaxZoom(),o=this.cluster.getBounds(),s=t.getMap();null!==s&&"fitBounds"in s&&s.fitBounds(o),this.timeOut=window.setTimeout((function(){var e=t.getMap();if(null!==e){"fitBounds"in e&&e.fitBounds(o);var s=e.getZoom()||0;null!==n&&s>n&&e.setZoom(n+1)}}),100)}e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()}},e.prototype.onMouseOver=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseover",this.cluster)},e.prototype.onMouseOut=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseout",this.cluster)},e.prototype.onAdd=function(){var e;this.div=document.createElement("div"),this.div.className=this.className,this.visible&&this.show(),null===(e=this.getPanes())||void 0===e||e.overlayMouseTarget.appendChild(this.div);var t=this.getMap();null!==t&&(this.boundsChangedListener=google.maps.event.addListener(t,"bounds_changed",this.onBoundsChanged),this.div.addEventListener("mousedown",this.onMouseDown),this.div.addEventListener("click",this.onClick),this.div.addEventListener("mouseover",this.onMouseOver),this.div.addEventListener("mouseout",this.onMouseOut))},e.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.hide(),null!==this.boundsChangedListener&&google.maps.event.removeListener(this.boundsChangedListener),this.div.removeEventListener("mousedown",this.onMouseDown),this.div.removeEventListener("click",this.onClick),this.div.removeEventListener("mouseover",this.onMouseOver),this.div.removeEventListener("mouseout",this.onMouseOut),this.div.parentNode.removeChild(this.div),null!==this.timeOut&&(window.clearTimeout(this.timeOut),this.timeOut=null),this.div=null)},e.prototype.draw=function(){if(this.visible&&null!==this.div&&this.center){var e=this.getPosFromLatLng(this.center);this.div.style.top=null!==e?"".concat(e.y,"px"):"0",this.div.style.left=null!==e?"".concat(e.x,"px"):"0"}},e.prototype.hide=function(){this.div&&(this.div.style.display="none"),this.visible=!1},e.prototype.show=function(){var e,t,n,o,s,i;if(this.div&&this.center){var r=null===this.sums||void 0===this.sums.title||""===this.sums.title?this.cluster.getClusterer().getTitle():this.sums.title,a=this.backgroundPosition.split(" "),l=parseInt((null===(e=a[0])||void 0===e?void 0:e.replace(/^\s+|\s+$/g,""))||"0",10),p=parseInt((null===(t=a[1])||void 0===t?void 0:t.replace(/^\s+|\s+$/g,""))||"0",10),u=this.getPosFromLatLng(this.center);this.div.className=this.className,this.div.setAttribute("style","cursor: pointer; position: absolute; top: ".concat(null!==u?"".concat(u.y,"px"):"0","; left: ").concat(null!==u?"".concat(u.x,"px"):"0","; width: ").concat(this.width,"px; height: ").concat(this.height,"px; "));var h=document.createElement("img");h.alt=r,h.src=this.url,h.width=this.width,h.height=this.height,h.setAttribute("style","position: absolute; top: ".concat(p,"px; left: ").concat(l,"px")),this.cluster.getClusterer().enableRetinaIcons||(h.style.clip="rect(-".concat(p,"px, -").concat(l+this.width,"px, -").concat(p+this.height,", -").concat(l,")"));var d=document.createElement("div");d.setAttribute("style","position: absolute; top: ".concat(this.anchorText[0],"px; left: ").concat(this.anchorText[1],"px; color: ").concat(this.textColor,"; font-size: ").concat(this.textSize,"px; font-family: ").concat(this.fontFamily,"; font-weight: ").concat(this.fontWeight,"; fontStyle: ").concat(this.fontStyle,"; text-decoration: ").concat(this.textDecoration,"; text-align: center; width: ").concat(this.width,"px; line-height: ").concat(this.height,"px")),(null===(n=this.sums)||void 0===n?void 0:n.text)&&(d.innerText="".concat(null===(o=this.sums)||void 0===o?void 0:o.text)),(null===(s=this.sums)||void 0===s?void 0:s.html)&&(d.innerHTML="".concat(null===(i=this.sums)||void 0===i?void 0:i.html)),this.div.innerHTML="",this.div.appendChild(h),this.div.appendChild(d),this.div.title=r,this.div.style.display=""}this.visible=!0},e.prototype.useStyle=function(e){this.sums=e;var t=this.cluster.getClusterer().getStyles(),n=t[Math.min(t.length-1,Math.max(0,e.index-1))];n&&(this.url=n.url,this.height=n.height,this.width=n.width,n.className&&(this.className="".concat(this.clusterClassName," ").concat(n.className)),this.anchorText=n.anchorText||[0,0],this.anchorIcon=n.anchorIcon||[this.height/2,this.width/2],this.textColor=n.textColor||"black",this.textSize=n.textSize||11,this.textDecoration=n.textDecoration||"none",this.fontWeight=n.fontWeight||"bold",this.fontStyle=n.fontStyle||"normal",this.fontFamily=n.fontFamily||"Arial,sans-serif",this.backgroundPosition=n.backgroundPosition||"0 0")},e.prototype.setCenter=function(e){this.center=e},e.prototype.getPosFromLatLng=function(e){var t=this.getProjection().fromLatLngToDivPixel(e);return null!==t&&(t.x-=this.anchorIcon[1],t.y-=this.anchorIcon[0]),t},e}(),De=function(){function e(e){this.markerClusterer=e,this.map=this.markerClusterer.getMap(),this.gridSize=this.markerClusterer.getGridSize(),this.minClusterSize=this.markerClusterer.getMinimumClusterSize(),this.averageCenter=this.markerClusterer.getAverageCenter(),this.markers=[],this.center=void 0,this.bounds=null,this.clusterIcon=new Se(this,this.markerClusterer.getStyles()),this.getSize=this.getSize.bind(this),this.getMarkers=this.getMarkers.bind(this),this.getCenter=this.getCenter.bind(this),this.getMap=this.getMap.bind(this),this.getClusterer=this.getClusterer.bind(this),this.getBounds=this.getBounds.bind(this),this.remove=this.remove.bind(this),this.addMarker=this.addMarker.bind(this),this.isMarkerInClusterBounds=this.isMarkerInClusterBounds.bind(this),this.calculateBounds=this.calculateBounds.bind(this),this.updateIcon=this.updateIcon.bind(this),this.isMarkerAlreadyAdded=this.isMarkerAlreadyAdded.bind(this)}return e.prototype.getSize=function(){return this.markers.length},e.prototype.getMarkers=function(){return this.markers},e.prototype.getCenter=function(){return this.center},e.prototype.getMap=function(){return this.map},e.prototype.getClusterer=function(){return this.markerClusterer},e.prototype.getBounds=function(){for(var e=new google.maps.LatLngBounds(this.center,this.center),t=0,n=this.getMarkers();t<n.length;t++){var o=n[t].getPosition();o&&e.extend(o)}return e},e.prototype.remove=function(){this.clusterIcon.setMap(null),this.markers=[],delete this.markers},e.prototype.addMarker=function(e){var t,n;if(this.isMarkerAlreadyAdded(e))return!1;if(this.center){if(this.averageCenter&&(n=e.getPosition())){var o=this.markers.length+1;this.center=new google.maps.LatLng((this.center.lat()*(o-1)+n.lat())/o,(this.center.lng()*(o-1)+n.lng())/o),this.calculateBounds()}}else(n=e.getPosition())&&(this.center=n,this.calculateBounds());e.isAdded=!0,this.markers.push(e);var s=this.markers.length,i=this.markerClusterer.getMaxZoom(),r=null===(t=this.map)||void 0===t?void 0:t.getZoom();if(null!==i&&void 0!==r&&r>i)e.getMap()!==this.map&&e.setMap(this.map);else if(s<this.minClusterSize)e.getMap()!==this.map&&e.setMap(this.map);else if(s===this.minClusterSize)for(var a=0,l=this.markers;a<l.length;a++){l[a].setMap(null)}else e.setMap(null);return!0},e.prototype.isMarkerInClusterBounds=function(e){if(null!==this.bounds){var t=e.getPosition();if(t)return this.bounds.contains(t)}return!1},e.prototype.calculateBounds=function(){this.bounds=this.markerClusterer.getExtendedBounds(new google.maps.LatLngBounds(this.center,this.center))},e.prototype.updateIcon=function(){var e,t=this.markers.length,n=this.markerClusterer.getMaxZoom(),o=null===(e=this.map)||void 0===e?void 0:e.getZoom();null!==n&&void 0!==o&&o>n||t<this.minClusterSize?this.clusterIcon.hide():(this.center&&this.clusterIcon.setCenter(this.center),this.clusterIcon.useStyle(this.markerClusterer.getCalculator()(this.markers,this.markerClusterer.getStyles().length)),this.clusterIcon.show())},e.prototype.isMarkerAlreadyAdded=function(e){if(this.markers.includes)return this.markers.includes(e);for(var t=0;t<this.markers.length;t++)if(e===this.markers[t])return!0;return!1},e}();function Ie(e,t){var n=e.length,o=n.toString().length,s=Math.min(o,t);return{text:n.toString(),index:s,title:""}}var je=[53,56,66,78,90],Be=function(){function e(t,n,o){void 0===n&&(n=[]),void 0===o&&(o={}),this.getMinimumClusterSize=this.getMinimumClusterSize.bind(this),this.setMinimumClusterSize=this.setMinimumClusterSize.bind(this),this.getEnableRetinaIcons=this.getEnableRetinaIcons.bind(this),this.setEnableRetinaIcons=this.setEnableRetinaIcons.bind(this),this.addToClosestCluster=this.addToClosestCluster.bind(this),this.getImageExtension=this.getImageExtension.bind(this),this.setImageExtension=this.setImageExtension.bind(this),this.getExtendedBounds=this.getExtendedBounds.bind(this),this.getAverageCenter=this.getAverageCenter.bind(this),this.setAverageCenter=this.setAverageCenter.bind(this),this.getTotalClusters=this.getTotalClusters.bind(this),this.fitMapToMarkers=this.fitMapToMarkers.bind(this),this.getIgnoreHidden=this.getIgnoreHidden.bind(this),this.setIgnoreHidden=this.setIgnoreHidden.bind(this),this.getClusterClass=this.getClusterClass.bind(this),this.setClusterClass=this.setClusterClass.bind(this),this.getTotalMarkers=this.getTotalMarkers.bind(this),this.getZoomOnClick=this.getZoomOnClick.bind(this),this.setZoomOnClick=this.setZoomOnClick.bind(this),this.getBatchSizeIE=this.getBatchSizeIE.bind(this),this.setBatchSizeIE=this.setBatchSizeIE.bind(this),this.createClusters=this.createClusters.bind(this),this.onZoomChanged=this.onZoomChanged.bind(this),this.getImageSizes=this.getImageSizes.bind(this),this.setImageSizes=this.setImageSizes.bind(this),this.getCalculator=this.getCalculator.bind(this),this.setCalculator=this.setCalculator.bind(this),this.removeMarkers=this.removeMarkers.bind(this),this.resetViewport=this.resetViewport.bind(this),this.getImagePath=this.getImagePath.bind(this),this.setImagePath=this.setImagePath.bind(this),this.pushMarkerTo=this.pushMarkerTo.bind(this),this.removeMarker=this.removeMarker.bind(this),this.clearMarkers=this.clearMarkers.bind(this),this.setupStyles=this.setupStyles.bind(this),this.getGridSize=this.getGridSize.bind(this),this.setGridSize=this.setGridSize.bind(this),this.getClusters=this.getClusters.bind(this),this.getMaxZoom=this.getMaxZoom.bind(this),this.setMaxZoom=this.setMaxZoom.bind(this),this.getMarkers=this.getMarkers.bind(this),this.addMarkers=this.addMarkers.bind(this),this.getStyles=this.getStyles.bind(this),this.setStyles=this.setStyles.bind(this),this.addMarker=this.addMarker.bind(this),this.onRemove=this.onRemove.bind(this),this.getTitle=this.getTitle.bind(this),this.setTitle=this.setTitle.bind(this),this.repaint=this.repaint.bind(this),this.onIdle=this.onIdle.bind(this),this.redraw=this.redraw.bind(this),this.onAdd=this.onAdd.bind(this),this.draw=this.draw.bind(this),this.extend=this.extend.bind(this),this.extend(e,google.maps.OverlayView),this.markers=[],this.clusters=[],this.listeners=[],this.activeMap=null,this.ready=!1,this.gridSize=o.gridSize||60,this.minClusterSize=o.minimumClusterSize||2,this.maxZoom=o.maxZoom||null,this.styles=o.styles||[],this.title=o.title||"",this.zoomOnClick=!0,void 0!==o.zoomOnClick&&(this.zoomOnClick=o.zoomOnClick),this.averageCenter=!1,void 0!==o.averageCenter&&(this.averageCenter=o.averageCenter),this.ignoreHidden=!1,void 0!==o.ignoreHidden&&(this.ignoreHidden=o.ignoreHidden),this.enableRetinaIcons=!1,void 0!==o.enableRetinaIcons&&(this.enableRetinaIcons=o.enableRetinaIcons),this.imagePath=o.imagePath||"https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m",this.imageExtension=o.imageExtension||"png",this.imageSizes=o.imageSizes||je,this.calculator=o.calculator||Ie,this.batchSize=o.batchSize||2e3,this.batchSizeIE=o.batchSizeIE||500,this.clusterClass=o.clusterClass||"cluster",-1!==navigator.userAgent.toLowerCase().indexOf("msie")&&(this.batchSize=this.batchSizeIE),this.timerRefStatic=null,this.setupStyles(),this.addMarkers(n,!0),this.setMap(t)}return e.prototype.onZoomChanged=function(){var e,t;this.resetViewport(!1),(null===(e=this.getMap())||void 0===e?void 0:e.getZoom())!==(this.get("minZoom")||0)&&(null===(t=this.getMap())||void 0===t?void 0:t.getZoom())!==this.get("maxZoom")||google.maps.event.trigger(this,"idle")},e.prototype.onIdle=function(){this.redraw()},e.prototype.onAdd=function(){var e=this.getMap();this.activeMap=e,this.ready=!0,this.repaint(),null!==e&&(this.listeners=[google.maps.event.addListener(e,"zoom_changed",this.onZoomChanged),google.maps.event.addListener(e,"idle",this.onIdle)])},e.prototype.onRemove=function(){for(var e=0,t=this.markers;e<t.length;e++){var n=t[e];n.getMap()!==this.activeMap&&n.setMap(this.activeMap)}for(var o=0,s=this.clusters;o<s.length;o++){s[o].remove()}this.clusters=[];for(var i=0,r=this.listeners;i<r.length;i++){var a=r[i];google.maps.event.removeListener(a)}this.listeners=[],this.activeMap=null,this.ready=!1},e.prototype.draw=function(){},e.prototype.getMap=function(){return null},e.prototype.getPanes=function(){return null},e.prototype.getProjection=function(){return{fromContainerPixelToLatLng:function(){return null},fromDivPixelToLatLng:function(){return null},fromLatLngToContainerPixel:function(){return null},fromLatLngToDivPixel:function(){return null},getVisibleRegion:function(){return null},getWorldWidth:function(){return 0}}},e.prototype.setMap=function(){},e.prototype.addListener=function(){return{remove:function(){}}},e.prototype.bindTo=function(){},e.prototype.get=function(){},e.prototype.notify=function(){},e.prototype.set=function(){},e.prototype.setValues=function(){},e.prototype.unbind=function(){},e.prototype.unbindAll=function(){},e.prototype.setupStyles=function(){if(!(this.styles.length>0))for(var e=0;e<this.imageSizes.length;e++)this.styles.push({url:"".concat(this.imagePath+(e+1),".").concat(this.imageExtension),height:this.imageSizes[e]||0,width:this.imageSizes[e]||0})},e.prototype.fitMapToMarkers=function(){for(var e=this.getMarkers(),t=new google.maps.LatLngBounds,n=0,o=e;n<o.length;n++){var s=o[n].getPosition();s&&t.extend(s)}var i=this.getMap();null!==i&&"fitBounds"in i&&i.fitBounds(t)},e.prototype.getGridSize=function(){return this.gridSize},e.prototype.setGridSize=function(e){this.gridSize=e},e.prototype.getMinimumClusterSize=function(){return this.minClusterSize},e.prototype.setMinimumClusterSize=function(e){this.minClusterSize=e},e.prototype.getMaxZoom=function(){return this.maxZoom},e.prototype.setMaxZoom=function(e){this.maxZoom=e},e.prototype.getStyles=function(){return this.styles},e.prototype.setStyles=function(e){this.styles=e},e.prototype.getTitle=function(){return this.title},e.prototype.setTitle=function(e){this.title=e},e.prototype.getZoomOnClick=function(){return this.zoomOnClick},e.prototype.setZoomOnClick=function(e){this.zoomOnClick=e},e.prototype.getAverageCenter=function(){return this.averageCenter},e.prototype.setAverageCenter=function(e){this.averageCenter=e},e.prototype.getIgnoreHidden=function(){return this.ignoreHidden},e.prototype.setIgnoreHidden=function(e){this.ignoreHidden=e},e.prototype.getEnableRetinaIcons=function(){return this.enableRetinaIcons},e.prototype.setEnableRetinaIcons=function(e){this.enableRetinaIcons=e},e.prototype.getImageExtension=function(){return this.imageExtension},e.prototype.setImageExtension=function(e){this.imageExtension=e},e.prototype.getImagePath=function(){return this.imagePath},e.prototype.setImagePath=function(e){this.imagePath=e},e.prototype.getImageSizes=function(){return this.imageSizes},e.prototype.setImageSizes=function(e){this.imageSizes=e},e.prototype.getCalculator=function(){return this.calculator},e.prototype.setCalculator=function(e){this.calculator=e},e.prototype.getBatchSizeIE=function(){return this.batchSizeIE},e.prototype.setBatchSizeIE=function(e){this.batchSizeIE=e},e.prototype.getClusterClass=function(){return this.clusterClass},e.prototype.setClusterClass=function(e){this.clusterClass=e},e.prototype.getMarkers=function(){return this.markers},e.prototype.getTotalMarkers=function(){return this.markers.length},e.prototype.getClusters=function(){return this.clusters},e.prototype.getTotalClusters=function(){return this.clusters.length},e.prototype.addMarker=function(e,t){this.pushMarkerTo(e),t||this.redraw()},e.prototype.addMarkers=function(e,t){for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var o=e[n];o&&this.pushMarkerTo(o)}t||this.redraw()},e.prototype.pushMarkerTo=function(e){var t=this;e.getDraggable()&&google.maps.event.addListener(e,"dragend",(function(){t.ready&&(e.isAdded=!1,t.repaint())})),e.isAdded=!1,this.markers.push(e)},e.prototype.removeMarker_=function(e){var t=-1;if(this.markers.indexOf)t=this.markers.indexOf(e);else for(var n=0;n<this.markers.length;n++)if(e===this.markers[n]){t=n;break}return-1!==t&&(e.setMap(null),this.markers.splice(t,1),!0)},e.prototype.removeMarker=function(e,t){var n=this.removeMarker_(e);return!t&&n&&this.repaint(),n},e.prototype.removeMarkers=function(e,t){for(var n=!1,o=0,s=e;o<s.length;o++){var i=s[o];n=n||this.removeMarker_(i)}return!t&&n&&this.repaint(),n},e.prototype.clearMarkers=function(){this.resetViewport(!0),this.markers=[]},e.prototype.repaint=function(){var e=this.clusters.slice();this.clusters=[],this.resetViewport(!1),this.redraw(),setTimeout((function(){for(var t=0,n=e;t<n.length;t++){n[t].remove()}}),0)},e.prototype.getExtendedBounds=function(e){var t=this.getProjection(),n=t.fromLatLngToDivPixel(new google.maps.LatLng(e.getNorthEast().lat(),e.getNorthEast().lng()));null!==n&&(n.x+=this.gridSize,n.y-=this.gridSize);var o=t.fromLatLngToDivPixel(new google.maps.LatLng(e.getSouthWest().lat(),e.getSouthWest().lng()));if(null!==o&&(o.x-=this.gridSize,o.y+=this.gridSize),null!==n){var s=t.fromDivPixelToLatLng(n);null!==s&&e.extend(s)}if(null!==o){var i=t.fromDivPixelToLatLng(o);null!==i&&e.extend(i)}return e},e.prototype.redraw=function(){this.createClusters(0)},e.prototype.resetViewport=function(e){for(var t=0,n=this.clusters;t<n.length;t++){n[t].remove()}this.clusters=[];for(var o=0,s=this.markers;o<s.length;o++){var i=s[o];i.isAdded=!1,e&&i.setMap(null)}},e.prototype.distanceBetweenPoints=function(e,t){var n=(t.lat()-e.lat())*Math.PI/180,o=(t.lng()-e.lng())*Math.PI/180,s=Math.sin(n/2)*Math.sin(n/2)+Math.cos(e.lat()*Math.PI/180)*Math.cos(t.lat()*Math.PI/180)*Math.sin(o/2)*Math.sin(o/2);return 2*Math.atan2(Math.sqrt(s),Math.sqrt(1-s))*6371},e.prototype.isMarkerInBounds=function(e,t){var n=e.getPosition();return!!n&&t.contains(n)},e.prototype.addToClosestCluster=function(e){for(var t,n=4e4,o=null,s=0,i=this.clusters;s<i.length;s++){var r=(t=i[s]).getCenter(),a=e.getPosition();if(r&&a){var l=this.distanceBetweenPoints(r,a);l<n&&(n=l,o=t)}}o&&o.isMarkerInClusterBounds(e)?o.addMarker(e):((t=new De(this)).addMarker(e),this.clusters.push(t))},e.prototype.createClusters=function(e){var t=this;if(this.ready){0===e&&(google.maps.event.trigger(this,"clusteringbegin",this),null!==this.timerRefStatic&&(window.clearTimeout(this.timerRefStatic),delete this.timerRefStatic));for(var n=this.getMap(),o=(null!==n&&"getBounds"in n?n.getBounds():null),s=((null==n?void 0:n.getZoom())||0)>3?new google.maps.LatLngBounds(null==o?void 0:o.getSouthWest(),null==o?void 0:o.getNorthEast()):new google.maps.LatLngBounds(new google.maps.LatLng(85.02070771743472,-178.48388434375),new google.maps.LatLng(-85.08136444384544,178.00048865625)),i=this.getExtendedBounds(s),r=Math.min(e+this.batchSize,this.markers.length),a=e;a<r;a++){var l=this.markers[a];l&&!l.isAdded&&this.isMarkerInBounds(l,i)&&(!this.ignoreHidden||this.ignoreHidden&&l.getVisible())&&this.addToClosestCluster(l)}if(r<this.markers.length)this.timerRefStatic=window.setTimeout((function(){t.createClusters(r)}),0);else{this.timerRefStatic=null,google.maps.event.trigger(this,"clusteringend",this);for(var p=0,u=this.clusters;p<u.length;p++){u[p].updateIcon()}}}},e.prototype.extend=function(e,t){return function(e){for(var t in e.prototype){var n=t;this.prototype[n]=e.prototype[n]}return this}.apply(e,[t])},e}();function _e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}var Te={onClick:"click",onClusteringBegin:"clusteringbegin",onClusteringEnd:"clusteringend",onMouseOut:"mouseout",onMouseOver:"mouseover"},Ue={averageCenter(e,t){e.setAverageCenter(t)},batchSizeIE(e,t){e.setBatchSizeIE(t)},calculator(e,t){e.setCalculator(t)},clusterClass(e,t){e.setClusterClass(t)},enableRetinaIcons(e,t){e.setEnableRetinaIcons(t)},gridSize(e,t){e.setGridSize(t)},ignoreHidden(e,t){e.setIgnoreHidden(t)},imageExtension(e,t){e.setImageExtension(t)},imagePath(e,t){e.setImagePath(t)},imageSizes(e,t){e.setImageSizes(t)},maxZoom(e,t){e.setMaxZoom(t)},minimumClusterSize(e,t){e.setMinimumClusterSize(t)},styles(e,t){e.setStyles(t)},title(e,t){e.setTitle(t)},zoomOnClick(e,t){e.setZoomOnClick(t)}},ze={};var Re=i((function(e){var{children:t,options:n,averageCenter:o,batchSizeIE:i,calculator:a,clusterClass:p,enableRetinaIcons:u,gridSize:h,ignoreHidden:d,imageExtension:g,imagePath:c,imageSizes:m,maxZoom:v,minimumClusterSize:f,styles:y,title:L,zoomOnClick:w,onClick:M,onClusteringBegin:C,onClusteringEnd:k,onMouseOver:P,onMouseOut:O,onLoad:E,onUnmount:S}=e,[D,I]=r(null),j=s(x),[B,_]=r(null),[T,U]=r(null),[z,R]=r(null),[A,Z]=r(null),[V,W]=r(null);return l((()=>{D&&O&&(null!==A&&google.maps.event.removeListener(A),Z(google.maps.event.addListener(D,Te.onMouseOut,O)))}),[O]),l((()=>{D&&P&&(null!==V&&google.maps.event.removeListener(V),W(google.maps.event.addListener(D,Te.onMouseOver,P)))}),[P]),l((()=>{D&&M&&(null!==B&&google.maps.event.removeListener(B),_(google.maps.event.addListener(D,Te.onClick,M)))}),[M]),l((()=>{D&&C&&(null!==T&&google.maps.event.removeListener(T),U(google.maps.event.addListener(D,Te.onClusteringBegin,C)))}),[C]),l((()=>{D&&k&&(null!==z&&google.maps.event.removeListener(z),U(google.maps.event.addListener(D,Te.onClusteringEnd,k)))}),[k]),l((()=>{void 0!==o&&null!==D&&Ue.averageCenter(D,o)}),[D,o]),l((()=>{void 0!==i&&null!==D&&Ue.batchSizeIE(D,i)}),[D,i]),l((()=>{void 0!==a&&null!==D&&Ue.calculator(D,a)}),[D,a]),l((()=>{void 0!==p&&null!==D&&Ue.clusterClass(D,p)}),[D,p]),l((()=>{void 0!==u&&null!==D&&Ue.enableRetinaIcons(D,u)}),[D,u]),l((()=>{void 0!==h&&null!==D&&Ue.gridSize(D,h)}),[D,h]),l((()=>{void 0!==d&&null!==D&&Ue.ignoreHidden(D,d)}),[D,d]),l((()=>{void 0!==g&&null!==D&&Ue.imageExtension(D,g)}),[D,g]),l((()=>{void 0!==c&&null!==D&&Ue.imagePath(D,c)}),[D,c]),l((()=>{void 0!==m&&null!==D&&Ue.imageSizes(D,m)}),[D,m]),l((()=>{void 0!==v&&null!==D&&Ue.maxZoom(D,v)}),[D,v]),l((()=>{void 0!==f&&null!==D&&Ue.minimumClusterSize(D,f)}),[D,f]),l((()=>{void 0!==y&&null!==D&&Ue.styles(D,y)}),[D,y]),l((()=>{void 0!==L&&null!==D&&Ue.title(D,L)}),[D,L]),l((()=>{void 0!==w&&null!==D&&Ue.zoomOnClick(D,w)}),[D,w]),l((()=>{if(j){var e=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_e(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_e(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},n||ze),t=new Be(j,[],e);return o&&Ue.averageCenter(t,o),i&&Ue.batchSizeIE(t,i),a&&Ue.calculator(t,a),p&&Ue.clusterClass(t,p),u&&Ue.enableRetinaIcons(t,u),h&&Ue.gridSize(t,h),d&&Ue.ignoreHidden(t,d),g&&Ue.imageExtension(t,g),c&&Ue.imagePath(t,c),m&&Ue.imageSizes(t,m),v&&Ue.maxZoom(t,v),f&&Ue.minimumClusterSize(t,f),y&&Ue.styles(t,y),L&&Ue.title(t,L),w&&Ue.zoomOnClick(t,w),O&&Z(google.maps.event.addListener(t,Te.onMouseOut,O)),P&&W(google.maps.event.addListener(t,Te.onMouseOver,P)),M&&_(google.maps.event.addListener(t,Te.onClick,M)),C&&U(google.maps.event.addListener(t,Te.onClusteringBegin,C)),k&&R(google.maps.event.addListener(t,Te.onClusteringEnd,k)),I(t),E&&E(t),()=>{null!==A&&google.maps.event.removeListener(A),null!==V&&google.maps.event.removeListener(V),null!==B&&google.maps.event.removeListener(B),null!==T&&google.maps.event.removeListener(T),null!==z&&google.maps.event.removeListener(z),S&&S(t)}}}),[]),null!==D&&t(D)||null}));class Ae extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"state",{markerClusterer:null}),b(this,"setClustererCallback",(()=>{null!==this.state.markerClusterer&&this.props.onLoad&&this.props.onLoad(this.state.markerClusterer)}))}componentDidMount(){if(this.context){var e=new Be(this.context,[],this.props.options);this.registeredEvents=D({updaterMap:Ue,eventMap:Te,prevProps:{},nextProps:this.props,instance:e}),this.setState((()=>({markerClusterer:e})),this.setClustererCallback)}}componentDidUpdate(e){this.state.markerClusterer&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:Ue,eventMap:Te,prevProps:e,nextProps:this.props,instance:this.state.markerClusterer}))}componentWillUnmount(){null!==this.state.markerClusterer&&(this.props.onUnmount&&this.props.onUnmount(this.state.markerClusterer),S(this.registeredEvents),this.state.markerClusterer.setMap(null))}render(){return null!==this.state.markerClusterer?this.props.children(this.state.markerClusterer):null}}function Ze(e){e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()}b(Ae,"contextType",x);var Ve=function(){function e(t){void 0===t&&(t={}),this.getCloseClickHandler=this.getCloseClickHandler.bind(this),this.closeClickHandler=this.closeClickHandler.bind(this),this.createInfoBoxDiv=this.createInfoBoxDiv.bind(this),this.addClickHandler=this.addClickHandler.bind(this),this.getCloseBoxImg=this.getCloseBoxImg.bind(this),this.getBoxWidths=this.getBoxWidths.bind(this),this.setBoxStyle=this.setBoxStyle.bind(this),this.setPosition=this.setPosition.bind(this),this.getPosition=this.getPosition.bind(this),this.setOptions=this.setOptions.bind(this),this.setContent=this.setContent.bind(this),this.setVisible=this.setVisible.bind(this),this.getContent=this.getContent.bind(this),this.getVisible=this.getVisible.bind(this),this.setZIndex=this.setZIndex.bind(this),this.getZIndex=this.getZIndex.bind(this),this.onRemove=this.onRemove.bind(this),this.panBox=this.panBox.bind(this),this.extend=this.extend.bind(this),this.close=this.close.bind(this),this.draw=this.draw.bind(this),this.show=this.show.bind(this),this.hide=this.hide.bind(this),this.open=this.open.bind(this),this.extend(e,google.maps.OverlayView),this.content=t.content||"",this.disableAutoPan=t.disableAutoPan||!1,this.maxWidth=t.maxWidth||0,this.pixelOffset=t.pixelOffset||new google.maps.Size(0,0),this.position=t.position||new google.maps.LatLng(0,0),this.zIndex=t.zIndex||null,this.boxClass=t.boxClass||"infoBox",this.boxStyle=t.boxStyle||{},this.closeBoxMargin=t.closeBoxMargin||"2px",this.closeBoxURL=t.closeBoxURL||"http://www.google.com/intl/en_us/mapfiles/close.gif",""===t.closeBoxURL&&(this.closeBoxURL=""),this.infoBoxClearance=t.infoBoxClearance||new google.maps.Size(1,1),void 0===t.visible&&(void 0===t.isHidden?t.visible=!0:t.visible=!t.isHidden),this.isHidden=!t.visible,this.alignBottom=t.alignBottom||!1,this.pane=t.pane||"floatPane",this.enableEventPropagation=t.enableEventPropagation||!1,this.div=null,this.closeListener=null,this.moveListener=null,this.mapListener=null,this.contextListener=null,this.eventListeners=null,this.fixedWidthSet=null}return e.prototype.createInfoBoxDiv=function(){var e=this;if(!this.div){this.div=document.createElement("div"),this.setBoxStyle(),"string"==typeof this.content?this.div.innerHTML=this.getCloseBoxImg()+this.content:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(this.content));var t=this.getPanes();if(null!==t&&t[this.pane].appendChild(this.div),this.addClickHandler(),this.div.style.width)this.fixedWidthSet=!0;else if(0!==this.maxWidth&&this.div.offsetWidth>this.maxWidth)this.div.style.width=this.maxWidth+"px",this.fixedWidthSet=!0;else{var n=this.getBoxWidths();this.div.style.width=this.div.offsetWidth-n.left-n.right+"px",this.fixedWidthSet=!1}if(this.panBox(this.disableAutoPan),!this.enableEventPropagation){this.eventListeners=[];for(var o=0,s=["mousedown","mouseover","mouseout","mouseup","click","dblclick","touchstart","touchend","touchmove"];o<s.length;o++){var i=s[o];this.eventListeners.push(google.maps.event.addListener(this.div,i,Ze))}this.eventListeners.push(google.maps.event.addListener(this.div,"mouseover",(function(){e.div&&(e.div.style.cursor="default")})))}this.contextListener=google.maps.event.addListener(this.div,"contextmenu",(function(t){t.returnValue=!1,t.preventDefault&&t.preventDefault(),e.enableEventPropagation||Ze(t)})),google.maps.event.trigger(this,"domready")}},e.prototype.getCloseBoxImg=function(){var e="";return""!==this.closeBoxURL&&(e='<img alt=""',e+=' aria-hidden="true"',e+=" src='"+this.closeBoxURL+"'",e+=" align=right",e+=" style='",e+=" position: relative;",e+=" cursor: pointer;",e+=" margin: "+this.closeBoxMargin+";",e+="'>"),e},e.prototype.addClickHandler=function(){this.closeListener=this.div&&this.div.firstChild&&""!==this.closeBoxURL?google.maps.event.addListener(this.div.firstChild,"click",this.getCloseClickHandler()):null},e.prototype.closeClickHandler=function(e){e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation(),google.maps.event.trigger(this,"closeclick"),this.close()},e.prototype.getCloseClickHandler=function(){return this.closeClickHandler},e.prototype.panBox=function(e){if(this.div&&!e){var t=this.getMap();if(t instanceof google.maps.Map){var n=0,o=0,s=t.getBounds();s&&!s.contains(this.position)&&t.setCenter(this.position);var i=t.getDiv(),r=i.offsetWidth,a=i.offsetHeight,l=this.pixelOffset.width,p=this.pixelOffset.height,u=this.div.offsetWidth,h=this.div.offsetHeight,d=this.infoBoxClearance.width,g=this.infoBoxClearance.height,c=this.getProjection().fromLatLngToContainerPixel(this.position);null!==c&&(c.x<-l+d?n=c.x+l-d:c.x+u+l+d>r&&(n=c.x+u+l+d-r),this.alignBottom?c.y<-p+g+h?o=c.y+p-g-h:c.y+p+g>a&&(o=c.y+p+g-a):c.y<-p+g?o=c.y+p-g:c.y+h+p+g>a&&(o=c.y+h+p+g-a)),0===n&&0===o||t.panBy(n,o)}}},e.prototype.setBoxStyle=function(){if(this.div){this.div.className=this.boxClass,this.div.style.cssText="";var e=this.boxStyle;for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(this.div.style[t]=e[t]);if(this.div.style.webkitTransform="translateZ(0)",void 0!==this.div.style.opacity&&""!==this.div.style.opacity){var n=parseFloat(this.div.style.opacity||"");this.div.style.msFilter='"progid:DXImageTransform.Microsoft.Alpha(Opacity='+100*n+')"',this.div.style.filter="alpha(opacity="+100*n+")"}this.div.style.position="absolute",this.div.style.visibility="hidden",null!==this.zIndex&&(this.div.style.zIndex=this.zIndex+""),this.div.style.overflow||(this.div.style.overflow="auto")}},e.prototype.getBoxWidths=function(){var e={top:0,bottom:0,left:0,right:0};if(!this.div)return e;if(document.defaultView){var t=this.div.ownerDocument,n=t&&t.defaultView?t.defaultView.getComputedStyle(this.div,""):null;n&&(e.top=parseInt(n.borderTopWidth||"",10)||0,e.bottom=parseInt(n.borderBottomWidth||"",10)||0,e.left=parseInt(n.borderLeftWidth||"",10)||0,e.right=parseInt(n.borderRightWidth||"",10)||0)}else if(document.documentElement.currentStyle){var o=this.div.currentStyle;o&&(e.top=parseInt(o.borderTopWidth||"",10)||0,e.bottom=parseInt(o.borderBottomWidth||"",10)||0,e.left=parseInt(o.borderLeftWidth||"",10)||0,e.right=parseInt(o.borderRightWidth||"",10)||0)}return e},e.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.div.parentNode.removeChild(this.div),this.div=null)},e.prototype.draw=function(){if(this.createInfoBoxDiv(),this.div){var e=this.getProjection().fromLatLngToDivPixel(this.position);null!==e&&(this.div.style.left=e.x+this.pixelOffset.width+"px",this.alignBottom?this.div.style.bottom=-(e.y+this.pixelOffset.height)+"px":this.div.style.top=e.y+this.pixelOffset.height+"px"),this.isHidden?this.div.style.visibility="hidden":this.div.style.visibility="visible"}},e.prototype.setOptions=function(e){void 0===e&&(e={}),void 0!==e.boxClass&&(this.boxClass=e.boxClass,this.setBoxStyle()),void 0!==e.boxStyle&&(this.boxStyle=e.boxStyle,this.setBoxStyle()),void 0!==e.content&&this.setContent(e.content),void 0!==e.disableAutoPan&&(this.disableAutoPan=e.disableAutoPan),void 0!==e.maxWidth&&(this.maxWidth=e.maxWidth),void 0!==e.pixelOffset&&(this.pixelOffset=e.pixelOffset),void 0!==e.alignBottom&&(this.alignBottom=e.alignBottom),void 0!==e.position&&this.setPosition(e.position),void 0!==e.zIndex&&this.setZIndex(e.zIndex),void 0!==e.closeBoxMargin&&(this.closeBoxMargin=e.closeBoxMargin),void 0!==e.closeBoxURL&&(this.closeBoxURL=e.closeBoxURL),void 0!==e.infoBoxClearance&&(this.infoBoxClearance=e.infoBoxClearance),void 0!==e.isHidden&&(this.isHidden=e.isHidden),void 0!==e.visible&&(this.isHidden=!e.visible),void 0!==e.enableEventPropagation&&(this.enableEventPropagation=e.enableEventPropagation),this.div&&this.draw()},e.prototype.setContent=function(e){this.content=e,this.div&&(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.fixedWidthSet||(this.div.style.width=""),"string"==typeof e?this.div.innerHTML=this.getCloseBoxImg()+e:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(e)),this.fixedWidthSet||(this.div.style.width=this.div.offsetWidth+"px","string"==typeof e?this.div.innerHTML=this.getCloseBoxImg()+e:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(e))),this.addClickHandler()),google.maps.event.trigger(this,"content_changed")},e.prototype.setPosition=function(e){this.position=e,this.div&&this.draw(),google.maps.event.trigger(this,"position_changed")},e.prototype.setVisible=function(e){this.isHidden=!e,this.div&&(this.div.style.visibility=this.isHidden?"hidden":"visible")},e.prototype.setZIndex=function(e){this.zIndex=e,this.div&&(this.div.style.zIndex=e+""),google.maps.event.trigger(this,"zindex_changed")},e.prototype.getContent=function(){return this.content},e.prototype.getPosition=function(){return this.position},e.prototype.getZIndex=function(){return this.zIndex},e.prototype.getVisible=function(){var e=this.getMap();return null!=e&&!this.isHidden},e.prototype.show=function(){this.isHidden=!1,this.div&&(this.div.style.visibility="visible")},e.prototype.hide=function(){this.isHidden=!0,this.div&&(this.div.style.visibility="hidden")},e.prototype.open=function(e,t){var n=this;t&&(this.position=t.getPosition(),this.moveListener=google.maps.event.addListener(t,"position_changed",(function(){var e=t.getPosition();n.setPosition(e)})),this.mapListener=google.maps.event.addListener(t,"map_changed",(function(){n.setMap(t.map)}))),this.setMap(e),this.div&&this.panBox()},e.prototype.close=function(){if(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.eventListeners){for(var e=0,t=this.eventListeners;e<t.length;e++){var n=t[e];google.maps.event.removeListener(n)}this.eventListeners=null}this.moveListener&&(google.maps.event.removeListener(this.moveListener),this.moveListener=null),this.mapListener&&(google.maps.event.removeListener(this.mapListener),this.mapListener=null),this.contextListener&&(google.maps.event.removeListener(this.contextListener),this.contextListener=null),this.setMap(null)},e.prototype.extend=function(e,t){return function(e){for(var t in e.prototype)Object.prototype.hasOwnProperty.call(this,t)||(this.prototype[t]=e.prototype[t]);return this}.apply(e,[t])},e}(),We=["position"],Ne=["position"];function He(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Ge(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?He(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):He(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Fe={onCloseClick:"closeclick",onContentChanged:"content_changed",onDomReady:"domready",onPositionChanged:"position_changed",onZindexChanged:"zindex_changed"},Ke={options(e,t){e.setOptions(t)},position(e,t){t instanceof google.maps.LatLng?e.setPosition(t):e.setPosition(new google.maps.LatLng(t.lat,t.lng))},visible(e,t){e.setVisible(t)},zIndex(e,t){e.setZIndex(t)}},Ye={};var qe,Je,Xe=i((function(e){var{children:t,anchor:n,options:o,position:i,zIndex:p,onCloseClick:u,onDomReady:d,onContentChanged:g,onPositionChanged:c,onZindexChanged:m,onLoad:f,onUnmount:y}=e,b=s(x),[L,w]=r(null),[M,k]=r(null),[P,O]=r(null),[E,S]=r(null),[D,I]=r(null),[j,B]=r(null),_=a(null);return l((()=>{b&&null!==L&&(L.close(),n?L.open(b,n):L.getPosition()&&L.open(b))}),[b,L,n]),l((()=>{o&&null!==L&&L.setOptions(o)}),[L,o]),l((()=>{if(i&&null!==L){var e=i instanceof google.maps.LatLng?i:new google.maps.LatLng(i.lat,i.lng);L.setPosition(e)}}),[i]),l((()=>{"number"==typeof p&&null!==L&&L.setZIndex(p)}),[p]),l((()=>{L&&u&&(null!==M&&google.maps.event.removeListener(M),k(google.maps.event.addListener(L,"closeclick",u)))}),[u]),l((()=>{L&&d&&(null!==P&&google.maps.event.removeListener(P),O(google.maps.event.addListener(L,"domready",d)))}),[d]),l((()=>{L&&g&&(null!==E&&google.maps.event.removeListener(E),S(google.maps.event.addListener(L,"content_changed",g)))}),[g]),l((()=>{L&&c&&(null!==D&&google.maps.event.removeListener(D),I(google.maps.event.addListener(L,"position_changed",c)))}),[c]),l((()=>{L&&m&&(null!==j&&google.maps.event.removeListener(j),B(google.maps.event.addListener(L,"zindex_changed",m)))}),[m]),l((()=>{if(b){var e,t=o||Ye,{position:s}=t,i=F(t,We);!s||s instanceof google.maps.LatLng||(e=new google.maps.LatLng(s.lat,s.lng));var r=new Ve(Ge(Ge({},i),e?{position:e}:{}));_.current=document.createElement("div"),w(r),u&&k(google.maps.event.addListener(r,"closeclick",u)),d&&O(google.maps.event.addListener(r,"domready",d)),g&&S(google.maps.event.addListener(r,"content_changed",g)),c&&I(google.maps.event.addListener(r,"position_changed",c)),m&&B(google.maps.event.addListener(r,"zindex_changed",m)),r.setContent(_.current),n?r.open(b,n):r.getPosition()?r.open(b):C(!1,"You must provide either an anchor or a position prop for <InfoBox>."),f&&f(r)}return()=>{null!==L&&(M&&google.maps.event.removeListener(M),E&&google.maps.event.removeListener(E),P&&google.maps.event.removeListener(P),D&&google.maps.event.removeListener(D),j&&google.maps.event.removeListener(j),y&&y(L),L.close())}}),[]),_.current?v(h.only(t),_.current):null}));class $e extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"containerElement",null),b(this,"state",{infoBox:null}),b(this,"open",((e,t)=>{t?null!==this.context&&e.open(this.context,t):e.getPosition()?null!==this.context&&e.open(this.context):C(!1,"You must provide either an anchor or a position prop for <InfoBox>.")})),b(this,"setInfoBoxCallback",(()=>{null!==this.state.infoBox&&null!==this.containerElement&&(this.state.infoBox.setContent(this.containerElement),this.open(this.state.infoBox,this.props.anchor),this.props.onLoad&&this.props.onLoad(this.state.infoBox))}))}componentDidMount(){var e,t=this.props.options||{},{position:n}=t,o=F(t,Ne);!n||n instanceof google.maps.LatLng||(e=new google.maps.LatLng(n.lat,n.lng));var s=new Ve(Ge(Ge({},o),e?{position:e}:{}));this.containerElement=document.createElement("div"),this.registeredEvents=D({updaterMap:Ke,eventMap:Fe,prevProps:{},nextProps:this.props,instance:s}),this.setState({infoBox:s},this.setInfoBoxCallback)}componentDidUpdate(e){var{infoBox:t}=this.state;null!==t&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:Ke,eventMap:Fe,prevProps:e,nextProps:this.props,instance:t}))}componentWillUnmount(){var{onUnmount:e}=this.props,{infoBox:t}=this.state;null!==t&&(e&&e(t),S(this.registeredEvents),t.close())}render(){return this.containerElement?v(h.only(this.props.children),this.containerElement):null}}b($e,"contextType",x);var Qe=(Je||(Je=1,qe=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var o,s,i;if(Array.isArray(t)){if((o=t.length)!=n.length)return!1;for(s=o;0!=s--;)if(!e(t[s],n[s]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((o=(i=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(s=o;0!=s--;)if(!Object.prototype.hasOwnProperty.call(n,i[s]))return!1;for(s=o;0!=s--;){var r=i[s];if(!e(t[r],n[r]))return!1}return!0}return t!=t&&n!=n}),qe),et=L(Qe),tt=[Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];class nt{static from(e){if(!(e instanceof ArrayBuffer))throw new Error("Data must be an instance of ArrayBuffer.");var[t,n]=new Uint8Array(e,0,2);if(219!==t)throw new Error("Data does not appear to be in a KDBush format.");var o=n>>4;if(1!==o)throw new Error("Got v".concat(o," data when expected v").concat(1,"."));var s=tt[15&n];if(!s)throw new Error("Unrecognized array type.");var[i]=new Uint16Array(e,2,1),[r]=new Uint32Array(e,4,1);return new nt(r,i,s,e)}constructor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:64,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Float64Array,o=arguments.length>3?arguments[3]:void 0;if(isNaN(e)||e<0)throw new Error("Unpexpected numItems value: ".concat(e,"."));this.numItems=+e,this.nodeSize=Math.min(Math.max(+t,2),65535),this.ArrayType=n,this.IndexArrayType=e<65536?Uint16Array:Uint32Array;var s=tt.indexOf(this.ArrayType),i=2*e*this.ArrayType.BYTES_PER_ELEMENT,r=e*this.IndexArrayType.BYTES_PER_ELEMENT,a=(8-r%8)%8;if(s<0)throw new Error("Unexpected typed array class: ".concat(n,"."));o&&o instanceof ArrayBuffer?(this.data=o,this.ids=new this.IndexArrayType(this.data,8,e),this.coords=new this.ArrayType(this.data,8+r+a,2*e),this._pos=2*e,this._finished=!0):(this.data=new ArrayBuffer(8+i+r+a),this.ids=new this.IndexArrayType(this.data,8,e),this.coords=new this.ArrayType(this.data,8+r+a,2*e),this._pos=0,this._finished=!1,new Uint8Array(this.data,0,2).set([219,16+s]),new Uint16Array(this.data,2,1)[0]=t,new Uint32Array(this.data,4,1)[0]=e)}add(e,t){var n=this._pos>>1;return this.ids[n]=n,this.coords[this._pos++]=e,this.coords[this._pos++]=t,n}finish(){var e=this._pos>>1;if(e!==this.numItems)throw new Error("Added ".concat(e," items when expected ").concat(this.numItems,"."));return ot(this.ids,this.coords,this.nodeSize,0,this.numItems-1,0),this._finished=!0,this}range(e,t,n,o){if(!this._finished)throw new Error("Data not yet indexed - call index.finish().");for(var{ids:s,coords:i,nodeSize:r}=this,a=[0,s.length-1,0],l=[];a.length;){var p=a.pop()||0,u=a.pop()||0,h=a.pop()||0;if(u-h<=r)for(var d=h;d<=u;d++){var g=i[2*d],c=i[2*d+1];g>=e&&g<=n&&c>=t&&c<=o&&l.push(s[d])}else{var m=h+u>>1,v=i[2*m],f=i[2*m+1];v>=e&&v<=n&&f>=t&&f<=o&&l.push(s[m]),(0===p?e<=v:t<=f)&&(a.push(h),a.push(m-1),a.push(1-p)),(0===p?n>=v:o>=f)&&(a.push(m+1),a.push(u),a.push(1-p))}}return l}within(e,t,n){if(!this._finished)throw new Error("Data not yet indexed - call index.finish().");for(var{ids:o,coords:s,nodeSize:i}=this,r=[0,o.length-1,0],a=[],l=n*n;r.length;){var p=r.pop()||0,u=r.pop()||0,h=r.pop()||0;if(u-h<=i)for(var d=h;d<=u;d++)at(s[2*d],s[2*d+1],e,t)<=l&&a.push(o[d]);else{var g=h+u>>1,c=s[2*g],m=s[2*g+1];at(c,m,e,t)<=l&&a.push(o[g]),(0===p?e-n<=c:t-n<=m)&&(r.push(h),r.push(g-1),r.push(1-p)),(0===p?e+n>=c:t+n>=m)&&(r.push(g+1),r.push(u),r.push(1-p))}}return a}}function ot(e,t,n,o,s,i){if(!(s-o<=n)){var r=o+s>>1;st(e,t,r,o,s,i),ot(e,t,n,o,r-1,1-i),ot(e,t,n,r+1,s,1-i)}}function st(e,t,n,o,s,i){for(;s>o;){if(s-o>600){var r=s-o+1,a=n-o+1,l=Math.log(r),p=.5*Math.exp(2*l/3),u=.5*Math.sqrt(l*p*(r-p)/r)*(a-r/2<0?-1:1);st(e,t,n,Math.max(o,Math.floor(n-a*p/r+u)),Math.min(s,Math.floor(n+(r-a)*p/r+u)),i)}var h=t[2*n+i],d=o,g=s;for(it(e,t,o,n),t[2*s+i]>h&&it(e,t,o,s);d<g;){for(it(e,t,d,g),d++,g--;t[2*d+i]<h;)d++;for(;t[2*g+i]>h;)g--}t[2*o+i]===h?it(e,t,o,g):it(e,t,++g,s),g<=n&&(o=g+1),n<=g&&(s=g-1)}}function it(e,t,n,o){rt(e,n,o),rt(t,2*n,2*o),rt(t,2*n+1,2*o+1)}function rt(e,t,n){var o=e[t];e[t]=e[n],e[n]=o}function at(e,t,n,o){var s=e-n,i=t-o;return s*s+i*i}var lt,pt={minZoom:0,maxZoom:16,minPoints:2,radius:40,extent:512,nodeSize:64,log:!1,generateId:!1,reduce:null,map:e=>e},ut=Math.fround||(lt=new Float32Array(1),e=>(lt[0]=+e,lt[0]));class ht{constructor(e){this.options=Object.assign(Object.create(pt),e),this.trees=new Array(this.options.maxZoom+1),this.stride=this.options.reduce?7:6,this.clusterProps=[]}load(e){var{log:t,minZoom:n,maxZoom:o}=this.options;t&&console.time("total time");var s="prepare ".concat(e.length," points");t&&console.time(s),this.points=e;for(var i=[],r=0;r<e.length;r++){var a=e[r];if(a.geometry){var[l,p]=a.geometry.coordinates,u=ut(ct(l)),h=ut(mt(p));i.push(u,h,1/0,r,-1,1),this.options.reduce&&i.push(0)}}var d=this.trees[o+1]=this._createTree(i);t&&console.timeEnd(s);for(var g=o;g>=n;g--){var c=+Date.now();d=this.trees[g]=this._createTree(this._cluster(d,g)),t&&console.log("z%d: %d clusters in %dms",g,d.numItems,+Date.now()-c)}return t&&console.timeEnd("total time"),this}getClusters(e,t){var n=((e[0]+180)%360+360)%360-180,o=Math.max(-90,Math.min(90,e[1])),s=180===e[2]?180:((e[2]+180)%360+360)%360-180,i=Math.max(-90,Math.min(90,e[3]));if(e[2]-e[0]>=360)n=-180,s=180;else if(n>s){var r=this.getClusters([n,o,180,i],t),a=this.getClusters([-180,o,s,i],t);return r.concat(a)}var l=this.trees[this._limitZoom(t)],p=l.range(ct(n),mt(i),ct(s),mt(o)),u=l.data,h=[];for(var d of p){var g=this.stride*d;h.push(u[g+5]>1?dt(u,g,this.clusterProps):this.points[u[g+3]])}return h}getChildren(e){var t=this._getOriginId(e),n=this._getOriginZoom(e),o="No cluster with the specified id.",s=this.trees[n];if(!s)throw new Error(o);var i=s.data;if(t*this.stride>=i.length)throw new Error(o);var r=this.options.radius/(this.options.extent*Math.pow(2,n-1)),a=i[t*this.stride],l=i[t*this.stride+1],p=s.within(a,l,r),u=[];for(var h of p){var d=h*this.stride;i[d+4]===e&&u.push(i[d+5]>1?dt(i,d,this.clusterProps):this.points[i[d+3]])}if(0===u.length)throw new Error(o);return u}getLeaves(e,t,n){t=t||10,n=n||0;var o=[];return this._appendLeaves(o,e,t,n,0),o}getTile(e,t,n){var o=this.trees[this._limitZoom(e)],s=Math.pow(2,e),{extent:i,radius:r}=this.options,a=r/i,l=(n-a)/s,p=(n+1+a)/s,u={features:[]};return this._addTileFeatures(o.range((t-a)/s,l,(t+1+a)/s,p),o.data,t,n,s,u),0===t&&this._addTileFeatures(o.range(1-a/s,l,1,p),o.data,s,n,s,u),t===s-1&&this._addTileFeatures(o.range(0,l,a/s,p),o.data,-1,n,s,u),u.features.length?u:null}getClusterExpansionZoom(e){for(var t=this._getOriginZoom(e)-1;t<=this.options.maxZoom;){var n=this.getChildren(e);if(t++,1!==n.length)break;e=n[0].properties.cluster_id}return t}_appendLeaves(e,t,n,o,s){var i=this.getChildren(t);for(var r of i){var a=r.properties;if(a&&a.cluster?s+a.point_count<=o?s+=a.point_count:s=this._appendLeaves(e,a.cluster_id,n,o,s):s<o?s++:e.push(r),e.length===n)break}return s}_createTree(e){for(var t=new nt(e.length/this.stride|0,this.options.nodeSize,Float32Array),n=0;n<e.length;n+=this.stride)t.add(e[n],e[n+1]);return t.finish(),t.data=e,t}_addTileFeatures(e,t,n,o,s,i){for(var r of e){var a=r*this.stride,l=t[a+5]>1,p=void 0,u=void 0,h=void 0;if(l)p=gt(t,a,this.clusterProps),u=t[a],h=t[a+1];else{var d=this.points[t[a+3]];p=d.properties;var[g,c]=d.geometry.coordinates;u=ct(g),h=mt(c)}var m={type:1,geometry:[[Math.round(this.options.extent*(u*s-n)),Math.round(this.options.extent*(h*s-o))]],tags:p},v=void 0;void 0!==(v=l||this.options.generateId?t[a+3]:this.points[t[a+3]].id)&&(m.id=v),i.features.push(m)}}_limitZoom(e){return Math.max(this.options.minZoom,Math.min(Math.floor(+e),this.options.maxZoom+1))}_cluster(e,t){for(var{radius:n,extent:o,reduce:s,minPoints:i}=this.options,r=n/(o*Math.pow(2,t)),a=e.data,l=[],p=this.stride,u=0;u<a.length;u+=p)if(!(a[u+2]<=t)){a[u+2]=t;var h=a[u],d=a[u+1],g=e.within(a[u],a[u+1],r),c=a[u+5],m=c;for(var v of g){var f=v*p;a[f+2]>t&&(m+=a[f+5])}if(m>c&&m>=i){var y=h*c,b=d*c,L=void 0,w=-1,M=(u/p<<5)+(t+1)+this.points.length;for(var C of g){var x=C*p;if(!(a[x+2]<=t)){a[x+2]=t;var k=a[x+5];y+=a[x]*k,b+=a[x+1]*k,a[x+4]=M,s&&(L||(L=this._map(a,u,!0),w=this.clusterProps.length,this.clusterProps.push(L)),s(L,this._map(a,x)))}}a[u+4]=M,l.push(y/m,b/m,1/0,M,-1,m),s&&l.push(w)}else{for(var P=0;P<p;P++)l.push(a[u+P]);if(m>1)for(var O of g){var E=O*p;if(!(a[E+2]<=t)){a[E+2]=t;for(var S=0;S<p;S++)l.push(a[E+S])}}}}return l}_getOriginId(e){return e-this.points.length>>5}_getOriginZoom(e){return(e-this.points.length)%32}_map(e,t,n){if(e[t+5]>1){var o=this.clusterProps[e[t+6]];return n?Object.assign({},o):o}var s=this.points[e[t+3]].properties,i=this.options.map(s);return n&&i===s?Object.assign({},i):i}}function dt(e,t,n){return{type:"Feature",id:e[t+3],properties:gt(e,t,n),geometry:{type:"Point",coordinates:[(i=e[t],360*(i-.5)),(o=e[t+1],s=(180-360*o)*Math.PI/180,360*Math.atan(Math.exp(s))/Math.PI-90)]}};var o,s,i;
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */}function gt(e,t,n){var o=e[t+5],s=o>=1e4?"".concat(Math.round(o/1e3),"k"):o>=1e3?"".concat(Math.round(o/100)/10,"k"):o,i=e[t+6],r=-1===i?{}:Object.assign({},n[i]);return Object.assign(r,{cluster:!0,cluster_id:e[t+3],point_count:o,point_count_abbreviated:s})}function ct(e){return e/360+.5}function mt(e){var t=Math.sin(e*Math.PI/180),n=.5-.25*Math.log((1+t)/(1-t))/Math.PI;return n<0?0:n>1?1:n}function vt(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(o=Object.getOwnPropertySymbols(e);s<o.length;s++)t.indexOf(o[s])<0&&Object.prototype.propertyIsEnumerable.call(e,o[s])&&(n[o[s]]=e[o[s]])}return n}class ft{static isAdvancedMarkerAvailable(e){return google.maps.marker&&!0===e.getMapCapabilities().isAdvancedMarkersAvailable}static isAdvancedMarker(e){return google.maps.marker&&e instanceof google.maps.marker.AdvancedMarkerElement}static setMap(e,t){this.isAdvancedMarker(e)?e.map=t:e.setMap(t)}static getPosition(e){if(this.isAdvancedMarker(e)){if(e.position){if(e.position instanceof google.maps.LatLng)return e.position;if(e.position.lat&&e.position.lng)return new google.maps.LatLng(e.position.lat,e.position.lng)}return new google.maps.LatLng(null)}return e.getPosition()}static getVisible(e){return!!this.isAdvancedMarker(e)||e.getVisible()}}class yt{constructor(e){var{markers:t,position:n}=e;this.markers=t,n&&(n instanceof google.maps.LatLng?this._position=n:this._position=new google.maps.LatLng(n))}get bounds(){if(0!==this.markers.length||this._position){var e=new google.maps.LatLngBounds(this._position,this._position);for(var t of this.markers)e.extend(ft.getPosition(t));return e}}get position(){return this._position||this.bounds.getCenter()}get count(){return this.markers.filter((e=>ft.getVisible(e))).length}push(e){this.markers.push(e)}delete(){this.marker&&(ft.setMap(this.marker,null),this.marker=void 0),this.markers.length=0}}var bt=(e,t,n,o)=>{var s=Lt(e.getBounds(),t,o);return n.filter((e=>s.contains(ft.getPosition(e))))},Lt=(e,t,n)=>{var{northEast:o,southWest:s}=Ct(e,t),i=xt({northEast:o,southWest:s},n);return kt(i,t)},wt=(e,t,n)=>{var o=Lt(e,t,n),s=o.getNorthEast(),i=o.getSouthWest();return[i.lng(),i.lat(),s.lng(),s.lat()]},Mt=(e,t)=>{var n=(t.lat-e.lat)*Math.PI/180,o=(t.lng-e.lng)*Math.PI/180,s=Math.sin(n/2),i=Math.sin(o/2),r=s*s+Math.cos(e.lat*Math.PI/180)*Math.cos(t.lat*Math.PI/180)*i*i;return 6371*(2*Math.atan2(Math.sqrt(r),Math.sqrt(1-r)))},Ct=(e,t)=>({northEast:t.fromLatLngToDivPixel(e.getNorthEast()),southWest:t.fromLatLngToDivPixel(e.getSouthWest())}),xt=(e,t)=>{var{northEast:n,southWest:o}=e;return n.x+=t,n.y-=t,o.x-=t,o.y+=t,{northEast:n,southWest:o}},kt=(e,t)=>{var{northEast:n,southWest:o}=e,s=t.fromDivPixelToLatLng(o),i=t.fromDivPixelToLatLng(n);return new google.maps.LatLngBounds(s,i)};class Pt{constructor(e){var{maxZoom:t=16}=e;this.maxZoom=t}noop(e){var{markers:t}=e;return St(t)}}class Ot extends Pt{constructor(e){var{viewportPadding:t=60}=e;super(vt(e,["viewportPadding"])),this.viewportPadding=60,this.viewportPadding=t}calculate(e){var{markers:t,map:n,mapCanvasProjection:o}=e;return n.getZoom()>=this.maxZoom?{clusters:this.noop({markers:t}),changed:!1}:{clusters:this.cluster({markers:bt(n,o,t,this.viewportPadding),map:n,mapCanvasProjection:o})}}}var Et,St=e=>e.map((e=>new yt({position:ft.getPosition(e),markers:[e]})));class Dt extends Pt{constructor(e){var{maxZoom:t,radius:n=60}=e,o=vt(e,["maxZoom","radius"]);super({maxZoom:t}),this.state={zoom:-1},this.superCluster=new ht(Object.assign({maxZoom:this.maxZoom,radius:n},o))}calculate(e){var t=!1,n={zoom:e.map.getZoom()};if(!et(e.markers,this.markers)){t=!0,this.markers=[...e.markers];var o=this.markers.map((e=>{var t=ft.getPosition(e);return{type:"Feature",geometry:{type:"Point",coordinates:[t.lng(),t.lat()]},properties:{marker:e}}}));this.superCluster.load(o)}return t||(this.state.zoom<=this.maxZoom||n.zoom<=this.maxZoom)&&(t=!et(this.state,n)),this.state=n,t&&(this.clusters=this.cluster(e)),{clusters:this.clusters,changed:t}}cluster(e){var{map:t}=e;return this.superCluster.getClusters([-180,-90,180,90],Math.round(t.getZoom())).map((e=>this.transformCluster(e)))}transformCluster(e){var{geometry:{coordinates:[t,n]},properties:o}=e;if(o.cluster)return new yt({markers:this.superCluster.getLeaves(o.cluster_id,1/0).map((e=>e.properties.marker)),position:{lat:n,lng:t}});var s=o.marker;return new yt({markers:[s],position:ft.getPosition(s)})}}class It{constructor(e,t){this.markers={sum:e.length};var n=t.map((e=>e.count)),o=n.reduce(((e,t)=>e+t),0);this.clusters={count:t.length,markers:{mean:o/t.length,sum:o,min:Math.min(...n),max:Math.max(...n)}}}}class jt{render(e,t,n){var{count:o,position:s}=e,i=o>Math.max(10,t.clusters.markers.mean)?"#ff0000":"#0000ff",r='<svg fill="'.concat(i,'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 240" width="50" height="50">\n<circle cx="120" cy="120" opacity=".6" r="70" />\n<circle cx="120" cy="120" opacity=".3" r="90" />\n<circle cx="120" cy="120" opacity=".2" r="110" />\n<text x="50%" y="50%" style="fill:#fff" text-anchor="middle" font-size="50" dominant-baseline="middle" font-family="roboto,arial,sans-serif">').concat(o,"</text>\n</svg>"),a="Cluster of ".concat(o," markers"),l=Number(google.maps.Marker.MAX_ZINDEX)+o;if(ft.isAdvancedMarkerAvailable(n)){var p=(new DOMParser).parseFromString(r,"image/svg+xml").documentElement;p.setAttribute("transform","translate(0 25)");var u={map:n,position:s,zIndex:l,title:a,content:p};return new google.maps.marker.AdvancedMarkerElement(u)}var h={position:s,zIndex:l,title:a,icon:{url:"data:image/svg+xml;base64,".concat(btoa(r)),anchor:new google.maps.Point(25,25)}};return new google.maps.Marker(h)}}class Bt{constructor(){!function(e,t){for(var n in t.prototype)e.prototype[n]=t.prototype[n]}(Bt,google.maps.OverlayView)}}!function(e){e.CLUSTERING_BEGIN="clusteringbegin",e.CLUSTERING_END="clusteringend",e.CLUSTER_CLICK="click"}(Et||(Et={}));var _t=(e,t,n)=>{n.fitBounds(t.bounds)};class Tt extends Bt{constructor(e){var{map:t,markers:n=[],algorithmOptions:o={},algorithm:s=new Dt(o),renderer:i=new jt,onClusterClick:r=_t}=e;super(),this.markers=[...n],this.clusters=[],this.algorithm=s,this.renderer=i,this.onClusterClick=r,t&&this.setMap(t)}addMarker(e,t){this.markers.includes(e)||(this.markers.push(e),t||this.render())}addMarkers(e,t){e.forEach((e=>{this.addMarker(e,!0)})),t||this.render()}removeMarker(e,t){var n=this.markers.indexOf(e);return-1!==n&&(ft.setMap(e,null),this.markers.splice(n,1),t||this.render(),!0)}removeMarkers(e,t){var n=!1;return e.forEach((e=>{n=this.removeMarker(e,!0)||n})),n&&!t&&this.render(),n}clearMarkers(e){this.markers.length=0,e||this.render()}render(){var e=this.getMap();if(e instanceof google.maps.Map&&e.getProjection()){google.maps.event.trigger(this,Et.CLUSTERING_BEGIN,this);var{clusters:t,changed:n}=this.algorithm.calculate({markers:this.markers,map:e,mapCanvasProjection:this.getProjection()});if(n||null==n){var o=new Set;for(var s of t)1==s.markers.length&&o.add(s.markers[0]);var i=[];for(var r of this.clusters)null!=r.marker&&(1==r.markers.length?o.has(r.marker)||ft.setMap(r.marker,null):i.push(r.marker));this.clusters=t,this.renderClusters(),requestAnimationFrame((()=>i.forEach((e=>ft.setMap(e,null)))))}google.maps.event.trigger(this,Et.CLUSTERING_END,this)}}onAdd(){this.idleListener=this.getMap().addListener("idle",this.render.bind(this)),this.render()}onRemove(){google.maps.event.removeListener(this.idleListener),this.reset()}reset(){this.markers.forEach((e=>ft.setMap(e,null))),this.clusters.forEach((e=>e.delete())),this.clusters=[]}renderClusters(){var e=new It(this.markers,this.clusters),t=this.getMap();this.clusters.forEach((n=>{1===n.markers.length?n.marker=n.markers[0]:(n.marker=this.renderer.render(n,e,t),n.markers.forEach((e=>ft.setMap(e,null))),this.onClusterClick&&n.marker.addListener("click",(e=>{google.maps.event.trigger(this,Et.CLUSTER_CLICK,n),this.onClusterClick(e,n,t)}))),ft.setMap(n.marker,t)}))}}var Ut=Object.freeze({__proto__:null,AbstractAlgorithm:Pt,AbstractViewportAlgorithm:Ot,Cluster:yt,ClusterStats:It,DefaultRenderer:jt,GridAlgorithm:class extends Ot{constructor(e){var{maxDistance:t=4e4,gridSize:n=40}=e;super(vt(e,["maxDistance","gridSize"])),this.clusters=[],this.state={zoom:-1},this.maxDistance=t,this.gridSize=n}calculate(e){var{markers:t,map:n,mapCanvasProjection:o}=e,s={zoom:n.getZoom()},i=!1;return this.state.zoom>=this.maxZoom&&s.zoom>=this.maxZoom||(i=!et(this.state,s)),this.state=s,n.getZoom()>=this.maxZoom?{clusters:this.noop({markers:t}),changed:i}:{clusters:this.cluster({markers:bt(n,o,t,this.viewportPadding),map:n,mapCanvasProjection:o})}}cluster(e){var{markers:t,map:n,mapCanvasProjection:o}=e;return this.clusters=[],t.forEach((e=>{this.addToClosestCluster(e,n,o)})),this.clusters}addToClosestCluster(e,t,n){for(var o=this.maxDistance,s=null,i=0;i<this.clusters.length;i++){var r=this.clusters[i],a=Mt(r.bounds.getCenter().toJSON(),ft.getPosition(e).toJSON());a<o&&(o=a,s=r)}if(s&&Lt(s.bounds,n,this.gridSize).contains(ft.getPosition(e)))s.push(e);else{var l=new yt({markers:[e]});this.clusters.push(l)}}},MarkerClusterer:Tt,get MarkerClustererEvents(){return Et},MarkerUtils:ft,NoopAlgorithm:class extends Pt{constructor(e){super(vt(e,[]))}calculate(e){var{markers:t,map:n,mapCanvasProjection:o}=e;return{clusters:this.cluster({markers:t,map:n,mapCanvasProjection:o}),changed:!1}}cluster(e){return this.noop(e)}},SuperClusterAlgorithm:Dt,SuperClusterViewportAlgorithm:class extends Ot{constructor(e){var{maxZoom:t,radius:n=60,viewportPadding:o=60}=e,s=vt(e,["maxZoom","radius","viewportPadding"]);super({maxZoom:t,viewportPadding:o}),this.superCluster=new ht(Object.assign({maxZoom:this.maxZoom,radius:n},s)),this.state={zoom:-1,view:[0,0,0,0]}}calculate(e){var t={zoom:Math.round(e.map.getZoom()),view:wt(e.map.getBounds(),e.mapCanvasProjection,this.viewportPadding)},n=!et(this.state,t);if(!et(e.markers,this.markers)){n=!0,this.markers=[...e.markers];var o=this.markers.map((e=>{var t=ft.getPosition(e);return{type:"Feature",geometry:{type:"Point",coordinates:[t.lng(),t.lat()]},properties:{marker:e}}}));this.superCluster.load(o)}return n&&(this.clusters=this.cluster(e),this.state=t),{clusters:this.clusters,changed:n}}cluster(e){var{map:t,mapCanvasProjection:n}=e,o={zoom:Math.round(t.getZoom()),view:wt(t.getBounds(),n,this.viewportPadding)};return this.superCluster.getClusters(o.view,o.zoom).map((e=>this.transformCluster(e)))}transformCluster(e){var{geometry:{coordinates:[t,n]},properties:o}=e;if(o.cluster)return new yt({markers:this.superCluster.getLeaves(o.cluster_id,1/0).map((e=>e.properties.marker)),position:{lat:n,lng:t}});var s=o.marker;return new yt({markers:[s],position:ft.getPosition(s)})}},defaultOnClusterClickHandler:_t,distanceBetweenPoints:Mt,extendBoundsToPaddedViewport:Lt,extendPixelBounds:xt,filterMarkersToPaddedViewport:bt,getPaddedViewport:wt,noop:St,pixelBoundsToLatLngBounds:kt});function zt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Rt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?zt(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):zt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var At=i((function(e){var{children:t,options:n}=e,o=function(e){var t=k(),[n,o]=r(null);return l((()=>{if(t&&null===n){var s=new Tt(Rt(Rt({},e),{},{map:t}));o(s)}}),[t]),n}(n);return null!==o?t(o):null})),Zt={onCloseClick:"closeclick",onContentChanged:"content_changed",onDomReady:"domready",onPositionChanged:"position_changed",onZindexChanged:"zindex_changed"},Vt={options(e,t){e.setOptions(t)},position(e,t){e.setPosition(t)},zIndex(e,t){e.setZIndex(t)}};var Wt=i((function(e){var{children:t,anchor:n,options:o,position:i,zIndex:p,onCloseClick:u,onDomReady:d,onContentChanged:g,onPositionChanged:c,onZindexChanged:m,onLoad:f,onUnmount:y}=e,b=s(x),[L,w]=r(null),[M,k]=r(null),[P,O]=r(null),[E,S]=r(null),[D,I]=r(null),[j,B]=r(null),_=a(null);return l((()=>{null!==L&&(L.close(),n?L.open(b,n):L.getPosition()&&L.open(b))}),[b,L,n]),l((()=>{o&&null!==L&&L.setOptions(o)}),[L,o]),l((()=>{i&&null!==L&&L.setPosition(i)}),[i]),l((()=>{"number"==typeof p&&null!==L&&L.setZIndex(p)}),[p]),l((()=>{L&&u&&(null!==M&&google.maps.event.removeListener(M),k(google.maps.event.addListener(L,"closeclick",u)))}),[u]),l((()=>{L&&d&&(null!==P&&google.maps.event.removeListener(P),O(google.maps.event.addListener(L,"domready",d)))}),[d]),l((()=>{L&&g&&(null!==E&&google.maps.event.removeListener(E),S(google.maps.event.addListener(L,"content_changed",g)))}),[g]),l((()=>{L&&c&&(null!==D&&google.maps.event.removeListener(D),I(google.maps.event.addListener(L,"position_changed",c)))}),[c]),l((()=>{L&&m&&(null!==j&&google.maps.event.removeListener(j),B(google.maps.event.addListener(L,"zindex_changed",m)))}),[m]),l((()=>{var e=new google.maps.InfoWindow(o);return w(e),_.current=document.createElement("div"),u&&k(google.maps.event.addListener(e,"closeclick",u)),d&&O(google.maps.event.addListener(e,"domready",d)),g&&S(google.maps.event.addListener(e,"content_changed",g)),c&&I(google.maps.event.addListener(e,"position_changed",c)),m&&B(google.maps.event.addListener(e,"zindex_changed",m)),e.setContent(_.current),i&&e.setPosition(i),p&&e.setZIndex(p),n?e.open(b,n):e.getPosition()?e.open(b):C(!1,"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>."),f&&f(e),()=>{M&&google.maps.event.removeListener(M),E&&google.maps.event.removeListener(E),P&&google.maps.event.removeListener(P),D&&google.maps.event.removeListener(D),j&&google.maps.event.removeListener(j),y&&y(e),e.close()}}),[]),_.current?v(h.only(t),_.current):null}));class Nt extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"containerElement",null),b(this,"state",{infoWindow:null}),b(this,"open",((e,t)=>{t?e.open(this.context,t):e.getPosition()?e.open(this.context):C(!1,"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>.")})),b(this,"setInfoWindowCallback",(()=>{null!==this.state.infoWindow&&null!==this.containerElement&&(this.state.infoWindow.setContent(this.containerElement),this.open(this.state.infoWindow,this.props.anchor),this.props.onLoad&&this.props.onLoad(this.state.infoWindow))}))}componentDidMount(){var e=new google.maps.InfoWindow(this.props.options);this.containerElement=document.createElement("div"),this.registeredEvents=D({updaterMap:Vt,eventMap:Zt,prevProps:{},nextProps:this.props,instance:e}),this.setState((()=>({infoWindow:e})),this.setInfoWindowCallback)}componentDidUpdate(e){null!==this.state.infoWindow&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:Vt,eventMap:Zt,prevProps:e,nextProps:this.props,instance:this.state.infoWindow}))}componentWillUnmount(){null!==this.state.infoWindow&&(S(this.registeredEvents),this.props.onUnmount&&this.props.onUnmount(this.state.infoWindow),this.state.infoWindow.close())}render(){return this.containerElement?v(h.only(this.props.children),this.containerElement):null}}function Ht(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Gt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ht(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ht(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}b(Nt,"contextType",x);var Ft={onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},Kt={draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},path(e,t){e.setPath(t)},visible(e,t){e.setVisible(t)}},Yt={};var qt=i((function(e){var{options:t,draggable:n,editable:o,visible:i,path:a,onDblClick:p,onDragEnd:u,onDragStart:h,onMouseDown:d,onMouseMove:g,onMouseOut:c,onMouseOver:m,onMouseUp:v,onRightClick:f,onClick:y,onDrag:b,onLoad:L,onUnmount:w}=e,M=s(x),[C,k]=r(null),[P,O]=r(null),[E,S]=r(null),[D,I]=r(null),[j,B]=r(null),[_,T]=r(null),[U,z]=r(null),[R,A]=r(null),[Z,V]=r(null),[W,N]=r(null),[H,G]=r(null),[F,K]=r(null);return l((()=>{null!==C&&C.setMap(M)}),[M]),l((()=>{void 0!==t&&null!==C&&C.setOptions(t)}),[C,t]),l((()=>{void 0!==n&&null!==C&&C.setDraggable(n)}),[C,n]),l((()=>{void 0!==o&&null!==C&&C.setEditable(o)}),[C,o]),l((()=>{void 0!==i&&null!==C&&C.setVisible(i)}),[C,i]),l((()=>{void 0!==a&&null!==C&&C.setPath(a)}),[C,a]),l((()=>{C&&p&&(null!==P&&google.maps.event.removeListener(P),O(google.maps.event.addListener(C,"dblclick",p)))}),[p]),l((()=>{C&&u&&(null!==E&&google.maps.event.removeListener(E),S(google.maps.event.addListener(C,"dragend",u)))}),[u]),l((()=>{C&&h&&(null!==D&&google.maps.event.removeListener(D),I(google.maps.event.addListener(C,"dragstart",h)))}),[h]),l((()=>{C&&d&&(null!==j&&google.maps.event.removeListener(j),B(google.maps.event.addListener(C,"mousedown",d)))}),[d]),l((()=>{C&&g&&(null!==_&&google.maps.event.removeListener(_),T(google.maps.event.addListener(C,"mousemove",g)))}),[g]),l((()=>{C&&c&&(null!==U&&google.maps.event.removeListener(U),z(google.maps.event.addListener(C,"mouseout",c)))}),[c]),l((()=>{C&&m&&(null!==R&&google.maps.event.removeListener(R),A(google.maps.event.addListener(C,"mouseover",m)))}),[m]),l((()=>{C&&v&&(null!==Z&&google.maps.event.removeListener(Z),V(google.maps.event.addListener(C,"mouseup",v)))}),[v]),l((()=>{C&&f&&(null!==W&&google.maps.event.removeListener(W),N(google.maps.event.addListener(C,"rightclick",f)))}),[f]),l((()=>{C&&y&&(null!==H&&google.maps.event.removeListener(H),G(google.maps.event.addListener(C,"click",y)))}),[y]),l((()=>{C&&b&&(null!==F&&google.maps.event.removeListener(F),K(google.maps.event.addListener(C,"drag",b)))}),[b]),l((()=>{var e=new google.maps.Polyline(Gt(Gt({},t||Yt),{},{map:M}));return a&&e.setPath(a),void 0!==i&&e.setVisible(i),void 0!==o&&e.setEditable(o),void 0!==n&&e.setDraggable(n),p&&O(google.maps.event.addListener(e,"dblclick",p)),u&&S(google.maps.event.addListener(e,"dragend",u)),h&&I(google.maps.event.addListener(e,"dragstart",h)),d&&B(google.maps.event.addListener(e,"mousedown",d)),g&&T(google.maps.event.addListener(e,"mousemove",g)),c&&z(google.maps.event.addListener(e,"mouseout",c)),m&&A(google.maps.event.addListener(e,"mouseover",m)),v&&V(google.maps.event.addListener(e,"mouseup",v)),f&&N(google.maps.event.addListener(e,"rightclick",f)),y&&G(google.maps.event.addListener(e,"click",y)),b&&K(google.maps.event.addListener(e,"drag",b)),k(e),L&&L(e),()=>{null!==P&&google.maps.event.removeListener(P),null!==E&&google.maps.event.removeListener(E),null!==D&&google.maps.event.removeListener(D),null!==j&&google.maps.event.removeListener(j),null!==_&&google.maps.event.removeListener(_),null!==U&&google.maps.event.removeListener(U),null!==R&&google.maps.event.removeListener(R),null!==Z&&google.maps.event.removeListener(Z),null!==W&&google.maps.event.removeListener(W),null!==H&&google.maps.event.removeListener(H),w&&w(e),e.setMap(null)}}),[]),null}));class Jt extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"state",{polyline:null}),b(this,"setPolylineCallback",(()=>{null!==this.state.polyline&&this.props.onLoad&&this.props.onLoad(this.state.polyline)}))}componentDidMount(){var e=new google.maps.Polyline(Gt(Gt({},this.props.options),{},{map:this.context}));this.registeredEvents=D({updaterMap:Kt,eventMap:Ft,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{polyline:e}}),this.setPolylineCallback)}componentDidUpdate(e){null!==this.state.polyline&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:Kt,eventMap:Ft,prevProps:e,nextProps:this.props,instance:this.state.polyline}))}componentWillUnmount(){null!==this.state.polyline&&(this.props.onUnmount&&this.props.onUnmount(this.state.polyline),S(this.registeredEvents),this.state.polyline.setMap(null))}render(){return null}}function Xt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function $t(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Xt(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Xt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}b(Jt,"contextType",x);var Qt={onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},en={draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},path(e,t){e.setPath(t)},paths(e,t){e.setPaths(t)},visible(e,t){e.setVisible(t)}};var tn=i((function(e){var{options:t,draggable:n,editable:o,visible:i,path:a,paths:p,onDblClick:u,onDragEnd:h,onDragStart:d,onMouseDown:g,onMouseMove:c,onMouseOut:m,onMouseOver:v,onMouseUp:f,onRightClick:y,onClick:b,onDrag:L,onLoad:w,onUnmount:M,onEdit:C}=e,k=s(x),[P,O]=r(null),[E,S]=r(null),[D,I]=r(null),[j,B]=r(null),[_,T]=r(null),[U,z]=r(null),[R,A]=r(null),[Z,V]=r(null),[W,N]=r(null),[H,G]=r(null),[F,K]=r(null),[Y,q]=r(null);return l((()=>{null!==P&&P.setMap(k)}),[k]),l((()=>{void 0!==t&&null!==P&&P.setOptions(t)}),[P,t]),l((()=>{void 0!==n&&null!==P&&P.setDraggable(n)}),[P,n]),l((()=>{void 0!==o&&null!==P&&P.setEditable(o)}),[P,o]),l((()=>{void 0!==i&&null!==P&&P.setVisible(i)}),[P,i]),l((()=>{void 0!==a&&null!==P&&P.setPath(a)}),[P,a]),l((()=>{void 0!==p&&null!==P&&P.setPaths(p)}),[P,p]),l((()=>{P&&"function"==typeof u&&(null!==E&&google.maps.event.removeListener(E),S(google.maps.event.addListener(P,"dblclick",u)))}),[u]),l((()=>{P&&(google.maps.event.addListener(P.getPath(),"insert_at",(()=>{null==C||C(P)})),google.maps.event.addListener(P.getPath(),"set_at",(()=>{null==C||C(P)})),google.maps.event.addListener(P.getPath(),"remove_at",(()=>{null==C||C(P)})))}),[P,C]),l((()=>{P&&"function"==typeof h&&(null!==D&&google.maps.event.removeListener(D),I(google.maps.event.addListener(P,"dragend",h)))}),[h]),l((()=>{P&&"function"==typeof d&&(null!==j&&google.maps.event.removeListener(j),B(google.maps.event.addListener(P,"dragstart",d)))}),[d]),l((()=>{P&&"function"==typeof g&&(null!==_&&google.maps.event.removeListener(_),T(google.maps.event.addListener(P,"mousedown",g)))}),[g]),l((()=>{P&&"function"==typeof c&&(null!==U&&google.maps.event.removeListener(U),z(google.maps.event.addListener(P,"mousemove",c)))}),[c]),l((()=>{P&&"function"==typeof m&&(null!==R&&google.maps.event.removeListener(R),A(google.maps.event.addListener(P,"mouseout",m)))}),[m]),l((()=>{P&&"function"==typeof v&&(null!==Z&&google.maps.event.removeListener(Z),V(google.maps.event.addListener(P,"mouseover",v)))}),[v]),l((()=>{P&&"function"==typeof f&&(null!==W&&google.maps.event.removeListener(W),N(google.maps.event.addListener(P,"mouseup",f)))}),[f]),l((()=>{P&&"function"==typeof y&&(null!==H&&google.maps.event.removeListener(H),G(google.maps.event.addListener(P,"rightclick",y)))}),[y]),l((()=>{P&&"function"==typeof b&&(null!==F&&google.maps.event.removeListener(F),K(google.maps.event.addListener(P,"click",b)))}),[b]),l((()=>{P&&"function"==typeof L&&(null!==Y&&google.maps.event.removeListener(Y),q(google.maps.event.addListener(P,"drag",L)))}),[L]),l((()=>{var e=new google.maps.Polygon($t($t({},t),{},{map:k}));return a&&e.setPath(a),p&&e.setPaths(p),void 0!==i&&e.setVisible(i),void 0!==o&&e.setEditable(o),void 0!==n&&e.setDraggable(n),u&&S(google.maps.event.addListener(e,"dblclick",u)),h&&I(google.maps.event.addListener(e,"dragend",h)),d&&B(google.maps.event.addListener(e,"dragstart",d)),g&&T(google.maps.event.addListener(e,"mousedown",g)),c&&z(google.maps.event.addListener(e,"mousemove",c)),m&&A(google.maps.event.addListener(e,"mouseout",m)),v&&V(google.maps.event.addListener(e,"mouseover",v)),f&&N(google.maps.event.addListener(e,"mouseup",f)),y&&G(google.maps.event.addListener(e,"rightclick",y)),b&&K(google.maps.event.addListener(e,"click",b)),L&&q(google.maps.event.addListener(e,"drag",L)),O(e),w&&w(e),()=>{null!==E&&google.maps.event.removeListener(E),null!==D&&google.maps.event.removeListener(D),null!==j&&google.maps.event.removeListener(j),null!==_&&google.maps.event.removeListener(_),null!==U&&google.maps.event.removeListener(U),null!==R&&google.maps.event.removeListener(R),null!==Z&&google.maps.event.removeListener(Z),null!==W&&google.maps.event.removeListener(W),null!==H&&google.maps.event.removeListener(H),null!==F&&google.maps.event.removeListener(F),M&&M(e),e.setMap(null)}}),[]),null}));class nn extends p{constructor(){super(...arguments),b(this,"registeredEvents",[])}componentDidMount(){var e=this.props.options||{};this.polygon=new google.maps.Polygon(e),this.polygon.setMap(this.context),this.registeredEvents=D({updaterMap:en,eventMap:Qt,prevProps:{},nextProps:this.props,instance:this.polygon}),this.props.onLoad&&this.props.onLoad(this.polygon)}componentDidUpdate(e){this.polygon&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:en,eventMap:Qt,prevProps:e,nextProps:this.props,instance:this.polygon}))}componentWillUnmount(){this.polygon&&(this.props.onUnmount&&this.props.onUnmount(this.polygon),S(this.registeredEvents),this.polygon&&this.polygon.setMap(null))}render(){return null}}function on(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function sn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?on(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):on(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}b(nn,"contextType",x);var rn={onBoundsChanged:"bounds_changed",onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},an={bounds(e,t){e.setBounds(t)},draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},visible(e,t){e.setVisible(t)}};var ln=i((function(e){var{options:t,bounds:n,draggable:o,editable:i,visible:a,onDblClick:p,onDragEnd:u,onDragStart:h,onMouseDown:d,onMouseMove:g,onMouseOut:c,onMouseOver:m,onMouseUp:v,onRightClick:f,onClick:y,onDrag:b,onBoundsChanged:L,onLoad:w,onUnmount:M}=e,C=s(x),[k,P]=r(null),[O,E]=r(null),[S,D]=r(null),[I,j]=r(null),[B,_]=r(null),[T,U]=r(null),[z,R]=r(null),[A,Z]=r(null),[V,W]=r(null),[N,H]=r(null),[G,F]=r(null),[K,Y]=r(null),[q,J]=r(null);return l((()=>{null!==k&&k.setMap(C)}),[C]),l((()=>{void 0!==t&&null!==k&&k.setOptions(t)}),[k,t]),l((()=>{void 0!==o&&null!==k&&k.setDraggable(o)}),[k,o]),l((()=>{void 0!==i&&null!==k&&k.setEditable(i)}),[k,i]),l((()=>{void 0!==a&&null!==k&&k.setVisible(a)}),[k,a]),l((()=>{void 0!==n&&null!==k&&k.setBounds(n)}),[k,n]),l((()=>{k&&p&&(null!==O&&google.maps.event.removeListener(O),E(google.maps.event.addListener(k,"dblclick",p)))}),[p]),l((()=>{k&&u&&(null!==S&&google.maps.event.removeListener(S),D(google.maps.event.addListener(k,"dragend",u)))}),[u]),l((()=>{k&&h&&(null!==I&&google.maps.event.removeListener(I),j(google.maps.event.addListener(k,"dragstart",h)))}),[h]),l((()=>{k&&d&&(null!==B&&google.maps.event.removeListener(B),_(google.maps.event.addListener(k,"mousedown",d)))}),[d]),l((()=>{k&&g&&(null!==T&&google.maps.event.removeListener(T),U(google.maps.event.addListener(k,"mousemove",g)))}),[g]),l((()=>{k&&c&&(null!==z&&google.maps.event.removeListener(z),R(google.maps.event.addListener(k,"mouseout",c)))}),[c]),l((()=>{k&&m&&(null!==A&&google.maps.event.removeListener(A),Z(google.maps.event.addListener(k,"mouseover",m)))}),[m]),l((()=>{k&&v&&(null!==V&&google.maps.event.removeListener(V),W(google.maps.event.addListener(k,"mouseup",v)))}),[v]),l((()=>{k&&f&&(null!==N&&google.maps.event.removeListener(N),H(google.maps.event.addListener(k,"rightclick",f)))}),[f]),l((()=>{k&&y&&(null!==G&&google.maps.event.removeListener(G),F(google.maps.event.addListener(k,"click",y)))}),[y]),l((()=>{k&&b&&(null!==K&&google.maps.event.removeListener(K),Y(google.maps.event.addListener(k,"drag",b)))}),[b]),l((()=>{k&&L&&(null!==q&&google.maps.event.removeListener(q),J(google.maps.event.addListener(k,"bounds_changed",L)))}),[L]),l((()=>{var e=new google.maps.Rectangle(sn(sn({},t),{},{map:C}));return void 0!==a&&e.setVisible(a),void 0!==i&&e.setEditable(i),void 0!==o&&e.setDraggable(o),void 0!==n&&e.setBounds(n),p&&E(google.maps.event.addListener(e,"dblclick",p)),u&&D(google.maps.event.addListener(e,"dragend",u)),h&&j(google.maps.event.addListener(e,"dragstart",h)),d&&_(google.maps.event.addListener(e,"mousedown",d)),g&&U(google.maps.event.addListener(e,"mousemove",g)),c&&R(google.maps.event.addListener(e,"mouseout",c)),m&&Z(google.maps.event.addListener(e,"mouseover",m)),v&&W(google.maps.event.addListener(e,"mouseup",v)),f&&H(google.maps.event.addListener(e,"rightclick",f)),y&&F(google.maps.event.addListener(e,"click",y)),b&&Y(google.maps.event.addListener(e,"drag",b)),L&&J(google.maps.event.addListener(e,"bounds_changed",L)),P(e),w&&w(e),()=>{null!==O&&google.maps.event.removeListener(O),null!==S&&google.maps.event.removeListener(S),null!==I&&google.maps.event.removeListener(I),null!==B&&google.maps.event.removeListener(B),null!==T&&google.maps.event.removeListener(T),null!==z&&google.maps.event.removeListener(z),null!==A&&google.maps.event.removeListener(A),null!==V&&google.maps.event.removeListener(V),null!==N&&google.maps.event.removeListener(N),null!==G&&google.maps.event.removeListener(G),null!==K&&google.maps.event.removeListener(K),null!==q&&google.maps.event.removeListener(q),M&&M(e),e.setMap(null)}}),[]),null}));class pn extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"state",{rectangle:null}),b(this,"setRectangleCallback",(()=>{null!==this.state.rectangle&&this.props.onLoad&&this.props.onLoad(this.state.rectangle)}))}componentDidMount(){var e=new google.maps.Rectangle(sn(sn({},this.props.options),{},{map:this.context}));this.registeredEvents=D({updaterMap:an,eventMap:rn,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{rectangle:e}}),this.setRectangleCallback)}componentDidUpdate(e){null!==this.state.rectangle&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:an,eventMap:rn,prevProps:e,nextProps:this.props,instance:this.state.rectangle}))}componentWillUnmount(){null!==this.state.rectangle&&(this.props.onUnmount&&this.props.onUnmount(this.state.rectangle),S(this.registeredEvents),this.state.rectangle.setMap(null))}render(){return null}}function un(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function hn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?un(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):un(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}b(pn,"contextType",x);var dn={onCenterChanged:"center_changed",onRadiusChanged:"radius_changed",onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},gn={center(e,t){e.setCenter(t)},draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},radius(e,t){e.setRadius(t)},visible(e,t){e.setVisible(t)}},cn={};var mn=i((function(e){var{options:t,center:n,radius:o,draggable:i,editable:a,visible:p,onDblClick:u,onDragEnd:h,onDragStart:d,onMouseDown:g,onMouseMove:c,onMouseOut:m,onMouseOver:v,onMouseUp:f,onRightClick:y,onClick:b,onDrag:L,onCenterChanged:w,onRadiusChanged:M,onLoad:C,onUnmount:k}=e,P=s(x),[O,E]=r(null),[S,D]=r(null),[I,j]=r(null),[B,_]=r(null),[T,U]=r(null),[z,R]=r(null),[A,Z]=r(null),[V,W]=r(null),[N,H]=r(null),[G,F]=r(null),[K,Y]=r(null),[q,J]=r(null),[X,$]=r(null),[Q,ee]=r(null);return l((()=>{null!==O&&O.setMap(P)}),[P]),l((()=>{void 0!==t&&null!==O&&O.setOptions(t)}),[O,t]),l((()=>{void 0!==i&&null!==O&&O.setDraggable(i)}),[O,i]),l((()=>{void 0!==a&&null!==O&&O.setEditable(a)}),[O,a]),l((()=>{void 0!==p&&null!==O&&O.setVisible(p)}),[O,p]),l((()=>{"number"==typeof o&&null!==O&&O.setRadius(o)}),[O,o]),l((()=>{void 0!==n&&null!==O&&O.setCenter(n)}),[O,n]),l((()=>{O&&u&&(null!==S&&google.maps.event.removeListener(S),D(google.maps.event.addListener(O,"dblclick",u)))}),[u]),l((()=>{O&&h&&(null!==I&&google.maps.event.removeListener(I),j(google.maps.event.addListener(O,"dragend",h)))}),[h]),l((()=>{O&&d&&(null!==B&&google.maps.event.removeListener(B),_(google.maps.event.addListener(O,"dragstart",d)))}),[d]),l((()=>{O&&g&&(null!==T&&google.maps.event.removeListener(T),U(google.maps.event.addListener(O,"mousedown",g)))}),[g]),l((()=>{O&&c&&(null!==z&&google.maps.event.removeListener(z),R(google.maps.event.addListener(O,"mousemove",c)))}),[c]),l((()=>{O&&m&&(null!==A&&google.maps.event.removeListener(A),Z(google.maps.event.addListener(O,"mouseout",m)))}),[m]),l((()=>{O&&v&&(null!==V&&google.maps.event.removeListener(V),W(google.maps.event.addListener(O,"mouseover",v)))}),[v]),l((()=>{O&&f&&(null!==N&&google.maps.event.removeListener(N),H(google.maps.event.addListener(O,"mouseup",f)))}),[f]),l((()=>{O&&y&&(null!==G&&google.maps.event.removeListener(G),F(google.maps.event.addListener(O,"rightclick",y)))}),[y]),l((()=>{O&&b&&(null!==K&&google.maps.event.removeListener(K),Y(google.maps.event.addListener(O,"click",b)))}),[b]),l((()=>{O&&L&&(null!==q&&google.maps.event.removeListener(q),J(google.maps.event.addListener(O,"drag",L)))}),[L]),l((()=>{O&&w&&(null!==X&&google.maps.event.removeListener(X),$(google.maps.event.addListener(O,"center_changed",w)))}),[b]),l((()=>{O&&M&&(null!==Q&&google.maps.event.removeListener(Q),ee(google.maps.event.addListener(O,"radius_changed",M)))}),[M]),l((()=>{var e=new google.maps.Circle(hn(hn({},t||cn),{},{map:P}));return"number"==typeof o&&e.setRadius(o),void 0!==n&&e.setCenter(n),"number"==typeof o&&e.setRadius(o),void 0!==p&&e.setVisible(p),void 0!==a&&e.setEditable(a),void 0!==i&&e.setDraggable(i),u&&D(google.maps.event.addListener(e,"dblclick",u)),h&&j(google.maps.event.addListener(e,"dragend",h)),d&&_(google.maps.event.addListener(e,"dragstart",d)),g&&U(google.maps.event.addListener(e,"mousedown",g)),c&&R(google.maps.event.addListener(e,"mousemove",c)),m&&Z(google.maps.event.addListener(e,"mouseout",m)),v&&W(google.maps.event.addListener(e,"mouseover",v)),f&&H(google.maps.event.addListener(e,"mouseup",f)),y&&F(google.maps.event.addListener(e,"rightclick",y)),b&&Y(google.maps.event.addListener(e,"click",b)),L&&J(google.maps.event.addListener(e,"drag",L)),w&&$(google.maps.event.addListener(e,"center_changed",w)),M&&ee(google.maps.event.addListener(e,"radius_changed",M)),E(e),C&&C(e),()=>{null!==S&&google.maps.event.removeListener(S),null!==I&&google.maps.event.removeListener(I),null!==B&&google.maps.event.removeListener(B),null!==T&&google.maps.event.removeListener(T),null!==z&&google.maps.event.removeListener(z),null!==A&&google.maps.event.removeListener(A),null!==V&&google.maps.event.removeListener(V),null!==N&&google.maps.event.removeListener(N),null!==G&&google.maps.event.removeListener(G),null!==K&&google.maps.event.removeListener(K),null!==X&&google.maps.event.removeListener(X),null!==Q&&google.maps.event.removeListener(Q),k&&k(e),e.setMap(null)}}),[]),null}));class vn extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"state",{circle:null}),b(this,"setCircleCallback",(()=>{null!==this.state.circle&&this.props.onLoad&&this.props.onLoad(this.state.circle)}))}componentDidMount(){var e=new google.maps.Circle(hn(hn({},this.props.options),{},{map:this.context}));this.registeredEvents=D({updaterMap:gn,eventMap:dn,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{circle:e}}),this.setCircleCallback)}componentDidUpdate(e){null!==this.state.circle&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:gn,eventMap:dn,prevProps:e,nextProps:this.props,instance:this.state.circle}))}componentWillUnmount(){var e;null!==this.state.circle&&(this.props.onUnmount&&this.props.onUnmount(this.state.circle),S(this.registeredEvents),null===(e=this.state.circle)||void 0===e||e.setMap(null))}render(){return null}}function fn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function yn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?fn(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}b(vn,"contextType",x);var bn={onClick:"click",onDblClick:"dblclick",onMouseDown:"mousedown",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick",onAddFeature:"addfeature",onRemoveFeature:"removefeature",onRemoveProperty:"removeproperty",onSetGeometry:"setgeometry",onSetProperty:"setproperty"},Ln={add(e,t){e.add(t)},addgeojson(e,t,n){e.addGeoJson(t,n)},contains(e,t){e.contains(t)},foreach(e,t){e.forEach(t)},loadgeojson(e,t,n,o){e.loadGeoJson(t,n,o)},overridestyle(e,t,n){e.overrideStyle(t,n)},remove(e,t){e.remove(t)},revertstyle(e,t){e.revertStyle(t)},controlposition(e,t){e.setControlPosition(t)},controls(e,t){e.setControls(t)},drawingmode(e,t){e.setDrawingMode(t)},map(e,t){e.setMap(t)},style(e,t){e.setStyle(t)},togeojson(e,t){e.toGeoJson(t)}};var wn=i((function(e){var{options:t,onClick:n,onDblClick:o,onMouseDown:i,onMouseMove:a,onMouseOut:p,onMouseOver:u,onMouseUp:h,onRightClick:d,onAddFeature:g,onRemoveFeature:c,onRemoveProperty:m,onSetGeometry:v,onSetProperty:f,onLoad:y,onUnmount:b}=e,L=s(x),[w,M]=r(null),[C,k]=r(null),[P,O]=r(null),[E,S]=r(null),[D,I]=r(null),[j,B]=r(null),[_,T]=r(null),[U,z]=r(null),[R,A]=r(null),[Z,V]=r(null),[W,N]=r(null),[H,G]=r(null),[F,K]=r(null),[Y,q]=r(null);return l((()=>{null!==w&&w.setMap(L)}),[L]),l((()=>{w&&o&&(null!==C&&google.maps.event.removeListener(C),k(google.maps.event.addListener(w,"dblclick",o)))}),[o]),l((()=>{w&&i&&(null!==P&&google.maps.event.removeListener(P),O(google.maps.event.addListener(w,"mousedown",i)))}),[i]),l((()=>{w&&a&&(null!==E&&google.maps.event.removeListener(E),S(google.maps.event.addListener(w,"mousemove",a)))}),[a]),l((()=>{w&&p&&(null!==D&&google.maps.event.removeListener(D),I(google.maps.event.addListener(w,"mouseout",p)))}),[p]),l((()=>{w&&u&&(null!==j&&google.maps.event.removeListener(j),B(google.maps.event.addListener(w,"mouseover",u)))}),[u]),l((()=>{w&&h&&(null!==_&&google.maps.event.removeListener(_),T(google.maps.event.addListener(w,"mouseup",h)))}),[h]),l((()=>{w&&d&&(null!==U&&google.maps.event.removeListener(U),z(google.maps.event.addListener(w,"rightclick",d)))}),[d]),l((()=>{w&&n&&(null!==R&&google.maps.event.removeListener(R),A(google.maps.event.addListener(w,"click",n)))}),[n]),l((()=>{w&&g&&(null!==Z&&google.maps.event.removeListener(Z),V(google.maps.event.addListener(w,"addfeature",g)))}),[g]),l((()=>{w&&c&&(null!==W&&google.maps.event.removeListener(W),N(google.maps.event.addListener(w,"removefeature",c)))}),[c]),l((()=>{w&&m&&(null!==H&&google.maps.event.removeListener(H),G(google.maps.event.addListener(w,"removeproperty",m)))}),[m]),l((()=>{w&&v&&(null!==F&&google.maps.event.removeListener(F),K(google.maps.event.addListener(w,"setgeometry",v)))}),[v]),l((()=>{w&&f&&(null!==Y&&google.maps.event.removeListener(Y),q(google.maps.event.addListener(w,"setproperty",f)))}),[f]),l((()=>{if(null!==L){var e=new google.maps.Data(yn(yn({},t),{},{map:L}));o&&k(google.maps.event.addListener(e,"dblclick",o)),i&&O(google.maps.event.addListener(e,"mousedown",i)),a&&S(google.maps.event.addListener(e,"mousemove",a)),p&&I(google.maps.event.addListener(e,"mouseout",p)),u&&B(google.maps.event.addListener(e,"mouseover",u)),h&&T(google.maps.event.addListener(e,"mouseup",h)),d&&z(google.maps.event.addListener(e,"rightclick",d)),n&&A(google.maps.event.addListener(e,"click",n)),g&&V(google.maps.event.addListener(e,"addfeature",g)),c&&N(google.maps.event.addListener(e,"removefeature",c)),m&&G(google.maps.event.addListener(e,"removeproperty",m)),v&&K(google.maps.event.addListener(e,"setgeometry",v)),f&&q(google.maps.event.addListener(e,"setproperty",f)),M(e),y&&y(e)}return()=>{w&&(null!==C&&google.maps.event.removeListener(C),null!==P&&google.maps.event.removeListener(P),null!==E&&google.maps.event.removeListener(E),null!==D&&google.maps.event.removeListener(D),null!==j&&google.maps.event.removeListener(j),null!==_&&google.maps.event.removeListener(_),null!==U&&google.maps.event.removeListener(U),null!==R&&google.maps.event.removeListener(R),null!==Z&&google.maps.event.removeListener(Z),null!==W&&google.maps.event.removeListener(W),null!==H&&google.maps.event.removeListener(H),null!==F&&google.maps.event.removeListener(F),null!==Y&&google.maps.event.removeListener(Y),b&&b(w),w.setMap(null))}}),[]),null}));class Mn extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"state",{data:null}),b(this,"setDataCallback",(()=>{null!==this.state.data&&this.props.onLoad&&this.props.onLoad(this.state.data)}))}componentDidMount(){if(null!==this.context){var e=new google.maps.Data(yn(yn({},this.props.options),{},{map:this.context}));this.registeredEvents=D({updaterMap:Ln,eventMap:bn,prevProps:{},nextProps:this.props,instance:e}),this.setState((()=>({data:e})),this.setDataCallback)}}componentDidUpdate(e){null!==this.state.data&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:Ln,eventMap:bn,prevProps:e,nextProps:this.props,instance:this.state.data}))}componentWillUnmount(){null!==this.state.data&&(this.props.onUnmount&&this.props.onUnmount(this.state.data),S(this.registeredEvents),this.state.data&&this.state.data.setMap(null))}render(){return null}}function Cn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function xn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Cn(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Cn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}b(Mn,"contextType",x);var kn={onClick:"click",onDefaultViewportChanged:"defaultviewport_changed",onStatusChanged:"status_changed"},Pn={options(e,t){e.setOptions(t)},url(e,t){e.setUrl(t)},zIndex(e,t){e.setZIndex(t)}};class On extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"state",{kmlLayer:null}),b(this,"setKmlLayerCallback",(()=>{null!==this.state.kmlLayer&&this.props.onLoad&&this.props.onLoad(this.state.kmlLayer)}))}componentDidMount(){var e=new google.maps.KmlLayer(xn(xn({},this.props.options),{},{map:this.context}));this.registeredEvents=D({updaterMap:Pn,eventMap:kn,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{kmlLayer:e}}),this.setKmlLayerCallback)}componentDidUpdate(e){null!==this.state.kmlLayer&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:Pn,eventMap:kn,prevProps:e,nextProps:this.props,instance:this.state.kmlLayer}))}componentWillUnmount(){null!==this.state.kmlLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.kmlLayer),S(this.registeredEvents),this.state.kmlLayer.setMap(null))}render(){return null}}function En(e,t){return"function"==typeof t?t(e.offsetWidth,e.offsetHeight):{x:0,y:0}}function Sn(e,t){return new t(e.lat,e.lng)}function Dn(e,t){return new t(new google.maps.LatLng(e.ne.lat,e.ne.lng),new google.maps.LatLng(e.sw.lat,e.sw.lng))}function In(e,t,n,o){return void 0!==n?function(e,t,n){var o=e&&e.fromLatLngToDivPixel(n.getNorthEast()),s=e&&e.fromLatLngToDivPixel(n.getSouthWest());return o&&s?{left:"".concat(s.x+t.x,"px"),top:"".concat(o.y+t.y,"px"),width:"".concat(o.x-s.x-t.x,"px"),height:"".concat(s.y-o.y-t.y,"px")}:{left:"-9999px",top:"-9999px"}}(e,t,(s=n,i=google.maps.LatLngBounds,r=Dn,s instanceof i?s:r(s,i))):function(e,t,n){var o=e&&e.fromLatLngToDivPixel(n);if(o){var{x:s,y:i}=o;return{left:"".concat(s+t.x,"px"),top:"".concat(i+t.y,"px")}}return{left:"-9999px",top:"-9999px"}}(e,t,function(e,t,n){return e instanceof t?e:n(e,t)}(o,google.maps.LatLng,Sn));var s,i,r}function jn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Bn(e,t,n,o,s){class i extends google.maps.OverlayView{constructor(e,t,n,o){super(),this.container=e,this.pane=t,this.position=n,this.bounds=o}onAdd(){var e,t=null===(e=this.getPanes())||void 0===e?void 0:e[this.pane];null==t||t.appendChild(this.container)}draw(){var e=this.getProjection(),t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?jn(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):jn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},this.container?En(this.container,s):{x:0,y:0}),n=In(e,t,this.bounds,this.position);for(var[o,i]of Object.entries(n))this.container.style[o]=i}onRemove(){null!==this.container.parentNode&&this.container.parentNode.removeChild(this.container)}}return new i(e,t,n,o)}function _n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Tn(e){return e?(e instanceof google.maps.LatLng?e:new google.maps.LatLng(e.lat,e.lng))+"":""}function Un(e){return e?(e instanceof google.maps.LatLngBounds?e:new google.maps.LatLngBounds(new google.maps.LatLng(e.south,e.east),new google.maps.LatLng(e.north,e.west)))+"":""}b(On,"contextType",x);var zn="floatPane",Rn="mapPane",An="markerLayer",Zn="overlayLayer",Vn="overlayMouseTarget";var Wn=i((function(e){var{position:t,bounds:n,mapPaneName:o,zIndex:i,onLoad:r,onUnmount:a,getPixelPositionOffset:p,children:h}=e,d=s(x),g=u((()=>{var e=document.createElement("div");return e.style.position="absolute",e}),[]),c=u((()=>Bn(g,o,t,n,p)),[g,o,t,n]);return l((()=>(null==r||r(c),null==c||c.setMap(d),()=>{null==a||a(c),null==c||c.setMap(null)})),[d,c]),l((()=>{g.style.zIndex="".concat(i)}),[i,g]),m.createPortal(h,g)}));class Nn extends p{constructor(e){super(e),b(this,"state",{paneEl:null,containerStyle:{position:"absolute"}}),b(this,"updatePane",(()=>{var e=this.props.mapPaneName,t=this.overlayView.getPanes();C(!!e,"OverlayView requires props.mapPaneName but got %s",e),t?this.setState({paneEl:t[e]}):this.setState({paneEl:null})})),b(this,"onAdd",(()=>{var e,t;this.updatePane(),null===(e=(t=this.props).onLoad)||void 0===e||e.call(t,this.overlayView)})),b(this,"onPositionElement",(()=>{var e,t,n,o,s,i,r=this.overlayView.getProjection(),a=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_n(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_n(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({x:0,y:0},this.containerRef.current?En(this.containerRef.current,this.props.getPixelPositionOffset):{}),l=In(r,a,this.props.bounds,this.props.position);(s=l,i={left:this.state.containerStyle.left,top:this.state.containerStyle.top,width:this.state.containerStyle.width,height:this.state.containerStyle.height},s.left!==i.left||s.top!==i.top||s.width!==i.height||s.height!==i.height)&&this.setState({containerStyle:{top:null!==(e=l.top)&&void 0!==e?e:0,left:null!==(t=l.left)&&void 0!==t?t:0,width:null!==(n=l.width)&&void 0!==n?n:0,height:null!==(o=l.height)&&void 0!==o?o:0,position:"absolute"}})})),b(this,"draw",(()=>{this.onPositionElement()})),b(this,"onRemove",(()=>{var e,t;this.setState((()=>({paneEl:null}))),null===(e=(t=this.props).onUnmount)||void 0===e||e.call(t,this.overlayView)})),this.containerRef=c();var t=new google.maps.OverlayView;t.onAdd=this.onAdd,t.draw=this.draw,t.onRemove=this.onRemove,this.overlayView=t}componentDidMount(){this.overlayView.setMap(this.context)}componentDidUpdate(e){var t=Tn(e.position),n=Tn(this.props.position),o=Un(e.bounds),s=Un(this.props.bounds);t===n&&o===s||this.overlayView.draw(),e.mapPaneName!==this.props.mapPaneName&&this.updatePane()}componentWillUnmount(){this.overlayView.setMap(null)}render(){var t=this.state.paneEl;return t?m.createPortal(e("div",{ref:this.containerRef,style:this.state.containerStyle,children:h.only(this.props.children)}),t):null}}function Hn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Gn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Hn(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Hn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}b(Nn,"FLOAT_PANE","floatPane"),b(Nn,"MAP_PANE","mapPane"),b(Nn,"MARKER_LAYER","markerLayer"),b(Nn,"OVERLAY_LAYER","overlayLayer"),b(Nn,"OVERLAY_MOUSE_TARGET","overlayMouseTarget"),b(Nn,"contextType",x);var Fn={onDblClick:"dblclick",onClick:"click"},Kn={opacity(e,t){e.setOpacity(t)}};var Yn=i((function(e){var{url:t,bounds:n,options:o,visible:i}=e,r=s(x),a=new google.maps.LatLngBounds(new google.maps.LatLng(n.south,n.west),new google.maps.LatLng(n.north,n.east)),p=u((()=>new google.maps.GroundOverlay(t,a,o)),[]);return l((()=>{null!==p&&p.setMap(r)}),[r]),l((()=>{void 0!==t&&null!==p&&(p.set("url",t),p.setMap(r))}),[p,t]),l((()=>{void 0!==i&&null!==p&&p.setOpacity(i?1:0)}),[p,i]),l((()=>{var e=new google.maps.LatLngBounds(new google.maps.LatLng(n.south,n.west),new google.maps.LatLng(n.north,n.east));void 0!==n&&null!==p&&(p.set("bounds",e),p.setMap(r))}),[p,n]),null}));class qn extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"state",{groundOverlay:null}),b(this,"setGroundOverlayCallback",(()=>{null!==this.state.groundOverlay&&this.props.onLoad&&this.props.onLoad(this.state.groundOverlay)}))}componentDidMount(){C(!!this.props.url||!!this.props.bounds,"For GroundOverlay, url and bounds are passed in to constructor and are immutable after instantiated. This is the behavior of Google Maps JavaScript API v3 ( See https://developers.google.com/maps/documentation/javascript/reference#GroundOverlay) Hence, use the corresponding two props provided by `react-google-maps-api`, url and bounds. In some cases, you'll need the GroundOverlay component to reflect the changes of url and bounds. You can leverage the React's key property to remount the component. Typically, just `key={url}` would serve your need. See https://github.com/tomchentw/react-google-maps/issues/655");var e=new google.maps.GroundOverlay(this.props.url,this.props.bounds,Gn(Gn({},this.props.options),{},{map:this.context}));this.registeredEvents=D({updaterMap:Kn,eventMap:Fn,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{groundOverlay:e}}),this.setGroundOverlayCallback)}componentDidUpdate(e){null!==this.state.groundOverlay&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:Kn,eventMap:Fn,prevProps:e,nextProps:this.props,instance:this.state.groundOverlay}))}componentWillUnmount(){this.state.groundOverlay&&(this.props.onUnmount&&this.props.onUnmount(this.state.groundOverlay),this.state.groundOverlay.setMap(null))}render(){return null}}function Jn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Xn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Jn(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Jn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}b(qn,"defaultProps",{onLoad:function(){}}),b(qn,"contextType",x);var $n={},Qn={data(e,t){e.setData(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)}};var eo=i((function(e){var{data:t,onLoad:n,onUnmount:o,options:i}=e,a=s(x),[p,u]=r(null);return l((()=>{google.maps.visualization||C(!!google.maps.visualization,'Did you include prop libraries={["visualization"]} in useJsApiScript? %s',google.maps.visualization)}),[]),l((()=>{C(!!t,"data property is required in HeatmapLayer %s",t)}),[t]),l((()=>{null!==p&&p.setMap(a)}),[a]),l((()=>{i&&null!==p&&p.setOptions(i)}),[p,i]),l((()=>{var e=new google.maps.visualization.HeatmapLayer(Xn(Xn({},i),{},{data:t,map:a}));return u(e),n&&n(e),()=>{null!==p&&(o&&o(p),p.setMap(null))}}),[]),null}));class to extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"state",{heatmapLayer:null}),b(this,"setHeatmapLayerCallback",(()=>{null!==this.state.heatmapLayer&&this.props.onLoad&&this.props.onLoad(this.state.heatmapLayer)}))}componentDidMount(){C(!!google.maps.visualization,'Did you include prop libraries={["visualization"]} to <LoadScript />? %s',google.maps.visualization),C(!!this.props.data,"data property is required in HeatmapLayer %s",this.props.data);var e=new google.maps.visualization.HeatmapLayer(Xn(Xn({},this.props.options),{},{data:this.props.data,map:this.context}));this.registeredEvents=D({updaterMap:Qn,eventMap:$n,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{heatmapLayer:e}}),this.setHeatmapLayerCallback)}componentDidUpdate(e){S(this.registeredEvents),this.registeredEvents=D({updaterMap:Qn,eventMap:$n,prevProps:e,nextProps:this.props,instance:this.state.heatmapLayer})}componentWillUnmount(){null!==this.state.heatmapLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.heatmapLayer),S(this.registeredEvents),this.state.heatmapLayer.setMap(null))}render(){return null}}b(to,"contextType",x);var no={onCloseClick:"closeclick",onPanoChanged:"pano_changed",onPositionChanged:"position_changed",onPovChanged:"pov_changed",onResize:"resize",onStatusChanged:"status_changed",onVisibleChanged:"visible_changed",onZoomChanged:"zoom_changed"},oo={register(e,t,n){e.registerPanoProvider(t,n)},links(e,t){e.setLinks(t)},motionTracking(e,t){e.setMotionTracking(t)},options(e,t){e.setOptions(t)},pano(e,t){e.setPano(t)},position(e,t){e.setPosition(t)},pov(e,t){e.setPov(t)},visible(e,t){e.setVisible(t)},zoom(e,t){e.setZoom(t)}};class so extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"state",{streetViewPanorama:null}),b(this,"setStreetViewPanoramaCallback",(()=>{null!==this.state.streetViewPanorama&&this.props.onLoad&&this.props.onLoad(this.state.streetViewPanorama)}))}componentDidMount(){var e,t,n=null!==(e=null===(t=this.context)||void 0===t?void 0:t.getStreetView())&&void 0!==e?e:null;this.registeredEvents=D({updaterMap:oo,eventMap:no,prevProps:{},nextProps:this.props,instance:n}),this.setState((()=>({streetViewPanorama:n})),this.setStreetViewPanoramaCallback)}componentDidUpdate(e){null!==this.state.streetViewPanorama&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:oo,eventMap:no,prevProps:e,nextProps:this.props,instance:this.state.streetViewPanorama}))}componentWillUnmount(){null!==this.state.streetViewPanorama&&(this.props.onUnmount&&this.props.onUnmount(this.state.streetViewPanorama),S(this.registeredEvents),this.state.streetViewPanorama.setVisible(!1))}render(){return null}}b(so,"contextType",x);class io extends p{constructor(){super(...arguments),b(this,"state",{streetViewService:null}),b(this,"setStreetViewServiceCallback",(()=>{null!==this.state.streetViewService&&this.props.onLoad&&this.props.onLoad(this.state.streetViewService)}))}componentDidMount(){var e=new google.maps.StreetViewService;this.setState((function(){return{streetViewService:e}}),this.setStreetViewServiceCallback)}componentWillUnmount(){null!==this.state.streetViewService&&this.props.onUnmount&&this.props.onUnmount(this.state.streetViewService)}render(){return null}}b(io,"contextType",x);class ro extends p{constructor(){super(...arguments),b(this,"state",{directionsService:null}),b(this,"setDirectionsServiceCallback",(()=>{null!==this.state.directionsService&&this.props.onLoad&&this.props.onLoad(this.state.directionsService)}))}componentDidMount(){C(!!this.props.options,"DirectionsService expected options object as parameter, but got %s",this.props.options);var e=new google.maps.DirectionsService;this.setState((function(){return{directionsService:e}}),this.setDirectionsServiceCallback)}componentDidUpdate(){null!==this.state.directionsService&&this.state.directionsService.route(this.props.options,this.props.callback)}componentWillUnmount(){null!==this.state.directionsService&&this.props.onUnmount&&this.props.onUnmount(this.state.directionsService)}render(){return null}}var ao={onDirectionsChanged:"directions_changed"},lo={directions(e,t){e.setDirections(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},panel(e,t){e.setPanel(t)},routeIndex(e,t){e.setRouteIndex(t)}};class po extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"state",{directionsRenderer:null}),b(this,"setDirectionsRendererCallback",(()=>{null!==this.state.directionsRenderer&&(this.state.directionsRenderer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.directionsRenderer))}))}componentDidMount(){var e=new google.maps.DirectionsRenderer(this.props.options);this.registeredEvents=D({updaterMap:lo,eventMap:ao,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{directionsRenderer:e}}),this.setDirectionsRendererCallback)}componentDidUpdate(e){null!==this.state.directionsRenderer&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:lo,eventMap:ao,prevProps:e,nextProps:this.props,instance:this.state.directionsRenderer}))}componentWillUnmount(){null!==this.state.directionsRenderer&&(this.props.onUnmount&&this.props.onUnmount(this.state.directionsRenderer),S(this.registeredEvents),this.state.directionsRenderer&&this.state.directionsRenderer.setMap(null))}render(){return null}}b(po,"contextType",x);class uo extends p{constructor(){super(...arguments),b(this,"state",{distanceMatrixService:null}),b(this,"setDistanceMatrixServiceCallback",(()=>{null!==this.state.distanceMatrixService&&this.props.onLoad&&this.props.onLoad(this.state.distanceMatrixService)}))}componentDidMount(){C(!!this.props.options,"DistanceMatrixService expected options object as parameter, but go %s",this.props.options);var e=new google.maps.DistanceMatrixService;this.setState((function(){return{distanceMatrixService:e}}),this.setDistanceMatrixServiceCallback)}componentDidUpdate(){null!==this.state.distanceMatrixService&&this.state.distanceMatrixService.getDistanceMatrix(this.props.options,this.props.callback)}componentWillUnmount(){null!==this.state.distanceMatrixService&&this.props.onUnmount&&this.props.onUnmount(this.state.distanceMatrixService)}render(){return null}}var ho={onPlacesChanged:"places_changed"},go={bounds(e,t){e.setBounds(t)}};class co extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"containerElement",c()),b(this,"state",{searchBox:null}),b(this,"setSearchBoxCallback",(()=>{null!==this.state.searchBox&&this.props.onLoad&&this.props.onLoad(this.state.searchBox)}))}componentDidMount(){if(C(!!google.maps.places,'You need to provide libraries={["places"]} prop to <LoadScript /> component %s',google.maps.places),null!==this.containerElement&&null!==this.containerElement.current){var e=this.containerElement.current.querySelector("input");if(null!==e){var t=new google.maps.places.SearchBox(e,this.props.options);this.registeredEvents=D({updaterMap:go,eventMap:ho,prevProps:{},nextProps:this.props,instance:t}),this.setState((function(){return{searchBox:t}}),this.setSearchBoxCallback)}}}componentDidUpdate(e){null!==this.state.searchBox&&(S(this.registeredEvents),this.registeredEvents=D({updaterMap:go,eventMap:ho,prevProps:e,nextProps:this.props,instance:this.state.searchBox}))}componentWillUnmount(){null!==this.state.searchBox&&(this.props.onUnmount&&this.props.onUnmount(this.state.searchBox),S(this.registeredEvents))}render(){return e("div",{ref:this.containerElement,children:h.only(this.props.children)})}}b(co,"contextType",x);var mo={onPlaceChanged:"place_changed"},vo={bounds(e,t){e.setBounds(t)},restrictions(e,t){e.setComponentRestrictions(t)},fields(e,t){e.setFields(t)},options(e,t){e.setOptions(t)},types(e,t){e.setTypes(t)}};class fo extends p{constructor(){super(...arguments),b(this,"registeredEvents",[]),b(this,"containerElement",c()),b(this,"state",{autocomplete:null}),b(this,"setAutocompleteCallback",(()=>{null!==this.state.autocomplete&&this.props.onLoad&&this.props.onLoad(this.state.autocomplete)}))}componentDidMount(){var e;C(!!google.maps.places,'You need to provide libraries={["places"]} prop to <LoadScript /> component %s',google.maps.places);var t=null===(e=this.containerElement.current)||void 0===e?void 0:e.querySelector("input");if(t){var n=new google.maps.places.Autocomplete(t,this.props.options);this.registeredEvents=D({updaterMap:vo,eventMap:mo,prevProps:{},nextProps:this.props,instance:n}),this.setState((()=>({autocomplete:n})),this.setAutocompleteCallback)}}componentDidUpdate(e){S(this.registeredEvents),this.registeredEvents=D({updaterMap:vo,eventMap:mo,prevProps:e,nextProps:this.props,instance:this.state.autocomplete})}componentWillUnmount(){null!==this.state.autocomplete&&S(this.registeredEvents)}render(){return e("div",{ref:this.containerElement,className:this.props.className,children:h.only(this.props.children)})}}b(fo,"defaultProps",{className:""}),b(fo,"contextType",x);export{fo as Autocomplete,ge as BicyclingLayer,de as BicyclingLayerF,vn as Circle,mn as CircleF,Mn as Data,wn as DataF,po as DirectionsRenderer,ro as DirectionsService,uo as DistanceMatrixService,we as DrawingManager,Le as DrawingManagerF,zn as FLOAT_PANE,B as GoogleMap,Ut as GoogleMapsMarkerClusterer,At as GoogleMarkerClusterer,qn as GroundOverlay,Yn as GroundOverlayF,to as HeatmapLayer,eo as HeatmapLayerF,$e as InfoBox,Xe as InfoBoxF,Nt as InfoWindow,Wt as InfoWindowF,On as KmlLayer,G as LoadScript,J as LoadScriptNext,Rn as MAP_PANE,An as MARKER_LAYER,x as MapContext,Ee as Marker,Ae as MarkerClusterer,Re as MarkerClustererF,Oe as MarkerF,Zn as OVERLAY_LAYER,Vn as OVERLAY_MOUSE_TARGET,Nn as OverlayView,Wn as OverlayViewF,nn as Polygon,tn as PolygonF,Jt as Polyline,qt as PolylineF,pn as Rectangle,ln as RectangleF,co as StandaloneSearchBox,so as StreetViewPanorama,io as StreetViewService,he as TrafficLayer,ue as TrafficLayerF,me as TransitLayer,ce as TransitLayerF,k as useGoogleMap,ie as useJsApiLoader,K as useLoadScript};
//# sourceMappingURL=esm.min.js.map
