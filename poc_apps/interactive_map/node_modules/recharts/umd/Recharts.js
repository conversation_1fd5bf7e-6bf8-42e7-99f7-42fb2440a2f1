/*! For license information please see Recharts.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react"),require("react-is"),require("react-dom")):"function"==typeof define&&define.amd?define(["react","react-is","react-dom"],t):"object"==typeof exports?exports.Recharts=t(require("react"),require("react-is"),require("react-dom")):e.Recharts=t(e.<PERSON><PERSON>,e.<PERSON>actIs,e.ReactDOM)}(this,((e,t,r)=>(()=>{var n={8:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(6773);t.debounce=function(e,t=0,r={}){"object"!=typeof r&&(r={});const{leading:i=!1,trailing:a=!0,maxWait:o}=r,l=Array(2);let c;i&&(l[0]="leading"),a&&(l[1]="trailing");let s=null;const u=n.debounce((function(...t){c=e.apply(this,t),s=null}),t,{edges:l}),f=function(...t){return null!=o&&(null===s&&(s=Date.now()),Date.now()-s>=o)?(c=e.apply(this,t),s=Date.now(),u.cancel(),u.schedule(),c):(u.apply(this,t),c)};return f.cancel=u.cancel,f.flush=()=>(u.flush(),c),f}},25:(e,t,r)=>{e.exports=r(1334).last},58:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(9181);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},184:(e,t,r)=>{e.exports=r(4259).sortBy},228:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw new TypeError("The listener must be a function");var l=new i(n,a||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],l]:e._events[c].push(l):(e._events[c]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=new Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var c,s,u=this._events[l],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,i),!0;case 5:return u.fn.call(u.context,t,n,i,a),!0;case 6:return u.fn.call(u.context,t,n,i,a,o),!0}for(s=1,c=new Array(f-1);s<f;s++)c[s-1]=arguments[s];u.fn.apply(u.context,c)}else{var d,p=u.length;for(s=0;s<p;s++)switch(u[s].once&&this.removeListener(e,u[s].fn,void 0,!0),f){case 1:u[s].fn.call(u[s].context);break;case 2:u[s].fn.call(u[s].context,t);break;case 3:u[s].fn.call(u[s].context,t,n);break;case 4:u[s].fn.call(u[s].context,t,n,i);break;default:if(!c)for(d=1,c=new Array(f-1);d<f;d++)c[d-1]=arguments[d];u[s].fn.apply(u[s].context,c)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var c=0,s=[],u=l.length;c<u;c++)(l[c].fn!==t||i&&!l[c].once||n&&l[c].context!==n)&&s.push(l[c]);s.length?this._events[a]=1===s.length?s[0]:s:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},305:(e,t,r)=>{e.exports=r(4200).get},316:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8509),i=r(58),a=r(4905),o=r(6761);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t)&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e))}},334:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.maxBy=function(e,t){if(0===e.length)return;let r=e[0],n=t(r);for(let i=1;i<e.length;i++){const a=e[i],o=t(a);o>n&&(n=o,r=a)}return r}},645:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},717:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8273);t.isMatch=function(e,t){return n.isMatchWith(e,t,(()=>{}))}},924:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8240),i=r(6440),a=r(8202);t.minBy=function(e,t){if(null!=e)return n.minBy(Array.from(e),a.iteratee(t??i.identity))}},993:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(7074),i=r(6012),a=r(2049),o=r(9184),l=r(6761);function c(e,t,r,n,i,a,o){const l=o(e,t,r,n,i,a);if(void 0!==l)return l;if(typeof e==typeof t)switch(typeof e){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return e===t;case"number":return e===t||Object.is(e,t);case"object":return s(e,t,a,o)}return s(e,t,a,o)}function s(e,t,r,u){if(Object.is(e,t))return!0;let f=a.getTag(e),d=a.getTag(t);if(f===o.argumentsTag&&(f=o.objectTag),d===o.argumentsTag&&(d=o.objectTag),f!==d)return!1;switch(f){case o.stringTag:return e.toString()===t.toString();case o.numberTag:{const r=e.valueOf(),n=t.valueOf();return l.eq(r,n)}case o.booleanTag:case o.dateTag:case o.symbolTag:return Object.is(e.valueOf(),t.valueOf());case o.regexpTag:return e.source===t.source&&e.flags===t.flags;case o.functionTag:return e===t}const p=(r=r??new Map).get(e),h=r.get(t);if(null!=p&&null!=h)return p===t;r.set(e,t),r.set(t,e);try{switch(f){case o.mapTag:if(e.size!==t.size)return!1;for(const[n,i]of e.entries())if(!t.has(n)||!c(i,t.get(n),n,e,t,r,u))return!1;return!0;case o.setTag:{if(e.size!==t.size)return!1;const n=Array.from(e.values()),i=Array.from(t.values());for(let a=0;a<n.length;a++){const o=n[a],l=i.findIndex((n=>c(o,n,void 0,e,t,r,u)));if(-1===l)return!1;i.splice(l,1)}return!0}case o.arrayTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:case o.bigUint64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.bigInt64ArrayTag:case o.float32ArrayTag:case o.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(e)!==Buffer.isBuffer(t))return!1;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(!c(e[n],t[n],n,e,t,r,u))return!1;return!0;case o.arrayBufferTag:return e.byteLength===t.byteLength&&s(new Uint8Array(e),new Uint8Array(t),r,u);case o.dataViewTag:return e.byteLength===t.byteLength&&e.byteOffset===t.byteOffset&&s(new Uint8Array(e),new Uint8Array(t),r,u);case o.errorTag:return e.name===t.name&&e.message===t.message;case o.objectTag:{if(!(s(e.constructor,t.constructor,r,u)||n.isPlainObject(e)&&n.isPlainObject(t)))return!1;const a=[...Object.keys(e),...i.getSymbols(e)],o=[...Object.keys(t),...i.getSymbols(t)];if(a.length!==o.length)return!1;for(let n=0;n<a.length;n++){const i=a[n],o=e[i];if(!Object.hasOwn(t,i))return!1;if(!c(o,t[i],i,e,t,r,u))return!1}return!0}default:return!1}}finally{r.delete(e),r.delete(t)}}t.isEqualWith=function(e,t,r){return c(e,t,void 0,void 0,void 0,void 0,r)}},1081:(e,t,r)=>{e.exports=r(2810).uniqBy},1334:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(645),i=r(4483),a=r(58);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},1366:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},1465:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},1576:(e,t,r)=>{e.exports=r(4167).omit},1846:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},2049:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},2067:(e,t,r)=>{e.exports=r(3667).sumBy},2162:(e,t,r)=>{"use strict";var n=r(5442),i=r(9888);var a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,c=n.useEffect,s=n.useMemo,u=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;f=s((function(){function e(e){if(!c){if(c=!0,o=e,e=n(e),void 0!==i&&d.hasValue){var t=d.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,c=!1,s=void 0===r?null:r;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]}),[t,r,n,i]);var p=o(e,f[0],f[1]);return c((function(){d.hasValue=!0,d.value=p}),[p]),u(p),p}},2520:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},2751:e=>{"use strict";e.exports=t},2810:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8805),i=r(6440),a=r(8161),o=r(8202);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},2938:(e,t,r)=>{e.exports=r(8695).isPlainObject},2972:(e,t,r)=>{e.exports=r(924).minBy},2984:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(2049);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},3025:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){const t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){const l=e[n];a?"\\"===l&&n+1<r?(n++,i+=e[n]):l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},3036:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(717),i=r(1465),a=r(3923),o=r(4200),l=r(7324);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){const i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},3097:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(3500),i=r(3998),a=r(3025);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map((e=>String(e)));const l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},c=t.map((e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e)?e:{key:e,path:a.toPath(e)})));return e.map((e=>({original:e,criteria:c.map((t=>((e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:l(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?l(t,e):"object"==typeof t?t[e]:t)(t,e)))}))).slice().sort(((e,t)=>{for(let i=0;i<c.length;i++){const a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0})).map((e=>e.original))}},3403:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(4200);t.property=function(e){return function(t){return n.get(t,e)}}},3412:(e,t,r)=>{e.exports=r(5012).range},3500:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:e!=e?4:0}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});t.compareValues=(e,t,n)=>{if(e!==t){const i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},3667:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8202);t.sumBy=function(e,t){if(!e||!e.length)return 0;let r;null!=t&&(t=n.iteratee(t));for(let n=0;n<e.length;n++){const i=t?t(e[n]):e[n];void 0!==i&&(void 0===r?r=i:r+=i)}return r}},3844:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(3964);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},3908:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},3923:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(9467);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},3964:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(6012),i=r(2049),a=r(9184),o=r(2520),l=r(3908);function c(e,t,r,n=new Map,u=void 0){const f=u?.(e,t,r,n);if(null!=f)return f;if(o.isPrimitive(e))return e;if(n.has(e))return n.get(e);if(Array.isArray(e)){const t=new Array(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=c(e[i],i,r,n,u);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){const t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){const t=new Map;n.set(e,t);for(const[i,a]of e)t.set(i,c(a,i,r,n,u));return t}if(e instanceof Set){const t=new Set;n.set(e,t);for(const i of e)t.add(c(i,void 0,r,n,u));return t}if("undefined"!=typeof Buffer&&Buffer.isBuffer(e))return e.subarray();if(l.isTypedArray(e)){const t=new(Object.getPrototypeOf(e).constructor)(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=c(e[i],i,r,n,u);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){const t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return n.set(e,t),s(t,e,r,n,u),t}if("undefined"!=typeof File&&e instanceof File){const t=new File([e],e.name,{type:e.type});return n.set(e,t),s(t,e,r,n,u),t}if(e instanceof Blob){const t=new Blob([e],{type:e.type});return n.set(e,t),s(t,e,r,n,u),t}if(e instanceof Error){const t=new e.constructor;return n.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,s(t,e,r,n,u),t}if("object"==typeof e&&function(e){switch(i.getTag(e)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(e)){const t=Object.create(Object.getPrototypeOf(e));return n.set(e,t),s(t,e,r,n,u),t}return e}function s(e,t,r=e,i,a){const o=[...Object.keys(t),...n.getSymbols(t)];for(let n=0;n<o.length;n++){const l=o[n],s=Object.getOwnPropertyDescriptor(e,l);(null==s||s.writable)&&(e[l]=c(t[l],l,r,i,a))}}t.cloneDeepWith=function(e,t){return c(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=c,t.copyProperties=s},3998:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(1366),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!("number"!=typeof e&&"boolean"!=typeof e&&null!=e&&!n.isSymbol(e))||("string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e)))}},4146:(e,t,r)=>{"use strict";var n=r(2751),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},l={};function c(e){return n.isMemo(e)?o:l[e.$$typeof]||i}l[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},l[n.Memo]=o;var s=Object.defineProperty,u=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,r,n){if("string"!=typeof r){if(h){var i=p(r);i&&i!==h&&e(t,i,n)}var o=u(r);f&&(o=o.concat(f(r)));for(var l=c(t),y=c(r),v=0;v<o.length;++v){var m=o[v];if(!(a[m]||n&&n[m]||y&&y[m]||l&&l[m])){var g=d(r,m);try{s(t,m,g)}catch(e){}}}}return t}},4167:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(7841),i=r(3844);t.omit=function(e,...t){if(null==e)return{};const r=i.cloneDeep(e);for(let e=0;e<t.length;e++){let i=t[e];switch(typeof i){case"object":Array.isArray(i)||(i=Array.from(i));for(let e=0;e<i.length;e++){const t=i[e];n.unset(r,t)}break;case"string":case"symbol":case"number":n.unset(r,i)}}return r}},4200:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8193),i=r(5112),a=r(1465),o=r(3025);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;const a=t[r];return void 0===a?i.isDeepKey(r)?e(t,o.toPath(r),l):l:a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));const e=t[r];return void 0===e?l:e}default:{if(Array.isArray(r))return function(e,t,r){if(0===t.length)return r;let i=e;for(let e=0;e<t.length;e++){if(null==i)return r;if(n.isUnsafeProperty(t[e]))return r;i=i[t[e]]}if(void 0===i)return r;return i}(t,r,l);if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;const e=t[r];return void 0===e?l:e}}}},4259:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(3097),i=r(5711),a=r(316);t.sortBy=function(e,...t){const r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},4297:(e,t,r)=>{e.exports=r(5259).throttle},4338:(e,t,r)=>{e.exports=r(5938).maxBy},4483:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},4569:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8919);t.toFinite=function(e){if(!e)return 0===e?e:0;if((e=n.toNumber(e))===1/0||e===-1/0){return(e<0?-1:1)*Number.MAX_VALUE}return e==e?e:0}},4905:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},5012:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(316),i=r(4569);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);const a=Math.max(Math.ceil((t-e)/(r||1)),0),o=new Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},5112:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},5259:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(8);t.throttle=function(e,t=0,r={}){"object"!=typeof r&&(r={});const{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,trailing:a,maxWait:t})}},5442:t=>{"use strict";t.exports=e},5711:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){const r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){const o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},5938:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(334),i=r(6440),a=r(8202);t.maxBy=function(e,t){if(null!=e)return n.maxBy(Array.from(e),a.iteratee(t??i.identity))}},6003:e=>{"use strict";e.exports=r},6012:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter((t=>Object.prototype.propertyIsEnumerable.call(e,t)))}},6440:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},6502:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},6761:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},6773:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t,{signal:r,edges:n}={}){let i,a=null;const o=null!=n&&n.includes("leading"),l=null==n||n.includes("trailing"),c=()=>{null!==a&&(e.apply(i,a),i=void 0,a=null)};let s=null;const u=()=>{null!=s&&clearTimeout(s),s=setTimeout((()=>{s=null,l&&c(),d()}),t)},f=()=>{null!==s&&(clearTimeout(s),s=null)},d=()=>{f(),i=void 0,a=null},p=function(...e){if(r?.aborted)return;i=this,a=e;const t=null==s;u(),o&&t&&c()};return p.schedule=u,p.cancel=d,p.flush=()=>{f(),c()},r?.addEventListener("abort",d,{once:!0}),p}},7074:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},7324:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(5112),i=r(8509),a=r(2984),o=r(3025);t.has=function(e,t){let r;if(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&null==e?.[t]?o.toPath(t):[t],0===r.length)return!1;let l=e;for(let e=0;e<r.length;e++){const t=r[e];if(null==l||!Object.hasOwn(l,t)){if(!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1}l=l[t]}return!0}},7541:(e,t,r)=>{e.exports=r(9341).isEqual},7841:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(4200),i=r(8193),a=r(5112),o=r(1465),l=r(3025);function c(e,t){const r=n.get(e,t.slice(0,-1),e),a=t[t.length-1];if(void 0===r?.[a])return!0;if(i.isUnsafeProperty(a))return!1;try{return delete r[a],!0}catch{return!1}}t.unset=function(e,t){if(null==e)return!0;switch(typeof t){case"symbol":case"number":case"object":if(Array.isArray(t))return c(e,t);if("number"==typeof t?t=o.toKey(t):"object"==typeof t&&(t=Object.is(t?.valueOf(),-0)?"-0":String(t)),i.isUnsafeProperty(t))return!1;if(void 0===e?.[t])return!0;try{return delete e[t],!0}catch{return!1}case"string":if(void 0===e?.[t]&&a.isDeepKey(t))return c(e,l.toPath(t));if(i.isUnsafeProperty(t))return!1;try{return delete e[t],!0}catch{return!1}}}},7861:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(717),i=r(3844);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},8161:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(58),i=r(1846);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},8193:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},8202:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(6440),i=r(3403),a=r(7861),o=r(3036);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":return Array.isArray(e)&&2===e.length?o.matchesProperty(e[0],e[1]):a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},8240:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.minBy=function(e,t){if(0===e.length)return;let r=e[0],n=t(r);for(let i=1;i<e.length;i++){const a=e[i],o=t(a);o<n&&(n=o,r=a)}return r}},8273:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(717),i=r(4905),a=r(2520),o=r(6761);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return c(e,t,r,n);if(t instanceof Map)return function(e,t,r,n){if(0===t.size)return!0;if(!(e instanceof Map))return!1;for(const[i,a]of t.entries()){if(!1===r(e.get(i),a,i,e,t,n))return!1}return!0}(e,t,r,n);if(t instanceof Set)return s(e,t,r,n);const i=Object.keys(t);if(null==e)return 0===i.length;if(0===i.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let o=0;o<i.length;o++){const l=i[o];if(!a.isPrimitive(e)&&!(l in e))return!1;if(void 0===t[l]&&void 0!==e[l])return!1;if(null===t[l]&&null!==e[l])return!1;if(!r(e[l],t[l],l,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":return Object.keys(t).length>0?l(e,{...t},r,n):o.eq(e,t);default:return i.isObject(e)?"string"!=typeof t||""===t:o.eq(e,t)}}function c(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;const i=new Set;for(let a=0;a<t.length;a++){const o=t[a];let l=!1;for(let c=0;c<e.length;c++){if(i.has(c))continue;let s=!1;if(r(e[c],o,a,e,t,n)&&(s=!0),s){i.add(c),l=!0;break}}if(!l)return!1}return!0}function s(e,t,r,n){return 0===t.size||e instanceof Set&&c([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,(function e(t,n,i,a,o,c){const s=r(t,n,i,a,o,c);return void 0!==s?Boolean(s):l(t,n,e,c)}),new Map)},t.isSetMatch=s},8351:function(e,t,r){var n;!function(){"use strict";var i,a=1e9,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,c="[DecimalError] ",s=c+"Invalid argument: ",u=c+"Exponent out of range: ",f=Math.floor,d=Math.pow,p=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,h=1e7,y=9007199254740991,v=f(1286742750677284.5),m={};function g(e,t){var r,n,i,a,o,c,s,u,f=e.constructor,d=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),l?k(t,d):t;if(s=e.d,u=t.d,o=e.e,i=t.e,s=s.slice(),a=o-i){for(a<0?(n=s,a=-a,c=u.length):(n=u,i=o,c=s.length),a>(c=(o=Math.ceil(d/7))>c?o+1:c+1)&&(a=c,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((c=s.length)-(a=u.length)<0&&(a=c,n=u,u=s,s=n),r=0;a;)r=(s[--a]=s[a]+u[a]+r)/h|0,s[a]%=h;for(r&&(s.unshift(r),++i),c=s.length;0==s[--c];)s.pop();return t.d=s,t.e=i,l?k(t,d):t}function b(e,t,r){if(e!==~~e||e<t||e>r)throw Error(s+e)}function x(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=A(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=A(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}m.absoluteValue=m.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},m.comparedTo=m.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(t=0,r=(n=a.d.length)<(i=e.d.length)?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1},m.decimalPlaces=m.dp=function(){var e=this,t=e.d.length-1,r=7*(t-e.e);if(t=e.d[t])for(;t%10==0;t/=10)r--;return r<0?0:r},m.dividedBy=m.div=function(e){return w(this,new this.constructor(e))},m.dividedToIntegerBy=m.idiv=function(e){var t=this.constructor;return k(w(this,new t(e),0,1),t.precision)},m.equals=m.eq=function(e){return!this.cmp(e)},m.exponent=function(){return P(this)},m.greaterThan=m.gt=function(e){return this.cmp(e)>0},m.greaterThanOrEqualTo=m.gte=function(e){return this.cmp(e)>=0},m.isInteger=m.isint=function(){return this.e>this.d.length-2},m.isNegative=m.isneg=function(){return this.s<0},m.isPositive=m.ispos=function(){return this.s>0},m.isZero=function(){return 0===this.s},m.lessThan=m.lt=function(e){return this.cmp(e)<0},m.lessThanOrEqualTo=m.lte=function(e){return this.cmp(e)<1},m.logarithm=m.log=function(e){var t,r=this,n=r.constructor,a=n.precision,o=a+5;if(void 0===e)e=new n(10);else if((e=new n(e)).s<1||e.eq(i))throw Error(c+"NaN");if(r.s<1)throw Error(c+(r.s?"NaN":"-Infinity"));return r.eq(i)?new n(0):(l=!1,t=w(j(r,o),j(e,o),o),l=!0,k(t,a))},m.minus=m.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?M(t,e):g(t,(e.s=-e.s,e))},m.modulo=m.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(!(e=new n(e)).s)throw Error(c+"NaN");return r.s?(l=!1,t=w(r,e,0,1).times(e),l=!0,r.minus(t)):k(new n(r),i)},m.naturalExponential=m.exp=function(){return O(this)},m.naturalLogarithm=m.ln=function(){return j(this)},m.negated=m.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},m.plus=m.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?g(t,e):M(t,(e.s=-e.s,e))},m.precision=m.sd=function(e){var t,r,n,i=this;if(void 0!==e&&e!==!!e&&1!==e&&0!==e)throw Error(s+e);if(t=P(i)+1,r=7*(n=i.d.length-1)+1,n=i.d[n]){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},m.squareRoot=m.sqrt=function(){var e,t,r,n,i,a,o,s=this,u=s.constructor;if(s.s<1){if(!s.s)return new u(0);throw Error(c+"NaN")}for(e=P(s),l=!1,0==(i=Math.sqrt(+s))||i==1/0?(((t=x(s.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new u(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new u(i.toString()),i=o=(r=u.precision)+3;;)if(n=(a=n).plus(w(s,a,o+2)).times(.5),x(a.d).slice(0,o)===(t=x(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(k(a,r+1,0),a.times(a).eq(s)){n=a;break}}else if("9999"!=t)break;o+=4}return l=!0,k(n,r)},m.times=m.mul=function(e){var t,r,n,i,a,o,c,s,u,f=this,d=f.constructor,p=f.d,y=(e=new d(e)).d;if(!f.s||!e.s)return new d(0);for(e.s*=f.s,r=f.e+e.e,(s=p.length)<(u=y.length)&&(a=p,p=y,y=a,o=s,s=u,u=o),a=[],n=o=s+u;n--;)a.push(0);for(n=u;--n>=0;){for(t=0,i=s+n;i>n;)c=a[i]+y[n]*p[i-n-1]+t,a[i--]=c%h|0,t=c/h|0;a[i]=(a[i]+t)%h|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,l?k(e,d.precision):e},m.toDecimalPlaces=m.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),void 0===e?r:(b(e,0,a),void 0===t?t=n.rounding:b(t,0,8),k(r,e+P(r)+1,t))},m.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=T(n,!0):(b(e,0,a),void 0===t?t=i.rounding:b(t,0,8),r=T(n=k(new i(n),e+1,t),!0,e+1)),r},m.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return void 0===e?T(i):(b(e,0,a),void 0===t?t=o.rounding:b(t,0,8),r=T((n=k(new o(i),e+P(i)+1,t)).abs(),!1,e+P(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)},m.toInteger=m.toint=function(){var e=this,t=e.constructor;return k(new t(e),P(e)+1,t.rounding)},m.toNumber=function(){return+this},m.toPower=m.pow=function(e){var t,r,n,a,o,s,u=this,d=u.constructor,p=+(e=new d(e));if(!e.s)return new d(i);if(!(u=new d(u)).s){if(e.s<1)throw Error(c+"Infinity");return u}if(u.eq(i))return u;if(n=d.precision,e.eq(i))return k(u,n);if(s=(t=e.e)>=(r=e.d.length-1),o=u.s,s){if((r=p<0?-p:p)<=y){for(a=new d(i),t=Math.ceil(n/7+4),l=!1;r%2&&C((a=a.times(u)).d,t),0!==(r=f(r/2));)C((u=u.times(u)).d,t);return l=!0,e.s<0?new d(i).div(a):k(a,n)}}else if(o<0)throw Error(c+"NaN");return o=o<0&&1&e.d[Math.max(t,r)]?-1:1,u.s=1,l=!1,a=e.times(j(u,n+12)),l=!0,(a=O(a)).s=o,a},m.toPrecision=function(e,t){var r,n,i=this,o=i.constructor;return void 0===e?n=T(i,(r=P(i))<=o.toExpNeg||r>=o.toExpPos):(b(e,1,a),void 0===t?t=o.rounding:b(t,0,8),n=T(i=k(new o(i),e,t),e<=(r=P(i))||r<=o.toExpNeg,e)),n},m.toSignificantDigits=m.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(b(e,1,a),void 0===t?t=r.rounding:b(t,0,8)),k(new r(this),e,t)},m.toString=m.valueOf=m.val=m.toJSON=function(){var e=this,t=P(e),r=e.constructor;return T(e,t<=r.toExpNeg||t>=r.toExpPos)};var w=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%h|0,n=r/h|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=e[r]<t[r]?1:0,e[r]=n*h+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,s,u,f,d,p,y,v,m,g,b,x,w,O,E,A,j,S,M=n.constructor,T=n.s==i.s?1:-1,C=n.d,D=i.d;if(!n.s)return new M(n);if(!i.s)throw Error(c+"Division by zero");for(s=n.e-i.e,j=D.length,E=C.length,v=(y=new M(T)).d=[],u=0;D[u]==(C[u]||0);)++u;if(D[u]>(C[u]||0)&&--s,(x=null==a?a=M.precision:o?a+(P(n)-P(i))+1:a)<0)return new M(0);if(x=x/7+2|0,u=0,1==j)for(f=0,D=D[0],x++;(u<E||f)&&x--;u++)w=f*h+(C[u]||0),v[u]=w/D|0,f=w%D|0;else{for((f=h/(D[0]+1)|0)>1&&(D=e(D,f),C=e(C,f),j=D.length,E=C.length),O=j,g=(m=C.slice(0,j)).length;g<j;)m[g++]=0;(S=D.slice()).unshift(0),A=D[0],D[1]>=h/2&&++A;do{f=0,(l=t(D,m,j,g))<0?(b=m[0],j!=g&&(b=b*h+(m[1]||0)),(f=b/A|0)>1?(f>=h&&(f=h-1),1==(l=t(d=e(D,f),m,p=d.length,g=m.length))&&(f--,r(d,j<p?S:D,p))):(0==f&&(l=f=1),d=D.slice()),(p=d.length)<g&&d.unshift(0),r(m,d,g),-1==l&&(l=t(D,m,j,g=m.length))<1&&(f++,r(m,j<g?S:D,g)),g=m.length):0===l&&(f++,m=[0]),v[u++]=f,l&&m[0]?m[g++]=C[O]||0:(m=[C[O]],g=1)}while((O++<E||void 0!==m[0])&&x--)}return v[0]||v.shift(),y.e=s,k(y,o?a+P(y)+1:a)}}();function O(e,t){var r,n,a,o,c,s=0,f=0,p=e.constructor,h=p.precision;if(P(e)>16)throw Error(u+P(e));if(!e.s)return new p(i);for(null==t?(l=!1,c=h):c=t,o=new p(.03125);e.abs().gte(.1);)e=e.times(o),f+=5;for(c+=Math.log(d(2,f))/Math.LN10*2+5|0,r=n=a=new p(i),p.precision=c;;){if(n=k(n.times(e),c),r=r.times(++s),x((o=a.plus(w(n,r,c))).d).slice(0,c)===x(a.d).slice(0,c)){for(;f--;)a=k(a.times(a),c);return p.precision=h,null==t?(l=!0,k(a,h)):a}a=o}}function P(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function E(e,t,r){if(t>e.LN10.sd())throw l=!0,r&&(e.precision=r),Error(c+"LN10 precision limit exceeded");return k(new e(e.LN10),t)}function A(e){for(var t="";e--;)t+="0";return t}function j(e,t){var r,n,a,o,s,u,f,d,p,h=1,y=e,v=y.d,m=y.constructor,g=m.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(i))return new m(0);if(null==t?(l=!1,d=g):d=t,y.eq(10))return null==t&&(l=!0),E(m,d);if(d+=10,m.precision=d,n=(r=x(v)).charAt(0),o=P(y),!(Math.abs(o)<15e14))return f=E(m,d+2,g).times(o+""),y=j(new m(n+"."+r.slice(1)),d-10).plus(f),m.precision=g,null==t?(l=!0,k(y,g)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=x((y=y.times(e)).d)).charAt(0),h++;for(o=P(y),n>1?(y=new m("0."+r),o++):y=new m(n+"."+r.slice(1)),u=s=y=w(y.minus(i),y.plus(i),d),p=k(y.times(y),d),a=3;;){if(s=k(s.times(p),d),x((f=u.plus(w(s,new m(a),d))).d).slice(0,d)===x(u.d).slice(0,d))return u=u.times(2),0!==o&&(u=u.plus(E(m,d+2,g).times(o+""))),u=w(u,new m(h),d),m.precision=g,null==t?(l=!0,k(u,g)):u;u=f,a+=2}}function S(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,r=r-n-1,e.e=f(r/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),l&&(e.e>v||e.e<-v))throw Error(u+r)}else e.s=0,e.e=0,e.d=[0];return e}function k(e,t,r){var n,i,a,o,c,s,p,y,m=e.d;for(o=1,a=m[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,p=m[y=0];else{if((y=Math.ceil((n+1)/7))>=(a=m.length))return e;for(p=a=m[y],o=1;a>=10;a/=10)o++;i=(n%=7)-7+o}if(void 0!==r&&(c=p/(a=d(10,o-i-1))%10|0,s=t<0||void 0!==m[y+1]||p%a,s=r<4?(c||s)&&(0==r||r==(e.s<0?3:2)):c>5||5==c&&(4==r||s||6==r&&(n>0?i>0?p/d(10,o-i):0:m[y-1])%10&1||r==(e.s<0?8:7))),t<1||!m[0])return s?(a=P(e),m.length=1,t=t-a-1,m[0]=d(10,(7-t%7)%7),e.e=f(-t/7)||0):(m.length=1,m[0]=e.e=e.s=0),e;if(0==n?(m.length=y,a=1,y--):(m.length=y+1,a=d(10,7-n),m[y]=i>0?(p/d(10,o-i)%d(10,i)|0)*a:0),s)for(;;){if(0==y){(m[0]+=a)==h&&(m[0]=1,++e.e);break}if(m[y]+=a,m[y]!=h)break;m[y--]=0,a=1}for(n=m.length;0===m[--n];)m.pop();if(l&&(e.e>v||e.e<-v))throw Error(u+P(e));return e}function M(e,t){var r,n,i,a,o,c,s,u,f,d,p=e.constructor,y=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),l?k(t,y):t;if(s=e.d,d=t.d,n=t.e,u=e.e,s=s.slice(),o=u-n){for((f=o<0)?(r=s,o=-o,c=d.length):(r=d,n=u,c=s.length),o>(i=Math.max(Math.ceil(y/7),c)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((f=(i=s.length)<(c=d.length))&&(c=i),i=0;i<c;i++)if(s[i]!=d[i]){f=s[i]<d[i];break}o=0}for(f&&(r=s,s=d,d=r,t.s=-t.s),c=s.length,i=d.length-c;i>0;--i)s[c++]=0;for(i=d.length;i>o;){if(s[--i]<d[i]){for(a=i;a&&0===s[--a];)s[a]=h-1;--s[a],s[i]+=h}s[i]-=d[i]}for(;0===s[--c];)s.pop();for(;0===s[0];s.shift())--n;return s[0]?(t.d=s,t.e=n,l?k(t,y):t):new p(0)}function T(e,t,r){var n,i=P(e),a=x(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+A(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+A(-i-1)+a,r&&(n=r-o)>0&&(a+=A(n))):i>=o?(a+=A(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+A(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=A(n))),e.s<0?"-"+a:a}function C(e,t){if(e.length>t)return e.length=t,!0}function D(e){if(!e||"object"!=typeof e)throw Error(c+"Object expected");var t,r,n,i=["precision",1,a,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]])){if(!(f(n)===n&&n>=i[t+1]&&n<=i[t+2]))throw Error(s+r+": "+n);this[r]=n}if(void 0!==(n=e[r="LN10"])){if(n!=Math.LN10)throw Error(s+r+": "+n);this[r]=new this(n)}return this}o=function e(t){var r,n,i;function a(e){var t=this;if(!(t instanceof a))return new a(e);if(t.constructor=a,e instanceof a)return t.s=e.s,t.e=e.e,void(t.d=(e=e.d)?e.slice():e);if("number"==typeof e){if(0*e!=0)throw Error(s+e);if(e>0)t.s=1;else{if(!(e<0))return t.s=0,t.e=0,void(t.d=[0]);e=-e,t.s=-1}return e===~~e&&e<1e7?(t.e=0,void(t.d=[e])):S(t,e.toString())}if("string"!=typeof e)throw Error(s+e);if(45===e.charCodeAt(0)?(e=e.slice(1),t.s=-1):t.s=1,!p.test(e))throw Error(s+e);S(t,e)}if(a.prototype=m,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=D,void 0===t&&(t={}),t)for(i=["precision","rounding","toExpNeg","toExpPos","LN10"],r=0;r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}(o),o.default=o.Decimal=o,i=new o(1),void 0===(n=function(){return o}.call(t,r,t,e))||(e.exports=n)}()},8493:(e,t,r)=>{"use strict";var n=r(5442);var i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,l=n.useLayoutEffect,c=n.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,u=n[1];return l((function(){i.value=r,i.getSnapshot=t,s(i)&&u({inst:i})}),[e,r,t]),o((function(){return s(i)&&u({inst:i}),e((function(){s(i)&&u({inst:i})}))}),[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:u},8509:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},8695:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e)return!1;if(null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){const t=e[Symbol.toStringTag];if(null==t)return!1;return!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},8805:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){const r=new Map;for(let n=0;n<e.length;n++){const i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},8919:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(1366);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},9181:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},9184:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},9242:(e,t,r)=>{"use strict";e.exports=r(2162)},9341:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(993),i=r(6502);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},9467:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});const n=r(3964),i=r(9184);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,((r,a,o,l)=>{const c=t?.(r,a,o,l);if(null!=c)return c;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{const t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{const t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}}))}},9888:(e,t,r)=>{"use strict";e.exports=r(8493)}},i={};function a(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={exports:{}};return n[e].call(r.exports,r,r.exports,a),r.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return(()=>{"use strict";a.r(o),a.d(o,{Area:()=>_S,AreaChart:()=>rC,Bar:()=>DP,BarChart:()=>XM,Brush:()=>fA,CartesianAxis:()=>hj,CartesianGrid:()=>Ij,Cell:()=>bg,ComposedChart:()=>lC,Cross:()=>al,Curve:()=>Qo,Customized:()=>Ob,DefaultLegendContent:()=>De,DefaultTooltipContent:()=>lo,Dot:()=>Mb,ErrorBar:()=>rP,Funnel:()=>$C,FunnelChart:()=>qC,Global:()=>mo,Label:()=>cb,LabelList:()=>xb,Layer:()=>F,Legend:()=>to,Line:()=>oS,LineChart:()=>WM,Pie:()=>Zw,PieChart:()=>JM,PolarAngleAxis:()=>Wx,PolarGrid:()=>ox,PolarRadiusAxis:()=>kx,Polygon:()=>Sb,Radar:()=>TO,RadarChart:()=>JT,RadialBar:()=>IE,RadialBarChart:()=>aC,Rectangle:()=>Wl,ReferenceArea:()=>JA,ReferenceDot:()=>XA,ReferenceLine:()=>RA,ResponsiveContainer:()=>gg,Sankey:()=>YT,Scatter:()=>bk,ScatterChart:()=>eC,Sector:()=>ql,SunburstChart:()=>bC,Surface:()=>K,Symbols:()=>Se,Text:()=>qg,Tooltip:()=>fg,Trapezoid:()=>mw,Treemap:()=>xT,XAxis:()=>kk,YAxis:()=>Rk,ZAxis:()=>GS,getNiceTickValues:()=>Tp,useActiveTooltipLabel:()=>HO,useChartHeight:()=>Vi,useChartWidth:()=>Xi});var e={};a.r(e),a.d(e,{scaleBand:()=>lc,scaleDiverging:()=>rp,scaleDivergingLog:()=>np,scaleDivergingPow:()=>ap,scaleDivergingSqrt:()=>op,scaleDivergingSymlog:()=>ip,scaleIdentity:()=>Ys,scaleImplicit:()=>ac,scaleLinear:()=>qs,scaleLog:()=>iu,scaleOrdinal:()=>oc,scalePoint:()=>sc,scalePow:()=>pu,scaleQuantile:()=>Au,scaleQuantize:()=>ju,scaleRadial:()=>vu,scaleSequential:()=>Yd,scaleSequentialLog:()=>Gd,scaleSequentialPow:()=>Jd,scaleSequentialQuantile:()=>ep,scaleSequentialSqrt:()=>Qd,scaleSequentialSymlog:()=>Zd,scaleSqrt:()=>hu,scaleSymlog:()=>cu,scaleThreshold:()=>Su,scaleTime:()=>Vd,scaleUtc:()=>$d,tickFormat:()=>$s});var t=a(5442);function r(e){var t,n,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=r(e[t]))&&(i&&(i+=" "),i+=n)}else for(n in e)e[n]&&(i&&(i+=" "),i+=n);return i}function n(){for(var e,t,n=0,i="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=r(e))&&(i&&(i+=" "),i+=t);return i}var i=a(305),l=a.n(i),c=a(2751),s=e=>0===e?0:e>0?1:-1,u=e=>"number"==typeof e&&e!=+e,f=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,d=e=>("number"==typeof e||e instanceof Number)&&!u(e),p=e=>d(e)||"string"==typeof e,h=0,y=e=>{var t=++h;return"".concat(e||"").concat(t)},v=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!d(e)&&"string"!=typeof e)return n;if(f(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return u(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},m=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1},g=(e,t)=>d(e)&&d(t)?r=>e+r*(t-e):()=>t;function b(e,t,r){return d(e)&&d(t)?e+r*(t-e):t}function x(e,t,r){if(e&&e.length)return e.find((e=>e&&("function"==typeof t?t(e):l()(e,t))===r))}var w=e=>null==e,O=e=>w(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1)),P=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],E=["points","pathLength"],A={svg:["viewBox","children"],polygon:E,polyline:E},j=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],S=(e,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,t.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach((e=>{j.includes(e)&&(i[e]=r||(t=>n[e](n,t)))})),i},k=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach((i=>{var a=e[i];j.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=((e,t,r)=>n=>(e(t,r,n),null))(a,t,r))})),n},M=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",T=null,C=null,D=e=>{if(e===T&&Array.isArray(C))return C;var r=[];return t.Children.forEach(e,(e=>{w(e)||((0,c.isFragment)(e)?r=r.concat(D(e.props.children)):r.push(e))})),C=r,T=e,r};function I(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map((e=>M(e))):[M(t)],D(e).forEach((e=>{var t=l()(e,"type.displayName")||l()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)})),r}var N=e=>!e||"object"!=typeof e||!("clipDot"in e)||Boolean(e.clipDot),_=(e,r,n)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var i=e;if((0,t.isValidElement)(e)&&(i=e.props),"object"!=typeof i&&"function"!=typeof i)return null;var a={};return Object.keys(i).forEach((e=>{var t;((e,t,r,n)=>{var i,a=null!==(i=n&&(null==A?void 0:A[n]))&&void 0!==i?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||P.includes(t))||r&&j.includes(t)})(null===(t=i)||void 0===t?void 0:t[e],e,r,n)&&(a[e]=i[e])})),a},R=["children","width","height","viewBox","className","style","title","desc"];function L(){return L=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},L.apply(null,arguments)}var K=(0,t.forwardRef)(((e,r)=>{var{children:i,width:a,height:o,viewBox:l,className:c,style:s,title:u,desc:f}=e,d=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,R),p=l||{width:a,height:o,x:0,y:0},h=n("recharts-surface",c);return t.createElement("svg",L({},_(d,!0,"svg"),{className:h,width:a,height:o,style:s,viewBox:"".concat(p.x," ").concat(p.y," ").concat(p.width," ").concat(p.height),ref:r}),t.createElement("title",null,u),t.createElement("desc",null,f),i)})),z=["children","className"];function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},B.apply(null,arguments)}var F=t.forwardRef(((e,r)=>{var{children:i,className:a}=e,o=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,z),l=n("recharts-layer",a);return t.createElement("g",B({className:l},_(o,!0),{ref:r}),i)})),W=a(6003),U=(0,t.createContext)(null);Math.abs,Math.atan2;const X=Math.cos,V=(Math.max,Math.min,Math.sin),$=Math.sqrt,H=Math.PI,q=2*H;const Y={draw(e,t){const r=$(t/H);e.moveTo(r,0),e.arc(0,0,r,0,q)}},G={draw(e,t){const r=$(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},Z=$(1/3),J=2*Z,Q={draw(e,t){const r=$(t/J),n=r*Z;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},ee={draw(e,t){const r=$(t),n=-r/2;e.rect(n,n,r,r)}},te=V(H/10)/V(7*H/10),re=V(q/10)*te,ne=-X(q/10)*te,ie={draw(e,t){const r=$(.8908130915292852*t),n=re*r,i=ne*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){const a=q*t/5,o=X(a),l=V(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},ae=$(3),oe={draw(e,t){const r=-$(t/(3*ae));e.moveTo(0,2*r),e.lineTo(-ae*r,-r),e.lineTo(ae*r,-r),e.closePath()}},le=-.5,ce=$(3)/2,se=1/$(12),ue=3*(se/2+1),fe={draw(e,t){const r=$(t/ue),n=r/2,i=r*se,a=n,o=r*se+r,l=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(l,c),e.lineTo(le*n-ce*i,ce*n+le*i),e.lineTo(le*a-ce*o,ce*a+le*o),e.lineTo(le*l-ce*c,ce*l+le*c),e.lineTo(le*n+ce*i,le*i-ce*n),e.lineTo(le*a+ce*o,le*o-ce*a),e.lineTo(le*l+ce*c,le*c-ce*l),e.closePath()}};function de(e){return function(){return e}}const pe=Math.PI,he=2*pe,ye=1e-6,ve=he-ye;function me(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class ge{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?me:function(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return me;const r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,n,i){if(e=+e,t=+t,r=+r,n=+n,(i=+i)<0)throw new Error(`negative radius: ${i}`);let a=this._x1,o=this._y1,l=r-e,c=n-t,s=a-e,u=o-t,f=s*s+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>ye)if(Math.abs(u*l-c*s)>ye&&i){let d=r-a,p=n-o,h=l*l+c*c,y=d*d+p*p,v=Math.sqrt(h),m=Math.sqrt(f),g=i*Math.tan((pe-Math.acos((h+f-y)/(2*v*m)))/2),b=g/m,x=g/v;Math.abs(b-1)>ye&&this._append`L${e+b*s},${t+b*u}`,this._append`A${i},${i},0,0,${+(u*d>s*p)},${this._x1=e+x*l},${this._y1=t+x*c}`}else this._append`L${this._x1=e},${this._y1=t}`;else;}arc(e,t,r,n,i,a){if(e=+e,t=+t,a=!!a,(r=+r)<0)throw new Error(`negative radius: ${r}`);let o=r*Math.cos(n),l=r*Math.sin(n),c=e+o,s=t+l,u=1^a,f=a?n-i:i-n;null===this._x1?this._append`M${c},${s}`:(Math.abs(this._x1-c)>ye||Math.abs(this._y1-s)>ye)&&this._append`L${c},${s}`,r&&(f<0&&(f=f%he+he),f>ve?this._append`A${r},${r},0,1,${u},${e-o},${t-l}A${r},${r},0,1,${u},${this._x1=c},${this._y1=s}`:f>ye&&this._append`A${r},${r},0,${+(f>=pe)},${u},${this._x1=e+r*Math.cos(i)},${this._y1=t+r*Math.sin(i)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function be(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{const e=Math.floor(r);if(!(e>=0))throw new RangeError(`invalid digits: ${r}`);t=e}return e},()=>new ge(t)}$(3),$(3);var xe=["type","size","sizeType"];function we(){return we=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},we.apply(null,arguments)}function Oe(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Pe(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Oe(Object(r),!0).forEach((function(t){Ee(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Oe(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ee(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ae={symbolCircle:Y,symbolCross:G,symbolDiamond:Q,symbolSquare:ee,symbolStar:ie,symbolTriangle:oe,symbolWye:fe},je=Math.PI/180,Se=e=>{var r,i,{type:a="circle",size:o=64,sizeType:l="area"}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,xe),s=Pe(Pe({},c),{},{type:a,size:o,sizeType:l}),{className:u,cx:f,cy:d}=s,p=_(s,!0);return f===+f&&d===+d&&o===+o?t.createElement("path",we({},p,{className:n("recharts-symbols",u),transform:"translate(".concat(f,", ").concat(d,")"),d:(r=(e=>{var t="symbol".concat(O(e));return Ae[t]||Y})(a),i=function(e,t){let r=null,n=be(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:de(e||Y),t="function"==typeof t?t:de(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:de(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:de(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i}().type(r).size(((e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return.5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*je;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}})(o,l,a)),i())})):null};function ke(){return ke=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ke.apply(null,arguments)}function Me(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Te(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Se.registerSymbol=(e,t)=>{Ae["symbol".concat(O(e))]=t};var Ce=32;class De extends t.PureComponent{renderIcon(e,r){var{inactiveColor:n}=this.props,i=16,a=Ce/6,o=Ce/3,l=e.inactive?n:e.color,c=null!=r?r:e.type;if("none"===c)return null;if("plainline"===c)return t.createElement("line",{strokeWidth:4,fill:"none",stroke:l,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:i,x2:Ce,y2:i,className:"recharts-legend-icon"});if("line"===c)return t.createElement("path",{strokeWidth:4,fill:"none",stroke:l,d:"M0,".concat(i,"h").concat(o,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(2*o,",").concat(i,"\n            H").concat(Ce,"M").concat(2*o,",").concat(i,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(o,",").concat(i),className:"recharts-legend-icon"});if("rect"===c)return t.createElement("path",{stroke:"none",fill:l,d:"M0,".concat(4,"h").concat(Ce,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(t.isValidElement(e.legendIcon)){var s=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Me(Object(r),!0).forEach((function(t){Te(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Me(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},e);return delete s.legendIcon,t.cloneElement(e.legendIcon,s)}return t.createElement(Se,{fill:l,cx:i,cy:i,size:Ce,sizeType:"diameter",type:c})}renderItems(){var{payload:e,iconSize:r,layout:i,formatter:a,inactiveColor:o,iconType:l}=this.props,c={x:0,y:0,width:Ce,height:Ce},s={display:"horizontal"===i?"inline-block":"block",marginRight:10},u={display:"inline-block",verticalAlign:"middle",marginRight:4};return e.map(((e,i)=>{var f=e.formatter||a,d=n({"recharts-legend-item":!0,["legend-item-".concat(i)]:!0,inactive:e.inactive});if("none"===e.type)return null;var p=e.inactive?o:e.color,h=f?f(e.value,e,i):e.value;return t.createElement("li",ke({className:d,style:s,key:"legend-item-".concat(i)},k(this.props,e,i)),t.createElement(K,{width:r,height:r,viewBox:c,style:u,"aria-label":"".concat(h," legend icon")},this.renderIcon(e,l)),t.createElement("span",{className:"recharts-legend-item-text",style:{color:p}},h))}))}render(){var{payload:e,layout:r,align:n}=this.props;if(!e||!e.length)return null;var i={padding:0,margin:0,textAlign:"horizontal"===r?n:"left"};return t.createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}Te(De,"displayName","Legend"),Te(De,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var Ie=a(1081),Ne=a.n(Ie);function _e(e,t,r){return!0===t?Ne()(e,r):"function"==typeof t?Ne()(e,t):e}var Re=a(9242),Le=(0,t.createContext)(null),Ke=e=>e,ze=()=>{var e=(0,t.useContext)(Le);return e?e.store.dispatch:Ke},Be=()=>{},Fe=()=>Be,We=(e,t)=>e===t;function Ue(e){var r=(0,t.useContext)(Le);return(0,Re.useSyncExternalStoreWithSelector)(r?r.subscription.addNestedSub:Fe,r?r.store.getState:Be,r?r.store.getState:Be,r?e:Be,We)}function Xe(e,t="expected a function, instead received "+typeof e){if("function"!=typeof e)throw new TypeError(t)}var Ve=e=>Array.isArray(e)?e:[e];function $e(e){const t=Array.isArray(e[0])?e[0]:e;return function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every((e=>"function"==typeof e))){const r=e.map((e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e)).join(", ");throw new TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}Symbol(),Object.getPrototypeOf({});var He="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function qe(e,t={}){let r={s:0,v:void 0,o:null,p:null};const{resultEqualityCheck:n}=t;let i,a=0;function o(){let t=r;const{length:o}=arguments;for(let e=0,r=o;e<r;e++){const r=arguments[e];if("function"==typeof r||"object"==typeof r&&null!==r){let e=t.o;null===e&&(t.o=e=new WeakMap);const n=e.get(r);void 0===n?(t={s:0,v:void 0,o:null,p:null},e.set(r,t)):t=n}else{let e=t.p;null===e&&(t.p=e=new Map);const n=e.get(r);void 0===n?(t={s:0,v:void 0,o:null,p:null},e.set(r,t)):t=n}}const l=t;let c;if(1===t.s)c=t.v;else if(c=e.apply(null,arguments),a++,n){const e=i?.deref?.()??i;null!=e&&n(e,c)&&(c=e,0!==a&&a--);i="object"==typeof c&&null!==c||"function"==typeof c?new He(c):c}return l.s=1,l.v=c,c}return o.clearCache=()=>{r={s:0,v:void 0,o:null,p:null},o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}function Ye(e,...t){const r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,i=0,a={},o=e.pop();"object"==typeof o&&(a=o,o=e.pop()),Xe(o,`createSelector expects an output function after the inputs, but received: [${typeof o}]`);const l={...r,...a},{memoize:c,memoizeOptions:s=[],argsMemoize:u=qe,argsMemoizeOptions:f=[],devModeChecks:d={}}=l,p=Ve(s),h=Ve(f),y=$e(e),v=c((function(){return n++,o.apply(null,arguments)}),...p);const m=u((function(){i++;const e=function(e,t){const r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(y,arguments);return t=v.apply(null,e),t}),...h);return Object.assign(m,{resultFunc:o,memoizedResultFunc:v,dependencies:y,dependencyRecomputations:()=>i,resetDependencyRecomputations:()=>{i=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:c,argsMemoize:u})};return Object.assign(n,{withTypes:()=>n}),n}var Ge=Ye(qe),Ze=Object.assign(((e,t=Ge)=>{!function(e,t="expected an object, instead received "+typeof e){if("object"!=typeof e)throw new TypeError(t)}(e,"createStructuredSelector expects first argument to be an object where each property is a selector, instead received a "+typeof e);const r=Object.keys(e),n=t(r.map((t=>e[t])),((...e)=>e.reduce(((e,t,n)=>(e[r[n]]=t,e)),{})));return n}),{withTypes:()=>Ze}),Je=a(184),Qe=a.n(Je),et=e=>e.legend.settings,tt=Ge([e=>e.legend.payload,et],((e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?Qe()(n,r):n}));var rt=1;function nt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[r,n]=(0,t.useState)({height:0,left:0,top:0,width:0}),i=(0,t.useCallback)((e=>{if(null!=e){var t=e.getBoundingClientRect(),i={height:t.height,left:t.left,top:t.top,width:t.width};(Math.abs(i.height-r.height)>rt||Math.abs(i.left-r.left)>rt||Math.abs(i.top-r.top)>rt||Math.abs(i.width-r.width)>rt)&&n({height:i.height,left:i.left,top:i.top,width:i.width})}}),[r.width,r.height,r.top,r.left,...e]);return[r,i]}function it(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];throw Error("[Immer] minified error nr: "+e+(r.length?" "+r.map((function(e){return"'"+e+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function at(e){return!!e&&!!e[qt]}function ot(e){var t;return!!e&&(function(e){if(!e||"object"!=typeof e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===Yt}(e)||Array.isArray(e)||!!e[Ht]||!!(null===(t=e.constructor)||void 0===t?void 0:t[Ht])||pt(e)||ht(e))}function lt(e,t,r){void 0===r&&(r=!1),0===ct(e)?(r?Object.keys:Gt)(e).forEach((function(n){r&&"symbol"==typeof n||t(n,e[n],e)})):e.forEach((function(r,n){return t(n,r,e)}))}function ct(e){var t=e[qt];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:pt(e)?2:ht(e)?3:0}function st(e,t){return 2===ct(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function ut(e,t){return 2===ct(e)?e.get(t):e[t]}function ft(e,t,r){var n=ct(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function dt(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function pt(e){return Ut&&e instanceof Map}function ht(e){return Xt&&e instanceof Set}function yt(e){return e.o||e.t}function vt(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=Zt(e);delete t[qt];for(var r=Gt(t),n=0;n<r.length;n++){var i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(Object.getPrototypeOf(e),t)}function mt(e,t){return void 0===t&&(t=!1),bt(e)||at(e)||!ot(e)||(ct(e)>1&&(e.set=e.add=e.clear=e.delete=gt),Object.freeze(e),t&&lt(e,(function(e,t){return mt(t,!0)}),!0)),e}function gt(){it(2)}function bt(e){return null==e||"object"!=typeof e||Object.isFrozen(e)}function xt(e){var t=Jt[e];return t||it(18,e),t}function wt(e,t){Jt[e]||(Jt[e]=t)}function Ot(){return Ft}function Pt(e,t){t&&(xt("Patches"),e.u=[],e.s=[],e.v=t)}function Et(e){At(e),e.p.forEach(St),e.p=null}function At(e){e===Ft&&(Ft=e.l)}function jt(e){return Ft={p:[],l:Ft,h:e,m:!0,_:0}}function St(e){var t=e[qt];0===t.i||1===t.i?t.j():t.g=!0}function kt(e,t){t._=t.p.length;var r=t.p[0],n=void 0!==e&&e!==r;return t.h.O||xt("ES5").S(t,e,n),n?(r[qt].P&&(Et(t),it(4)),ot(e)&&(e=Mt(t,e),t.l||Ct(t,e)),t.u&&xt("Patches").M(r[qt].t,e,t.u,t.s)):e=Mt(t,r,[]),Et(t),t.u&&t.v(t.u,t.s),e!==$t?e:void 0}function Mt(e,t,r){if(bt(t))return t;var n=t[qt];if(!n)return lt(t,(function(i,a){return Tt(e,n,t,i,a,r)}),!0),t;if(n.A!==e)return t;if(!n.P)return Ct(e,n.t,!0),n.t;if(!n.I){n.I=!0,n.A._--;var i=4===n.i||5===n.i?n.o=vt(n.k):n.o,a=i,o=!1;3===n.i&&(a=new Set(i),i.clear(),o=!0),lt(a,(function(t,a){return Tt(e,n,i,t,a,r,o)})),Ct(e,i,!1),r&&e.u&&xt("Patches").N(n,r,e.u,e.s)}return n.o}function Tt(e,t,r,n,i,a,o){if(at(i)){var l=Mt(e,i,a&&t&&3!==t.i&&!st(t.R,n)?a.concat(n):void 0);if(ft(r,n,l),!at(l))return;e.m=!1}else o&&r.add(i);if(ot(i)&&!bt(i)){if(!e.h.D&&e._<1)return;Mt(e,i),t&&t.A.l||Ct(e,i)}}function Ct(e,t,r){void 0===r&&(r=!1),!e.l&&e.h.D&&e.m&&mt(t,r)}function Dt(e,t){var r=e[qt];return(r?yt(r):e)[t]}function It(e,t){if(t in e)for(var r=Object.getPrototypeOf(e);r;){var n=Object.getOwnPropertyDescriptor(r,t);if(n)return n;r=Object.getPrototypeOf(r)}}function Nt(e){e.P||(e.P=!0,e.l&&Nt(e.l))}function _t(e){e.o||(e.o=vt(e.t))}function Rt(e,t,r){var n=pt(t)?xt("MapSet").F(t,r):ht(t)?xt("MapSet").T(t,r):e.O?function(e,t){var r=Array.isArray(e),n={i:r?1:0,A:t?t.A:Ot(),P:!1,I:!1,R:{},l:t,t:e,k:null,o:null,j:null,C:!1},i=n,a=Qt;r&&(i=[n],a=er);var o=Proxy.revocable(i,a),l=o.revoke,c=o.proxy;return n.k=c,n.j=l,c}(t,r):xt("ES5").J(t,r);return(r?r.A:Ot()).p.push(n),n}function Lt(e){return at(e)||it(22,e),function e(t){if(!ot(t))return t;var r,n=t[qt],i=ct(t);if(n){if(!n.P&&(n.i<4||!xt("ES5").K(n)))return n.t;n.I=!0,r=Kt(t,i),n.I=!1}else r=Kt(t,i);return lt(r,(function(t,i){n&&ut(n.t,t)===i||ft(r,t,e(i))})),3===i?new Set(r):r}(e)}function Kt(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return vt(e)}function zt(){function e(e,t){var r=i[e];return r?r.enumerable=t:i[e]=r={configurable:!0,enumerable:t,get:function(){var t=this[qt];return Qt.get(t,e)},set:function(t){var r=this[qt];Qt.set(r,e,t)}},r}function t(e){for(var t=e.length-1;t>=0;t--){var i=e[t][qt];if(!i.P)switch(i.i){case 5:n(i)&&Nt(i);break;case 4:r(i)&&Nt(i)}}}function r(e){for(var t=e.t,r=e.k,n=Gt(r),i=n.length-1;i>=0;i--){var a=n[i];if(a!==qt){var o=t[a];if(void 0===o&&!st(t,a))return!0;var l=r[a],c=l&&l[qt];if(c?c.t!==o:!dt(l,o))return!0}}var s=!!t[qt];return n.length!==Gt(t).length+(s?0:1)}function n(e){var t=e.k;if(t.length!==e.t.length)return!0;var r=Object.getOwnPropertyDescriptor(t,t.length-1);if(r&&!r.get)return!0;for(var n=0;n<t.length;n++)if(!t.hasOwnProperty(n))return!0;return!1}var i={};wt("ES5",{J:function(t,r){var n=Array.isArray(t),i=function(t,r){if(t){for(var n=Array(r.length),i=0;i<r.length;i++)Object.defineProperty(n,""+i,e(i,!0));return n}var a=Zt(r);delete a[qt];for(var o=Gt(a),l=0;l<o.length;l++){var c=o[l];a[c]=e(c,t||!!a[c].enumerable)}return Object.create(Object.getPrototypeOf(r),a)}(n,t),a={i:n?5:4,A:r?r.A:Ot(),P:!1,I:!1,R:{},l:r,t,k:i,o:null,g:!1,C:!1};return Object.defineProperty(i,qt,{value:a,writable:!0}),i},S:function(e,r,i){i?at(r)&&r[qt].A===e&&t(e.p):(e.u&&function e(t){if(t&&"object"==typeof t){var r=t[qt];if(r){var i=r.t,a=r.k,o=r.R,l=r.i;if(4===l)lt(a,(function(t){t!==qt&&(void 0!==i[t]||st(i,t)?o[t]||e(a[t]):(o[t]=!0,Nt(r)))})),lt(i,(function(e){void 0!==a[e]||st(a,e)||(o[e]=!1,Nt(r))}));else if(5===l){if(n(r)&&(Nt(r),o.length=!0),a.length<i.length)for(var c=a.length;c<i.length;c++)o[c]=!1;else for(var s=i.length;s<a.length;s++)o[s]=!0;for(var u=Math.min(a.length,i.length),f=0;f<u;f++)a.hasOwnProperty(f)||(o[f]=!0),void 0===o[f]&&e(a[f])}}}}(e.p[0]),t(e.p))},K:function(e){return 4===e.i?r(e):n(e)}})}var Bt,Ft,Wt="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),Ut="undefined"!=typeof Map,Xt="undefined"!=typeof Set,Vt="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,$t=Wt?Symbol.for("immer-nothing"):((Bt={})["immer-nothing"]=!0,Bt),Ht=Wt?Symbol.for("immer-draftable"):"__$immer_draftable",qt=Wt?Symbol.for("immer-state"):"__$immer_state",Yt=("undefined"!=typeof Symbol&&Symbol.iterator,""+Object.prototype.constructor),Gt="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,Zt=Object.getOwnPropertyDescriptors||function(e){var t={};return Gt(e).forEach((function(r){t[r]=Object.getOwnPropertyDescriptor(e,r)})),t},Jt={},Qt={get:function(e,t){if(t===qt)return e;var r=yt(e);if(!st(r,t))return function(e,t,r){var n,i=It(t,r);return i?"value"in i?i.value:null===(n=i.get)||void 0===n?void 0:n.call(e.k):void 0}(e,r,t);var n=r[t];return e.I||!ot(n)?n:n===Dt(e.t,t)?(_t(e),e.o[t]=Rt(e.A.h,n,e)):n},has:function(e,t){return t in yt(e)},ownKeys:function(e){return Reflect.ownKeys(yt(e))},set:function(e,t,r){var n=It(yt(e),t);if(null==n?void 0:n.set)return n.set.call(e.k,r),!0;if(!e.P){var i=Dt(yt(e),t),a=null==i?void 0:i[qt];if(a&&a.t===r)return e.o[t]=r,e.R[t]=!1,!0;if(dt(r,i)&&(void 0!==r||st(e.t,t)))return!0;_t(e),Nt(e)}return e.o[t]===r&&(void 0!==r||t in e.o)||Number.isNaN(r)&&Number.isNaN(e.o[t])||(e.o[t]=r,e.R[t]=!0),!0},deleteProperty:function(e,t){return void 0!==Dt(e.t,t)||t in e.t?(e.R[t]=!1,_t(e),Nt(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var r=yt(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.i||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty:function(){it(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){it(12)}},er={};lt(Qt,(function(e,t){er[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),er.deleteProperty=function(e,t){return er.set.call(this,e,t,void 0)},er.set=function(e,t,r){return Qt.set.call(this,e[0],t,r,e[0])};var tr=function(){function e(e){var t=this;this.O=Vt,this.D=!0,this.produce=function(e,r,n){if("function"==typeof e&&"function"!=typeof r){var i=r;r=e;var a=t;return function(e){var t=this;void 0===e&&(e=i);for(var n=arguments.length,o=Array(n>1?n-1:0),l=1;l<n;l++)o[l-1]=arguments[l];return a.produce(e,(function(e){var n;return(n=r).call.apply(n,[t,e].concat(o))}))}}var o;if("function"!=typeof r&&it(6),void 0!==n&&"function"!=typeof n&&it(7),ot(e)){var l=jt(t),c=Rt(t,e,void 0),s=!0;try{o=r(c),s=!1}finally{s?Et(l):At(l)}return"undefined"!=typeof Promise&&o instanceof Promise?o.then((function(e){return Pt(l,n),kt(e,l)}),(function(e){throw Et(l),e})):(Pt(l,n),kt(o,l))}if(!e||"object"!=typeof e){if(void 0===(o=r(e))&&(o=e),o===$t&&(o=void 0),t.D&&mt(o,!0),n){var u=[],f=[];xt("Patches").M(e,o,u,f),n(u,f)}return o}it(21,e)},this.produceWithPatches=function(e,r){if("function"==typeof e)return function(r){for(var n=arguments.length,i=Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];return t.produceWithPatches(r,(function(t){return e.apply(void 0,[t].concat(i))}))};var n,i,a=t.produce(e,r,(function(e,t){n=e,i=t}));return"undefined"!=typeof Promise&&a instanceof Promise?a.then((function(e){return[e,n,i]})):[a,n,i]},"boolean"==typeof(null==e?void 0:e.useProxies)&&this.setUseProxies(e.useProxies),"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze)}var t=e.prototype;return t.createDraft=function(e){ot(e)||it(8),at(e)&&(e=Lt(e));var t=jt(this),r=Rt(this,e,void 0);return r[qt].C=!0,At(t),r},t.finishDraft=function(e,t){var r=(e&&e[qt]).A;return Pt(r,t),kt(void 0,r)},t.setAutoFreeze=function(e){this.D=e},t.setUseProxies=function(e){e&&!Vt&&it(20),this.O=e},t.applyPatches=function(e,t){var r;for(r=t.length-1;r>=0;r--){var n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));var i=xt("Patches").$;return at(e)?i(e,t):this.produce(e,(function(e){return i(e,t)}))},e}(),rr=new tr,nr=rr.produce;rr.produceWithPatches.bind(rr),rr.setAutoFreeze.bind(rr),rr.setUseProxies.bind(rr),rr.applyPatches.bind(rr),rr.createDraft.bind(rr),rr.finishDraft.bind(rr);const ir=nr;function ar(e){return ar="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ar(e)}function or(e){var t=function(e,t){if("object"!=ar(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ar(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ar(t)?t:t+""}function lr(e,t,r){return(t=or(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function sr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?cr(Object(r),!0).forEach((function(t){lr(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cr(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ur(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var fr="function"==typeof Symbol&&Symbol.observable||"@@observable",dr=function(){return Math.random().toString(36).substring(7).split("").join(".")},pr={INIT:"@@redux/INIT"+dr(),REPLACE:"@@redux/REPLACE"+dr(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+dr()}};function hr(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function yr(e,t,r){var n;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(ur(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(ur(1));return r(yr)(e,t)}if("function"!=typeof e)throw new Error(ur(2));var i=e,a=t,o=[],l=o,c=!1;function s(){l===o&&(l=o.slice())}function u(){if(c)throw new Error(ur(3));return a}function f(e){if("function"!=typeof e)throw new Error(ur(4));if(c)throw new Error(ur(5));var t=!0;return s(),l.push(e),function(){if(t){if(c)throw new Error(ur(6));t=!1,s();var r=l.indexOf(e);l.splice(r,1),o=null}}}function d(e){if(!hr(e))throw new Error(ur(7));if(void 0===e.type)throw new Error(ur(8));if(c)throw new Error(ur(9));try{c=!0,a=i(a,e)}finally{c=!1}for(var t=o=l,r=0;r<t.length;r++){(0,t[r])()}return e}return d({type:pr.INIT}),(n={dispatch:d,subscribe:f,getState:u,replaceReducer:function(e){if("function"!=typeof e)throw new Error(ur(10));i=e,d({type:pr.REPLACE})}})[fr]=function(){var e,t=f;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(ur(11));function r(){e.next&&e.next(u())}return r(),{unsubscribe:t(r)}}})[fr]=function(){return this},e},n}function vr(e){for(var t=Object.keys(e),r={},n=0;n<t.length;n++){var i=t[n];0,"function"==typeof e[i]&&(r[i]=e[i])}var a,o=Object.keys(r);try{!function(e){Object.keys(e).forEach((function(t){var r=e[t];if(void 0===r(void 0,{type:pr.INIT}))throw new Error(ur(12));if(void 0===r(void 0,{type:pr.PROBE_UNKNOWN_ACTION()}))throw new Error(ur(13))}))}(r)}catch(e){a=e}return function(e,t){if(void 0===e&&(e={}),a)throw a;for(var n=!1,i={},l=0;l<o.length;l++){var c=o[l],s=r[c],u=e[c],f=s(u,t);if(void 0===f){t&&t.type;throw new Error(ur(14))}i[c]=f,n=n||f!==u}return(n=n||o.length!==Object.keys(e).length)?i:e}}function mr(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function gr(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return function(){var r=e.apply(void 0,arguments),n=function(){throw new Error(ur(15))},i={getState:r.getState,dispatch:function(){return n.apply(void 0,arguments)}},a=t.map((function(e){return e(i)}));return n=mr.apply(void 0,a)(r.dispatch),sr(sr({},r),{},{dispatch:n})}}}function br(e){return function(t){var r=t.dispatch,n=t.getState;return function(t){return function(i){return"function"==typeof i?i(r,n,e):t(i)}}}}var xr=br();xr.withExtraArgument=br;const wr=xr;var Or,Pr=(Or=function(e,t){return Or=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},Or(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}Or(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),Er=function(e,t){var r,n,i,a,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function l(a){return function(l){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,n&&(i=2&a[0]?n.return:a[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,a[1])).done)return i;switch(n=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,n=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],n=0}finally{r=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,l])}}},Ar=function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e},jr=Object.defineProperty,Sr=Object.defineProperties,kr=Object.getOwnPropertyDescriptors,Mr=Object.getOwnPropertySymbols,Tr=Object.prototype.hasOwnProperty,Cr=Object.prototype.propertyIsEnumerable,Dr=function(e,t,r){return t in e?jr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r},Ir=function(e,t){for(var r in t||(t={}))Tr.call(t,r)&&Dr(e,r,t[r]);if(Mr)for(var n=0,i=Mr(t);n<i.length;n++){r=i[n];Cr.call(t,r)&&Dr(e,r,t[r])}return e},Nr=function(e,t){return Sr(e,kr(t))},_r=function(e,t,r){return new Promise((function(n,i){var a=function(e){try{l(r.next(e))}catch(e){i(e)}},o=function(e){try{l(r.throw(e))}catch(e){i(e)}},l=function(e){return e.done?n(e.value):Promise.resolve(e.value).then(a,o)};l((r=r.apply(e,t)).next())}))},Rr="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?mr:mr.apply(null,arguments)};"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;function Lr(e){if("object"!=typeof e||null===e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;for(var r=t;null!==Object.getPrototypeOf(r);)r=Object.getPrototypeOf(r);return t===r}function Kr(e,t){function r(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];if(t){var i=t.apply(void 0,r);if(!i)throw new Error("prepareAction did not return an object");return Ir(Ir({type:e,payload:i.payload},"meta"in i&&{meta:i.meta}),"error"in i&&{error:i.error})}return{type:e,payload:r[0]}}return r.toString=function(){return""+e},r.type=e,r.match=function(t){return t.type===e},r}function zr(e){return Lr(e)&&"type"in e}var Br=function(e){function t(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];var i=e.apply(this,r)||this;return Object.setPrototypeOf(i,t.prototype),i}return Pr(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,Ar([void 0],e[0].concat(this)))):new(t.bind.apply(t,Ar([void 0],e.concat(this))))},t}(Array),Fr=function(e){function t(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];var i=e.apply(this,r)||this;return Object.setPrototypeOf(i,t.prototype),i}return Pr(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,Ar([void 0],e[0].concat(this)))):new(t.bind.apply(t,Ar([void 0],e.concat(this))))},t}(Array);function Wr(e){return ot(e)?ir(e,(function(){})):e}function Ur(){return function(e){return function(e){void 0===e&&(e={});var t=e.thunk,r=void 0===t||t,n=(e.immutableCheck,e.serializableCheck,e.actionCreatorCheck,new Br);r&&(!function(e){return"boolean"==typeof e}(r)?n.push(wr.withExtraArgument(r.extraArgument)):n.push(wr));0;return n}(e)}}function Xr(e){var t,r=Ur(),n=e||{},i=n.reducer,a=void 0===i?void 0:i,o=n.middleware,l=void 0===o?r():o,c=n.devTools,s=void 0===c||c,u=n.preloadedState,f=void 0===u?void 0:u,d=n.enhancers,p=void 0===d?void 0:d;if("function"==typeof a)t=a;else{if(!Lr(a))throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');t=vr(a)}var h=l;"function"==typeof h&&(h=h(r));var y=gr.apply(void 0,h),v=mr;s&&(v=Rr(Ir({trace:!1},"object"==typeof s&&s)));var m=new Fr(y),g=m;return Array.isArray(p)?g=Ar([y],p):"function"==typeof p&&(g=p(m)),yr(t,f,v.apply(void 0,g))}function Vr(e){var t,r={},n=[],i={addCase:function(e,t){var n="string"==typeof e?e:e.type;if(!n)throw new Error("`builder.addCase` cannot be called with an empty action type");if(n in r)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return r[n]=t,i},addMatcher:function(e,t){return n.push({matcher:e,reducer:t}),i},addDefaultCase:function(e){return t=e,i}};return e(i),[r,n,t]}function $r(e){var t=e.name;if(!t)throw new Error("`name` is a required option for createSlice");var r,n="function"==typeof e.initialState?e.initialState:Wr(e.initialState),i=e.reducers||{},a=Object.keys(i),o={},l={},c={};function s(){var t="function"==typeof e.extraReducers?Vr(e.extraReducers):[e.extraReducers],r=t[0],i=void 0===r?{}:r,a=t[1],o=void 0===a?[]:a,c=t[2],s=void 0===c?void 0:c,u=Ir(Ir({},i),l);return function(e,t,r,n){void 0===r&&(r=[]);var i,a="function"==typeof t?Vr(t):[t,r,n],o=a[0],l=a[1],c=a[2];if(function(e){return"function"==typeof e}(e))i=function(){return Wr(e())};else{var s=Wr(e);i=function(){return s}}function u(e,t){void 0===e&&(e=i());var r=Ar([o[t.type]],l.filter((function(e){return(0,e.matcher)(t)})).map((function(e){return e.reducer})));return 0===r.filter((function(e){return!!e})).length&&(r=[c]),r.reduce((function(e,r){if(r){var n;if(at(e))return void 0===(n=r(e,t))?e:n;if(ot(e))return ir(e,(function(e){return r(e,t)}));if(void 0===(n=r(e,t))){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e}),e)}return u.getInitialState=i,u}(n,(function(e){for(var t in u)e.addCase(t,u[t]);for(var r=0,n=o;r<n.length;r++){var i=n[r];e.addMatcher(i.matcher,i.reducer)}s&&e.addDefaultCase(s)}))}return a.forEach((function(e){var r,n,a=i[e],s=function(e,t){return e+"/"+t}(t,e);"reducer"in a?(r=a.reducer,n=a.prepare):r=a,o[e]=r,l[s]=r,c[e]=n?Kr(s,n):Kr(s)})),{name:t,reducer:function(e,t){return r||(r=s()),r(e,t)},actions:c,caseReducers:o,getInitialState:function(){return r||(r=s()),r.getInitialState()}}}var Hr=function(e){void 0===e&&(e=21);for(var t="",r=e;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},qr=["name","message","stack","code"],Yr=function(e,t){this.payload=e,this.meta=t},Gr=function(e,t){this.payload=e,this.meta=t},Zr=function(e){if("object"==typeof e&&null!==e){for(var t={},r=0,n=qr;r<n.length;r++){var i=n[r];"string"==typeof e[i]&&(t[i]=e[i])}return t}return{message:String(e)}};!function(){function e(e,t,r){var n=Kr(e+"/fulfilled",(function(e,t,r,n){return{payload:e,meta:Nr(Ir({},n||{}),{arg:r,requestId:t,requestStatus:"fulfilled"})}})),i=Kr(e+"/pending",(function(e,t,r){return{payload:void 0,meta:Nr(Ir({},r||{}),{arg:t,requestId:e,requestStatus:"pending"})}})),a=Kr(e+"/rejected",(function(e,t,n,i,a){return{payload:i,error:(r&&r.serializeError||Zr)(e||"Rejected"),meta:Nr(Ir({},a||{}),{arg:n,requestId:t,rejectedWithValue:!!i,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)})}})),o="undefined"!=typeof AbortController?AbortController:function(){function e(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return e.prototype.abort=function(){0},e}();return Object.assign((function(e){return function(l,c,s){var u,f=(null==r?void 0:r.idGenerator)?r.idGenerator(e):Hr(),d=new o;function p(e){u=e,d.abort()}var h=function(){return _r(this,null,(function(){var o,h,y,v,m,g;return Er(this,(function(b){switch(b.label){case 0:return b.trys.push([0,4,,5]),function(e){return null!==e&&"object"==typeof e&&"function"==typeof e.then}(v=null==(o=null==r?void 0:r.condition)?void 0:o.call(r,e,{getState:c,extra:s}))?[4,v]:[3,2];case 1:v=b.sent(),b.label=2;case 2:if(!1===v||d.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return m=new Promise((function(e,t){return d.signal.addEventListener("abort",(function(){return t({name:"AbortError",message:u||"Aborted"})}))})),l(i(f,e,null==(h=null==r?void 0:r.getPendingMeta)?void 0:h.call(r,{requestId:f,arg:e},{getState:c,extra:s}))),[4,Promise.race([m,Promise.resolve(t(e,{dispatch:l,getState:c,extra:s,requestId:f,signal:d.signal,abort:p,rejectWithValue:function(e,t){return new Yr(e,t)},fulfillWithValue:function(e,t){return new Gr(e,t)}})).then((function(t){if(t instanceof Yr)throw t;return t instanceof Gr?n(t.payload,f,e,t.meta):n(t,f,e)}))])];case 3:return y=b.sent(),[3,5];case 4:return g=b.sent(),y=g instanceof Yr?a(null,f,e,g.payload,g.meta):a(g,f,e),[3,5];case 5:return r&&!r.dispatchConditionRejection&&a.match(y)&&y.meta.condition||l(y),[2,y]}}))}))}();return Object.assign(h,{abort:p,requestId:f,arg:e,unwrap:function(){return h.then(Jr)}})}}),{pending:i,rejected:a,fulfilled:n,typePrefix:e})}e.withTypes=function(){return e}}();function Jr(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var Qr=function(e,t){if("function"!=typeof e)throw new TypeError(t+" is not a function")},en=function(){},tn=function(e,t){return void 0===t&&(t=en),e.catch(t),e},rn=function(e,t){return e.addEventListener("abort",t,{once:!0}),function(){return e.removeEventListener("abort",t)}},nn=function(e,t){var r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},an="listener",on="completed",ln="cancelled",cn="task-"+ln,sn="task-"+on,un=an+"-"+ln,fn=an+"-"+on,dn=function(e){this.code=e,this.name="TaskAbortError",this.message="task "+ln+" (reason: "+e+")"},pn=function(e){if(e.aborted)throw new dn(e.reason)};function hn(e,t){var r=en;return new Promise((function(n,i){var a=function(){return i(new dn(e.reason))};e.aborted?a():(r=rn(e,a),t.finally((function(){return r()})).then(n,i))})).finally((function(){r=en}))}var yn=function(e){return function(t){return tn(hn(e,t).then((function(t){return pn(e),t})))}},vn=function(e){var t=yn(e);return function(e){return t(new Promise((function(t){return setTimeout(t,e)})))}},mn=Object.assign,gn={},bn="listenerMiddleware",xn=function(e,t){return function(r,n){Qr(r,"taskExecutor");var i,a=new AbortController;i=a,rn(e,(function(){return nn(i,e.reason)}));var o,l,c=(o=function(){return _r(void 0,null,(function(){var t;return Er(this,(function(n){switch(n.label){case 0:return pn(e),pn(a.signal),[4,r({pause:yn(a.signal),delay:vn(a.signal),signal:a.signal})];case 1:return t=n.sent(),pn(a.signal),[2,t]}}))}))},l=function(){return nn(a,sn)},_r(void 0,null,(function(){var e;return Er(this,(function(t){switch(t.label){case 0:return t.trys.push([0,3,4,5]),[4,Promise.resolve()];case 1:return t.sent(),[4,o()];case 2:return[2,{status:"ok",value:t.sent()}];case 3:return[2,{status:(e=t.sent())instanceof dn?"cancelled":"rejected",error:e}];case 4:return null==l||l(),[7];case 5:return[2]}}))})));return(null==n?void 0:n.autoJoin)&&t.push(c),{result:yn(e)(c),cancel:function(){nn(a,cn)}}}},wn=function(e,t){return function(r,n){return tn(function(r,n){return _r(void 0,null,(function(){var i,a,o,l;return Er(this,(function(c){switch(c.label){case 0:pn(t),i=function(){},a=new Promise((function(t,n){var a=e({predicate:r,effect:function(e,r){r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=function(){a(),n()}})),o=[a],null!=n&&o.push(new Promise((function(e){return setTimeout(e,n,null)}))),c.label=1;case 1:return c.trys.push([1,,3,4]),[4,hn(t,Promise.race(o))];case 2:return l=c.sent(),pn(t),[2,l];case 3:return i(),[7];case 4:return[2]}}))}))}(r,n))}},On=function(e){var t=e.type,r=e.actionCreator,n=e.matcher,i=e.predicate,a=e.effect;if(t)i=Kr(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(!i)throw new Error("Creating or removing a listener requires one of the known fields for matching an action");return Qr(a,"options.listener"),{predicate:i,type:t,effect:a}},Pn=function(e){e.pending.forEach((function(e){nn(e,un)}))},En=function(e,t,r){try{e(t,r)}catch(e){setTimeout((function(){throw e}),0)}},An=Kr(bn+"/add"),jn=Kr(bn+"/removeAll"),Sn=Kr(bn+"/remove"),kn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];console.error.apply(console,Ar([bn+"/error"],e))};function Mn(e){var t=this;void 0===e&&(e={});var r=new Map,n=e.extra,i=e.onError,a=void 0===i?kn:i;Qr(a,"onError");var o=function(e){for(var t=0,n=Array.from(r.values());t<n.length;t++){var i=n[t];if(e(i))return i}},l=function(e){var t=o((function(t){return t.effect===e.effect}));return t||(t=function(e){var t=On(e),r=t.type,n=t.predicate,i=t.effect;return{id:Hr(),effect:i,type:r,predicate:n,pending:new Set,unsubscribe:function(){throw new Error("Unsubscribe not initialized")}}}(e)),function(e){return e.unsubscribe=function(){return r.delete(e.id)},r.set(e.id,e),function(t){e.unsubscribe(),(null==t?void 0:t.cancelActive)&&Pn(e)}}(t)},c=function(e){var t=On(e),r=t.type,n=t.effect,i=t.predicate,a=o((function(e){return("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n}));return a&&(a.unsubscribe(),e.cancelActive&&Pn(a)),!!a},s=function(e,i,o,c){return _r(t,null,(function(){var t,s,u,f;return Er(this,(function(d){switch(d.label){case 0:t=new AbortController,s=wn(l,t.signal),u=[],d.label=1;case 1:return d.trys.push([1,3,4,6]),e.pending.add(t),[4,Promise.resolve(e.effect(i,mn({},o,{getOriginalState:c,condition:function(e,t){return s(e,t).then(Boolean)},take:s,delay:vn(t.signal),pause:yn(t.signal),extra:n,signal:t.signal,fork:xn(t.signal,u),unsubscribe:e.unsubscribe,subscribe:function(){r.set(e.id,e)},cancelActiveListeners:function(){e.pending.forEach((function(e,r,n){e!==t&&(nn(e,un),n.delete(e))}))}})))];case 2:return d.sent(),[3,6];case 3:return(f=d.sent())instanceof dn||En(a,f,{raisedBy:"effect"}),[3,6];case 4:return[4,Promise.allSettled(u)];case 5:return d.sent(),nn(t,fn),e.pending.delete(t),[7];case 6:return[2]}}))}))},u=function(e){return function(){e.forEach(Pn),e.clear()}}(r);return{middleware:function(e){return function(t){return function(n){if(!zr(n))return t(n);if(An.match(n))return l(n.payload);if(!jn.match(n)){if(Sn.match(n))return c(n.payload);var i,o=e.getState(),f=function(){if(o===gn)throw new Error(bn+": getOriginalState can only be called synchronously");return o};try{if(i=t(n),r.size>0)for(var d=e.getState(),p=Array.from(r.values()),h=0,y=p;h<y.length;h++){var v=y[h],m=!1;try{m=v.predicate(n,d,o)}catch(e){m=!1,En(a,e,{raisedBy:"predicate"})}m&&s(v,n,e,f)}}finally{o=gn}return i}u()}}},startListening:l,stopListening:c,clearListeners:u}}"function"==typeof queueMicrotask&&queueMicrotask.bind("undefined"!=typeof window?window:void 0!==a.g?a.g:globalThis);var Tn,Cn=function(e){return function(t){setTimeout(t,e)}};"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:Cn(10);zt();var Dn=$r({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:In,setLayout:Nn,setChartSize:_n,setScale:Rn}=Dn.actions,Ln=Dn.reducer;function Kn(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}Array.prototype.slice;function zn(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function Bn(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function Fn(e,t){return e[t]}function Wn(e){const t=[];return t.key=e,t}function Un(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Xn(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Un(Object(r),!0).forEach((function(t){Vn(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Un(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Vn(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var $n=Math.PI/180,Hn=e=>180*e/Math.PI,qn=(e,t,r,n)=>({x:e+Math.cos(-$n*n)*r,y:t+Math.sin(-$n*n)*r}),Yn=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},Gn=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=((e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)})({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var l=(r-i)/o,c=Math.acos(l);return n>a&&(c=2*Math.PI-c),{radius:o,angle:Hn(c),angleInRadian:c}},Zn=(e,t)=>{var{startAngle:r,endAngle:n}=t,i=Math.floor(r/360),a=Math.floor(n/360);return e+360*Math.min(i,a)},Jn=(e,t)=>{var{x:r,y:n}=e,{radius:i,angle:a}=Gn({x:r,y:n},t),{innerRadius:o,outerRadius:l}=t;if(i<o||i>l)return null;if(0===i)return null;var c,{startAngle:s,endAngle:u}=(e=>{var{startAngle:t,endAngle:r}=e,n=Math.floor(t/360),i=Math.floor(r/360),a=Math.min(n,i);return{startAngle:t-360*a,endAngle:r-360*a}})(t),f=a;if(s<=u){for(;f>u;)f-=360;for(;f<s;)f+=360;c=f>=s&&f<=u}else{for(;f>s;)f-=360;for(;f<u;)f+=360;c=f>=u&&f<=s}return c?Xn(Xn({},t),{},{radius:i,angle:Zn(f,t)}):null},Qn=e=>(0,t.isValidElement)(e)||"function"==typeof e||"boolean"==typeof e||null==e?"":e.className;function ei(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ti(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ei(Object(r),!0).forEach((function(t){ri(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ei(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ri(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ni(e,t,r){return w(e)||w(t)?r:p(t)?l()(e,t,r):"function"==typeof t?t(e):r}var ii=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,ai=(e,t,r,n)=>{if(n)return e.map((e=>e.coordinate));var i,a,o=e.map((e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate)));return i||o.push(t),a||o.push(r),o},oi=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:c,categoricalDomain:f,tickCount:d,ticks:p,niceTicks:h,axisType:y}=e;if(!o)return null;var v="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,m=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/v:0;return m="angleAxis"===y&&a&&a.length>=2?2*s(a[0]-a[1])*m:m,t&&(p||h)?(p||h||[]).map(((e,t)=>{var r=n?n.indexOf(e):e;return{coordinate:o(r)+m,value:e,offset:m,index:t}})).filter((e=>!u(e.coordinate))):c&&f?f.map(((e,t)=>({coordinate:o(e)+m,value:e,index:t,offset:m}))):o.ticks&&!r&&null!=d?o.ticks(d).map(((e,t)=>({coordinate:o(e)+m,value:e,offset:m,index:t}))):o.domain().map(((e,t)=>({coordinate:o(e)+m,value:n?n[e]:e,index:t,offset:m})))},li=1e-4,ci=(e,t)=>{if(!t||2!==t.length||!d(t[0])||!d(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!d(e[0])||e[0]<r)&&(i[0]=r),(!d(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},si={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=u(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}Kn(e,t)}},none:Kn,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,l=0;o<r;++o)l+=e[o][n][1]||0;i[n][1]+=i[n][0]=-l/2}Kn(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var l=0,c=0,s=0;l<i;++l){for(var u=e[t[l]],f=u[o][1]||0,d=(f-(u[o-1][1]||0))/2,p=0;p<l;++p){var h=e[t[p]];d+=(h[o][1]||0)-(h[o-1][1]||0)}c+=f,s+=d*f}r[o-1][1]+=r[o-1][0]=a,c&&(a-=s/c)}r[o-1][1]+=r[o-1][0]=a,Kn(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=u(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},ui=(e,t,r)=>{var n=si[r],i=function(){var e=de([]),t=Bn,r=Kn,n=Fn;function i(i){var a,o,l=Array.from(e.apply(this,arguments),Wn),c=l.length,s=-1;for(const e of i)for(a=0,++s;a<c;++a)(l[a][s]=[0,+n(e,l[a].key,s,i)]).data=e;for(a=0,o=zn(t(l));a<c;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:de(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:de(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?Bn:"function"==typeof e?e:de(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?Kn:e,i):r},i}().keys(t).value(((e,t)=>+ni(e,t,0))).order(Bn).offset(n);return i(e)};function fi(e){return null==e?void 0:String(e)}function di(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=e;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!w(i[t.dataKey])){var l=x(r,"value",i[t.dataKey]);if(l)return l.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=ni(i,w(o)?t.dataKey:o);return w(c)?null:t.scale(c)}var pi=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=ni(a,t.dataKey,t.scale.domain()[o]);return w(l)?null:t.scale(l)-i/2+n},hi=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},yi=(e,t,r)=>{var n;if(null!=e)return n=Object.keys(e).reduce(((n,i)=>{var a=e[i],{stackedData:o}=a,l=o.reduce(((e,n)=>{var i,a,o=(i=n.slice(t,r+1),a=i.flat(2).filter(d),[Math.min(...a),Math.max(...a)]);return[Math.min(e[0],o[0]),Math.max(e[1],o[1])]}),[1/0,-1/0]);return[Math.min(l[0],n[0]),Math.max(l[1],n[1])]}),[1/0,-1/0]),[n[0]===1/0?0:n[0],n[1]===-1/0?0:n[1]]},vi=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,mi=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,gi=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var i=Qe()(t,(e=>e.coordinate)),a=1/0,o=1,l=i.length;o<l;o++){var c=i[o],s=i[o-1];a=Math.min((c.coordinate||0)-(s.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function bi(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return ti(ti({},t),{},{dataKey:r,payload:n,value:i,name:a})}function xi(e,t){return e?String(e):"string"==typeof t?t:void 0}var wi=e=>e.layout.width,Oi=e=>e.layout.height,Pi=e=>e.layout.scale,Ei=e=>e.layout.margin,Ai=Ge((e=>e.cartesianAxis.xAxis),(e=>Object.values(e))),ji=Ge((e=>e.cartesianAxis.yAxis),(e=>Object.values(e))),Si=["#1890FF","#66B5FF","#41D9C7","#2FC25B","#6EDB8F","#9AE65C","#FACC14","#E6965C","#57AD71","#223273","#738AE6","#7564CC","#8543E0","#A877ED","#5C8EE6","#13C2C2","#70E0E0","#5CA3E6","#3436C7","#8082FF","#DD81E6","#F04864","#FA7D92","#D598D9"],ki="data-recharts-item-index",Mi="data-recharts-item-data-key";function Ti(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ci(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ti(Object(r),!0).forEach((function(t){Di(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ti(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Di(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ii=Ge([wi,Oi,Ei,e=>e.brush.height,Ai,ji,et,e=>e.legend.size],((e,t,r,n,i,a,o,c)=>{var s=a.reduce(((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:60;return Ci(Ci({},e),{},{[r]:e[r]+n})}return e}),{left:r.left||0,right:r.right||0}),u=i.reduce(((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:Ci(Ci({},e),{},{[r]:l()(e,"".concat(r))+t.height})}),{top:r.top||0,bottom:r.bottom||0}),f=Ci(Ci({},u),s),p=f.bottom;f.bottom+=n,f=((e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&d(e[a]))return ti(ti({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&d(e[o]))return ti(ti({},e),{},{[o]:e[o]+(i||0)})}return e})(f,o,c);var h=e-f.left-f.right,y=t-f.top-f.bottom;return Ci(Ci({brushBottom:p},f),{},{width:Math.max(h,0),height:Math.max(y,0)})})),Ni=Ge(Ii,(e=>({x:e.left,y:e.top,width:e.width,height:e.height}))),_i=Ge(wi,Oi,((e,t)=>({x:0,y:0,width:e,height:t}))),Ri=(0,t.createContext)(null),Li=()=>null!=(0,t.useContext)(Ri),Ki=e=>{var{children:r}=e;return t.createElement(Ri.Provider,{value:!0},r)},zi=e=>e.brush,Bi=Ge([zi,Ii,Ei],((e,t,r)=>({height:e.height,x:d(e.x)?e.x:t.left,y:d(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:d(e.width)?e.width:t.width}))),Fi=()=>{var e,t=Li(),r=Ue(Ni),n=Ue(Bi),i=null===(e=Ue(zi))||void 0===e?void 0:e.padding;return t&&n&&i?{width:n.width-i.left-i.right,height:n.height-i.top-i.bottom,x:i.left,y:i.top}:r},Wi={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},Ui=()=>{var e;return null!==(e=Ue(Ii))&&void 0!==e?e:Wi},Xi=()=>Ue(wi),Vi=()=>Ue(Oi),$i={top:0,right:0,bottom:0,left:0},Hi=e=>e.layout.layoutType,qi=()=>Ue(Hi),Yi=e=>{var r=ze();return(0,t.useEffect)((()=>{r(_n(e))}),[r,e]),null},Gi=e=>{var{margin:r}=e,n=ze();return(0,t.useEffect)((()=>{n(In(r))}),[n,r]),null},Zi=Symbol.for("immer-nothing"),Ji=Symbol.for("immer-draftable"),Qi=Symbol.for("immer-state");function ea(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var ta=Object.getPrototypeOf;function ra(e){return!!e&&!!e[Qi]}function na(e){return!!e&&(aa(e)||Array.isArray(e)||!!e[Ji]||!!e.constructor?.[Ji]||ua(e)||fa(e))}var ia=Object.prototype.constructor.toString();function aa(e){if(!e||"object"!=typeof e)return!1;const t=ta(e);if(null===t)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===ia}function oa(e,t){0===la(e)?Reflect.ownKeys(e).forEach((r=>{t(r,e[r],e)})):e.forEach(((r,n)=>t(n,r,e)))}function la(e){const t=e[Qi];return t?t.type_:Array.isArray(e)?1:ua(e)?2:fa(e)?3:0}function ca(e,t){return 2===la(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function sa(e,t,r){const n=la(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function ua(e){return e instanceof Map}function fa(e){return e instanceof Set}function da(e){return e.copy_||e.base_}function pa(e,t){if(ua(e))return new Map(e);if(fa(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=aa(e);if(!0===t||"class_only"===t&&!r){const t=Object.getOwnPropertyDescriptors(e);delete t[Qi];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){const i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(ta(e),t)}{const t=ta(e);if(null!==t&&r)return{...e};const n=Object.create(t);return Object.assign(n,e)}}function ha(e,t=!1){return va(e)||ra(e)||!na(e)||(la(e)>1&&(e.set=e.add=e.clear=e.delete=ya),Object.freeze(e),t&&Object.entries(e).forEach((([e,t])=>ha(t,!0)))),e}function ya(){ea(2)}function va(e){return Object.isFrozen(e)}var ma,ga={};function ba(e){const t=ga[e];return t||ea(0),t}function xa(){return ma}function wa(e,t){t&&(ba("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function Oa(e){Pa(e),e.drafts_.forEach(Aa),e.drafts_=null}function Pa(e){e===ma&&(ma=e.parent_)}function Ea(e){return ma={drafts_:[],parent_:ma,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function Aa(e){const t=e[Qi];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function ja(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return void 0!==e&&e!==r?(r[Qi].modified_&&(Oa(t),ea(4)),na(e)&&(e=Sa(t,e),t.parent_||Ma(t,e)),t.patches_&&ba("Patches").generateReplacementPatches_(r[Qi].base_,e,t.patches_,t.inversePatches_)):e=Sa(t,r,[]),Oa(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==Zi?e:void 0}function Sa(e,t,r){if(va(t))return t;const n=t[Qi];if(!n)return oa(t,((i,a)=>ka(e,n,t,i,a,r))),t;if(n.scope_!==e)return t;if(!n.modified_)return Ma(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const t=n.copy_;let i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),oa(i,((i,o)=>ka(e,n,t,i,o,r,a))),Ma(e,t,!1),r&&e.patches_&&ba("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function ka(e,t,r,n,i,a,o){if(ra(i)){const o=Sa(e,i,a&&t&&3!==t.type_&&!ca(t.assigned_,n)?a.concat(n):void 0);if(sa(r,n,o),!ra(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(na(i)&&!va(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;Sa(e,i),t&&t.scope_.parent_||"symbol"==typeof n||!Object.prototype.propertyIsEnumerable.call(r,n)||Ma(e,i)}}function Ma(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&ha(t,r)}var Ta={get(e,t){if(t===Qi)return e;const r=da(e);if(!ca(r,t))return function(e,t,r){const n=Ia(t,r);return n?"value"in n?n.value:n.get?.call(e.draft_):void 0}(e,r,t);const n=r[t];return e.finalized_||!na(n)?n:n===Da(e.base_,t)?(_a(e),e.copy_[t]=Ra(n,e)):n},has:(e,t)=>t in da(e),ownKeys:e=>Reflect.ownKeys(da(e)),set(e,t,r){const n=Ia(da(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const n=Da(da(e),t),i=n?.[Qi];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}(r,n)&&(void 0!==r||ca(e.base_,t)))return!0;_a(e),Na(e)}return e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==Da(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,_a(e),Na(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const r=da(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){ea(11)},getPrototypeOf:e=>ta(e.base_),setPrototypeOf(){ea(12)}},Ca={};function Da(e,t){const r=e[Qi];return(r?da(r):e)[t]}function Ia(e,t){if(!(t in e))return;let r=ta(e);for(;r;){const e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=ta(r)}}function Na(e){e.modified_||(e.modified_=!0,e.parent_&&Na(e.parent_))}function _a(e){e.copy_||(e.copy_=pa(e.base_,e.scope_.immer_.useStrictShallowCopy_))}oa(Ta,((e,t)=>{Ca[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),Ca.deleteProperty=function(e,t){return Ca.set.call(this,e,t,void 0)},Ca.set=function(e,t,r){return Ta.set.call(this,e[0],t,r,e[0])};function Ra(e,t){const r=ua(e)?ba("MapSet").proxyMap_(e,t):fa(e)?ba("MapSet").proxySet_(e,t):function(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:xa(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let i=n,a=Ta;r&&(i=[n],a=Ca);const{revoke:o,proxy:l}=Proxy.revocable(i,a);return n.draft_=l,n.revoke_=o,l}(e,t);return(t?t.scope_:xa()).drafts_.push(r),r}function La(e){if(!na(e)||va(e))return e;const t=e[Qi];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=pa(e,t.scope_.immer_.useStrictShallowCopy_)}else r=pa(e,!0);return oa(r,((e,t)=>{sa(r,e,La(t))})),t&&(t.finalized_=!1),r}var Ka=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{if("function"==typeof e&&"function"!=typeof t){const r=t;t=e;const n=this;return function(e=r,...i){return n.produce(e,(e=>t.call(this,e,...i)))}}let n;if("function"!=typeof t&&ea(6),void 0!==r&&"function"!=typeof r&&ea(7),na(e)){const i=Ea(this),a=Ra(e,void 0);let o=!0;try{n=t(a),o=!1}finally{o?Oa(i):Pa(i)}return wa(i,r),ja(n,i)}if(!e||"object"!=typeof e){if(n=t(e),void 0===n&&(n=e),n===Zi&&(n=void 0),this.autoFreeze_&&ha(n,!0),r){const t=[],i=[];ba("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}ea(1)},this.produceWithPatches=(e,t)=>{if("function"==typeof e)return(t,...r)=>this.produceWithPatches(t,(t=>e(t,...r)));let r,n;const i=this.produce(e,t,((e,t)=>{r=e,n=t}));return[i,r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){na(e)||ea(8),ra(e)&&(e=function(e){ra(e)||ea(10);return La(e)}(e));const t=Ea(this),r=Ra(e,void 0);return r[Qi].isManual_=!0,Pa(t),r}finishDraft(e,t){const r=e&&e[Qi];r&&r.isManual_||ea(9);const{scope_:n}=r;return wa(n,t),ja(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));const n=ba("Patches").applyPatches_;return ra(e)?n(e,t):this.produce(e,(e=>n(e,t)))}};Ka.produce,Ka.produceWithPatches.bind(Ka),Ka.setAutoFreeze.bind(Ka),Ka.setUseStrictShallowCopy.bind(Ka),Ka.applyPatches.bind(Ka),Ka.createDraft.bind(Ka),Ka.finishDraft.bind(Ka);var za=$r({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push(t.payload)},removeLegendPayload(e,t){var r=Lt(e).payload.indexOf(t.payload);r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:Ba,setLegendSettings:Fa,addLegendPayload:Wa,removeLegendPayload:Ua}=za.actions,Xa=za.reducer,Va=["contextPayload"];function $a(){return $a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$a.apply(null,arguments)}function Ha(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function qa(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ha(Object(r),!0).forEach((function(t){Ya(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ha(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ya(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ga(e){return e.value}function Za(e){var{contextPayload:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Va),i=_e(r,e.payloadUniqBy,Ga),a=qa(qa({},n),{},{payload:i});return t.isValidElement(e.content)?t.cloneElement(e.content,a):"function"==typeof e.content?t.createElement(e.content,a):t.createElement(De,a)}function Ja(e){var r=ze();return(0,t.useEffect)((()=>{r(Fa(e))}),[r,e]),null}function Qa(e){var r=ze();return(0,t.useEffect)((()=>(r(Ba(e)),()=>{r(Ba({width:0,height:0}))})),[r,e]),null}function eo(e){var r,n=Ue(tt),i=(0,t.useContext)(U),a=null!==(r=Ue((e=>e.layout.margin)))&&void 0!==r?r:$i,{width:o,height:l,wrapperStyle:c,portal:s}=e,[u,f]=nt([n]),d=Xi(),p=Vi(),h=d-(a.left||0)-(a.right||0),y=to.getWidthOrHeight(e.layout,l,o,h),v=s?c:qa(qa({position:"absolute",width:(null==y?void 0:y.width)||o||"auto",height:(null==y?void 0:y.height)||l||"auto"},function(e,t,r,n,i,a){var o,l,{layout:c,align:s,verticalAlign:u}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(o="center"===s&&"vertical"===c?{left:((n||0)-a.width)/2}:"right"===s?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(l="middle"===u?{top:((i||0)-a.height)/2}:"bottom"===u?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),qa(qa({},o),l)}(c,e,a,d,p,u)),c),m=null!=s?s:i;if(null==m)return null;var g=t.createElement("div",{className:"recharts-legend-wrapper",style:v,ref:f},t.createElement(Ja,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign,itemSorter:e.itemSorter}),t.createElement(Qa,{width:u.width,height:u.height}),t.createElement(Za,$a({},e,y,{margin:a,chartWidth:d,chartHeight:p,contextPayload:n})));return(0,W.createPortal)(g,m)}class to extends t.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&d(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return t.createElement(eo,this.props)}}function ro(){return ro=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ro.apply(null,arguments)}function no(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function io(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?no(Object(r),!0).forEach((function(t){ao(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):no(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ao(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oo(e){return Array.isArray(e)&&p(e[0])&&p(e[1])?e.join(" ~ "):e}Ya(to,"displayName","Legend"),Ya(to,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"});var lo=e=>{var{separator:r=" : ",contentStyle:i={},itemStyle:a={},labelStyle:o={},payload:l,formatter:c,itemSorter:s,wrapperClassName:u,labelClassName:f,label:d,labelFormatter:h,accessibilityLayer:y=!1}=e,v=io({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},i),m=io({margin:0},o),g=!w(d),b=g?d:"",x=n("recharts-default-tooltip",u),O=n("recharts-tooltip-label",f);g&&h&&null!=l&&(b=h(d,l));var P=y?{role:"status","aria-live":"assertive"}:{};return t.createElement("div",ro({className:x,style:v},P),t.createElement("p",{className:O,style:m},t.isValidElement(b)?b:"".concat(b)),(()=>{if(l&&l.length){var e=(s?Qe()(l,s):l).map(((e,n)=>{if("none"===e.type)return null;var i=e.formatter||c||oo,{value:o,name:s}=e,u=o,f=s;if(i){var d=i(o,s,e,n,l);if(Array.isArray(d))[u,f]=d;else{if(null==d)return null;u=d}}var h=io({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},a);return t.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(n),style:h},p(f)?t.createElement("span",{className:"recharts-tooltip-item-name"},f):null,p(f)?t.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,t.createElement("span",{className:"recharts-tooltip-item-value"},u),t.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))}));return t.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},co="recharts-tooltip-wrapper",so={visibility:"hidden"};function uo(e){var{coordinate:t,translateX:r,translateY:i}=e;return n(co,{["".concat(co,"-right")]:d(r)&&t&&d(t.x)&&r>=t.x,["".concat(co,"-left")]:d(r)&&t&&d(t.x)&&r<t.x,["".concat(co,"-bottom")]:d(i)&&t&&d(t.y)&&i>=t.y,["".concat(co,"-top")]:d(i)&&t&&d(t.y)&&i<t.y})}function fo(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:c,viewBoxDimension:s}=e;if(a&&d(a[n]))return a[n];var u=r[n]-l-(i>0?i:0),f=r[n]+i;if(t[n])return o[n]?u:f;var p=c[n];return null==p?0:o[n]?u<p?Math.max(f,p):Math.max(u,p):null==s?0:f+l>p+s?Math.max(u,p):Math.max(f,p)}function po(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ho(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?po(Object(r),!0).forEach((function(t){yo(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):po(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function yo(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class vo extends t.PureComponent{constructor(){super(...arguments),yo(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),yo(this,"handleKeyDown",(e=>{var t,r,n,i;"Escape"===e.key&&this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(t=null===(r=this.props.coordinate)||void 0===r?void 0:r.x)&&void 0!==t?t:0,y:null!==(n=null===(i=this.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==n?n:0}})}))}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)===this.state.dismissedAtCoordinate.x&&(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}render(){var{active:e,allowEscapeViewBox:r,animationDuration:n,animationEasing:i,children:a,coordinate:o,hasPayload:l,isAnimationActive:c,offset:s,position:u,reverseDirection:f,useTranslate3d:d,viewBox:p,wrapperStyle:h,lastBoundingBox:y,innerRef:v,hasPortalFromProps:m}=this.props,{cssClasses:g,cssProperties:b}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:l,reverseDirection:c,tooltipBox:s,useTranslate3d:u,viewBox:f}=e;return t=s.height>0&&s.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=fo({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:l,reverseDirection:c,tooltipDimension:s.width,viewBox:f,viewBoxDimension:f.width}),translateY:n=fo({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:l,reverseDirection:c,tooltipDimension:s.height,viewBox:f,viewBoxDimension:f.height}),useTranslate3d:u}):so,{cssProperties:t,cssClasses:uo({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:r,coordinate:o,offsetTopLeft:s,position:u,reverseDirection:f,tooltipBox:{height:y.height,width:y.width},useTranslate3d:d,viewBox:p}),x=m?{}:ho(ho({transition:c&&e?"transform ".concat(n,"ms ").concat(i):void 0},b),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&l?"visible":"hidden",position:"absolute",top:0,left:0}),w=ho(ho({},x),{},{visibility:!this.state.dismissed&&e&&l?"visible":"hidden"},h);return t.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:g,style:w,ref:v},a)}}var mo={isSsr:!("undefined"!=typeof window&&window.document&&Boolean(window.document.createElement)&&window.setTimeout)},go=()=>Ue((e=>e.rootProps.accessibilityLayer));function bo(){}function xo(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function wo(e){this._context=e}function Oo(e){this._context=e}function Po(e){this._context=e}wo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:xo(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:xo(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},Oo.prototype={areaStart:bo,areaEnd:bo,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:xo(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},Po.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:xo(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class Eo{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function Ao(e){this._context=e}function jo(e){this._context=e}function So(e){return new jo(e)}function ko(e){return e<0?-1:1}function Mo(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),l=(a*i+o*n)/(n+i);return(ko(a)+ko(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(l))||0}function To(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Co(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function Do(e){this._context=e}function Io(e){this._context=new No(e)}function No(e){this._context=e}function _o(e){this._context=e}function Ro(e){var t,r,n=e.length-1,i=new Array(n),a=new Array(n),o=new Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[n-1]=(e[n]+i[n-1])/2,t=0;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function Lo(e,t){this._context=e,this._t=t}function Ko(e){return e[0]}function zo(e){return e[1]}function Bo(e,t){var r=de(!0),n=null,i=So,a=null,o=be(l);function l(l){var c,s,u,f=(l=zn(l)).length,d=!1;for(null==n&&(a=i(u=o())),c=0;c<=f;++c)!(c<f&&r(s=l[c],c,l))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(s,c,l),+t(s,c,l));if(u)return a=null,u+""||null}return e="function"==typeof e?e:void 0===e?Ko:de(e),t="function"==typeof t?t:void 0===t?zo:de(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:de(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:de(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:de(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function Fo(e,t,r){var n=null,i=de(!0),a=null,o=So,l=null,c=be(s);function s(s){var u,f,d,p,h,y=(s=zn(s)).length,v=!1,m=new Array(y),g=new Array(y);for(null==a&&(l=o(h=c())),u=0;u<=y;++u){if(!(u<y&&i(p=s[u],u,s))===v)if(v=!v)f=u,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=u-1;d>=f;--d)l.point(m[d],g[d]);l.lineEnd(),l.areaEnd()}v&&(m[u]=+e(p,u,s),g[u]=+t(p,u,s),l.point(n?+n(p,u,s):m[u],r?+r(p,u,s):g[u]))}if(h)return l=null,h+""||null}function u(){return Bo().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?Ko:de(+e),t="function"==typeof t?t:de(void 0===t?0:+t),r="function"==typeof r?r:void 0===r?zo:de(+r),s.x=function(t){return arguments.length?(e="function"==typeof t?t:de(+t),n=null,s):e},s.x0=function(t){return arguments.length?(e="function"==typeof t?t:de(+t),s):e},s.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:de(+e),s):n},s.y=function(e){return arguments.length?(t="function"==typeof e?e:de(+e),r=null,s):t},s.y0=function(e){return arguments.length?(t="function"==typeof e?e:de(+e),s):t},s.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:de(+e),s):r},s.lineX0=s.lineY0=function(){return u().x(e).y(t)},s.lineY1=function(){return u().x(e).y(r)},s.lineX1=function(){return u().x(n).y(t)},s.defined=function(e){return arguments.length?(i="function"==typeof e?e:de(!!e),s):i},s.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),s):o},s.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),s):a},s}function Wo(e){return Number.isFinite(e)}function Uo(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}function Xo(){return Xo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xo.apply(null,arguments)}function Vo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function $o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Vo(Object(r),!0).forEach((function(t){Ho(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vo(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ho(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Ao.prototype={areaStart:bo,areaEnd:bo,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}},jo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},Do.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Co(this,this._t0,To(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t=+t,(e=+e)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Co(this,To(this,r=Mo(this,e,t)),r);break;default:Co(this,this._t0,r=Mo(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(Io.prototype=Object.create(Do.prototype)).point=function(e,t){Do.prototype.point.call(this,t,e)},No.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},_o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=Ro(e),i=Ro(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},Lo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var qo={curveBasisClosed:function(e){return new Oo(e)},curveBasisOpen:function(e){return new Po(e)},curveBasis:function(e){return new wo(e)},curveBumpX:function(e){return new Eo(e,!0)},curveBumpY:function(e){return new Eo(e,!1)},curveLinearClosed:function(e){return new Ao(e)},curveLinear:So,curveMonotoneX:function(e){return new Do(e)},curveMonotoneY:function(e){return new Io(e)},curveNatural:function(e){return new _o(e)},curveStep:function(e){return new Lo(e,.5)},curveStepAfter:function(e){return new Lo(e,1)},curveStepBefore:function(e){return new Lo(e,0)}},Yo=e=>Wo(e.x)&&Wo(e.y),Go=e=>e.x,Zo=e=>e.y,Jo=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=((e,t)=>{if("function"==typeof e)return e;var r="curve".concat(O(e));return"curveMonotone"!==r&&"curveBump"!==r||!t?qo[r]||So:qo["".concat(r).concat("vertical"===t?"Y":"X")]})(r,a),c=o?n.filter(Yo):n;if(Array.isArray(i)){var s=o?i.filter((e=>Yo(e))):i,u=c.map(((e,t)=>$o($o({},e),{},{base:s[t]})));return t="vertical"===a?Fo().y(Zo).x1(Go).x0((e=>e.base.x)):Fo().x(Go).y1(Zo).y0((e=>e.base.y)),t.defined(Yo).curve(l),t(u)}return(t="vertical"===a&&d(i)?Fo().y(Zo).x1(Go).x0(i):d(i)?Fo().x(Go).y1(Zo).y0(i):Bo().x(Go).y(Zo)).defined(Yo).curve(l),t(c)},Qo=e=>{var{className:r,points:i,path:a,pathRef:o}=e;if(!(i&&i.length||a))return null;var l=i&&i.length?Jo(e):a;return t.createElement("path",Xo({},_(e,!1),S(e),{className:n("recharts-curve",r),d:null===l?void 0:l,ref:o}))},el=["x","y","top","left","width","height","className"];function tl(){return tl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},tl.apply(null,arguments)}function rl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function nl(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var il=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),al=e=>{var{x:r=0,y:i=0,top:a=0,left:o=0,width:l=0,height:c=0,className:s}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,el),f=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rl(Object(r),!0).forEach((function(t){nl(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rl(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({x:r,y:i,top:a,left:o,width:l,height:c},u);return d(r)&&d(i)&&d(l)&&d(c)&&d(a)&&d(o)?t.createElement("path",tl({},_(f,!0),{className:n("recharts-cross",s),d:il(r,i,l,c,a,o)})):null};function ol(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ll(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cl(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ol(Object(r),!0).forEach((function(t){ll(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ol(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},e),n=t;return Object.keys(t).reduce(((e,t)=>(void 0===e[t]&&void 0!==n[t]&&(e[t]=n[t]),e)),r)}var sl=a(7541),ul=a.n(sl);var fl=1e-4,dl=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],pl=(e,t)=>e.map(((e,r)=>e*t**r)).reduce(((e,t)=>e+t)),hl=(e,t)=>r=>{var n=dl(e,t);return pl(n,r)},yl=function(){for(var e,t,r,n,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map((e=>parseFloat(e))))}else 4===a.length&&([e,r,t,n]=a);var c,s,u=hl(e,t),f=hl(r,n),d=(c=e,s=t,e=>{var t=[...dl(c,s).map(((e,t)=>e*t)).slice(1),0];return pl(t,e)}),p=e=>e>1?1:e<0?0:e,h=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=u(r)-t,a=d(r);if(Math.abs(i-t)<fl||a<fl)return f(r);r=p(r-i/a)}return f(r)};return h.isStepper=!1,h},vl=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return yl(e);case"spring":return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return Math.abs(l-i)<fl&&Math.abs(o)<fl?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i}();default:if("cubic-bezier"===e.split("(")[0])return yl(e)}return"function"==typeof e?e:null};function ml(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function gl(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ml(Object(r),!0).forEach((function(t){bl(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ml(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function bl(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var xl=(e,t)=>Object.keys(t).reduce(((r,n)=>gl(gl({},r),{},{[n]:e(n,t[n])})),{});function wl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ol(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?wl(Object(r),!0).forEach((function(t){Pl(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wl(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Pl(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var El=(e,t,r)=>e+(t-e)*r,Al=e=>{var{from:t,to:r}=e;return t!==r},jl=(e,t,r)=>{var n=xl(((t,r)=>{if(Al(r)){var[n,i]=e(r.from,r.to,r.velocity);return Ol(Ol({},r),{},{from:n,velocity:i})}return r}),t);return r<1?xl(((e,t)=>Al(t)?Ol(Ol({},t),{},{velocity:El(t.velocity,n[e].velocity,r),from:El(t.from,n[e].from,r)}):t),t):jl(e,n,r-1)};function Sl(e,t,r,n,i,a){var o,l=n.reduce(((r,n)=>Ol(Ol({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}})),{}),c=null,s=n=>{o||(o=n);var u=(n-o)/r.dt;l=jl(r,l,u),i(Ol(Ol(Ol({},e),t),xl(((e,t)=>t.from),l))),o=n,Object.values(l).filter(Al).length&&(c=a.setTimeout(s))};return()=>(c=a.setTimeout(s),()=>{c()})}const kl=(e,t,r,n,i,a)=>{var o,l,c=(o=e,l=t,[Object.keys(o),Object.keys(l)].reduce(((e,t)=>e.filter((e=>t.includes(e))))));return!0===r.isStepper?Sl(e,t,r,c,i,a):function(e,t,r,n,i,a,o){var l,c=null,s=i.reduce(((r,n)=>Ol(Ol({},r),{},{[n]:[e[n],t[n]]})),{}),u=i=>{l||(l=i);var f=(i-l)/n,d=xl(((e,t)=>El(...t,r(f))),s);if(a(Ol(Ol(Ol({},e),t),d)),f<1)c=o.setTimeout(u);else{var p=xl(((e,t)=>El(...t,r(1))),s);a(Ol(Ol(Ol({},e),t),p))}};return()=>(c=o.setTimeout(u),()=>{c()})}(e,t,r,n,c,i,a)};class Ml{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var Tl=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function Cl(){return Cl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cl.apply(null,arguments)}function Dl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Il(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Dl(Object(r),!0).forEach((function(t){Nl(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dl(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Nl(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _l(){return e=new Ml,t=()=>null,r=!1,n=null,i=a=>{if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,[l,...c]=o;return"number"==typeof l?void(n=e.setTimeout(i.bind(null,c),l)):(i(l),void(n=e.setTimeout(i.bind(null,c))))}"object"==typeof a&&t(a),"function"==typeof a&&a()}},{stop:()=>{r=!0},start:e=>{r=!1,n&&(n(),n=null),i(e)},subscribe:e=>(t=e,()=>{t=()=>null}),getTimeoutController:()=>e};var e,t,r,n,i}class Rl extends t.PureComponent{constructor(e,t){super(e,t),Nl(this,"mounted",!1),Nl(this,"manager",null),Nl(this,"stopJSAnimation",null),Nl(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0)return this.state={style:{}},void("function"==typeof o&&(this.state={style:a}));if(i){if("function"==typeof o)return void(this.state={style:i});this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:a,from:o}=this.props,{style:l}=this.state;if(r)if(t){if(!(ul()(e.to,a)&&e.canBegin&&e.isActive)){var c=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=c||i?o:e.to;if(this.state&&l){var u={style:n?{[n]:s}:s};(n&&l[n]!==s||!n&&l!==s)&&this.setState(u)}this.runAnimation(Il(Il({},this.props),{},{from:s,begin:0}))}}else{var f={style:n?{[n]:a}:a};this.state&&l&&(n&&l[n]!==a||!n&&l!==a)&&this.setState(f)}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=e,c=kl(t,r,vl(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=c()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:c}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"!=typeof a&&"function"!=typeof c&&"spring"!==a){var s=n?{[n]:i}:i,u=((e,t,r)=>e.map((e=>{return"".concat((n=e,n.replace(/([A-Z])/g,(e=>"-".concat(e.toLowerCase()))))," ").concat(t,"ms ").concat(r);var n})).join(","))(Object.keys(s),r,a);this.manager.start([o,t,Il(Il({},s),{},{transition:u}),r,l])}else this.runJSAnimation(e)}render(){var e=this.props,{children:r,begin:n,duration:i,attributeName:a,easing:o,isActive:l,from:c,to:s,canBegin:u,onAnimationEnd:f,shouldReAnimate:d,onAnimationReStart:p,animationManager:h}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Tl),v=t.Children.count(r),m=this.state.style;if("function"==typeof r)return r(m);if(!l||0===v||i<=0)return r;var g=e=>{var{style:r={},className:n}=e.props;return(0,t.cloneElement)(e,Il(Il({},y),{},{style:Il(Il({},r),m),className:n}))};return 1===v?g(t.Children.only(r)):t.createElement("div",null,t.Children.map(r,(e=>g(e))))}}Nl(Rl,"displayName","Animate"),Nl(Rl,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var Ll=(0,t.createContext)(null);function Kl(e){var r,n,i=(0,t.useContext)(Ll);return t.createElement(Rl,Cl({},e,{animationManager:null!==(r=null!==(n=e.animationManager)&&void 0!==n?n:i)&&void 0!==r?r:_l()}))}function zl(){return zl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zl.apply(null,arguments)}var Bl=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,c=r>=0?1:-1,s=n>=0&&r>=0||n<0&&r<0?1:0;if(o>0&&i instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*u[0]),u[0]>0&&(a+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(s,",").concat(e+c*u[0],",").concat(t)),a+="L ".concat(e+r-c*u[1],",").concat(t),u[1]>0&&(a+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(s,",\n        ").concat(e+r,",").concat(t+l*u[1])),a+="L ".concat(e+r,",").concat(t+n-l*u[2]),u[2]>0&&(a+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(s,",\n        ").concat(e+r-c*u[2],",").concat(t+n)),a+="L ".concat(e+c*u[3],",").concat(t+n),u[3]>0&&(a+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(s,",\n        ").concat(e,",").concat(t+n-l*u[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var d=Math.min(o,i);a="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+c*d,",").concat(t,"\n            L ").concat(e+r-c*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r-c*d,",").concat(t+n,"\n            L ").concat(e+c*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e,",").concat(t+n-l*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},Fl={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Wl=e=>{var r=cl(e,Fl),i=(0,t.useRef)(null),[a,o]=(0,t.useState)(-1);(0,t.useEffect)((()=>{if(i.current&&i.current.getTotalLength)try{var e=i.current.getTotalLength();e&&o(e)}catch(e){}}),[]);var{x:l,y:c,width:s,height:u,radius:f,className:d}=r,{animationEasing:p,animationDuration:h,animationBegin:y,isAnimationActive:v,isUpdateAnimationActive:m}=r;if(l!==+l||c!==+c||s!==+s||u!==+u||0===s||0===u)return null;var g=n("recharts-rectangle",d);return m?t.createElement(Kl,{canBegin:a>0,from:{width:s,height:u,x:l,y:c},to:{width:s,height:u,x:l,y:c},duration:h,animationEasing:p,isActive:m},(e=>{var{width:n,height:o,x:l,y:c}=e;return t.createElement(Kl,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:h,isActive:v,easing:p},t.createElement("path",zl({},_(r,!0),{className:g,d:Bl(l,c,n,o,f),ref:i})))})):t.createElement("path",zl({},_(r,!0),{className:g,d:Bl(l,c,s,u,f)}))};function Ul(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[qn(t,r,n,i),qn(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function Xl(){return Xl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xl.apply(null,arguments)}var Vl=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:o,cornerRadius:l,cornerIsExternal:c}=e,s=l*(o?1:-1)+n,u=Math.asin(l/s)/$n,f=c?i:i+a*u,d=c?i-a*u:i;return{center:qn(t,r,s,f),circleTangency:qn(t,r,n,f),lineTangency:qn(t,r,s*Math.cos(u*$n),d),theta:u}},$l=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:o}=e,l=((e,t)=>s(t-e)*Math.min(Math.abs(t-e),359.999))(a,o),c=a+l,u=qn(t,r,i,a),f=qn(t,r,i,c),d="M ".concat(u.x,",").concat(u.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(a>c),",\n    ").concat(f.x,",").concat(f.y,"\n  ");if(n>0){var p=qn(t,r,n,a),h=qn(t,r,n,c);d+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(a<=c),",\n            ").concat(p.x,",").concat(p.y," Z")}else d+="L ".concat(t,",").concat(r," Z");return d},Hl={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},ql=e=>{var r=cl(e,Hl),{cx:i,cy:a,innerRadius:o,outerRadius:l,cornerRadius:c,forceCornerRadius:u,cornerIsExternal:f,startAngle:d,endAngle:p,className:h}=r;if(l<o||d===p)return null;var y,m=n("recharts-sector",h),g=l-o,b=v(c,g,0,!0);return y=b>0&&Math.abs(d-p)<360?(e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:l,startAngle:c,endAngle:u}=e,f=s(u-c),{circleTangency:d,lineTangency:p,theta:h}=Vl({cx:t,cy:r,radius:i,angle:c,sign:f,cornerRadius:a,cornerIsExternal:l}),{circleTangency:y,lineTangency:v,theta:m}=Vl({cx:t,cy:r,radius:i,angle:u,sign:-f,cornerRadius:a,cornerIsExternal:l}),g=l?Math.abs(c-u):Math.abs(c-u)-h-m;if(g<0)return o?"M ".concat(p.x,",").concat(p.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*-a,",0\n      "):$l({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:c,endAngle:u});var b="M ".concat(p.x,",").concat(p.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(v.x,",").concat(v.y,"\n  ");if(n>0){var{circleTangency:x,lineTangency:w,theta:O}=Vl({cx:t,cy:r,radius:n,angle:c,sign:f,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),{circleTangency:P,lineTangency:E,theta:A}=Vl({cx:t,cy:r,radius:n,angle:u,sign:-f,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),j=l?Math.abs(c-u):Math.abs(c-u)-O-A;if(j<0&&0===a)return"".concat(b,"L").concat(t,",").concat(r,"Z");b+="L".concat(E.x,",").concat(E.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(j>180),",").concat(+(f>0),",").concat(x.x,",").concat(x.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(f<0),",").concat(w.x,",").concat(w.y,"Z")}else b+="L".concat(t,",").concat(r,"Z");return b})({cx:i,cy:a,innerRadius:o,outerRadius:l,cornerRadius:Math.min(b,g/2),forceCornerRadius:u,cornerIsExternal:f,startAngle:d,endAngle:p}):$l({cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:d,endAngle:p}),t.createElement("path",Xl({},_(r,!0),{className:m,d:y}))};function Yl(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return Ul(t);var{cx:l,cy:c,innerRadius:s,outerRadius:u,angle:f}=t,d=qn(l,c,s,f),p=qn(l,c,u,f);n=d.x,i=d.y,a=p.x,o=p.y}return[{x:n,y:i},{x:a,y:o}]}var Gl=a(3412),Zl=a.n(Gl);function Jl(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function Ql(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class ec extends Map{constructor(e,t=ic){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(const[t,r]of e)this.set(t,r)}get(e){return super.get(tc(this,e))}has(e){return super.has(tc(this,e))}set(e,t){return super.set(rc(this,e),t)}delete(e){return super.delete(nc(this,e))}}Set;function tc({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function rc({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function nc({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function ic(e){return null!==e&&"object"==typeof e?e.valueOf():e}const ac=Symbol("implicit");function oc(){var e=new ec,t=[],r=[],n=ac;function i(i){let a=e.get(i);if(void 0===a){if(n!==ac)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();t=[],e=new ec;for(const n of r)e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return oc(t,r).unknown(n)},Jl.apply(i,arguments),i}function lc(){var e,t,r=oc().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,c=0,s=0,u=.5;function f(){var r=n().length,f=o<a,d=f?o:a,p=f?a:o;e=(p-d)/Math.max(1,r-c+2*s),l&&(e=Math.floor(e)),d+=(p-d-e*(r-c))*u,t=e*(1-c),l&&(d=Math.round(d),t=Math.round(t));var h=function(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=new Array(i);++n<i;)a[n]=e+n*r;return a}(r).map((function(t){return d+e*t}));return i(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a=+a,o=+o,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a=+a,o=+o,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(c=Math.min(1,s=+e),f()):c},r.paddingInner=function(e){return arguments.length?(c=Math.min(1,e),f()):c},r.paddingOuter=function(e){return arguments.length?(s=+e,f()):s},r.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},r.copy=function(){return lc(n(),[a,o]).round(l).paddingInner(c).paddingOuter(s).align(u)},Jl.apply(f(),arguments)}function cc(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return cc(t())},e}function sc(){return cc(lc.apply(null,arguments).paddingInner(1))}const uc=Math.sqrt(50),fc=Math.sqrt(10),dc=Math.sqrt(2);function pc(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=uc?10:a>=fc?5:a>=dc?2:1;let l,c,s;return i<0?(s=Math.pow(10,-i)/o,l=Math.round(e*s),c=Math.round(t*s),l/s<e&&++l,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,l=Math.round(e/s),c=Math.round(t/s),l*s<e&&++l,c*s>t&&--c),c<l&&.5<=r&&r<2?pc(e,t,2*r):[l,c,s]}function hc(e,t,r){if(!((r=+r)>0))return[];if((e=+e)===(t=+t))return[e];const n=t<e,[i,a,o]=n?pc(t,e,r):pc(e,t,r);if(!(a>=i))return[];const l=a-i+1,c=new Array(l);if(n)if(o<0)for(let e=0;e<l;++e)c[e]=(a-e)/-o;else for(let e=0;e<l;++e)c[e]=(a-e)*o;else if(o<0)for(let e=0;e<l;++e)c[e]=(i+e)/-o;else for(let e=0;e<l;++e)c[e]=(i+e)*o;return c}function yc(e,t,r){return pc(e=+e,t=+t,r=+r)[2]}function vc(e,t,r){r=+r;const n=(t=+t)<(e=+e),i=n?yc(t,e,r):yc(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function mc(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function gc(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function bc(e){let t,r,n;function i(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{const t=i+a>>>1;r(e[t],n)<0?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=mc,r=(t,r)=>mc(e(t),r),n=(t,r)=>e(t)-r):(t=e===mc||e===gc?e:xc,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){const o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{const t=i+a>>>1;r(e[t],n)<=0?i=t+1:a=t}while(i<a)}return i}}}function xc(){return 0}function wc(e){return null===e?NaN:+e}const Oc=bc(mc),Pc=Oc.right,Ec=(Oc.left,bc(wc).center,Pc);function Ac(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function jc(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function Sc(){}var kc=.7,Mc=1/kc,Tc="\\s*([+-]?\\d+)\\s*",Cc="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Dc="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Ic=/^#([0-9a-f]{3,8})$/,Nc=new RegExp(`^rgb\\(${Tc},${Tc},${Tc}\\)$`),_c=new RegExp(`^rgb\\(${Dc},${Dc},${Dc}\\)$`),Rc=new RegExp(`^rgba\\(${Tc},${Tc},${Tc},${Cc}\\)$`),Lc=new RegExp(`^rgba\\(${Dc},${Dc},${Dc},${Cc}\\)$`),Kc=new RegExp(`^hsl\\(${Cc},${Dc},${Dc}\\)$`),zc=new RegExp(`^hsla\\(${Cc},${Dc},${Dc},${Cc}\\)$`),Bc={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function Fc(){return this.rgb().formatHex()}function Wc(){return this.rgb().formatRgb()}function Uc(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=Ic.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?Xc(t):3===r?new Hc(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?Vc(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?Vc(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=Nc.exec(e))?new Hc(t[1],t[2],t[3],1):(t=_c.exec(e))?new Hc(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=Rc.exec(e))?Vc(t[1],t[2],t[3],t[4]):(t=Lc.exec(e))?Vc(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=Kc.exec(e))?Qc(t[1],t[2]/100,t[3]/100,1):(t=zc.exec(e))?Qc(t[1],t[2]/100,t[3]/100,t[4]):Bc.hasOwnProperty(e)?Xc(Bc[e]):"transparent"===e?new Hc(NaN,NaN,NaN,0):null}function Xc(e){return new Hc(e>>16&255,e>>8&255,255&e,1)}function Vc(e,t,r,n){return n<=0&&(e=t=r=NaN),new Hc(e,t,r,n)}function $c(e,t,r,n){return 1===arguments.length?function(e){return e instanceof Sc||(e=Uc(e)),e?new Hc((e=e.rgb()).r,e.g,e.b,e.opacity):new Hc}(e):new Hc(e,t,r,null==n?1:n)}function Hc(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function qc(){return`#${Jc(this.r)}${Jc(this.g)}${Jc(this.b)}`}function Yc(){const e=Gc(this.opacity);return`${1===e?"rgb(":"rgba("}${Zc(this.r)}, ${Zc(this.g)}, ${Zc(this.b)}${1===e?")":`, ${e})`}`}function Gc(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Zc(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Jc(e){return((e=Zc(e))<16?"0":"")+e.toString(16)}function Qc(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ts(e,t,r,n)}function es(e){if(e instanceof ts)return new ts(e.h,e.s,e.l,e.opacity);if(e instanceof Sc||(e=Uc(e)),!e)return new ts;if(e instanceof ts)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,c=(a+i)/2;return l?(o=t===a?(r-n)/l+6*(r<n):r===a?(n-t)/l+2:(t-r)/l+4,l/=c<.5?a+i:2-a-i,o*=60):l=c>0&&c<1?0:o,new ts(o,l,c,e.opacity)}function ts(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function rs(e){return(e=(e||0)%360)<0?e+360:e}function ns(e){return Math.max(0,Math.min(1,e||0))}function is(e,t,r){return 255*(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)}function as(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}Ac(Sc,Uc,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Fc,formatHex:Fc,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return es(this).formatHsl()},formatRgb:Wc,toString:Wc}),Ac(Hc,$c,jc(Sc,{brighter(e){return e=null==e?Mc:Math.pow(Mc,e),new Hc(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?kc:Math.pow(kc,e),new Hc(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Hc(Zc(this.r),Zc(this.g),Zc(this.b),Gc(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:qc,formatHex:qc,formatHex8:function(){return`#${Jc(this.r)}${Jc(this.g)}${Jc(this.b)}${Jc(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Yc,toString:Yc})),Ac(ts,(function(e,t,r,n){return 1===arguments.length?es(e):new ts(e,t,r,null==n?1:n)}),jc(Sc,{brighter(e){return e=null==e?Mc:Math.pow(Mc,e),new ts(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?kc:Math.pow(kc,e),new ts(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new Hc(is(e>=240?e-240:e+120,i,n),is(e,i,n),is(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new ts(rs(this.h),ns(this.s),ns(this.l),Gc(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Gc(this.opacity);return`${1===e?"hsl(":"hsla("}${rs(this.h)}, ${100*ns(this.s)}%, ${100*ns(this.l)}%${1===e?")":`, ${e})`}`}}));const os=e=>()=>e;function ls(e,t){return function(r){return e+r*t}}function cs(e){return 1==(e=+e)?ss:function(t,r){return r-t?function(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}(t,r,e):os(isNaN(t)?r:t)}}function ss(e,t){var r=t-e;return r?ls(e,r):os(isNaN(e)?t:e)}const us=function e(t){var r=cs(t);function n(e,t){var n=r((e=$c(e)).r,(t=$c(t)).r),i=r(e.g,t.g),a=r(e.b,t.b),o=ss(e.opacity,t.opacity);return function(t){return e.r=n(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return n.gamma=e,n}(1);function fs(e){return function(t){var r,n,i=t.length,a=new Array(i),o=new Array(i),l=new Array(i);for(r=0;r<i;++r)n=$c(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}fs((function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return as((r-n/t)*t,o,i,a,l)}})),fs((function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return as((r-n/t)*t,i,a,o,l)}}));function ds(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=new Array(i),o=new Array(n);for(r=0;r<i;++r)a[r]=xs(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}function ps(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function hs(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function ys(e,t){var r,n={},i={};for(r in null!==e&&"object"==typeof e||(e={}),null!==t&&"object"==typeof t||(t={}),t)r in e?n[r]=xs(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}var vs=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ms=new RegExp(vs.source,"g");function gs(e,t){var r,n,i,a=vs.lastIndex=ms.lastIndex=0,o=-1,l=[],c=[];for(e+="",t+="";(r=vs.exec(e))&&(n=ms.exec(t));)(i=n.index)>a&&(i=t.slice(a,i),l[o]?l[o]+=i:l[++o]=i),(r=r[0])===(n=n[0])?l[o]?l[o]+=n:l[++o]=n:(l[++o]=null,c.push({i:o,x:hs(r,n)})),a=ms.lastIndex;return a<t.length&&(i=t.slice(a),l[o]?l[o]+=i:l[++o]=i),l.length<2?c[0]?function(e){return function(t){return e(t)+""}}(c[0].x):function(e){return function(){return e}}(t):(t=c.length,function(e){for(var r,n=0;n<t;++n)l[(r=c[n]).i]=r.x(e);return l.join("")})}function bs(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}}function xs(e,t){var r,n=typeof t;return null==t||"boolean"===n?os(t):("number"===n?hs:"string"===n?(r=Uc(t))?(t=r,us):gs:t instanceof Uc?us:t instanceof Date?ps:function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}(t)?bs:Array.isArray(t)?ds:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?ys:hs)(e,t)}function ws(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function Os(e){return+e}var Ps=[0,1];function Es(e){return e}function As(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:function(e){return function(){return e}}(isNaN(t)?NaN:.5)}function js(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=As(i,n),a=r(o,a)):(n=As(n,i),a=r(a,o)),function(e){return a(n(e))}}function Ss(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=As(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=Ec(e,t,1,n)-1;return a[r](i[r](t))}}function ks(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function Ms(){var e,t,r,n,i,a,o=Ps,l=Ps,c=xs,s=Es;function u(){var e=Math.min(o.length,l.length);return s!==Es&&(s=function(e,t){var r;return e>t&&(r=e,e=t,t=r),function(r){return Math.max(e,Math.min(t,r))}}(o[0],o[e-1])),n=e>2?Ss:js,i=a=null,f}function f(t){return null==t||isNaN(t=+t)?r:(i||(i=n(o.map(e),l,c)))(e(s(t)))}return f.invert=function(r){return s(t((a||(a=n(l,o.map(e),hs)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,Os),u()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),u()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),c=ws,u()},f.clamp=function(e){return arguments.length?(s=!!e||Es,u()):s!==Es},f.interpolate=function(e){return arguments.length?(c=e,u()):c},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,u()}}function Ts(){return Ms()(Es,Es)}var Cs,Ds=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Is(e){if(!(t=Ds.exec(e)))throw new Error("invalid format: "+e);var t;return new Ns({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function Ns(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function _s(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function Rs(e){return(e=_s(Math.abs(e)))?e[1]:NaN}function Ls(e,t){var r=_s(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}Is.prototype=Ns.prototype,Ns.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const Ks={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Ls(100*e,t),r:Ls,s:function(e,t){var r=_s(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(Cs=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+_s(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function zs(e){return e}var Bs,Fs,Ws,Us=Array.prototype.map,Xs=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Vs(e){var t,r,n=void 0===e.grouping||void 0===e.thousands?zs:(t=Us.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],c=0;i>0&&l>0&&(c+l+1>n&&(l=Math.max(1,n-c)),a.push(e.substring(i-=l,i+l)),!((c+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),i=void 0===e.currency?"":e.currency[0]+"",a=void 0===e.currency?"":e.currency[1]+"",o=void 0===e.decimal?".":e.decimal+"",l=void 0===e.numerals?zs:function(e){return function(t){return t.replace(/[0-9]/g,(function(t){return e[+t]}))}}(Us.call(e.numerals,String)),c=void 0===e.percent?"%":e.percent+"",s=void 0===e.minus?"−":e.minus+"",u=void 0===e.nan?"NaN":e.nan+"";function f(e){var t=(e=Is(e)).fill,r=e.align,f=e.sign,d=e.symbol,p=e.zero,h=e.width,y=e.comma,v=e.precision,m=e.trim,g=e.type;"n"===g?(y=!0,g="g"):Ks[g]||(void 0===v&&(v=12),m=!0,g="g"),(p||"0"===t&&"="===r)&&(p=!0,t="0",r="=");var b="$"===d?i:"#"===d&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",x="$"===d?a:/[%p]/.test(g)?c:"",w=Ks[g],O=/[defgprs%]/.test(g);function P(e){var i,a,c,d=b,P=x;if("c"===g)P=w(e)+P,e="";else{var E=(e=+e)<0||1/e<0;if(e=isNaN(e)?u:w(Math.abs(e),v),m&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),E&&0==+e&&"+"!==f&&(E=!1),d=(E?"("===f?f:s:"-"===f||"("===f?"":f)+d,P=("s"===g?Xs[8+Cs/3]:"")+P+(E&&"("===f?")":""),O)for(i=-1,a=e.length;++i<a;)if(48>(c=e.charCodeAt(i))||c>57){P=(46===c?o+e.slice(i+1):e.slice(i))+P,e=e.slice(0,i);break}}y&&!p&&(e=n(e,1/0));var A=d.length+e.length+P.length,j=A<h?new Array(h-A+1).join(t):"";switch(y&&p&&(e=n(j+e,j.length?h-P.length:1/0),j=""),r){case"<":e=d+e+P+j;break;case"=":e=d+j+e+P;break;case"^":e=j.slice(0,A=j.length>>1)+d+e+P+j.slice(A);break;default:e=j+d+e+P}return l(e)}return v=void 0===v?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),P.toString=function(){return e+""},P}return{format:f,formatPrefix:function(e,t){var r=f(((e=Is(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(Rs(t)/3))),i=Math.pow(10,-n),a=Xs[8+n/3];return function(e){return r(i*e)+a}}}}function $s(e,t,r,n){var i,a=vc(e,t,r);switch((n=Is(null==n?",f":n)).type){case"s":var o=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(i=function(e,t){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Rs(t)/3)))-Rs(Math.abs(e)))}(a,o))||(n.precision=i),Ws(n,o);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(i=function(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,Rs(t)-Rs(e))+1}(a,Math.max(Math.abs(e),Math.abs(t))))||(n.precision=i-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(i=function(e){return Math.max(0,-Rs(Math.abs(e)))}(a))||(n.precision=i-2*("%"===n.type))}return Fs(n)}function Hs(e){var t=e.domain;return e.ticks=function(e){var r=t();return hc(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return $s(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,c=a[o],s=a[l],u=10;for(s<c&&(i=c,c=s,s=i,i=o,o=l,l=i);u-- >0;){if((i=yc(c,s,r))===n)return a[o]=c,a[l]=s,t(a);if(i>0)c=Math.floor(c/i)*i,s=Math.ceil(s/i)*i;else{if(!(i<0))break;c=Math.ceil(c*i)/i,s=Math.floor(s*i)/i}n=i}return e},e}function qs(){var e=Ts();return e.copy=function(){return ks(e,qs())},Jl.apply(e,arguments),Hs(e)}function Ys(e){var t;function r(e){return null==e||isNaN(e=+e)?t:e}return r.invert=r,r.domain=r.range=function(t){return arguments.length?(e=Array.from(t,Os),r):e.slice()},r.unknown=function(e){return arguments.length?(t=e,r):t},r.copy=function(){return Ys(e).unknown(t)},e=arguments.length?Array.from(e,Os):[0,1],Hs(r)}function Gs(e,t){var r,n=0,i=(e=e.slice()).length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function Zs(e){return Math.log(e)}function Js(e){return Math.exp(e)}function Qs(e){return-Math.log(-e)}function eu(e){return-Math.exp(-e)}function tu(e){return isFinite(e)?+("1e"+e):e<0?0:e}function ru(e){return(t,r)=>-e(-t,r)}function nu(e){const t=e(Zs,Js),r=t.domain;let n,i,a=10;function o(){return n=function(e){return e===Math.E?Math.log:10===e&&Math.log10||2===e&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}(a),i=function(e){return 10===e?tu:e===Math.E?Math.exp:t=>Math.pow(e,t)}(a),r()[0]<0?(n=ru(n),i=ru(i),e(Qs,eu)):e(Zs,Js),t}return t.base=function(e){return arguments.length?(a=+e,o()):a},t.domain=function(e){return arguments.length?(r(e),o()):r()},t.ticks=e=>{const t=r();let o=t[0],l=t[t.length-1];const c=l<o;c&&([o,l]=[l,o]);let s,u,f=n(o),d=n(l);const p=null==e?10:+e;let h=[];if(!(a%1)&&d-f<p){if(f=Math.floor(f),d=Math.ceil(d),o>0){for(;f<=d;++f)for(s=1;s<a;++s)if(u=f<0?s/i(-f):s*i(f),!(u<o)){if(u>l)break;h.push(u)}}else for(;f<=d;++f)for(s=a-1;s>=1;--s)if(u=f>0?s/i(-f):s*i(f),!(u<o)){if(u>l)break;h.push(u)}2*h.length<p&&(h=hc(o,l,p))}else h=hc(f,d,Math.min(d-f,p)).map(i);return c?h.reverse():h},t.tickFormat=(e,r)=>{if(null==e&&(e=10),null==r&&(r=10===a?"s":","),"function"!=typeof r&&(a%1||null!=(r=Is(r)).precision||(r.trim=!0),r=Fs(r)),e===1/0)return r;const o=Math.max(1,a*e/t.ticks().length);return e=>{let t=e/i(Math.round(n(e)));return t*a<a-.5&&(t*=a),t<=o?r(e):""}},t.nice=()=>r(Gs(r(),{floor:e=>i(Math.floor(n(e))),ceil:e=>i(Math.ceil(n(e)))})),t}function iu(){const e=nu(Ms()).domain([1,10]);return e.copy=()=>ks(e,iu()).base(e.base()),Jl.apply(e,arguments),e}function au(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function ou(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function lu(e){var t=1,r=e(au(t),ou(t));return r.constant=function(r){return arguments.length?e(au(t=+r),ou(t)):t},Hs(r)}function cu(){var e=lu(Ms());return e.copy=function(){return ks(e,cu()).constant(e.constant())},Jl.apply(e,arguments)}function su(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function uu(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function fu(e){return e<0?-e*e:e*e}function du(e){var t=e(Es,Es),r=1;return t.exponent=function(t){return arguments.length?1===(r=+t)?e(Es,Es):.5===r?e(uu,fu):e(su(r),su(1/r)):r},Hs(t)}function pu(){var e=du(Ms());return e.copy=function(){return ks(e,pu()).exponent(e.exponent())},Jl.apply(e,arguments),e}function hu(){return pu.apply(null,arguments).exponent(.5)}function yu(e){return Math.sign(e)*e*e}function vu(){var e,t=Ts(),r=[0,1],n=!1;function i(r){var i=function(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}(t(r));return isNaN(i)?e:n?Math.round(i):i}return i.invert=function(e){return t.invert(yu(e))},i.domain=function(e){return arguments.length?(t.domain(e),i):t.domain()},i.range=function(e){return arguments.length?(t.range((r=Array.from(e,Os)).map(yu)),i):r.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(n=!!e,i):n},i.clamp=function(e){return arguments.length?(t.clamp(e),i):t.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return vu(t.domain(),r).round(n).clamp(t.clamp()).unknown(e)},Jl.apply(i,arguments),Hs(i)}function mu(e,t){let r;if(void 0===t)for(const t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function gu(e,t){let r;if(void 0===t)for(const t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function bu(e=mc){if(e===mc)return xu;if("function"!=typeof e)throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}function xu(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:e>t?1:0)}function wu(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=void 0===i?xu:bu(i);n>r;){if(n-r>600){const a=n-r+1,o=t-r+1,l=Math.log(a),c=.5*Math.exp(2*l/3),s=.5*Math.sqrt(l*c*(a-c)/a)*(o-a/2<0?-1:1);wu(e,t,Math.max(r,Math.floor(t-o*c/a+s)),Math.min(n,Math.floor(t+(a-o)*c/a+s)),i)}const a=e[t];let o=r,l=n;for(Ou(e,r,t),i(e[n],a)>0&&Ou(e,r,n);o<l;){for(Ou(e,o,l),++o,--l;i(e[o],a)<0;)++o;for(;i(e[l],a)>0;)--l}0===i(e[r],a)?Ou(e,r,l):(++l,Ou(e,l,n)),l<=t&&(r=l+1),t<=l&&(n=l-1)}return e}function Ou(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function Pu(e,t,r){if(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t=+t)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n=+n)>=n&&(yield n)}}(e,r)),(n=e.length)&&!isNaN(t=+t)){if(t<=0||n<2)return gu(e);if(t>=1)return mu(e);var n,i=(n-1)*t,a=Math.floor(i),o=mu(wu(e,a).subarray(0,a+1));return o+(gu(e.subarray(a+1))-o)*(i-a)}}function Eu(e,t,r=wc){if((n=e.length)&&!isNaN(t=+t)){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(+r(e[a+1],a+1,e)-o)*(i-a)}}function Au(){var e,t=[],r=[],n=[];function i(){var e=0,i=Math.max(1,r.length);for(n=new Array(i-1);++e<i;)n[e-1]=Eu(t,e/i);return a}function a(t){return null==t||isNaN(t=+t)?e:r[Ec(n,t)]}return a.invertExtent=function(e){var i=r.indexOf(e);return i<0?[NaN,NaN]:[i>0?n[i-1]:t[0],i<n.length?n[i]:t[t.length-1]]},a.domain=function(e){if(!arguments.length)return t.slice();t=[];for(let r of e)null==r||isNaN(r=+r)||t.push(r);return t.sort(mc),i()},a.range=function(e){return arguments.length?(r=Array.from(e),i()):r.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return n.slice()},a.copy=function(){return Au().domain(t).range(r).unknown(e)},Jl.apply(a,arguments)}function ju(){var e,t=0,r=1,n=1,i=[.5],a=[0,1];function o(t){return null!=t&&t<=t?a[Ec(i,t,0,n)]:e}function l(){var e=-1;for(i=new Array(n);++e<n;)i[e]=((e+1)*r-(e-n)*t)/(n+1);return o}return o.domain=function(e){return arguments.length?([t,r]=e,t=+t,r=+r,l()):[t,r]},o.range=function(e){return arguments.length?(n=(a=Array.from(e)).length-1,l()):a.slice()},o.invertExtent=function(e){var o=a.indexOf(e);return o<0?[NaN,NaN]:o<1?[t,i[0]]:o>=n?[i[n-1],r]:[i[o-1],i[o]]},o.unknown=function(t){return arguments.length?(e=t,o):o},o.thresholds=function(){return i.slice()},o.copy=function(){return ju().domain([t,r]).range(a).unknown(e)},Jl.apply(Hs(o),arguments)}function Su(){var e,t=[.5],r=[0,1],n=1;function i(i){return null!=i&&i<=i?r[Ec(t,i,0,n)]:e}return i.domain=function(e){return arguments.length?(t=Array.from(e),n=Math.min(t.length,r.length-1),i):t.slice()},i.range=function(e){return arguments.length?(r=Array.from(e),n=Math.min(t.length,r.length-1),i):r.slice()},i.invertExtent=function(e){var n=r.indexOf(e);return[t[n-1],t[n]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return Su().domain(t).range(r).unknown(e)},Jl.apply(i,arguments)}Bs=Vs({thousands:",",grouping:[3],currency:["$",""]}),Fs=Bs.format,Ws=Bs.formatPrefix;const ku=1e3,Mu=6e4,Tu=36e5,Cu=864e5,Du=6048e5,Iu=2592e6,Nu=31536e6,_u=new Date,Ru=new Date;function Lu(e,t,r,n){function i(t){return e(t=0===arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{const t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{const o=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n&&a>0))return o;let l;do{o.push(l=new Date(+r)),t(r,a),e(r)}while(l<r&&r<n);return o},i.filter=r=>Lu((t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)}),((e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););})),r&&(i.count=(t,n)=>(_u.setTime(+t),Ru.setTime(+n),e(_u),e(Ru),Math.floor(r(_u,Ru))),i.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null)),i}const Ku=Lu((()=>{}),((e,t)=>{e.setTime(+e+t)}),((e,t)=>t-e));Ku.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?Lu((t=>{t.setTime(Math.floor(t/e)*e)}),((t,r)=>{t.setTime(+t+r*e)}),((t,r)=>(r-t)/e)):Ku:null);Ku.range;const zu=Lu((e=>{e.setTime(e-e.getMilliseconds())}),((e,t)=>{e.setTime(+e+t*ku)}),((e,t)=>(t-e)/ku),(e=>e.getUTCSeconds())),Bu=(zu.range,Lu((e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ku)}),((e,t)=>{e.setTime(+e+t*Mu)}),((e,t)=>(t-e)/Mu),(e=>e.getMinutes()))),Fu=(Bu.range,Lu((e=>{e.setUTCSeconds(0,0)}),((e,t)=>{e.setTime(+e+t*Mu)}),((e,t)=>(t-e)/Mu),(e=>e.getUTCMinutes()))),Wu=(Fu.range,Lu((e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ku-e.getMinutes()*Mu)}),((e,t)=>{e.setTime(+e+t*Tu)}),((e,t)=>(t-e)/Tu),(e=>e.getHours()))),Uu=(Wu.range,Lu((e=>{e.setUTCMinutes(0,0,0)}),((e,t)=>{e.setTime(+e+t*Tu)}),((e,t)=>(t-e)/Tu),(e=>e.getUTCHours()))),Xu=(Uu.range,Lu((e=>e.setHours(0,0,0,0)),((e,t)=>e.setDate(e.getDate()+t)),((e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Mu)/Cu),(e=>e.getDate()-1))),Vu=(Xu.range,Lu((e=>{e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+t)}),((e,t)=>(t-e)/Cu),(e=>e.getUTCDate()-1))),$u=(Vu.range,Lu((e=>{e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+t)}),((e,t)=>(t-e)/Cu),(e=>Math.floor(e/Cu))));$u.range;function Hu(e){return Lu((t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)}),((e,t)=>{e.setDate(e.getDate()+7*t)}),((e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Mu)/Du))}const qu=Hu(0),Yu=Hu(1),Gu=Hu(2),Zu=Hu(3),Ju=Hu(4),Qu=Hu(5),ef=Hu(6);qu.range,Yu.range,Gu.range,Zu.range,Ju.range,Qu.range,ef.range;function tf(e){return Lu((t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)}),((e,t)=>(t-e)/Du))}const rf=tf(0),nf=tf(1),af=tf(2),of=tf(3),lf=tf(4),cf=tf(5),sf=tf(6),uf=(rf.range,nf.range,af.range,of.range,lf.range,cf.range,sf.range,Lu((e=>{e.setDate(1),e.setHours(0,0,0,0)}),((e,t)=>{e.setMonth(e.getMonth()+t)}),((e,t)=>t.getMonth()-e.getMonth()+12*(t.getFullYear()-e.getFullYear())),(e=>e.getMonth()))),ff=(uf.range,Lu((e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)}),((e,t)=>t.getUTCMonth()-e.getUTCMonth()+12*(t.getUTCFullYear()-e.getUTCFullYear())),(e=>e.getUTCMonth()))),df=(ff.range,Lu((e=>{e.setMonth(0,1),e.setHours(0,0,0,0)}),((e,t)=>{e.setFullYear(e.getFullYear()+t)}),((e,t)=>t.getFullYear()-e.getFullYear()),(e=>e.getFullYear())));df.every=e=>isFinite(e=Math.floor(e))&&e>0?Lu((t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)}),((t,r)=>{t.setFullYear(t.getFullYear()+r*e)})):null;df.range;const pf=Lu((e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)}),((e,t)=>t.getUTCFullYear()-e.getUTCFullYear()),(e=>e.getUTCFullYear()));pf.every=e=>isFinite(e=Math.floor(e))&&e>0?Lu((t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),((t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)})):null;pf.range;function hf(e,t,r,n,i,a){const o=[[zu,1,ku],[zu,5,5e3],[zu,15,15e3],[zu,30,3e4],[a,1,Mu],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,Tu],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,Cu],[n,2,1728e5],[r,1,Du],[t,1,Iu],[t,3,7776e6],[e,1,Nu]];function l(t,r,n){const i=Math.abs(r-t)/n,a=bc((([,,e])=>e)).right(o,i);if(a===o.length)return e.every(vc(t/Nu,r/Nu,n));if(0===a)return Ku.every(Math.max(vc(t,r,n),1));const[l,c]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(c)}return[function(e,t,r){const n=t<e;n&&([e,t]=[t,e]);const i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}const[yf,vf]=hf(pf,ff,rf,$u,Uu,Fu),[mf,gf]=hf(df,uf,qu,Xu,Wu,Bu);function bf(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function xf(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function wf(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var Of,Pf,Ef,Af={"-":"",_:" ",0:"0"},jf=/^\s*\d+/,Sf=/^%/,kf=/[\\^$*+?|[\]().{}]/g;function Mf(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function Tf(e){return e.replace(kf,"\\$&")}function Cf(e){return new RegExp("^(?:"+e.map(Tf).join("|")+")","i")}function Df(e){return new Map(e.map(((e,t)=>[e.toLowerCase(),t])))}function If(e,t,r){var n=jf.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function Nf(e,t,r){var n=jf.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function _f(e,t,r){var n=jf.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function Rf(e,t,r){var n=jf.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function Lf(e,t,r){var n=jf.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function Kf(e,t,r){var n=jf.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function zf(e,t,r){var n=jf.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function Bf(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function Ff(e,t,r){var n=jf.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function Wf(e,t,r){var n=jf.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function Uf(e,t,r){var n=jf.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function Xf(e,t,r){var n=jf.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function Vf(e,t,r){var n=jf.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function $f(e,t,r){var n=jf.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function Hf(e,t,r){var n=jf.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function qf(e,t,r){var n=jf.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function Yf(e,t,r){var n=jf.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function Gf(e,t,r){var n=Sf.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function Zf(e,t,r){var n=jf.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function Jf(e,t,r){var n=jf.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function Qf(e,t){return Mf(e.getDate(),t,2)}function ed(e,t){return Mf(e.getHours(),t,2)}function td(e,t){return Mf(e.getHours()%12||12,t,2)}function rd(e,t){return Mf(1+Xu.count(df(e),e),t,3)}function nd(e,t){return Mf(e.getMilliseconds(),t,3)}function id(e,t){return nd(e,t)+"000"}function ad(e,t){return Mf(e.getMonth()+1,t,2)}function od(e,t){return Mf(e.getMinutes(),t,2)}function ld(e,t){return Mf(e.getSeconds(),t,2)}function cd(e){var t=e.getDay();return 0===t?7:t}function sd(e,t){return Mf(qu.count(df(e)-1,e),t,2)}function ud(e){var t=e.getDay();return t>=4||0===t?Ju(e):Ju.ceil(e)}function fd(e,t){return e=ud(e),Mf(Ju.count(df(e),e)+(4===df(e).getDay()),t,2)}function dd(e){return e.getDay()}function pd(e,t){return Mf(Yu.count(df(e)-1,e),t,2)}function hd(e,t){return Mf(e.getFullYear()%100,t,2)}function yd(e,t){return Mf((e=ud(e)).getFullYear()%100,t,2)}function vd(e,t){return Mf(e.getFullYear()%1e4,t,4)}function md(e,t){var r=e.getDay();return Mf((e=r>=4||0===r?Ju(e):Ju.ceil(e)).getFullYear()%1e4,t,4)}function gd(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+Mf(t/60|0,"0",2)+Mf(t%60,"0",2)}function bd(e,t){return Mf(e.getUTCDate(),t,2)}function xd(e,t){return Mf(e.getUTCHours(),t,2)}function wd(e,t){return Mf(e.getUTCHours()%12||12,t,2)}function Od(e,t){return Mf(1+Vu.count(pf(e),e),t,3)}function Pd(e,t){return Mf(e.getUTCMilliseconds(),t,3)}function Ed(e,t){return Pd(e,t)+"000"}function Ad(e,t){return Mf(e.getUTCMonth()+1,t,2)}function jd(e,t){return Mf(e.getUTCMinutes(),t,2)}function Sd(e,t){return Mf(e.getUTCSeconds(),t,2)}function kd(e){var t=e.getUTCDay();return 0===t?7:t}function Md(e,t){return Mf(rf.count(pf(e)-1,e),t,2)}function Td(e){var t=e.getUTCDay();return t>=4||0===t?lf(e):lf.ceil(e)}function Cd(e,t){return e=Td(e),Mf(lf.count(pf(e),e)+(4===pf(e).getUTCDay()),t,2)}function Dd(e){return e.getUTCDay()}function Id(e,t){return Mf(nf.count(pf(e)-1,e),t,2)}function Nd(e,t){return Mf(e.getUTCFullYear()%100,t,2)}function _d(e,t){return Mf((e=Td(e)).getUTCFullYear()%100,t,2)}function Rd(e,t){return Mf(e.getUTCFullYear()%1e4,t,4)}function Ld(e,t){var r=e.getUTCDay();return Mf((e=r>=4||0===r?lf(e):lf.ceil(e)).getUTCFullYear()%1e4,t,4)}function Kd(){return"+0000"}function zd(){return"%"}function Bd(e){return+e}function Fd(e){return Math.floor(+e/1e3)}function Wd(e){return new Date(e)}function Ud(e){return e instanceof Date?+e:+new Date(+e)}function Xd(e,t,r,n,i,a,o,l,c,s){var u=Ts(),f=u.invert,d=u.domain,p=s(".%L"),h=s(":%S"),y=s("%I:%M"),v=s("%I %p"),m=s("%a %d"),g=s("%b %d"),b=s("%B"),x=s("%Y");function w(e){return(c(e)<e?p:l(e)<e?h:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?m:g:r(e)<e?b:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?d(Array.from(e,Ud)):d().map(Wd)},u.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:s(t)},u.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(Gs(r,e)):u},u.copy=function(){return ks(u,Xd(e,t,r,n,i,a,o,l,c,s))},u}function Vd(){return Jl.apply(Xd(mf,gf,df,uf,qu,Xu,Wu,Bu,zu,Pf).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function $d(){return Jl.apply(Xd(yf,vf,pf,ff,rf,Vu,Uu,Fu,zu,Ef).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Hd(){var e,t,r,n,i,a=0,o=1,l=Es,c=!1;function s(t){return null==t||isNaN(t=+t)?i:l(0===r?.5:(t=(n(t)-e)*r,c?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),s):[l(0),l(1)]}}return s.domain=function(i){return arguments.length?([a,o]=i,e=n(a=+a),t=n(o=+o),r=e===t?0:1/(t-e),s):[a,o]},s.clamp=function(e){return arguments.length?(c=!!e,s):c},s.interpolator=function(e){return arguments.length?(l=e,s):l},s.range=u(xs),s.rangeRound=u(ws),s.unknown=function(e){return arguments.length?(i=e,s):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),s}}function qd(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function Yd(){var e=Hs(Hd()(Es));return e.copy=function(){return qd(e,Yd())},Ql.apply(e,arguments)}function Gd(){var e=nu(Hd()).domain([1,10]);return e.copy=function(){return qd(e,Gd()).base(e.base())},Ql.apply(e,arguments)}function Zd(){var e=lu(Hd());return e.copy=function(){return qd(e,Zd()).constant(e.constant())},Ql.apply(e,arguments)}function Jd(){var e=du(Hd());return e.copy=function(){return qd(e,Jd()).exponent(e.exponent())},Ql.apply(e,arguments)}function Qd(){return Jd.apply(null,arguments).exponent(.5)}function ep(){var e=[],t=Es;function r(r){if(null!=r&&!isNaN(r=+r))return t((Ec(e,r,1)-1)/(e.length-1))}return r.domain=function(t){if(!arguments.length)return e.slice();e=[];for(let r of t)null==r||isNaN(r=+r)||e.push(r);return e.sort(mc),r},r.interpolator=function(e){return arguments.length?(t=e,r):t},r.range=function(){return e.map(((r,n)=>t(n/(e.length-1))))},r.quantiles=function(t){return Array.from({length:t+1},((r,n)=>Pu(e,n/t)))},r.copy=function(){return ep(t).domain(e)},Ql.apply(r,arguments)}function tp(){var e,t,r,n,i,a,o,l=0,c=.5,s=1,u=1,f=Es,d=!1;function p(e){return isNaN(e=+e)?o:(e=.5+((e=+a(e))-t)*(u*e<u*t?n:i),f(d?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=xs);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),p):[f(0),f(.5),f(1)]}}return p.domain=function(o){return arguments.length?([l,c,s]=o,e=a(l=+l),t=a(c=+c),r=a(s=+s),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),u=t<e?-1:1,p):[l,c,s]},p.clamp=function(e){return arguments.length?(d=!!e,p):d},p.interpolator=function(e){return arguments.length?(f=e,p):f},p.range=h(xs),p.rangeRound=h(ws),p.unknown=function(e){return arguments.length?(o=e,p):o},function(o){return a=o,e=o(l),t=o(c),r=o(s),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),u=t<e?-1:1,p}}function rp(){var e=Hs(tp()(Es));return e.copy=function(){return qd(e,rp())},Ql.apply(e,arguments)}function np(){var e=nu(tp()).domain([.1,1,10]);return e.copy=function(){return qd(e,np()).base(e.base())},Ql.apply(e,arguments)}function ip(){var e=lu(tp());return e.copy=function(){return qd(e,ip()).constant(e.constant())},Ql.apply(e,arguments)}function ap(){var e=du(tp());return e.copy=function(){return qd(e,ap()).exponent(e.exponent())},Ql.apply(e,arguments)}function op(){return ap.apply(null,arguments).exponent(.5)}!function(e){Of=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,c=e.shortMonths,s=Cf(i),u=Df(i),f=Cf(a),d=Df(a),p=Cf(o),h=Df(o),y=Cf(l),v=Df(l),m=Cf(c),g=Df(c),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:Qf,e:Qf,f:id,g:yd,G:md,H:ed,I:td,j:rd,L:nd,m:ad,M:od,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:Bd,s:Fd,S:ld,u:cd,U:sd,V:fd,w:dd,W:pd,x:null,X:null,y:hd,Y:vd,Z:gd,"%":zd},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:bd,e:bd,f:Ed,g:_d,G:Ld,H:xd,I:wd,j:Od,L:Pd,m:Ad,M:jd,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:Bd,s:Fd,S:Sd,u:kd,U:Md,V:Cd,w:Dd,W:Id,x:null,X:null,y:Nd,Y:Rd,Z:Kd,"%":zd},w={a:function(e,t,r){var n=p.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=m.exec(t.slice(r));return n?(e.m=g.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return E(e,t,r,n)},d:Uf,e:Uf,f:Yf,g:zf,G:Kf,H:Vf,I:Vf,j:Xf,L:qf,m:Wf,M:$f,p:function(e,t,r){var n=s.exec(t.slice(r));return n?(e.p=u.get(n[0].toLowerCase()),r+n[0].length):-1},q:Ff,Q:Zf,s:Jf,S:Hf,u:Nf,U:_f,V:Rf,w:If,W:Lf,x:function(e,t,n){return E(e,r,t,n)},X:function(e,t,r){return E(e,n,t,r)},y:zf,Y:Kf,Z:Bf,"%":Gf};function O(e,t){return function(r){var n,i,a,o=[],l=-1,c=0,s=e.length;for(r instanceof Date||(r=new Date(+r));++l<s;)37===e.charCodeAt(l)&&(o.push(e.slice(c,l)),null!=(i=Af[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),c=l+1);return o.push(e.slice(c,l)),o.join("")}}function P(e,t){return function(r){var n,i,a=wf(1900,void 0,1);if(E(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(t&&!("Z"in a)&&(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(i=(n=xf(wf(a.y,0,1))).getUTCDay(),n=i>4||0===i?nf.ceil(n):nf(n),n=Vu.offset(n,7*(a.V-1)),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(i=(n=bf(wf(a.y,0,1))).getDay(),n=i>4||0===i?Yu.ceil(n):Yu(n),n=Xu.offset(n,7*(a.V-1)),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:"W"in a?1:0),i="Z"in a?xf(wf(a.y,0,1)).getUTCDay():bf(wf(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,xf(a)):bf(a)}}function E(e,t,r,n){for(var i,a,o=0,l=t.length,c=r.length;o<l;){if(n>=c)return-1;if(37===(i=t.charCodeAt(o++))){if(i=t.charAt(o++),!(a=w[i in Af?t.charAt(o++):i])||(n=a(e,r,n))<0)return-1}else if(i!=r.charCodeAt(n++))return-1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=P(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=P(e+="",!0);return t.toString=function(){return e},t}}}(e),Pf=Of.format,Of.parse,Ef=Of.utcFormat,Of.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});var lp=e=>e.chartData,cp=Ge([lp],(e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}})),sp=(e,t,r,n)=>n?cp(e):lp(e);function up(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if(Wo(t)&&Wo(r))return!0}return!1}function fp(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var dp=a(8351),pp=a.n(dp),hp=e=>e,yp={"@@functional/placeholder":!0},vp=e=>e===yp,mp=e=>function t(){return 0===arguments.length||1===arguments.length&&vp(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},gp=(e,t)=>1===e?t:mp((function(){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter((e=>e!==yp)).length;return a>=e?t(...n):gp(e-a,mp((function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];var a=n.map((e=>vp(e)?r.shift():e));return t(...a,...r)})))})),bp=e=>gp(e.length,e),xp=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},wp=bp(((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map((e=>t[e])).map(e))),Op=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),Pp=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every(((e,r)=>{var n;return e===(null===(n=t)||void 0===n?void 0:n[r])}))?r:(t=i,r=e(...i))}};function Ep(e){return 0===e?1:Math.floor(new(pp())(e).abs().log(10).toNumber())+1}function Ap(e,t,r){for(var n=new(pp())(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}bp(((e,t,r)=>{var n=+e;return n+r*(+t-n)})),bp(((e,t,r)=>{var n=t-+e;return(r-e)/(n=n||1/0)})),bp(((e,t,r)=>{var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))}));var jp=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},Sp=(e,t,r)=>{if(e.lte(0))return new(pp())(0);var n=Ep(e.toNumber()),i=new(pp())(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new(pp())(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return t?new(pp())(l.toNumber()):new(pp())(Math.ceil(l.toNumber()))},kp=(e,t,r)=>{var n=new(pp())(1),i=new(pp())(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new(pp())(10).pow(Ep(e)-1),i=new(pp())(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new(pp())(Math.floor(e)))}else 0===e?i=new(pp())(Math.floor((t-1)/2)):r||(i=new(pp())(Math.floor(e)));var o=Math.floor((t-1)/2),l=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return hp;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce(((e,t)=>t(e)),i(...arguments))}}(wp((e=>i.add(new(pp())(e-o).mul(n)).toNumber())),xp);return l(0,t)},Mp=function(e,t,r,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(pp())(0),tickMin:new(pp())(0),tickMax:new(pp())(0)};var a,o=Sp(new(pp())(t).sub(e).div(r-1),n,i);a=e<=0&&t>=0?new(pp())(0):(a=new(pp())(e).add(t).div(2)).sub(new(pp())(a).mod(o));var l=Math.ceil(a.sub(e).div(o).toNumber()),c=Math.ceil(new(pp())(t).sub(a).div(o).toNumber()),s=l+c+1;return s>r?Mp(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,l=t>0?l:l+(r-s)),{step:o,tickMin:a.sub(new(pp())(l).mul(o)),tickMax:a.add(new(pp())(c).mul(o))})};var Tp=Pp((function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(n,2),[o,l]=jp([t,r]);if(o===-1/0||l===1/0){var c=l===1/0?[o,...xp(0,n-1).map((()=>1/0))]:[...xp(0,n-1).map((()=>-1/0)),l];return t>r?Op(c):c}if(o===l)return kp(o,n,i);var{step:s,tickMin:u,tickMax:f}=Mp(o,l,a,i,0),d=Ap(u,f.add(new(pp())(.1).mul(s)),s);return t>r?Op(d):d})),Cp=Pp((function(e,t){var[r,n]=e,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],[a,o]=jp([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),c=Sp(new(pp())(o).sub(a).div(l-1),i,0),s=[...Ap(new(pp())(a),new(pp())(o).sub(new(pp())(.99).mul(c)),c),o];return r>n?Op(s):s})),Dp=e=>e.rootProps.maxBarSize,Ip=e=>e.rootProps.barGap,Np=e=>e.rootProps.barCategoryGap,_p=e=>e.rootProps.barSize,Rp=e=>e.rootProps.stackOffset,Lp=e=>e.options.chartName,Kp=e=>e.rootProps.syncId,zp=e=>e.rootProps.syncMethod,Bp=e=>e.options.eventEmitter,Fp={allowDuplicatedCategory:!0,angleAxisId:0,axisLine:!0,cx:0,cy:0,orientation:"outer",reversed:!1,scale:"auto",tick:!0,tickLine:!0,tickSize:8,type:"category"},Wp={allowDataOverflow:!1,allowDuplicatedCategory:!0,angle:0,axisLine:!0,cx:0,cy:0,orientation:"right",radiusAxisId:0,scale:"auto",stroke:"#ccc",tick:!0,tickCount:5,type:"number"},Up=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t},Xp={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:Fp.angleAxisId,includeHidden:!1,name:void 0,reversed:Fp.reversed,scale:Fp.scale,tick:Fp.tick,tickCount:void 0,ticks:void 0,type:Fp.type,unit:void 0},Vp={allowDataOverflow:Wp.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:Wp.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:Wp.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:Wp.scale,tick:Wp.tick,tickCount:Wp.tickCount,ticks:void 0,type:Wp.type,unit:void 0},$p={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:Fp.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:Fp.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:Fp.scale,tick:Fp.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},Hp={allowDataOverflow:Wp.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:Wp.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:Wp.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:Wp.scale,tick:Wp.tick,tickCount:Wp.tickCount,ticks:void 0,type:"category",unit:void 0},qp=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?$p:Xp,Yp=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?Hp:Vp,Gp=e=>e.polarOptions,Zp=Ge([wi,Oi,Ii],Yn),Jp=Ge([Gp,Zp],((e,t)=>{if(null!=e)return v(e.innerRadius,t,0)})),Qp=Ge([Gp,Zp],((e,t)=>{if(null!=e)return v(e.outerRadius,t,.8*t)})),eh=Ge([Gp],(e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]})),th=Ge([qp,eh],Up),rh=Ge([Zp,Jp,Qp],((e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]})),nh=Ge([Yp,rh],Up),ih=Ge([Hi,Gp,Jp,Qp,wi,Oi],((e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:l,startAngle:c,endAngle:s}=t;return{cx:v(o,i,i/2),cy:v(l,a,a/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:s,clockWise:!1}}})),ah=(e,t)=>t,oh=(e,t,r)=>r;function lh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ch(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?lh(Object(r),!0).forEach((function(t){sh(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lh(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function sh(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var uh=[0,"auto"],fh={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},dh=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?fh:r},ph={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:uh,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},hh=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?ph:r},yh={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},vh=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?yh:r},mh=(e,t,r)=>{switch(t){case"xAxis":return dh(e,r);case"yAxis":return hh(e,r);case"zAxis":return vh(e,r);case"angleAxis":return qp(e,r);case"radiusAxis":return Yp(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},gh=(e,t,r)=>{switch(t){case"xAxis":return dh(e,r);case"yAxis":return hh(e,r);case"angleAxis":return qp(e,r);case"radiusAxis":return Yp(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},bh=e=>e.graphicalItems.countOfBars>0;function xh(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var wh=e=>e.graphicalItems.cartesianItems,Oh=Ge([ah,oh],xh),Ph=(e,t,r)=>e.filter(r).filter((e=>!0===(null==t?void 0:t.includeHidden)||!e.hide)),Eh=Ge([wh,mh,Oh],Ph),Ah=e=>e.filter((e=>void 0===e.stackId)),jh=Ge([Eh],Ah),Sh=e=>e.map((e=>e.data)).filter(Boolean).flat(1),kh=Ge([Eh],Sh),Mh=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},Th=Ge([kh,sp],Mh),Ch=(e,t,r)=>null!=(null==t?void 0:t.dataKey)?e.map((e=>({value:ni(e,t.dataKey)}))):r.length>0?r.map((e=>e.dataKey)).flatMap((t=>e.map((e=>({value:ni(e,t)}))))):e.map((e=>({value:e}))),Dh=Ge([Th,mh,Eh],Ch);function Ih(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function Nh(e){return e.filter((e=>p(e)||e instanceof Date)).map(Number).filter((e=>!1===u(e)))}function _h(e,t,r){return!r||"number"!=typeof t||u(t)?[]:r.length?Nh(r.flatMap((r=>{var n,i,a=ni(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,Wo(n)&&Wo(i))return[t-n,t+i]}))):[]}var Rh=(e,t,r)=>{var n=t.reduce(((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e)),{});return Object.fromEntries(Object.entries(n).map((t=>{var[n,i]=t,a=i.map((e=>e.dataKey));return[n,{stackedData:ui(e,a,r),graphicalItems:i}]})))},Lh=Ge([Th,Eh,Rp],Rh),Kh=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=yi(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},zh=Ge([Lh,lp,ah],Kh),Bh=(e,t,r,n)=>r.length>0?e.flatMap((e=>r.flatMap((r=>{var i,a,o=null===(i=r.errorBars)||void 0===i?void 0:i.filter((e=>Ih(n,e))),l=ni(e,null!==(a=t.dataKey)&&void 0!==a?a:r.dataKey);return{value:l,errorDomain:_h(e,l,o)}})))).filter(Boolean):null!=(null==t?void 0:t.dataKey)?e.map((e=>({value:ni(e,t.dataKey),errorDomain:[]}))):e.map((e=>({value:e,errorDomain:[]}))),Fh=Ge(Th,mh,jh,ah,Bh);function Wh(e){var{value:t}=e;if(p(t)||t instanceof Date)return t}var Uh=e=>{var t;if(null==e||!("domain"in e))return uh;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=Nh(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!==(t=null==e?void 0:e.domain)&&void 0!==t?t:uh},Xh=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},Vh=e=>e.referenceElements.dots,$h=(e,t,r)=>e.filter((e=>"extendDomain"===e.ifOverflow)).filter((e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r)),Hh=Ge([Vh,ah,oh],$h),qh=e=>e.referenceElements.areas,Yh=Ge([qh,ah,oh],$h),Gh=e=>e.referenceElements.lines,Zh=Ge([Gh,ah,oh],$h),Jh=(e,t)=>{var r=Nh(e.map((e=>"xAxis"===t?e.x:e.y)));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},Qh=Ge(Hh,ah,Jh),ey=(e,t)=>{var r=Nh(e.flatMap((e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2])));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ty=Ge([Yh,ah],ey),ry=(e,t)=>{var r=Nh(e.map((e=>"xAxis"===t?e.x:e.y)));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ny=Ge(Zh,ah,ry),iy=Ge(Qh,ny,ty,((e,t,r)=>Xh(e,r,t))),ay=Ge([mh],Uh),oy=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if(Wo(i))r=i;else if("function"==typeof i)return;if(Wo(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(up(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(up(n))return fp(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if(d(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&vi.test(o)){var c=vi.exec(o);if(null==c||null==t)i=void 0;else{var s=+c[1];i=t[0]-s}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if(d(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&mi.test(l)){var u=mi.exec(l);if(null==u||null==t)a=void 0;else{var f=+u[1];a=t[1]+f}}else a=null==t?void 0:t[1];var p=[i,a];if(up(p))return null==t?p:fp(p,t,r)}}}(t,Xh(r,i,(e=>{var t=e.flatMap((e=>[e.value,e.errorDomain])).flat(1),r=Nh(t);if(0!==r.length)return[Math.min(...r),Math.max(...r)]})(n)),e.allowDataOverflow)},ly=Ge([mh,ay,zh,Fh,iy],oy),cy=[0,1],sy=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:c}=e,s=ii(t,a);return s&&null==l?Zl()(0,r.length):"category"===c?((e,t,r)=>{var n=e.map(Wh).filter((e=>null!=e));return r&&(null==t.dataKey||t.allowDuplicatedCategory&&m(n))?Zl()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))})(n,e,s):"expand"===i?cy:o}},uy=Ge([mh,Hi,Th,Dh,Rp,ah,ly],sy),fy=(t,r,n,i,a)=>{if(null!=t){var{scale:o,type:l}=t;if("auto"===o)return"radial"===r&&"radiusAxis"===a?"band":"radial"===r&&"angleAxis"===a?"linear":"category"===l&&i&&(i.indexOf("LineChart")>=0||i.indexOf("AreaChart")>=0||i.indexOf("ComposedChart")>=0&&!n)?"point":"category"===l?"band":"linear";if("string"==typeof o){var c="scale".concat(O(o));return c in e?c:"point"}}},dy=Ge([mh,Hi,bh,Lp,ah],fy);function py(t,r,n,i){if(null!=n&&null!=i){if("function"==typeof t.scale)return t.scale.copy().domain(n).range(i);var a=function(t){if(null!=t){if(t in e)return e[t]();var r="scale".concat(O(t));return r in e?e[r]():void 0}}(r);if(null!=a){var o=a.domain(n).range(i);return(e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-li,a=Math.max(n[0],n[1])+li,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}})(o),o}}}var hy=(e,t,r)=>{var n=Uh(t);if("auto"===r||"linear"===r)return null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&up(e)?Tp(e,t.tickCount,t.allowDecimals):null!=t&&t.tickCount&&"number"===t.type&&up(e)?Cp(e,t.tickCount,t.allowDecimals):void 0},yy=Ge([uy,gh,dy],hy),vy=(e,t,r,n)=>{if("angleAxis"!==n&&"number"===(null==e?void 0:e.type)&&up(t)&&Array.isArray(r)&&r.length>0){var i=t[0],a=r[0],o=t[1],l=r[r.length-1];return[Math.min(i,a),Math.max(o,l)]}return t},my=Ge([mh,uy,yy,ah],vy),gy=Ge(Dh,mh,((e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(Nh(e.map((e=>e.value)))).sort(((e,t)=>e-t));if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++){var o=n[a+1]-n[a];r=Math.min(r,o)}return r/i}})),by=Ge(gy,Hi,Np,Ii,((e,t,r,n)=>n),((e,t,r,n,i)=>{if(!Wo(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=v(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0})),xy=Ge(dh,((e,t)=>{var r=dh(e,t);return null==r||"string"!=typeof r.padding?0:by(e,"xAxis",t,r.padding)}),((e,t)=>{var r,n;if(null==e)return{left:0,right:0};var{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!==(r=i.left)&&void 0!==r?r:0)+t,right:(null!==(n=i.right)&&void 0!==n?n:0)+t}})),wy=Ge(hh,((e,t)=>{var r=hh(e,t);return null==r||"string"!=typeof r.padding?0:by(e,"yAxis",t,r.padding)}),((e,t)=>{var r,n;if(null==e)return{top:0,bottom:0};var{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!==(r=i.top)&&void 0!==r?r:0)+t,bottom:(null!==(n=i.bottom)&&void 0!==n?n:0)+t}})),Oy=Ge([Ii,xy,Bi,zi,(e,t,r)=>r],((e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]})),Py=Ge([Ii,Hi,wy,Bi,zi,(e,t,r)=>r],((e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]})),Ey=(e,t,r,n)=>{var i;switch(t){case"xAxis":return Oy(e,r,n);case"yAxis":return Py(e,r,n);case"zAxis":return null===(i=vh(e,r))||void 0===i?void 0:i.range;case"angleAxis":return eh(e);case"radiusAxis":return rh(e,r);default:return}},Ay=Ge([mh,Ey],Up),jy=Ge([mh,dy,my,Ay],py);Ge(Eh,ah,((e,t)=>e.flatMap((e=>{var t;return null!==(t=e.errorBars)&&void 0!==t?t:[]})).filter((e=>Ih(t,e)))));function Sy(e,t){return e.id<t.id?-1:e.id>t.id?1:0}var ky=(e,t)=>t,My=(e,t,r)=>r,Ty=Ge(Ai,ky,My,((e,t,r)=>e.filter((e=>e.orientation===t)).filter((e=>e.mirror===r)).sort(Sy))),Cy=Ge(ji,ky,My,((e,t,r)=>e.filter((e=>e.orientation===t)).filter((e=>e.mirror===r)).sort(Sy))),Dy=(e,t)=>({width:e.width,height:t.height}),Iy=Ge(Ii,dh,Dy),Ny=Ge(Oi,Ii,Ty,ky,My,((e,t,r,n,i)=>{var a,o={};return r.forEach((r=>{var l=Dy(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}})(t,n,e));var c="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(c)*l.height,a+=(c?-1:1)*l.height})),o})),_y=Ge(wi,Ii,Cy,ky,My,((e,t,r,n,i)=>{var a,o={};return r.forEach((r=>{var l=((e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height}))(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}})(t,n,e));var c="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(c)*l.width,a+=(c?-1:1)*l.width})),o})),Ry=Ge(Ii,hh,((e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height}))),Ly=(e,t,r)=>{switch(t){case"xAxis":return Iy(e,r).width;case"yAxis":return Ry(e,r).height;default:return}},Ky=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=ii(e,n),c=t.map((e=>e.value));return o&&l&&"category"===a&&i&&m(c)?c:void 0}},zy=Ge([Hi,Dh,mh,ah],Ky),By=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;return!ii(e,n)||"number"!==i&&"auto"===a?void 0:t.map((e=>e.value))}},Fy=Ge([Hi,Dh,gh,ah],By),Wy=Ge([Hi,(e,t,r)=>{switch(t){case"xAxis":return dh(e,r);case"yAxis":return hh(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},dy,jy,zy,Fy,Ey,yy,ah],((e,t,r,n,i,a,o,l,c)=>{if(null==t)return null;var s=ii(e,c);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:c,categoricalDomain:a,duplicateDomain:i,isCategorical:s,niceTicks:l,range:o,realScaleType:r,scale:n}})),Uy=(e,t,r,n,i,a,o,l,c)=>{if(null!=t&&null!=n){var f=ii(e,c),{type:d,ticks:p,tickCount:h}=t,y="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,v="category"===d&&n.bandwidth?n.bandwidth()/y:0;v="angleAxis"===c&&null!=a&&a.length>=2?2*s(a[0]-a[1])*v:v;var m=p||i;return m?m.map(((e,t)=>{var r=o?o.indexOf(e):e;return{index:t,coordinate:n(r)+v,value:e,offset:v}})).filter((e=>!u(e.coordinate))):f&&l?l.map(((e,t)=>({coordinate:n(e)+v,value:e,index:t,offset:v}))):n.ticks?n.ticks(h).map((e=>({coordinate:n(e)+v,value:e,offset:v}))):n.domain().map(((e,t)=>({coordinate:n(e)+v,value:o?o[e]:e,index:t,offset:v})))}},Xy=Ge([Hi,gh,dy,jy,yy,Ey,zy,Fy,ah],Uy),Vy=(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=ii(e,o),{tickCount:c}=t,u=0;return u="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*s(n[0]-n[1])*u:u,l&&a?a.map(((e,t)=>({coordinate:r(e)+u,value:e,index:t,offset:u}))):r.ticks?r.ticks(c).map((e=>({coordinate:r(e)+u,value:e,offset:u}))):r.domain().map(((e,t)=>({coordinate:r(e)+u,value:i?i[e]:e,index:t,offset:u})))}},$y=Ge([Hi,gh,jy,Ey,zy,Fy,ah],Vy),Hy=Ge(mh,jy,((e,t)=>{if(null!=e&&null!=t)return ch(ch({},e),{},{scale:t})})),qy=Ge([mh,dy,uy,Ay],py),Yy=Ge(((e,t,r)=>vh(e,r)),qy,((e,t)=>{if(null!=e&&null!=t)return ch(ch({},e),{},{scale:t})})),Gy=Ge([Hi,Ai,ji],((e,t,r)=>{switch(e){case"horizontal":return t.some((e=>e.reversed))?"right-to-left":"left-to-right";case"vertical":return r.some((e=>e.reversed))?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})),Zy=e=>e.options.defaultTooltipEventType,Jy=e=>e.options.validateTooltipEventTypes;function Qy(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function ev(e,t){return Qy(t,Zy(e),Jy(e))}var tv=(e,t)=>{var r,n=Number(t);if(!u(n)&&null!=t)return n>=0?null==e||null===(r=e[n])||void 0===r?void 0:r.value:void 0},rv={active:!1,index:null,dataKey:void 0,coordinate:void 0},nv=$r({name:"tooltip",initialState:{itemInteraction:{click:rv,hover:rv},axisInteraction:{click:rv,hover:rv},keyboardInteraction:rv,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push(t.payload)},removeTooltipEntrySettings(e,t){var r=Lt(e).tooltipItemPayloads.indexOf(t.payload);r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:iv,removeTooltipEntrySettings:av,setTooltipSettingsState:ov,setActiveMouseOverItemIndex:lv,mouseLeaveItem:cv,mouseLeaveChart:sv,setActiveClickItemIndex:uv,setMouseOverAxisIndex:fv,setMouseClickAxisIndex:dv,setSyncInteraction:pv,setKeyboardInteraction:hv}=nv.actions,yv=nv.reducer;function vv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function mv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vv(Object(r),!0).forEach((function(t){gv(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vv(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function gv(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var bv=(e,t,r,n)=>{if(null==t)return rv;var i=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==i)return rv;if(i.active)return i;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var a=!0===e.settings.active;if(null!=i.index){if(a)return mv(mv({},i),{},{active:!0})}else if(null!=n)return{active:!0,coordinate:void 0,dataKey:void 0,index:n};return mv(mv({},rv),{},{coordinate:i.coordinate})},xv=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var n=Number(r);if(!Wo(n))return r;var i=1/0;return t.length>0&&(i=t.length-1),String(Math.max(0,Math.min(n,i)))},wv=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var c=o[0],s=null==c?void 0:l(c.positions,a);if(null!=s)return s;var u=null==i?void 0:i[Number(a)];if(u)return"horizontal"===r?{x:u.coordinate,y:(n.top+t)/2}:{x:(n.left+e)/2,y:u.coordinate}}},Ov=(e,t,r,n)=>{return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter((e=>{var t;return(null===(t=e.settings)||void 0===t?void 0:t.dataKey)===i}));var i},Pv=e=>e.options.tooltipPayloadSearcher,Ev=e=>e.tooltip,Av=e=>{var t=Hi(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},jv=e=>e.tooltip.settings.axisId,Sv=e=>{var t=Av(e),r=jv(e);return gh(e,t,r)},kv=Ge([Sv,Hi,bh,Lp,Av],fy),Mv=Ge([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],((e,t)=>[...e,...t])),Tv=Ge([Av,jv],xh),Cv=Ge([Mv,Sv,Tv],Ph),Dv=Ge([Cv],Sh),Iv=Ge([Dv,lp],Mh),Nv=Ge([Iv,Sv,Cv],Ch),_v=Ge([Sv],Uh),Rv=Ge([Iv,Cv,Rp],Rh),Lv=Ge([Rv,lp,Av],Kh),Kv=Ge([Cv],Ah),zv=Ge([Iv,Sv,Kv,Av],Bh),Bv=Ge([Vh,Av,jv],$h),Fv=Ge([Bv,Av],Jh),Wv=Ge([qh,Av,jv],$h),Uv=Ge([Wv,Av],ey),Xv=Ge([Gh,Av,jv],$h),Vv=Ge([Xv,Av],ry),$v=Ge([Fv,Vv,Uv],Xh),Hv=Ge([Sv,_v,Lv,zv,$v],oy),qv=Ge([Sv,Hi,Iv,Nv,Rp,Av,Hv],sy),Yv=Ge([qv,Sv,kv],hy),Gv=Ge([Sv,qv,Yv,Av],vy),Zv=e=>{var t=Av(e),r=jv(e);return Ey(e,t,r,!1)},Jv=Ge([Sv,Zv],Up),Qv=Ge([Sv,kv,Gv,Jv],py),em=Ge([Hi,Nv,Sv,Av],Ky),tm=Ge([Hi,Nv,Sv,Av],By),rm=Ge([Hi,Sv,kv,Qv,Zv,em,tm,Av],((e,t,r,n,i,a,o,l)=>{if(t){var{type:c}=t,u=ii(e,l);if(n){var f="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,d="category"===c&&n.bandwidth?n.bandwidth()/f:0;return d="angleAxis"===l&&null!=i&&(null==i?void 0:i.length)>=2?2*s(i[0]-i[1])*d:d,u&&o?o.map(((e,t)=>({coordinate:n(e)+d,value:e,index:t,offset:d}))):n.domain().map(((e,t)=>({coordinate:n(e)+d,value:a?a[e]:e,index:t,offset:d})))}}})),nm=Ge([Zy,Jy,e=>e.tooltip.settings],((e,t,r)=>Qy(r.shared,e,t))),im=e=>e.tooltip.settings.trigger,am=e=>e.tooltip.settings.defaultIndex,om=Ge([Ev,nm,im,am],bv),lm=Ge([om,Iv],xv),cm=Ge([rm,lm],tv),sm=Ge([om],(e=>{if(e)return e.dataKey})),um=Ge([Ev,nm,im,am],Ov),fm=Ge([wi,Oi,Hi,Ii,rm,am,um,Pv],wv),dm=Ge([om,fm],((e,t)=>null!=e&&e.coordinate?e.coordinate:t)),pm=Ge([om],(e=>e.active));function hm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ym(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hm(Object(r),!0).forEach((function(t){vm(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hm(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function vm(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var mm=()=>Ue(Sv),gm=()=>{var e=mm(),t=Ue(rm),r=Ue(Qv);return gi(ym(ym({},e),{},{scale:r}),t)};function bm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function xm(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?bm(Object(r),!0).forEach((function(t){wm(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bm(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function wm(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Om=()=>Ue(Lp),Pm=(e,t)=>t,Em=(e,t,r)=>r,Am=(e,t,r,n)=>n;var jm=Ge(rm,(e=>Qe()(e,(e=>e.coordinate)))),Sm=Ge([Ev,Pm,Em,Am],bv),km=Ge([Sm,Iv],xv),Mm=(e,t,r)=>{if(null!=t){var n=Ev(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},Tm=Ge([Ev,Pm,Em,Am],Ov),Cm=Ge([wi,Oi,Hi,Ii,rm,Am,Tm,Pv],wv),Dm=Ge([Sm,Cm],((e,t)=>{var r;return null!==(r=e.coordinate)&&void 0!==r?r:t})),Im=Ge(rm,km,tv);var Nm=Ge([Tm,km,lp,Sv,Im,Pv,Pm],((e,t,r,n,i,a,o)=>{if(null!=t&&null!=a){var{chartData:l,computedData:c,dataStartIndex:s,dataEndIndex:u}=r;return e.reduce(((e,r)=>{var f,d,p,h,y,v,{dataDefinedOnItem:m,settings:g}=r,b=function(e,t){return null!=e?e:t}(m,l),w=(d=b,p=s,h=u,Array.isArray(d)&&d&&p+h!==0?d.slice(p,h+1):d),O=null!==(f=null==g?void 0:g.dataKey)&&void 0!==f?f:null==n?void 0:n.dataKey,P=null==g?void 0:g.nameKey;(y=null==n||!n.dataKey||null!=n&&n.allowDuplicatedCategory||!Array.isArray(w)||"axis"!==o?a(w,t,c,P):x(w,n.dataKey,i),Array.isArray(y))?y.forEach((t=>{var r=xm(xm({},g),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push(bi({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:ni(t.payload,t.dataKey),name:t.name}))})):e.push(bi({tooltipEntrySettings:g,dataKey:O,payload:y,value:ni(y,O),name:null!==(v=ni(y,P))&&void 0!==v?v:null==g?void 0:g.name}));return e}),[])}})),_m=Ge([Sm],(e=>({isActive:e.active,activeIndex:e.index})));function Rm(){return Rm=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Rm.apply(null,arguments)}function Lm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Km(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Lm(Object(r),!0).forEach((function(t){zm(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lm(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function zm(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Bm(e){var r,i,{coordinate:a,payload:o,index:l,offset:c,tooltipAxisBandSize:s,layout:u,cursor:f,tooltipEventType:d,chartName:p}=e,h=a,y=o,v=l;if(!f||!h||"ScatterChart"!==p&&"axis"!==d)return null;if("ScatterChart"===p)r=h,i=al;else if("BarChart"===p)r=function(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===e?t.x-i:r.left+.5,y:"horizontal"===e?r.top+.5:t.y-i,width:"horizontal"===e?n:r.width-1,height:"horizontal"===e?r.height-1:n}}(u,h,c,s),i=Wl;else if("radial"===u){var{cx:m,cy:g,radius:b,startAngle:x,endAngle:w}=Ul(h);r={cx:m,cy:g,startAngle:x,endAngle:w,innerRadius:b,outerRadius:b},i=ql}else r={points:Yl(u,h,c)},i=Qo;var O="object"==typeof f&&"className"in f?f.className:void 0,P=Km(Km(Km(Km({stroke:"#ccc",pointerEvents:"none"},c),r),_(f,!1)),{},{payload:y,payloadIndex:v,className:n("recharts-tooltip-cursor",O)});return(0,t.isValidElement)(f)?(0,t.cloneElement)(f,P):(0,t.createElement)(i,P)}function Fm(e){var r=gm(),n=Ui(),i=qi(),a=Om();return t.createElement(Bm,Rm({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:n,layout:i,tooltipAxisBandSize:r,chartName:a}))}var Wm=(0,t.createContext)(null),Um=()=>(0,t.useContext)(Wm);var Xm=new(a(228)),Vm="recharts.syncEvent.tooltip",$m="recharts.syncEvent.brush";function Hm(e,t){if(t){var r=Number.parseInt(t,10);if(!u(r))return null==e?void 0:e[r]}}var qm=$r({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),Ym=qm.reducer,{createEventEmitter:Gm}=qm.actions;function Zm(e){return e.tooltip.syncInteraction}var Jm=$r({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload)return e.dataStartIndex=0,void(e.dataEndIndex=0);t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:Qm,setDataStartEndIndexes:eg,setComputedData:tg}=Jm.actions,rg=Jm.reducer,ng=()=>{};function ig(){var e=ze();(0,t.useEffect)((()=>{e(Gm())}),[e]),function(){var e=Ue(Kp),r=Ue(Bp),n=ze(),i=Ue(zp),a=Ue(rm),o=qi(),l=Fi(),c=Ue((e=>e.rootProps.className));(0,t.useEffect)((()=>{if(null==e)return ng;var t=(t,c,s)=>{if(r!==s&&e===t)if("index"!==i){if(null!=a){var u;if("function"==typeof i){var f={activeTooltipIndex:null==c.payload.index?void 0:Number(c.payload.index),isTooltipActive:c.payload.active,activeIndex:null==c.payload.index?void 0:Number(c.payload.index),activeLabel:c.payload.label,activeDataKey:c.payload.dataKey,activeCoordinate:c.payload.coordinate},d=i(a,f);u=a[d]}else"value"===i&&(u=a.find((e=>String(e.value)===c.payload.label)));var{coordinate:p}=c.payload;if(null!=u&&!1!==c.payload.active&&null!=p&&null!=l){var{x:h,y}=p,v=Math.min(h,l.x+l.width),m=Math.min(y,l.y+l.height),g={x:"horizontal"===o?u.coordinate:v,y:"horizontal"===o?m:u.coordinate},b=pv({active:c.payload.active,coordinate:g,dataKey:c.payload.dataKey,index:String(u.index),label:c.payload.label});n(b)}else n(pv({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}))}}else n(c)};return Xm.on(Vm,t),()=>{Xm.off(Vm,t)}}),[c,n,r,e,i,a,o,l])}(),function(){var e=Ue(Kp),r=Ue(Bp),n=ze();(0,t.useEffect)((()=>{if(null==e)return ng;var t=(t,i,a)=>{r!==a&&e===t&&n(eg(i))};return Xm.on($m,t),()=>{Xm.off($m,t)}}),[n,r,e])}()}function ag(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function og(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ag(Object(r),!0).forEach((function(t){lg(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ag(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function lg(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cg(e){return e.dataKey}var sg=[],ug={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!mo.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function fg(e){var r=cl(e,ug),{active:n,allowEscapeViewBox:i,animationDuration:a,animationEasing:o,content:l,filterNull:c,isAnimationActive:s,offset:u,payloadUniqBy:f,position:d,reverseDirection:p,useTranslate3d:h,wrapperStyle:y,cursor:v,shared:m,trigger:g,defaultIndex:b,portal:x,axisId:w}=r,O=ze(),P="number"==typeof b?String(b):b;(0,t.useEffect)((()=>{O(ov({shared:m,trigger:g,axisId:w,active:n,defaultIndex:P}))}),[O,m,g,w,n,P]);var E=Fi(),A=go(),j=function(e){return Ue((t=>ev(t,e)))}(m),{activeIndex:S,isActive:k}=Ue((e=>_m(e,j,g,P))),M=Ue((e=>Nm(e,j,g,P))),T=Ue((e=>Im(e,j,g,P))),C=Ue((e=>Dm(e,j,g,P))),D=M,I=Um(),N=null!=n?n:k,[_,R]=nt([D,N]),L="axis"===j?T:void 0;!function(e,r,n,i,a,o){var l=Ue((t=>Mm(t,e,r))),c=Ue(Bp),s=Ue(Kp),u=Ue(zp),f=Ue(Zm),d=null==f?void 0:f.active;(0,t.useEffect)((()=>{if(!d&&null!=s&&null!=c){var e=pv({active:o,coordinate:n,dataKey:l,index:a,label:"number"==typeof i?String(i):i});Xm.emit(Vm,s,e,c)}}),[d,n,l,a,i,c,s,u,o])}(j,g,C,L,S,N);var K=null!=x?x:I;if(null==K)return null;var z=null!=D?D:sg;N||(z=sg),c&&z.length&&(z=_e(D.filter((e=>null!=e.value&&(!0!==e.hide||r.includeHidden))),f,cg));var B=z.length>0,F=t.createElement(vo,{allowEscapeViewBox:i,animationDuration:a,animationEasing:o,isAnimationActive:s,active:N,coordinate:C,hasPayload:B,offset:u,position:d,reverseDirection:p,useTranslate3d:h,viewBox:E,wrapperStyle:y,lastBoundingBox:_,innerRef:R,hasPortalFromProps:Boolean(x)},function(e,r){return t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?t.createElement(e,r):t.createElement(lo,r)}(l,og(og({},r),{},{payload:z,label:L,active:N,coordinate:C,accessibilityLayer:A})));return t.createElement(t.Fragment,null,(0,W.createPortal)(F,K),N&&t.createElement(Fm,{cursor:v,tooltipEventType:j,coordinate:C,payload:D,index:S}))}var dg=a(4297),pg=a.n(dg),hg=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]};function yg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function vg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yg(Object(r),!0).forEach((function(t){mg(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yg(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function mg(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var gg=(0,t.forwardRef)(((e,r)=>{var{aspect:i,initialDimension:a={width:-1,height:-1},width:o="100%",height:l="100%",minWidth:c=0,minHeight:s,maxHeight:u,children:d,debounce:p=0,id:h,className:y,onResize:v,style:m={}}=e,g=(0,t.useRef)(null),b=(0,t.useRef)();b.current=v,(0,t.useImperativeHandle)(r,(()=>g.current));var[x,w]=(0,t.useState)({containerWidth:a.width,containerHeight:a.height}),O=(0,t.useCallback)(((e,t)=>{w((r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}}))}),[]);(0,t.useEffect)((()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;O(r,n),null===(t=b.current)||void 0===t||t.call(b,r,n)};p>0&&(e=pg()(e,p,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=g.current.getBoundingClientRect();return O(r,n),t.observe(g.current),()=>{t.disconnect()}}),[O,p]);var P=(0,t.useMemo)((()=>{var{containerWidth:e,containerHeight:r}=x;if(e<0||r<0)return null;hg(f(o)||f(l),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",o,l),hg(!i||i>0,"The aspect(%s) must be greater than zero.",i);var n=f(o)?e:o,a=f(l)?r:l;return i&&i>0&&(n?a=n/i:a&&(n=a*i),u&&a>u&&(a=u)),hg(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,o,l,c,s,i),t.Children.map(d,(e=>(0,t.cloneElement)(e,{width:n,height:a,style:vg({height:"100%",width:"100%",maxHeight:a,maxWidth:n},e.props.style)})))}),[i,d,l,u,s,c,x,o]);return t.createElement("div",{id:h?"".concat(h):void 0,className:n("recharts-responsive-container",y),style:vg(vg({},m),{},{width:o,height:l,minWidth:c,minHeight:s,maxHeight:u}),ref:g},P)})),bg=e=>null;function xg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function wg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xg(Object(r),!0).forEach((function(t){Og(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xg(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Og(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}bg.displayName="Cell";var Pg={widthCache:{},cacheCount:0},Eg={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Ag="recharts_measurement_span";var jg=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||mo.isSsr)return{width:0,height:0};var r,n=(r=wg({},t),Object.keys(r).forEach((e=>{r[e]||delete r[e]})),r),i=JSON.stringify({text:e,copyStyle:n});if(Pg.widthCache[i])return Pg.widthCache[i];try{var a=document.getElementById(Ag);a||((a=document.createElement("span")).setAttribute("id",Ag),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=wg(wg({},Eg),n);Object.assign(a.style,o),a.textContent="".concat(e);var l=a.getBoundingClientRect(),c={width:l.width,height:l.height};return Pg.widthCache[i]=c,++Pg.cacheCount>2e3&&(Pg.cacheCount=0,Pg.widthCache={}),c}catch(e){return{width:0,height:0}}},Sg=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,kg=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Mg=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,Tg=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Cg={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},Dg=Object.keys(Cg),Ig="NaN";class Ng{static parse(e){var t,[,r,n]=null!==(t=Tg.exec(e))&&void 0!==t?t:[];return new Ng(parseFloat(r),null!=n?n:"")}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,u(e)&&(this.unit=""),""===t||Mg.test(t)||(this.num=NaN,this.unit=""),Dg.includes(t)&&(this.num=function(e,t){return e*Cg[t]}(e,t),this.unit="px")}add(e){return this.unit!==e.unit?new Ng(NaN,""):new Ng(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new Ng(NaN,""):new Ng(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new Ng(NaN,""):new Ng(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new Ng(NaN,""):new Ng(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return u(this.num)}}function _g(e){if(e.includes(Ig))return Ig;for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!==(r=Sg.exec(t))&&void 0!==r?r:[],o=Ng.parse(null!=n?n:""),l=Ng.parse(null!=a?a:""),c="*"===i?o.multiply(l):o.divide(l);if(c.isNaN())return Ig;t=t.replace(Sg,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var s,[,u,f,d]=null!==(s=kg.exec(t))&&void 0!==s?s:[],p=Ng.parse(null!=u?u:""),h=Ng.parse(null!=d?d:""),y="+"===f?p.add(h):p.subtract(h);if(y.isNaN())return Ig;t=t.replace(kg,y.toString())}return t}var Rg=/\(([^()]*)\)/;function Lg(e){var t=e.replace(/\s+/g,"");return t=function(e){for(var t,r=e;null!=(t=Rg.exec(r));){var[,n]=t;r=r.replace(Rg,_g(n))}return r}(t),t=_g(t)}function Kg(e){var t=function(e){try{return Lg(e)}catch(e){return Ig}}(e.slice(5,-1));return t===Ig?"":t}var zg=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],Bg=["dx","dy","angle","className","breakAll"];function Fg(){return Fg=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Fg.apply(null,arguments)}function Wg(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var Ug=/[ \f\n\r\t\v\u2028\u2029]+/,Xg=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];return w(t)||(i=r?t.toString().split(""):t.toString().split(Ug)),{wordsWithComputedWidth:i.map((e=>({word:e,width:jg(e,n).width}))),spaceWidth:r?0:jg(" ",n).width}}catch(e){return null}},Vg=e=>[{words:w(e)?[]:e.toString().split(Ug)}],$g=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:o}=e;if((t||r)&&!mo.isSsr){var l=Xg({breakAll:a,children:n,style:i});if(!l)return Vg(n);var{wordsWithComputedWidth:c,spaceWidth:s}=l;return((e,t,r,n,i)=>{var{maxLines:a,children:o,style:l,breakAll:c}=e,s=d(a),u=o,f=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce(((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];if(l&&(null==n||i||l.width+o+r<Number(n)))l.words.push(a),l.width+=o+r;else{var c={words:[a],width:o};e.push(c)}return e}),[])},p=f(t),h=e=>e.reduce(((e,t)=>e.width>t.width?e:t));if(!s||i)return p;if(!(p.length>a||h(p).width>Number(n)))return p;for(var y,v=e=>{var t=u.slice(0,e),r=Xg({breakAll:c,style:l,children:t+"…"}).wordsWithComputedWidth,i=f(r);return[i.length>a||h(i).width>Number(n),i]},m=0,g=u.length-1,b=0;m<=g&&b<=u.length-1;){var x=Math.floor((m+g)/2),w=x-1,[O,P]=v(w),[E]=v(x);if(O||E||(m=x+1),O&&E&&(g=x-1),!O&&E){y=P;break}b++}return y||p})({breakAll:a,children:n,maxLines:o,style:i},c,s,t,r)}return Vg(n)},Hg="#808080",qg=(0,t.forwardRef)(((e,r)=>{var{x:i=0,y:a=0,lineHeight:o="1em",capHeight:l="0.71em",scaleToFit:c=!1,textAnchor:s="start",verticalAnchor:u="end",fill:f=Hg}=e,h=Wg(e,zg),y=(0,t.useMemo)((()=>$g({breakAll:h.breakAll,children:h.children,maxLines:h.maxLines,scaleToFit:c,style:h.style,width:h.width})),[h.breakAll,h.children,h.maxLines,c,h.style,h.width]),{dx:v,dy:m,angle:g,className:b,breakAll:x}=h,w=Wg(h,Bg);if(!p(i)||!p(a))return null;var O,P=i+(d(v)?v:0),E=a+(d(m)?m:0);switch(u){case"start":O=Kg("calc(".concat(l,")"));break;case"middle":O=Kg("calc(".concat((y.length-1)/2," * -").concat(o," + (").concat(l," / 2))"));break;default:O=Kg("calc(".concat(y.length-1," * -").concat(o,")"))}var A=[];if(c){var j=y[0].width,{width:S}=h;A.push("scale(".concat(d(S)?S/j:1,")"))}return g&&A.push("rotate(".concat(g,", ").concat(P,", ").concat(E,")")),A.length&&(w.transform=A.join(" ")),t.createElement("text",Fg({},_(w,!0),{ref:r,x:P,y:E,className:n("recharts-text",b),textAnchor:s,fill:f.includes("url")?Hg:f}),y.map(((e,r)=>{var n=e.words.join(x?"":" ");return t.createElement("tspan",{x:P,dy:0===r?O:o,key:"".concat(n,"-").concat(r)},n)})))}));qg.displayName="Text";var Yg=["offset"],Gg=["labelRef"];function Zg(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Jg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Qg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Jg(Object(r),!0).forEach((function(t){eb(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Jg(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function eb(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tb(){return tb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},tb.apply(null,arguments)}var rb=e=>{var{value:t,formatter:r}=e,n=w(e.children)?t:e.children;return"function"==typeof r?r(n):n},nb=e=>null!=e&&"function"==typeof e,ib=(e,r,i)=>{var a,o,{position:l,viewBox:c,offset:u,className:f}=e,{cx:d,cy:p,innerRadius:h,outerRadius:v,startAngle:m,endAngle:g,clockWise:b}=c,x=(h+v)/2,O=((e,t)=>s(t-e)*Math.min(Math.abs(t-e),360))(m,g),P=O>=0?1:-1;"insideStart"===l?(a=m+P*u,o=b):"insideEnd"===l?(a=g-P*u,o=!b):"end"===l&&(a=g+P*u,o=b),o=O<=0?o:!o;var E=qn(d,p,x,a),A=qn(d,p,x,a+359*(o?1:-1)),j="M".concat(E.x,",").concat(E.y,"\n    A").concat(x,",").concat(x,",0,1,").concat(o?0:1,",\n    ").concat(A.x,",").concat(A.y),S=w(e.id)?y("recharts-radial-line-"):e.id;return t.createElement("text",tb({},i,{dominantBaseline:"central",className:n("recharts-radial-bar-label",f)}),t.createElement("defs",null,t.createElement("path",{id:S,d:j})),t.createElement("textPath",{xlinkHref:"#".concat(S)},r))},ab=e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:c,endAngle:s}=t,u=(c+s)/2;if("outside"===n){var{x:f,y:d}=qn(i,a,l+r,u);return{x:f,y:d,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var p=(o+l)/2,{x:h,y}=qn(i,a,p,u);return{x:h,y,textAnchor:"middle",verticalAnchor:"middle"}},ob=(e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:l,height:c}=t,s=c>=0?1:-1,u=s*n,p=s>0?"end":"start",h=s>0?"start":"end",y=l>=0?1:-1,m=y*n,g=y>0?"end":"start",b=y>0?"start":"end";if("top"===i)return Qg(Qg({},{x:a+l/2,y:o-s*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(o-r.y,0),width:l}:{});if("bottom"===i)return Qg(Qg({},{x:a+l/2,y:o+c+u,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(o+c),0),width:l}:{});if("left"===i){var x={x:a-m,y:o+c/2,textAnchor:g,verticalAnchor:"middle"};return Qg(Qg({},x),r?{width:Math.max(x.x-r.x,0),height:c}:{})}if("right"===i){var w={x:a+l+m,y:o+c/2,textAnchor:b,verticalAnchor:"middle"};return Qg(Qg({},w),r?{width:Math.max(r.x+r.width-w.x,0),height:c}:{})}var O=r?{width:l,height:c}:{};return"insideLeft"===i?Qg({x:a+m,y:o+c/2,textAnchor:b,verticalAnchor:"middle"},O):"insideRight"===i?Qg({x:a+l-m,y:o+c/2,textAnchor:g,verticalAnchor:"middle"},O):"insideTop"===i?Qg({x:a+l/2,y:o+u,textAnchor:"middle",verticalAnchor:h},O):"insideBottom"===i?Qg({x:a+l/2,y:o+c-u,textAnchor:"middle",verticalAnchor:p},O):"insideTopLeft"===i?Qg({x:a+m,y:o+u,textAnchor:b,verticalAnchor:h},O):"insideTopRight"===i?Qg({x:a+l-m,y:o+u,textAnchor:g,verticalAnchor:h},O):"insideBottomLeft"===i?Qg({x:a+m,y:o+c-u,textAnchor:b,verticalAnchor:p},O):"insideBottomRight"===i?Qg({x:a+l-m,y:o+c-u,textAnchor:g,verticalAnchor:p},O):i&&"object"==typeof i&&(d(i.x)||f(i.x))&&(d(i.y)||f(i.y))?Qg({x:a+v(i.x,l),y:o+v(i.y,c),textAnchor:"end",verticalAnchor:"end"},O):Qg({x:a+l/2,y:o+c/2,textAnchor:"middle",verticalAnchor:"middle"},O)},lb=e=>"cx"in e&&d(e.cx);function cb(e){var r,{offset:i=5}=e,a=Qg({offset:i},Zg(e,Yg)),{viewBox:o,position:l,value:c,children:s,content:u,className:f="",textBreakAll:d,labelRef:p}=a,h=Fi(),y=o||h;if(!y||w(c)&&w(s)&&!(0,t.isValidElement)(u)&&"function"!=typeof u)return null;if((0,t.isValidElement)(u)){var{labelRef:v}=a,m=Zg(a,Gg);return(0,t.cloneElement)(u,m)}if("function"==typeof u){if(r=(0,t.createElement)(u,a),(0,t.isValidElement)(r))return r}else r=rb(a);var g=lb(y),b=_(a,!0);if(g&&("insideStart"===l||"insideEnd"===l||"end"===l))return ib(a,r,b);var x=g?ab(a):ob(a,y);return t.createElement(qg,tb({ref:p,className:n("recharts-label",f)},b,x,{breakAll:d}),r)}cb.displayName="Label";var sb=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:l,innerRadius:c,outerRadius:s,x:u,y:f,top:p,left:h,width:y,height:v,clockWise:m,labelViewBox:g}=e;if(g)return g;if(d(y)&&d(v)){if(d(u)&&d(f))return{x:u,y:f,width:y,height:v};if(d(p)&&d(h))return{x:p,y:h,width:y,height:v}}return d(u)&&d(f)?{x:u,y:f,width:0,height:0}:d(t)&&d(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:c||0,outerRadius:s||l||o||0,clockWise:m}:e.viewBox?e.viewBox:void 0};cb.parseViewBox=sb,cb.renderCallByParent=function(e,r){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var{children:i,labelRef:a}=e,o=sb(e),l=I(i,cb).map(((e,n)=>(0,t.cloneElement)(e,{viewBox:r||o,key:"label-".concat(n)})));if(!n)return l;var c=((e,r,n)=>{if(!e)return null;var i={viewBox:r,labelRef:n};return!0===e?t.createElement(cb,tb({key:"label-implicit"},i)):p(e)?t.createElement(cb,tb({key:"label-implicit",value:e},i)):(0,t.isValidElement)(e)?e.type===cb?(0,t.cloneElement)(e,Qg({key:"label-implicit"},i)):t.createElement(cb,tb({key:"label-implicit",content:e},i)):nb(e)?t.createElement(cb,tb({key:"label-implicit",content:e},i)):e&&"object"==typeof e?t.createElement(cb,tb({},e,{key:"label-implicit"},i)):null})(e.label,r||o,a);return[c,...l]};var ub=a(25),fb=a.n(ub),db=["valueAccessor"],pb=["data","dataKey","clockWise","id","textBreakAll"];function hb(){return hb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},hb.apply(null,arguments)}function yb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function vb(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yb(Object(r),!0).forEach((function(t){mb(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yb(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function mb(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gb(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var bb=e=>Array.isArray(e.value)?fb()(e.value):e.value;function xb(e){var{valueAccessor:r=bb}=e,n=gb(e,db),{data:i,dataKey:a,clockWise:o,id:l,textBreakAll:c}=n,s=gb(n,pb);return i&&i.length?t.createElement(F,{className:"recharts-label-list"},i.map(((e,n)=>{var i=w(a)?r(e,n):ni(e&&e.payload,a),u=w(l)?{}:{id:"".concat(l,"-").concat(n)};return t.createElement(cb,hb({},_(e,!0),s,u,{parentViewBox:e.parentViewBox,value:i,textBreakAll:c,viewBox:cb.parseViewBox(w(o)?e:vb(vb({},e),{},{clockWise:o})),key:"label-".concat(n),index:n}))}))):null}xb.displayName="LabelList",xb.renderCallByParent=function(e,r){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var{children:i}=e,a=I(i,xb).map(((e,n)=>(0,t.cloneElement)(e,{data:r,key:"labelList-".concat(n)})));return n?[function(e,r){return e?!0===e?t.createElement(xb,{key:"labelList-implicit",data:r}):t.isValidElement(e)||nb(e)?t.createElement(xb,{key:"labelList-implicit",data:r,content:e}):"object"==typeof e?t.createElement(xb,hb({data:r},e,{key:"labelList-implicit"})):null:null}(e.label,r),...a]:a};var wb=["component"];function Ob(e){var r,{component:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,wb);return(0,t.isValidElement)(n)?r=(0,t.cloneElement)(n,i):"function"==typeof n?r=(0,t.createElement)(n,i):hg(!1,"Customized's props `component` must be React.element or Function, but got %s.",typeof n),t.createElement(F,{className:"recharts-customized-wrapper"},r)}Ob.displayName="Customized";var Pb=["points","className","baseLinePoints","connectNulls"];function Eb(){return Eb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Eb.apply(null,arguments)}var Ab=e=>e&&e.x===+e.x&&e.y===+e.y,jb=(e,t)=>{var r=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach((e=>{Ab(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])})),Ab(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t}(e);t&&(r=[r.reduce(((e,t)=>[...e,...t]),[])]);var n=r.map((e=>e.reduce(((e,t,r)=>"".concat(e).concat(0===r?"M":"L").concat(t.x,",").concat(t.y)),""))).join("");return 1===r.length?"".concat(n,"Z"):n},Sb=e=>{var{points:r,className:i,baseLinePoints:a,connectNulls:o}=e,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Pb);if(!r||!r.length)return null;var c=n("recharts-polygon",i);if(a&&a.length){var s=l.stroke&&"none"!==l.stroke,u=((e,t,r)=>{var n=jb(e,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(jb(t.reverse(),r).slice(1))})(r,a,o);return t.createElement("g",{className:c},t.createElement("path",Eb({},_(l,!0),{fill:"Z"===u.slice(-1)?l.fill:"none",stroke:"none",d:u})),s?t.createElement("path",Eb({},_(l,!0),{fill:"none",d:jb(r,o)})):null,s?t.createElement("path",Eb({},_(l,!0),{fill:"none",d:jb(a,o)})):null)}var f=jb(r,o);return t.createElement("path",Eb({},_(l,!0),{fill:"Z"===f.slice(-1)?l.fill:"none",className:c,d:f}))};function kb(){return kb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},kb.apply(null,arguments)}var Mb=e=>{var{cx:r,cy:i,r:a,className:o}=e,l=n("recharts-dot",o);return r===+r&&i===+i&&a===+a?t.createElement("circle",kb({},_(e,!1),S(e),{className:l,cx:r,cy:i,r:a})):null},Tb=e=>e.graphicalItems.polarItems,Cb=Ge([ah,oh],xh),Db=Ge([Tb,mh,Cb],Ph),Ib=Ge([Db],Sh),Nb=Ge([Ib,cp],Mh),_b=Ge([Nb,mh,Db],Ch),Rb=Ge([Nb,mh,Db],((e,t,r)=>r.length>0?e.flatMap((e=>r.flatMap((r=>{var n;return{value:ni(e,null!==(n=t.dataKey)&&void 0!==n?n:r.dataKey),errorDomain:[]}})))).filter(Boolean):null!=(null==t?void 0:t.dataKey)?e.map((e=>({value:ni(e,t.dataKey),errorDomain:[]}))):e.map((e=>({value:e,errorDomain:[]}))))),Lb=()=>{},Kb=Ge([mh,ay,Lb,Rb,Lb],oy),zb=Ge([mh,Hi,Nb,_b,Rp,ah,Kb],sy),Bb=Ge([zb,mh,dy],hy),Fb=Ge([mh,zb,Bb,ah],vy),Wb=(e,t,r)=>{switch(t){case"angleAxis":return qp(e,r);case"radiusAxis":return Yp(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},Ub=(e,t,r)=>{switch(t){case"angleAxis":return th(e,r);case"radiusAxis":return nh(e,r);default:throw new Error("Unexpected axis type: ".concat(t))}},Xb=Ge([Wb,dy,Fb,Ub],py),Vb=Ge([Hi,_b,gh,ah],By),$b=Ge([Hi,Wb,dy,Xb,Bb,Ub,zy,Vb,ah],Uy),Hb=Ge([Hi,Wb,Xb,Ub,zy,Vb,ah],Vy),qb=Ge([(e,t)=>$b(e,"angleAxis",t,!1)],(e=>{if(e)return e.map((e=>e.coordinate))})),Yb=Ge([(e,t)=>$b(e,"radiusAxis",t,!1)],(e=>{if(e)return e.map((e=>e.coordinate))})),Gb=["gridType","radialLines","angleAxisId","radiusAxisId","cx","cy","innerRadius","outerRadius"];function Zb(){return Zb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zb.apply(null,arguments)}function Jb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Qb(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Jb(Object(r),!0).forEach((function(t){ex(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Jb(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ex(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var tx=(e,t,r,n)=>{var i="";return n.forEach(((n,a)=>{var o=qn(t,r,e,n);i+=a?"L ".concat(o.x,",").concat(o.y):"M ".concat(o.x,",").concat(o.y)})),i+="Z"},rx=e=>{var{cx:r,cy:n,innerRadius:i,outerRadius:a,polarAngles:o,radialLines:l}=e;if(!o||!o.length||!l)return null;var c=Qb({stroke:"#ccc"},_(e,!1));return t.createElement("g",{className:"recharts-polar-grid-angle"},o.map((e=>{var o=qn(r,n,i,e),l=qn(r,n,a,e);return t.createElement("line",Zb({},c,{key:"line-".concat(e),x1:o.x,y1:o.y,x2:l.x,y2:l.y}))})))},nx=e=>{var{cx:r,cy:i,radius:a,index:o}=e,l=Qb(Qb({stroke:"#ccc"},_(e,!1)),{},{fill:"none"});return t.createElement("circle",Zb({},l,{className:n("recharts-polar-grid-concentric-circle",e.className),key:"circle-".concat(o),cx:r,cy:i,r:a}))},ix=e=>{var{radius:r,index:i}=e,a=Qb(Qb({stroke:"#ccc"},_(e,!1)),{},{fill:"none"});return t.createElement("path",Zb({},a,{className:n("recharts-polar-grid-concentric-polygon",e.className),key:"path-".concat(i),d:tx(r,e.cx,e.cy,e.polarAngles)}))},ax=e=>{var{polarRadius:r,gridType:n}=e;return r&&r.length?t.createElement("g",{className:"recharts-polar-grid-concentric"},r.map(((r,i)=>{var a=i;return"circle"===n?t.createElement(nx,Zb({key:a},e,{radius:r,index:i})):t.createElement(ix,Zb({key:a},e,{radius:r,index:i}))}))):null},ox=e=>{var r,n,i,a,o,l,c,s,{gridType:u="polygon",radialLines:f=!0,angleAxisId:d=0,radiusAxisId:p=0,cx:h,cy:y,innerRadius:v,outerRadius:m}=e,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Gb),b=Ue(ih),x=Qb({cx:null!==(r=null!==(n=null==b?void 0:b.cx)&&void 0!==n?n:h)&&void 0!==r?r:0,cy:null!==(i=null!==(a=null==b?void 0:b.cy)&&void 0!==a?a:y)&&void 0!==i?i:0,innerRadius:null!==(o=null!==(l=null==b?void 0:b.innerRadius)&&void 0!==l?l:v)&&void 0!==o?o:0,outerRadius:null!==(c=null!==(s=null==b?void 0:b.outerRadius)&&void 0!==s?s:m)&&void 0!==c?c:0},g),{polarAngles:w,polarRadius:O,cx:P,cy:E,innerRadius:A,outerRadius:j}=x,S=Ue((e=>qb(e,d))),k=Ue((e=>Yb(e,p))),M=Array.isArray(w)?w:S,T=Array.isArray(O)?O:k;return j<=0||null==M||null==T?null:t.createElement("g",{className:"recharts-polar-grid"},t.createElement(rx,Zb({cx:P,cy:E,innerRadius:A,outerRadius:j,gridType:u,radialLines:f},x,{polarAngles:M,polarRadius:T})),t.createElement(ax,Zb({cx:P,cy:E,innerRadius:A,outerRadius:j,gridType:u,radialLines:f},x,{polarAngles:M,polarRadius:T})))};ox.displayName="PolarGrid";var lx=a(4338),cx=a.n(lx),sx=a(2972),ux=a.n(sx),fx=$r({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=t.payload},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=t.payload},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:dx,removeRadiusAxis:px,addAngleAxis:hx,removeAngleAxis:yx}=fx.actions,vx=fx.reducer,mx=["cx","cy","angle","axisLine"],gx=["angle","tickFormatter","stroke","tick"];function bx(){return bx=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bx.apply(null,arguments)}function xx(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function wx(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xx(Object(r),!0).forEach((function(t){Ox(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xx(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ox(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Px(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var Ex="radiusAxis";function Ax(e){var r=ze();return(0,t.useEffect)((()=>(r(dx(e)),()=>{r(px(e))}))),null}var jx=(e,r)=>{var{angle:i,tickFormatter:a,stroke:o,tick:l}=e,c=Px(e,gx),s=(e=>{var t;switch(e){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t})(e.orientation),u=_(c,!1),f=_(l,!1),d=r.map(((r,c)=>{var d=((e,t,r,n)=>{var{coordinate:i}=e;return qn(r,n,i,t)})(r,e.angle,e.cx,e.cy),p=wx(wx(wx(wx({textAnchor:s,transform:"rotate(".concat(90-i,", ").concat(d.x,", ").concat(d.y,")")},u),{},{stroke:"none",fill:o},f),{},{index:c},d),{},{payload:r});return t.createElement(F,bx({className:n("recharts-polar-radius-axis-tick",Qn(l)),key:"tick-".concat(r.coordinate)},k(e,r,c)),((e,r,n)=>t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?e(r):t.createElement(qg,bx({},r,{className:"recharts-polar-radius-axis-tick-value"}),n))(l,p,a?a(r.value,c):r.value))}));return t.createElement(F,{className:"recharts-polar-radius-axis-ticks"},d)},Sx=e=>{var{radiusAxisId:r}=e,i=Ue(ih),a=Ue((e=>Xb(e,"radiusAxis",r))),o=Ue((e=>$b(e,"radiusAxis",r,!1)));if(null==i||!o||!o.length)return null;var l=wx(wx(wx({},e),{},{scale:a},i),{},{radius:i.outerRadius}),{tick:c,axisLine:s}=l;return t.createElement(F,{className:n("recharts-polar-radius-axis",Ex,l.className)},s&&((e,r)=>{var{cx:n,cy:i,angle:a,axisLine:o}=e,l=Px(e,mx),c=r.reduce(((e,t)=>[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)]),[1/0,-1/0]),s=qn(n,i,c[0],a),u=qn(n,i,c[1],a),f=wx(wx(wx({},_(l,!1)),{},{fill:"none"},_(o,!1)),{},{x1:s.x,y1:s.y,x2:u.x,y2:u.y});return t.createElement("line",bx({className:"recharts-polar-radius-axis-line"},f))})(l,o),c&&jx(l,o),cb.renderCallByParent(l,((e,t,r,n)=>{var i=cx()(n,(e=>e.coordinate||0));return{cx:t,cy:r,startAngle:e,endAngle:e,innerRadius:ux()(n,(e=>e.coordinate||0)).coordinate||0,outerRadius:i.coordinate||0}})(l.angle,l.cx,l.cy,o)))};class kx extends t.PureComponent{render(){return t.createElement(t.Fragment,null,t.createElement(Ax,{domain:this.props.domain,id:this.props.radiusAxisId,scale:this.props.scale,type:this.props.type,dataKey:this.props.dataKey,unit:void 0,name:this.props.name,allowDuplicatedCategory:this.props.allowDuplicatedCategory,allowDataOverflow:this.props.allowDataOverflow,reversed:this.props.reversed,includeHidden:this.props.includeHidden,allowDecimals:this.props.allowDecimals,tickCount:this.props.tickCount,ticks:this.props.ticks,tick:this.props.tick}),t.createElement(Sx,this.props))}}Ox(kx,"displayName","PolarRadiusAxis"),Ox(kx,"axisType",Ex),Ox(kx,"defaultProps",Wp);var Mx=["children"];function Tx(){return Tx=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Tx.apply(null,arguments)}function Cx(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Dx(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Cx(Object(r),!0).forEach((function(t){Ix(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cx(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ix(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Nx=Math.PI/180,_x=1e-5,Rx="angleAxis";function Lx(e){var r=ze(),n=(0,t.useMemo)((()=>{var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Mx);return r}),[e]),i=Ue((e=>qp(e,n.id))),a=n===i;return(0,t.useEffect)((()=>(r(hx(n)),()=>{r(yx(n))})),[r,n]),a?e.children:null}var Kx=e=>{var{cx:r,cy:n,radius:i,axisLineType:a,axisLine:o,ticks:l}=e;if(!o)return null;var c=Dx(Dx({},_(e,!1)),{},{fill:"none"},_(o,!1));if("circle"===a)return t.createElement(Mb,Tx({className:"recharts-polar-angle-axis-line"},c,{cx:r,cy:n,r:i}));var s=l.map((e=>qn(r,n,i,e.coordinate)));return t.createElement(Sb,Tx({className:"recharts-polar-angle-axis-line"},c,{points:s}))},zx=e=>{var{tick:r,tickProps:n,value:i}=e;return r?t.isValidElement(r)?t.cloneElement(r,n):"function"==typeof r?r(n):t.createElement(qg,Tx({},n,{className:"recharts-polar-angle-axis-tick-value"}),i):null},Bx=e=>{var{tick:r,tickLine:i,tickFormatter:a,stroke:o,ticks:l}=e,c=_(e,!1),s=_(r,!1),u=Dx(Dx({},c),{},{fill:"none"},_(i,!1)),f=l.map(((l,f)=>{var d=((e,t)=>{var{cx:r,cy:n,radius:i,orientation:a,tickSize:o}=t,l=o||8,c=qn(r,n,i,e.coordinate),s=qn(r,n,i+("inner"===a?-1:1)*l,e.coordinate);return{x1:c.x,y1:c.y,x2:s.x,y2:s.y}})(l,e),p=((e,t)=>{var r=Math.cos(-e.coordinate*Nx);return r>_x?"outer"===t?"start":"end":r<-_x?"outer"===t?"end":"start":"middle"})(l,e.orientation),h=Dx(Dx(Dx({textAnchor:p},c),{},{stroke:"none",fill:o},s),{},{index:f,payload:l,x:d.x2,y:d.y2});return t.createElement(F,Tx({className:n("recharts-polar-angle-axis-tick",Qn(r)),key:"tick-".concat(l.coordinate)},k(e,l,f)),i&&t.createElement("line",Tx({className:"recharts-polar-angle-axis-tick-line"},u,d)),t.createElement(zx,{tick:r,tickProps:h,value:a?a(l.value,f):l.value}))}));return t.createElement(F,{className:"recharts-polar-angle-axis-ticks"},f)},Fx=e=>{var{angleAxisId:r}=e,i=Ue(ih),a=Ue((e=>Xb(e,"angleAxis",r))),o=Li(),l=Ue((e=>$b(e,"angleAxis",r,o)));if(null==i||!l||!l.length)return null;var c=Dx(Dx(Dx({},e),{},{scale:a},i),{},{radius:i.outerRadius});return t.createElement(F,{className:n("recharts-polar-angle-axis",Rx,c.className)},t.createElement(Kx,Tx({},c,{ticks:l})),t.createElement(Bx,Tx({},c,{ticks:l})))};class Wx extends t.PureComponent{render(){return this.props.radius<=0?null:t.createElement(Lx,{id:this.props.angleAxisId,scale:this.props.scale,type:this.props.type,dataKey:this.props.dataKey,unit:void 0,name:this.props.name,allowDuplicatedCategory:!1,allowDataOverflow:!1,reversed:this.props.reversed,includeHidden:!1,allowDecimals:this.props.allowDecimals,tickCount:this.props.tickCount,ticks:this.props.ticks,tick:this.props.tick,domain:this.props.domain},t.createElement(Fx,this.props))}}function Ux(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Xx(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ux(Object(r),!0).forEach((function(t){Vx(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ux(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Vx(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Ix(Wx,"displayName","PolarAngleAxis"),Ix(Wx,"axisType",Rx),Ix(Wx,"defaultProps",Fp);var $x=(e,t)=>t,Hx=[],qx=(e,t,r)=>0===(null==r?void 0:r.length)?Hx:r,Yx=Ge([cp,$x,qx],((e,t,r)=>{var n,{chartData:i}=e;if((n=null!=(null==t?void 0:t.data)&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map((e=>Xx(Xx({},t.presentationProps),e.props)))),null!=n)return n})),Gx=Ge([Yx,$x,qx],((e,t,r)=>{if(null!=e)return e.map(((e,n)=>{var i,a,o=ni(e,t.nameKey,t.name);return a=null!=r&&null!==(i=r[n])&&void 0!==i&&null!==(i=i.props)&&void 0!==i&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:xi(o,t.dataKey),color:a,payload:e,type:t.legendType}}))})),Zx=Ge([Tb,$x],((e,t)=>{if(e.some((e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data)))return t})),Jx=Ge([Yx,Zx,qx,Ii],((e,t,r,n)=>{if(null!=t&&null!=e)return function(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:c,startAngle:u,endAngle:f,dataKey:p,nameKey:h,tooltipType:y}=i,v=Math.abs(i.minAngle),m=Uw(u,f),g=Math.abs(m),b=a.length<=1?0:null!==(t=i.paddingAngle)&&void 0!==t?t:0,x=a.filter((e=>0!==ni(e,p,0))).length,w=g-x*v-(g>=360?x:x-1)*b,O=a.reduce(((e,t)=>{var r=ni(t,p,0);return e+(d(r)?r:0)}),0);O>0&&(r=a.map(((e,t)=>{var r,a=ni(e,p,0),f=ni(e,h,t),g=Ww(i,l,e),x=(d(a)?a:0)/O,P=Rw(Rw({},e),o&&o[t]&&o[t].props),E=(r=t?n.endAngle+s(m)*b*(0!==a?1:0):u)+s(m)*((0!==a?v:0)+x*w),A=(r+E)/2,j=(g.innerRadius+g.outerRadius)/2,S=[{name:f,value:a,payload:P,dataKey:p,type:y}],k=qn(g.cx,g.cy,j,A);return n=Rw(Rw(Rw(Rw({},i.presentationProps),{},{percent:x,cornerRadius:c,name:f,tooltipPayload:S,midAngle:A,middleRadius:j,tooltipPosition:k},P),g),{},{value:ni(e,p),startAngle:r,endAngle:E,payload:P,paddingAngle:s(m)*b})})));return r}({offset:n,pieSettings:t,displayedData:e,cells:r})})),Qx=$r({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push(t.payload)},removeCartesianGraphicalItem(e,t){var r=Lt(e).cartesianItems.indexOf(t.payload);r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push(t.payload)},removePolarGraphicalItem(e,t){var r=Lt(e).polarItems.indexOf(t.payload);r>-1&&e.polarItems.splice(r,1)}}}),{addBar:ew,removeBar:tw,addCartesianGraphicalItem:rw,removeCartesianGraphicalItem:nw,addPolarGraphicalItem:iw,removePolarGraphicalItem:aw}=Qx.actions,ow=Qx.reducer;function lw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function cw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?lw(Object(r),!0).forEach((function(t){sw(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lw(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function sw(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uw(e){var r=ze();return(0,t.useEffect)((()=>{var t=cw(cw({},e),{},{stackId:fi(e.stackId)});return r(rw(t)),()=>{r(nw(t))}}),[r,e]),null}function fw(e){var r=ze();return(0,t.useEffect)((()=>(r(iw(e)),()=>{r(aw(e))})),[r,e]),null}var dw=a(2938),pw=a.n(dw);function hw(){return hw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},hw.apply(null,arguments)}var yw=(e,t,r,n,i)=>{var a,o=r-n;return a="M ".concat(e,",").concat(t),a+="L ".concat(e+r,",").concat(t),a+="L ".concat(e+r-o/2,",").concat(t+i),a+="L ".concat(e+r-o/2-n,",").concat(t+i),a+="L ".concat(e,",").concat(t," Z")},vw={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},mw=e=>{var r=cl(e,vw),i=(0,t.useRef)(),[a,o]=(0,t.useState)(-1);(0,t.useEffect)((()=>{if(i.current&&i.current.getTotalLength)try{var e=i.current.getTotalLength();e&&o(e)}catch(e){}}),[]);var{x:l,y:c,upperWidth:s,lowerWidth:u,height:f,className:d}=r,{animationEasing:p,animationDuration:h,animationBegin:y,isUpdateAnimationActive:v}=r;if(l!==+l||c!==+c||s!==+s||u!==+u||f!==+f||0===s&&0===u||0===f)return null;var m=n("recharts-trapezoid",d);return v?t.createElement(Kl,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:f,x:l,y:c},to:{upperWidth:s,lowerWidth:u,height:f,x:l,y:c},duration:h,animationEasing:p,isActive:v},(e=>{var{upperWidth:n,lowerWidth:o,height:l,x:c,y:s}=e;return t.createElement(Kl,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:h,easing:p},t.createElement("path",hw({},_(r,!0),{className:m,d:yw(c,s,n,o,l),ref:i})))})):t.createElement("g",null,t.createElement("path",hw({},_(r,!0),{className:m,d:yw(l,c,s,u,f)})))},gw=["option","shapeType","propTransformer","activeClassName","isActive"];function bw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function xw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?bw(Object(r),!0).forEach((function(t){ww(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bw(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ww(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ow(e,t){return xw(xw({},t),e)}function Pw(e){var{shapeType:r,elementProps:n}=e;switch(r){case"rectangle":return t.createElement(Wl,n);case"trapezoid":return t.createElement(mw,n);case"sector":return t.createElement(ql,n);case"symbols":if(function(e){return"symbols"===e}(r))return t.createElement(Se,n);break;default:return null}}function Ew(e){return(0,t.isValidElement)(e)?e.props:e}function Aw(e){var r,{option:n,shapeType:i,propTransformer:a=Ow,activeClassName:o="recharts-active-shape",isActive:l}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,gw);if((0,t.isValidElement)(n))r=(0,t.cloneElement)(n,xw(xw({},c),Ew(n)));else if("function"==typeof n)r=n(c);else if(pw()(n)&&"boolean"!=typeof n){var s=a(n,c);r=t.createElement(Pw,{shapeType:i,elementProps:s})}else{var u=c;r=t.createElement(Pw,{shapeType:i,elementProps:u})}return l?t.createElement(F,{className:o},r):r}var jw=(e,t)=>{var r=ze();return(n,i)=>a=>{null==e||e(n,i,a),r(lv({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},Sw=e=>{var t=ze();return(r,n)=>i=>{null==e||e(r,n,i),t(cv())}},kw=(e,t)=>{var r=ze();return(n,i)=>a=>{null==e||e(n,i,a),r(uv({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}};function Mw(e){var{fn:r,args:n}=e,i=ze(),a=Li();return(0,t.useEffect)((()=>{if(!a){var e=r(n);return i(iv(e)),()=>{i(av(e))}}}),[r,n,i,a]),null}var Tw=()=>{};function Cw(e){var{legendPayload:r}=e,n=ze(),i=Li();return(0,t.useEffect)((()=>i?Tw:(n(Wa(r)),()=>{n(Ua(r))})),[n,i,r]),null}function Dw(e){var{legendPayload:r}=e,n=ze(),i=Ue(Hi);return(0,t.useEffect)((()=>"centric"!==i&&"radial"!==i?Tw:(n(Wa(r)),()=>{n(Ua(r))})),[n,i,r]),null}function Iw(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",n=(0,t.useRef)(y(r)),i=(0,t.useRef)(e);return i.current!==e&&(n.current=y(r),i.current=e),n.current}var Nw=["onMouseEnter","onClick","onMouseLeave"];function _w(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Rw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_w(Object(r),!0).forEach((function(t){Lw(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_w(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Lw(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Kw(){return Kw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Kw.apply(null,arguments)}function zw(e){var r=(0,t.useMemo)((()=>_(e,!1)),[e]),n=(0,t.useMemo)((()=>I(e.children,bg)),[e.children]),i=(0,t.useMemo)((()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:r})),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,r]),a=Ue((e=>Gx(e,i,n)));return t.createElement(Dw,{legendPayload:a})}function Bw(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:c,tooltipType:s}=e;return{dataDefinedOnItem:null==n?void 0:n.map((e=>e.tooltipPayload)),positions:null==n?void 0:n.map((e=>e.tooltipPosition)),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:xi(l,t),hide:c,type:s,color:o,unit:""}}}var Fw=(e,t)=>e>t?"start":e<t?"end":"middle",Ww=(e,t,r)=>{var{top:n,left:i,width:a,height:o}=t,l=Yn(a,o),c=i+v(e.cx,a,a/2),s=n+v(e.cy,o,o/2),u=v(e.innerRadius,l,0),f=((e,t,r)=>"function"==typeof t?t(e):v(t,r,.8*r))(r,e.outerRadius,l);return{cx:c,cy:s,innerRadius:u,outerRadius:f,maxRadius:e.maxRadius||Math.sqrt(a*a+o*o)/2}},Uw=(e,t)=>s(t-e)*Math.min(Math.abs(t-e),360);function Xw(e){var{sectors:r,props:i,showLabels:a}=e,{label:o,labelLine:l,dataKey:c}=i;if(!a||!o||!r)return null;var s=_(i,!1),u=_(o,!1),f=_(l,!1),d="object"==typeof o&&"offsetRadius"in o&&o.offsetRadius||20,p=r.map(((e,r)=>{var i=(e.startAngle+e.endAngle)/2,a=qn(e.cx,e.cy,e.outerRadius+d,i),p=Rw(Rw(Rw(Rw({},s),e),{},{stroke:"none"},u),{},{index:r,textAnchor:Fw(a.x,e.cx)},a),h=Rw(Rw(Rw(Rw({},s),e),{},{fill:"none",stroke:e.fill},f),{},{index:r,points:[qn(e.cx,e.cy,e.outerRadius,i),a],key:"line"});return t.createElement(F,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(r)},l&&((e,r)=>{if(t.isValidElement(e))return t.cloneElement(e,r);if("function"==typeof e)return e(r);var i=n("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return t.createElement(Qo,Kw({},r,{type:"linear",className:i}))})(l,h),((e,r,i)=>{if(t.isValidElement(e))return t.cloneElement(e,r);var a=i;if("function"==typeof e&&(a=e(r),t.isValidElement(a)))return a;var o=n("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return t.createElement(qg,Kw({},r,{alignmentBaseline:"middle",className:o}),a)})(o,p,ni(e,c)))}));return t.createElement(F,{className:"recharts-pie-labels"},p)}function Vw(e){var{sectors:r,activeShape:n,inactiveShape:i,allOtherPieProps:a,showLabels:o}=e,l=Ue(lm),{onMouseEnter:c,onClick:s,onMouseLeave:u}=a,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(a,Nw),d=jw(c,a.dataKey),p=Sw(u),h=kw(s,a.dataKey);return null==r?null:t.createElement(t.Fragment,null,r.map(((e,o)=>{if(0===(null==e?void 0:e.startAngle)&&0===(null==e?void 0:e.endAngle)&&1!==r.length)return null;var c=n&&String(o)===l,s=c?n:l?i:null,u=Rw(Rw({},e),{},{stroke:e.stroke,tabIndex:-1,[ki]:o,[Mi]:a.dataKey});return t.createElement(F,Kw({tabIndex:-1,className:"recharts-pie-sector"},k(f,e,o),{onMouseEnter:d(e,o),onMouseLeave:p(e,o),onClick:h(e,o),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(o)}),t.createElement(Aw,Kw({option:s,isActive:c,shapeType:"sector"},u)))})),t.createElement(Xw,{sectors:r,props:a,showLabels:o}))}function $w(e){var{props:r,previousSectorsRef:n}=e,{sectors:i,isAnimationActive:a,animationBegin:o,animationDuration:c,animationEasing:s,activeShape:u,inactiveShape:f,onAnimationStart:d,onAnimationEnd:p}=r,h=Iw(r,"recharts-pie-"),y=n.current,[v,m]=(0,t.useState)(!0),b=(0,t.useCallback)((()=>{"function"==typeof p&&p(),m(!1)}),[p]),x=(0,t.useCallback)((()=>{"function"==typeof d&&d(),m(!0)}),[d]);return t.createElement(Kl,{begin:o,duration:c,isActive:a,easing:s,from:{t:0},to:{t:1},onAnimationStart:x,onAnimationEnd:b,key:h},(e=>{var{t:a}=e,o=[],c=(i&&i[0]).startAngle;return i.forEach(((e,t)=>{var r=y&&y[t],n=t>0?l()(e,"paddingAngle",0):0;if(r){var i=g(r.endAngle-r.startAngle,e.endAngle-e.startAngle),s=Rw(Rw({},e),{},{startAngle:c+n,endAngle:c+i(a)+n});o.push(s),c=s.endAngle}else{var{endAngle:u,startAngle:f}=e,d=g(0,u-f)(a),p=Rw(Rw({},e),{},{startAngle:c+n,endAngle:c+d+n});o.push(p),c=p.endAngle}})),n.current=o,t.createElement(F,null,t.createElement(Vw,{sectors:o,activeShape:u,inactiveShape:f,allOtherPieProps:r,showLabels:!v}))}))}function Hw(e){var{sectors:r,isAnimationActive:n,activeShape:i,inactiveShape:a}=e,o=(0,t.useRef)(null),l=o.current;return n&&r&&r.length&&(!l||l!==r)?t.createElement($w,{props:e,previousSectorsRef:o}):t.createElement(Vw,{sectors:r,activeShape:i,inactiveShape:a,allOtherPieProps:e,showLabels:!0})}function qw(e){var{hide:r,className:i,rootTabIndex:a}=e,o=n("recharts-pie",i);return r?null:t.createElement(F,{tabIndex:a,className:o},t.createElement(Hw,e))}var Yw={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!mo.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function Gw(e){var r=cl(e,Yw),n=(0,t.useMemo)((()=>I(e.children,bg)),[e.children]),i=_(r,!1),a=(0,t.useMemo)((()=>({name:r.name,nameKey:r.nameKey,tooltipType:r.tooltipType,data:r.data,dataKey:r.dataKey,cx:r.cx,cy:r.cy,startAngle:r.startAngle,endAngle:r.endAngle,minAngle:r.minAngle,paddingAngle:r.paddingAngle,innerRadius:r.innerRadius,outerRadius:r.outerRadius,cornerRadius:r.cornerRadius,legendType:r.legendType,fill:r.fill,presentationProps:i})),[r.cornerRadius,r.cx,r.cy,r.data,r.dataKey,r.endAngle,r.innerRadius,r.minAngle,r.name,r.nameKey,r.outerRadius,r.paddingAngle,r.startAngle,r.tooltipType,r.legendType,r.fill,i]),o=Ue((e=>Jx(e,a,n)));return t.createElement(t.Fragment,null,t.createElement(Mw,{fn:Bw,args:Rw(Rw({},r),{},{sectors:o})}),t.createElement(qw,Kw({},r,{sectors:o})))}class Zw extends t.PureComponent{constructor(){super(...arguments),Lw(this,"id",y("recharts-pie-"))}render(){return t.createElement(t.Fragment,null,t.createElement(fw,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),t.createElement(zw,this.props),t.createElement(Gw,this.props),this.props.children)}}function Jw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Qw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Jw(Object(r),!0).forEach((function(t){eO(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Jw(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function eO(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Lw(Zw,"displayName","Pie"),Lw(Zw,"defaultProps",Yw);function tO(e){var r,{points:n,mainColor:i,activeDot:a,itemDataKey:o}=e,l=mm(),c=Ue(lm),s=Ue(cm);if(!c)return null;var u=l.dataKey;if(u&&!l.allowDuplicatedCategory){var f="function"==typeof u?e=>u(e.payload):"payload.".concat(u);r=x(n,f,s)}else r=null==n?void 0:n[Number(c)];return w(r)?null:(e=>{var{point:r,childIndex:n,mainColor:i,activeDot:a,dataKey:o}=e;if(!1===a||null==r.x||null==r.y)return null;var l,c=Qw(Qw({index:n,dataKey:o,cx:r.x,cy:r.y,r:4,fill:null!=i?i:"none",strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},_(a,!1)),S(a));return l=(0,t.isValidElement)(a)?(0,t.cloneElement)(a,c):"function"==typeof a?a(c):t.createElement(Mb,c),t.createElement(F,{className:"recharts-active-dot"},l)})({point:r,childIndex:Number(c),mainColor:i,dataKey:o,activeDot:a})}var rO=e=>t.createElement(fw,e);function nO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function iO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nO(Object(r),!0).forEach((function(t){aO(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nO(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function aO(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var oO=(e,t)=>Xb(e,"radiusAxis",t),lO=Ge([oO],(e=>{if(null!=e)return{scale:e}})),cO=Ge([Yp,oO],((e,t)=>{if(null!=e&&null!=t)return iO(iO({},e),{},{scale:t})})),sO=(e,t,r)=>qp(e,r),uO=(e,t,r)=>Xb(e,"angleAxis",r),fO=Ge([sO,uO],((e,t)=>{if(null!=e&&null!=t)return iO(iO({},e),{},{scale:t})})),dO=Ge([sO,uO,ih],((e,t,r)=>{if(null!=r&&null!=t)return{scale:t,type:e.type,dataKey:e.dataKey,cx:r.cx,cy:r.cy}})),pO=Ge([Hi,cO,(e,t,r,n)=>$b(e,"radiusAxis",t,n),fO,(e,t,r,n)=>$b(e,"angleAxis",r,n)],((e,t,r,n,i)=>ii(e,"radiusAxis")?gi(t,r,!1):gi(n,i,!1))),hO=Ge([Tb,(e,t,r,n,i)=>i],((e,t)=>{if(e.some((e=>"radar"===e.type&&t===e.dataKey)))return t})),yO=Ge([lO,dO,cp,hO,pO],((e,t,r,n,i)=>{var{chartData:a,dataStartIndex:o,dataEndIndex:l}=r;if(null!=e&&null!=t&&null!=a&&null!=i&&null!=n)return function(e){var{radiusAxis:t,angleAxis:r,displayedData:n,dataKey:i,bandSize:a}=e,{cx:o,cy:l}=r,c=!1,s=[],u="number"!==r.type&&null!=a?a:0;n.forEach(((e,n)=>{var a=ni(e,r.dataKey,n),f=ni(e,i),d=r.scale(a)+u,p=Array.isArray(f)?fb()(f):f,h=w(p)?void 0:t.scale(p);Array.isArray(f)&&f.length>=2&&(c=!0),s.push(mO(mO({},qn(o,l,h,d)),{},{name:a,value:f,cx:o,cy:l,radius:h,angle:d,payload:e}))}));var f=[];c&&s.forEach((e=>{if(Array.isArray(e.value)){var r=e.value[0],n=w(r)?void 0:t.scale(r);f.push(mO(mO({},e),{},{radius:n},qn(o,l,n,e.angle)))}else f.push(e)}));return{points:s,isRange:c,baseLinePoints:f}}({radiusAxis:e,angleAxis:t,displayedData:a.slice(o,l+1),dataKey:n,bandSize:i})}));function vO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function mO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vO(Object(r),!0).forEach((function(t){gO(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vO(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function gO(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bO(){return bO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bO.apply(null,arguments)}function xO(e,t){return e&&"none"!==e?e:t}var wO=e=>{var{dataKey:t,name:r,stroke:n,fill:i,legendType:a,hide:o}=e;return[{inactive:o,dataKey:t,type:a,color:xO(n,i),value:xi(r,t),payload:e}]};function OO(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,tooltipType:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,nameKey:void 0,dataKey:t,name:xi(a,t),hide:o,type:l,color:xO(r,i),unit:""}}}function PO(e){var{points:r,props:i}=e,{dot:a,dataKey:o}=i;if(!a)return null;var l=_(i,!1),c=_(a,!0),s=r.map(((e,r)=>{var i=mO(mO(mO({key:"dot-".concat(r),r:3},l),c),{},{dataKey:o,cx:e.x,cy:e.y,index:r,payload:e});return function(e,r){return t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?e(r):t.createElement(Mb,bO({},r,{className:n("recharts-radar-dot","boolean"!=typeof e?e.className:"")}))}(a,i)}));return t.createElement(F,{className:"recharts-radar-dots"},s)}function EO(e){var{points:r,props:n,showLabels:i}=e;if(null==r)return null;var a,{shape:o,isRange:l,baseLinePoints:c,connectNulls:s}=n;return a=t.isValidElement(o)?t.cloneElement(o,mO(mO({},n),{},{points:r})):"function"==typeof o?o(mO(mO({},n),{},{points:r})):t.createElement(Sb,bO({},_(n,!0),{onMouseEnter:e=>{var{onMouseEnter:t}=n;t&&t(n,e)},onMouseLeave:e=>{var{onMouseLeave:t}=n;t&&t(n,e)},points:r,baseLinePoints:l?c:null,connectNulls:s})),t.createElement(F,{className:"recharts-radar-polygon"},a,t.createElement(PO,{props:n,points:r}),i&&xb.renderCallByParent(n,r))}function AO(e){var{props:r,previousPointsRef:n}=e,{points:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c,onAnimationEnd:s,onAnimationStart:u}=r,f=n.current,d=Iw(r,"recharts-radar-"),[p,h]=(0,t.useState)(!0),y=(0,t.useCallback)((()=>{"function"==typeof s&&s(),h(!1)}),[s]),v=(0,t.useCallback)((()=>{"function"==typeof u&&u(),h(!0)}),[u]);return t.createElement(Kl,{begin:o,duration:l,isActive:a,easing:c,from:{t:0},to:{t:1},key:"radar-".concat(d),onAnimationEnd:y,onAnimationStart:v},(e=>{var{t:a}=e,o=f&&f.length/i.length,l=1===a?i:i.map(((e,t)=>{var r=f&&f[Math.floor(t*o)];if(r){var n=g(r.x,e.x),i=g(r.y,e.y);return mO(mO({},e),{},{x:n(a),y:i(a)})}var l=g(e.cx,e.x),c=g(e.cy,e.y);return mO(mO({},e),{},{x:l(a),y:c(a)})}));return a>0&&(n.current=l),t.createElement(EO,{points:l,props:r,showLabels:!p})}))}function jO(e){var{points:r,isAnimationActive:n,isRange:i}=e,a=(0,t.useRef)(void 0),o=a.current;return!(n&&r&&r.length)||i||o&&o===r?t.createElement(EO,{points:r,props:e,showLabels:!0}):t.createElement(AO,{props:e,previousPointsRef:a})}var SO={angleAxisId:0,radiusAxisId:0,hide:!1,activeDot:!0,dot:!1,legendType:"rect",isAnimationActive:!mo.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"};class kO extends t.PureComponent{render(){var{hide:e,className:r,points:i}=this.props;if(e)return null;var a=n("recharts-radar",r);return t.createElement(t.Fragment,null,t.createElement(F,{className:a},t.createElement(jO,this.props)),t.createElement(tO,{points:i,mainColor:xO(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}}function MO(e){var r=Li(),n=Ue((t=>yO(t,e.radiusAxisId,e.angleAxisId,r,e.dataKey)));return t.createElement(kO,bO({},e,{points:null==n?void 0:n.points,baseLinePoints:null==n?void 0:n.baseLinePoints,isRange:null==n?void 0:n.isRange}))}class TO extends t.PureComponent{render(){return t.createElement(t.Fragment,null,t.createElement(rO,{data:void 0,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:this.props.angleAxisId,radiusAxisId:this.props.radiusAxisId,stackId:void 0,barSize:void 0,type:"radar"}),t.createElement(Dw,{legendPayload:wO(this.props)}),t.createElement(Mw,{fn:OO,args:this.props}),t.createElement(MO,this.props))}}function CO(){return CO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},CO.apply(null,arguments)}function DO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function IO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?DO(Object(r),!0).forEach((function(t){NO(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):DO(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function NO(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _O(e){return"string"==typeof e?parseInt(e,10):e}function RO(e,t){var r="".concat(t.cx||e.cx),n=Number(r),i="".concat(t.cy||e.cy),a=Number(i);return IO(IO(IO({},t),e),{},{cx:n,cy:a})}function LO(e){return t.createElement(Aw,CO({shapeType:"sector",propTransformer:RO},e))}gO(TO,"displayName","Radar"),gO(TO,"defaultProps",SO);var KO=()=>{var e=ze();return(0,t.useEffect)((()=>(e(ew()),()=>{e(tw())}))),null},zO=["children"];var BO=()=>{},FO=(0,t.createContext)({addErrorBar:BO,removeErrorBar:BO}),WO={data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0},UO=(0,t.createContext)(WO);function XO(e){var{children:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,zO);return t.createElement(UO.Provider,{value:n},r)}var VO=e=>{var{children:r,xAxisId:n,yAxisId:i,zAxisId:a,dataKey:o,data:l,stackId:c,hide:s,type:u,barSize:f}=e,[d,p]=t.useState([]),h=(0,t.useCallback)((e=>{p((t=>[...t,e]))}),[p]),y=(0,t.useCallback)((e=>{p((t=>t.filter((t=>t!==e))))}),[p]),v=Li();return t.createElement(FO.Provider,{value:{addErrorBar:h,removeErrorBar:y}},t.createElement(uw,{type:u,data:l,xAxisId:n,yAxisId:i,zAxisId:a,dataKey:o,errorBars:d,stackId:c,hide:s,barSize:f,isPanorama:v}),r)};function $O(e){var{addErrorBar:r,removeErrorBar:n}=(0,t.useContext)(FO);return(0,t.useEffect)((()=>(r(e),()=>{n(e)})),[r,n,e]),null}var HO=()=>Ue(cm),qO=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function YO(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function GO(){return GO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},GO.apply(null,arguments)}function ZO(e){var{direction:r,width:n,dataKey:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c}=e,s=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,qO),u=_(s,!1),{data:f,dataPointFormatter:d,xAxisId:p,yAxisId:h,errorBarOffset:y}=(0,t.useContext)(UO),v=(e=>{var t=Li();return Ue((r=>Hy(r,"xAxis",e,t)))})(p),m=(e=>{var t=Li();return Ue((r=>Hy(r,"yAxis",e,t)))})(h);if(null==(null==v?void 0:v.scale)||null==(null==m?void 0:m.scale)||null==f)return null;if("x"===r&&"number"!==v.type)return null;var g=f.map((e=>{var{x:s,y:f,value:p,errorVal:h}=d(e,i,r);if(!h)return null;var g,b,x=[];if(Array.isArray(h)?[g,b]=h:g=b=h,"x"===r){var{scale:w}=v,O=f+y,P=O+n,E=O-n,A=w(p-g),j=w(p+b);x.push({x1:j,y1:P,x2:j,y2:E}),x.push({x1:A,y1:O,x2:j,y2:O}),x.push({x1:A,y1:P,x2:A,y2:E})}else if("y"===r){var{scale:S}=m,k=s+y,M=k-n,T=k+n,C=S(p-g),D=S(p+b);x.push({x1:M,y1:D,x2:T,y2:D}),x.push({x1:k,y1:C,x2:k,y2:D}),x.push({x1:M,y1:C,x2:T,y2:C})}var I="".concat(s+y,"px ").concat(f+y,"px");return t.createElement(F,GO({className:"recharts-errorBar",key:"bar-".concat(x.map((e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2))))},u),x.map((e=>{var r=a?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return t.createElement(Kl,{from:{transform:"scaleY(0)",transformOrigin:I},to:{transform:"scaleY(1)",transformOrigin:I},begin:o,easing:c,isActive:a,duration:l,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:I}},t.createElement("line",GO({},e,{style:r})))})))}));return t.createElement(F,{className:"recharts-errorBars"},g)}var JO=(0,t.createContext)(void 0);function QO(e){var{direction:r,children:n}=e;return t.createElement(JO.Provider,{value:r},n)}var eP={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function tP(e){var r,n,i=(r=e.direction,n=(0,t.useContext)(JO),null!=r?r:null!=n?n:"x"),{width:a,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:s}=cl(e,eP);return t.createElement(t.Fragment,null,t.createElement($O,{dataKey:e.dataKey,direction:i}),t.createElement(ZO,GO({},e,{direction:i,width:a,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:s})))}class rP extends t.Component{render(){return t.createElement(tP,this.props)}}YO(rP,"defaultProps",eP),YO(rP,"displayName","ErrorBar");var nP="Invariant failed";var iP=["x","y"];function aP(){return aP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},aP.apply(null,arguments)}function oP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function lP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?oP(Object(r),!0).forEach((function(t){cP(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oP(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function cP(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sP(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,iP),a="".concat(r),o=parseInt(a,10),l="".concat(n),c=parseInt(l,10),s="".concat(t.height||i.height),u=parseInt(s,10),f="".concat(t.width||i.width),d=parseInt(f,10);return lP(lP(lP(lP(lP({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:u,width:d,name:t.name,radius:t.radius})}function uP(e){return t.createElement(Aw,aP({shapeType:"rectangle",propTransformer:sP,activeClassName:"recharts-active-bar"},e))}var fP=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if(d(e))return e;var i=d(r)||w(r);return i?e(r,n):(i||function(e){if(!e)throw new Error(nP)}(!1),t)}};function dP(e,t){var r,n,i=Ue((t=>dh(t,e))),a=Ue((e=>hh(e,t))),o=null!==(r=null==i?void 0:i.allowDataOverflow)&&void 0!==r?r:fh.allowDataOverflow,l=null!==(n=null==a?void 0:a.allowDataOverflow)&&void 0!==n?n:ph.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function pP(e){var{xAxisId:r,yAxisId:n,clipPathId:i}=e,a=Ui(),{needClipX:o,needClipY:l,needClip:c}=dP(r,n);if(!c)return null;var{left:s,top:u,width:f,height:d}=a;return t.createElement("clipPath",{id:"clipPath-".concat(i)},t.createElement("rect",{x:o?s:s-f/2,y:l?u:u-d/2,width:o?f:2*f,height:l?d:2*d}))}var hP=["onMouseEnter","onMouseLeave","onClick"],yP=["value","background","tooltipPosition"],vP=["onMouseEnter","onClick","onMouseLeave"];function mP(){return mP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},mP.apply(null,arguments)}function gP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function bP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gP(Object(r),!0).forEach((function(t){xP(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gP(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function xP(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wP(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var OP=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:xi(r,t),payload:e}]};function PP(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:xi(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function EP(e){var r=Ue(lm),{data:n,dataKey:i,background:a,allOtherBarProps:o}=e,{onMouseEnter:l,onMouseLeave:c,onClick:s}=o,u=wP(o,hP),f=jw(l,i),d=Sw(c),p=kw(s,i);if(!a||null==n)return null;var h=_(a,!1);return t.createElement(t.Fragment,null,n.map(((e,n)=>{var{value:o,background:l,tooltipPosition:c}=e,s=wP(e,yP);if(!l)return null;var y=f(e,n),v=d(e,n),m=p(e,n),g=bP(bP(bP(bP(bP({option:a,isActive:String(n)===r},s),{},{fill:"#eee"},l),h),k(u,e,n)),{},{onMouseEnter:y,onMouseLeave:v,onClick:m,dataKey:i,index:n,className:"recharts-bar-background-rectangle"});return t.createElement(uP,mP({key:"background-bar-".concat(n)},g))})))}function AP(e){var{data:r,props:n,showLabels:i}=e,a=_(n,!1),{shape:o,dataKey:l,activeBar:c}=n,s=Ue(lm),u=Ue(sm),{onMouseEnter:f,onClick:d,onMouseLeave:p}=n,h=wP(n,vP),y=jw(f,l),v=Sw(p),m=kw(d,l);return r?t.createElement(t.Fragment,null,r.map(((e,r)=>{var n=c&&String(r)===s&&(null==u||l===u),i=n?c:o,f=bP(bP(bP({},a),e),{},{isActive:n,option:i,index:r,dataKey:l});return t.createElement(F,mP({className:"recharts-bar-rectangle"},k(h,e,r),{onMouseEnter:y(e,r),onMouseLeave:v(e,r),onClick:m(e,r),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(r)}),t.createElement(uP,f))})),i&&xb.renderCallByParent(n,r)):null}function jP(e){var{props:r,previousRectanglesRef:n}=e,{data:i,layout:a,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:s,onAnimationEnd:u,onAnimationStart:f}=r,d=n.current,p=Iw(r,"recharts-bar-"),[h,y]=(0,t.useState)(!1),v=(0,t.useCallback)((()=>{"function"==typeof u&&u(),y(!1)}),[u]),m=(0,t.useCallback)((()=>{"function"==typeof f&&f(),y(!0)}),[f]);return t.createElement(Kl,{begin:l,duration:c,isActive:o,easing:s,from:{t:0},to:{t:1},onAnimationEnd:v,onAnimationStart:m,key:p},(e=>{var{t:o}=e,l=1===o?i:i.map(((e,t)=>{var r=d&&d[t];if(r){var n=g(r.x,e.x),i=g(r.y,e.y),l=g(r.width,e.width),c=g(r.height,e.height);return bP(bP({},e),{},{x:n(o),y:i(o),width:l(o),height:c(o)})}if("horizontal"===a){var s=g(0,e.height)(o);return bP(bP({},e),{},{y:e.y+e.height-s,height:s})}var u=g(0,e.width)(o);return bP(bP({},e),{},{width:u})}));return o>0&&(n.current=l),t.createElement(F,null,t.createElement(AP,{props:r,data:l,showLabels:!h}))}))}function SP(e){var{data:r,isAnimationActive:n}=e,i=(0,t.useRef)(null);return n&&r&&r.length&&(null==i.current||i.current!==r)?t.createElement(jP,{previousRectanglesRef:i,props:e}):t.createElement(AP,{props:e,data:r,showLabels:!0})}var kP=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:ni(e,t)}};class MP extends t.PureComponent{constructor(){super(...arguments),xP(this,"id",y("recharts-bar-"))}render(){var{hide:e,data:r,dataKey:i,className:a,xAxisId:o,yAxisId:l,needClip:c,background:s,id:u,layout:f}=this.props;if(e)return null;var d=n("recharts-bar",a),p=w(u)?this.id:u;return t.createElement(F,{className:d},c&&t.createElement("defs",null,t.createElement(pP,{clipPathId:p,xAxisId:o,yAxisId:l})),t.createElement(F,{className:"recharts-bar-rectangles",clipPath:c?"url(#clipPath-".concat(p,")"):null},t.createElement(EP,{data:r,dataKey:i,background:s,allOtherBarProps:this.props}),t.createElement(SP,this.props)),t.createElement(QO,{direction:"horizontal"===f?"y":"x"},this.props.children))}}var TP={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!mo.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function CP(e){var r,{xAxisId:n,yAxisId:i,hide:a,legendType:o,minPointSize:l,activeBar:c,animationBegin:s,animationDuration:u,animationEasing:f,isAnimationActive:d}=cl(e,TP),{needClip:p}=dP(n,i),h=qi(),y=Li(),v=(0,t.useMemo)((()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:l,stackId:fi(e.stackId)})),[e.barSize,e.dataKey,e.maxBarSize,l,e.stackId]),m=I(e.children,bg),g=Ue((e=>YP(e,n,i,y,v,m)));if("vertical"!==h&&"horizontal"!==h)return null;var b=null==g?void 0:g[0];return r=null==b||null==b.height||null==b.width?0:"vertical"===h?b.height/2:b.width/2,t.createElement(XO,{xAxisId:n,yAxisId:i,data:g,dataPointFormatter:kP,errorBarOffset:r},t.createElement(MP,mP({},e,{layout:h,needClip:p,data:g,xAxisId:n,yAxisId:i,hide:a,legendType:o,minPointSize:l,activeBar:c,animationBegin:s,animationDuration:u,animationEasing:f,isAnimationActive:d})))}class DP extends t.PureComponent{render(){return t.createElement(VO,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},t.createElement(KO,null),t.createElement(Cw,{legendPayload:OP(this.props)}),t.createElement(Mw,{fn:PP,args:this.props}),t.createElement(CP,this.props))}}function IP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function NP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?IP(Object(r),!0).forEach((function(t){_P(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):IP(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function _P(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}xP(DP,"displayName","Bar"),xP(DP,"defaultProps",TP);var RP=(e,t,r,n,i)=>i,LP=(e,t,r)=>{var n=null!=r?r:e;if(!w(n))return v(n,t,0)},KP=Ge([Hi,wh,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],((e,t,r,n,i)=>t.filter((t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n)).filter((e=>e.isPanorama===i)).filter((e=>!1===e.hide)).filter((e=>"bar"===e.type))));function zP(e){return null!=e.stackId&&null!=e.dataKey}var BP=(e,t,r)=>{var n=e.filter(zP),i=e.filter((e=>null==e.stackId)),a=n.reduce(((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e)),{}),o=Object.entries(a).map((e=>{var[n,i]=e,a=i.map((e=>e.dataKey));return{stackId:n,dataKeys:a,barSize:LP(t,r,i[0].barSize)}})),l=i.map((e=>({stackId:void 0,dataKeys:[e.dataKey].filter((e=>null!=e)),barSize:LP(t,r,e.barSize)})));return[...o,...l]},FP=Ge([KP,_p,(e,t,r)=>"horizontal"===Hi(e)?Ly(e,"xAxis",t):Ly(e,"yAxis",r)],BP),WP=(e,t,r,n)=>{var i,a;return"horizontal"===Hi(e)?(i=Hy(e,"xAxis",t,n),a=$y(e,"xAxis",t,n)):(i=Hy(e,"yAxis",r,n),a=$y(e,"yAxis",r,n)),gi(i,a)};var UP=(e,t,r,n,i,a,o)=>{var l=w(o)?t:o,c=function(e,t,r,n,i){var a=n.length;if(!(a<1)){var o,l=v(e,r,0,!0),c=[];if(Wo(n[0].barSize)){var s=!1,u=r/a,f=n.reduce(((e,t)=>e+(t.barSize||0)),0);(f+=(a-1)*l)>=r&&(f-=(a-1)*l,l=0),f>=r&&u>0&&(s=!0,f=a*(u*=.9));var d={offset:((r-f)/2|0)-l,size:0};o=n.reduce(((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:d.offset+d.size+l,size:s?u:null!==(r=t.barSize)&&void 0!==r?r:0}}];return d=n[n.length-1].position,n}),c)}else{var p=v(t,r,0,!0);r-2*p-(a-1)*l<=0&&(l=0);var h=(r-2*p-(a-1)*l)/a;h>1&&(h>>=0);var y=Wo(i)?Math.min(h,i):h;o=n.reduce(((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:p+(h+l)*r+(h-y)/2,size:y}}]),c)}return o}}(r,n,i!==a?i:a,e,l);return i!==a&&null!=c&&(c=c.map((e=>NP(NP({},e),{},{position:NP(NP({},e.position),{},{offset:e.position.offset-i/2})})))),c},XP=Ge([FP,Dp,Ip,Np,(e,t,r,n,i)=>{var a,o,l,c,s=Hi(e),u=Dp(e),{maxBarSize:f}=i,d=w(f)?u:f;return"horizontal"===s?(l=Hy(e,"xAxis",t,n),c=$y(e,"xAxis",t,n)):(l=Hy(e,"yAxis",r,n),c=$y(e,"yAxis",r,n)),null!==(a=null!==(o=gi(l,c,!0))&&void 0!==o?o:d)&&void 0!==a?a:0},WP,(e,t,r,n,i)=>i.maxBarSize],UP),VP=Ge([XP,RP],((e,t)=>{if(null!=e){var r=e.find((e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey)));if(null!=r)return r.position}})),$P=(e,t)=>{if(e&&null!=(null==t?void 0:t.dataKey)){var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:i}=n;if(i)return i.find((e=>e.key===t.dataKey))}}}},HP=Ge([wh,RP],((e,t)=>{if(e.some((e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId)))return t})),qP=Ge([(e,t,r,n)=>"horizontal"===Hi(e)?Lh(e,"yAxis",r,n):Lh(e,"xAxis",t,n),RP],$P),YP=Ge([Ii,(e,t,r,n)=>Hy(e,"xAxis",t,n),(e,t,r,n)=>Hy(e,"yAxis",r,n),(e,t,r,n)=>$y(e,"xAxis",t,n),(e,t,r,n)=>$y(e,"yAxis",r,n),VP,Hi,sp,WP,qP,HP,(e,t,r,n,i,a)=>a],((e,t,r,n,i,a,o,l,c,f,d,p)=>{var{chartData:h,dataStartIndex:y,dataEndIndex:v}=l;if(null!=d&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=c){var m,{data:g}=d;if(null!=(m=null!=g&&g.length>0?g:null==h?void 0:h.slice(y,v+1)))return function(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:c,yAxisTicks:f,stackedData:d,displayedData:p,offset:h,cells:y}=e,v="horizontal"===t?l:o,m=d?v.scale.domain():null,g=hi({numericAxis:v});return p.map(((e,p)=>{var v,b,x,w,O,P;d?v=ci(d[p],m):(v=ni(e,r),Array.isArray(v)||(v=[g,v]));var E=fP(n,0)(v[1],p);if("horizontal"===t){var A,[j,S]=[l.scale(v[0]),l.scale(v[1])];b=pi({axis:o,ticks:c,bandSize:a,offset:i.offset,entry:e,index:p}),x=null!==(A=null!=S?S:j)&&void 0!==A?A:void 0,w=i.size;var k=j-S;if(O=u(k)?0:k,P={x:b,y:h.top,width:w,height:h.height},Math.abs(E)>0&&Math.abs(O)<Math.abs(E)){var M=s(O||E)*(Math.abs(E)-Math.abs(O));x-=M,O+=M}}else{var[T,C]=[o.scale(v[0]),o.scale(v[1])];b=T,x=pi({axis:l,ticks:f,bandSize:a,offset:i.offset,entry:e,index:p}),w=C-T,O=i.size,P={x:h.left,y:x,width:h.width,height:O},Math.abs(E)>0&&Math.abs(w)<Math.abs(E)&&(w+=s(w||E)*(Math.abs(E)-Math.abs(w)))}return bP(bP({},e),{},{x:b,y:x,width:w,height:O,value:d?v:v[1],payload:e,background:P,tooltipPosition:{x:b+w/2,y:x+O/2}},y&&y[p]&&y[p].props)}))}({layout:o,barSettings:d,pos:a,bandSize:c,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:f,displayedData:m,offset:e,cells:p})}}));function GP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ZP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?GP(Object(r),!0).forEach((function(t){JP(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):GP(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function JP(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var QP=Ge([(e,t)=>Yp(e,t),(e,t)=>Xb(e,"radiusAxis",t)],((e,t)=>{if(null!=e&&null!=t)return ZP(ZP({},e),{},{scale:t})})),eE=(e,t,r,n)=>Hb(e,"radiusAxis",t,n),tE=Ge([(e,t,r)=>qp(e,r),(e,t,r)=>Xb(e,"angleAxis",r)],((e,t)=>{if(null!=e&&null!=t)return ZP(ZP({},e),{},{scale:t})})),rE=(e,t,r,n)=>$b(e,"angleAxis",r,n),nE=Ge([Tb,(e,t,r,n)=>n],((e,t)=>{if(e.some((e=>"radialBar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId)))return t})),iE=Ge([Hi,QP,eE,tE,rE],((e,t,r,n,i)=>ii(e,"radiusAxis")?gi(t,r,!1):gi(n,i,!1))),aE=Ge([tE,QP,Hi],((e,t,r)=>{var n="radial"===r?e:t;if(null!=n&&null!=n.scale)return hi({numericAxis:n})})),oE=(e,t,r,n,i)=>n.maxBarSize,lE=Ge([Hi,Tb,(e,t,r,n,i)=>r,(e,t,r,n,i)=>t],((e,t,r,n)=>t.filter((t=>"centric"===e?t.angleAxisId===r:t.radiusAxisId===n)).filter((e=>!1===e.hide)).filter((e=>"radialBar"===e.type)))),cE=Ge([lE,_p,()=>{}],BP),sE=Ge([Hi,Dp,tE,rE,QP,eE,oE],((e,t,r,n,i,a,o)=>{var l,c,s,u,f=w(o)?t:o;return"centric"===e?null!==(s=null!==(u=gi(r,n,!0))&&void 0!==u?u:f)&&void 0!==s?s:0:null!==(l=null!==(c=gi(i,a,!0))&&void 0!==c?c:f)&&void 0!==l?l:0})),uE=Ge([cE,Dp,Ip,Np,sE,iE,oE],UP),fE=Ge([uE,nE],((e,t)=>{if(null!=e&&null!=t){var r=e.find((e=>e.stackId===t.stackId&&null!=t.dataKey&&e.dataKeys.includes(t.dataKey)));if(null!=r)return r.position}})),dE=Ge([Nb,Db,Rp],Rh),pE=Ge([(e,t,r)=>"centric"===Hi(e)?dE(e,"radiusAxis",t):dE(e,"angleAxis",r),nE],$P),hE=Ge([tE,rE,QP,eE,lp,nE,iE,Hi,aE,ih,(e,t,r,n,i)=>i,fE,pE],((e,t,r,n,i,a,o,l,c,u,f,d,p)=>{var{chartData:h,dataStartIndex:y,dataEndIndex:v}=i;if(null==a||null==r||null==e||null==h||null==o||null==d||"centric"!==l&&"radial"!==l||null==n)return[];var{dataKey:m,minPointSize:g}=a,{cx:b,cy:x,startAngle:w,endAngle:O}=u;return function(e){var{displayedData:t,stackedData:r,dataStartIndex:n,stackedDomain:i,dataKey:a,baseValue:o,layout:l,radiusAxis:c,radiusAxisTicks:u,bandSize:f,pos:d,angleAxis:p,minPointSize:h,cx:y,cy:v,angleAxisTicks:m,cells:g,startAngle:b,endAngle:x}=e;return(null!=t?t:[]).map(((e,t)=>{var w,O,P,E,A,j;if(r?w=ci(r[n+t],i):(w=ni(e,a),Array.isArray(w)||(w=[o,w])),"radial"===l){O=pi({axis:c,ticks:u,bandSize:f,offset:d.offset,entry:e,index:t}),A=p.scale(w[1]),E=p.scale(w[0]),P=(null!=O?O:0)+d.size;var S=A-E;if(Math.abs(h)>0&&Math.abs(S)<Math.abs(h))A+=s(S||h)*(Math.abs(h)-Math.abs(S));j={background:{cx:y,cy:v,innerRadius:O,outerRadius:P,startAngle:b,endAngle:x}}}else{O=c.scale(w[0]),P=c.scale(w[1]),A=(null!=(E=pi({axis:p,ticks:m,bandSize:f,offset:d.offset,entry:e,index:t}))?E:0)+d.size;var k=P-O;if(Math.abs(h)>0&&Math.abs(k)<Math.abs(h))P+=s(k||h)*(Math.abs(h)-Math.abs(k))}return wE(wE(wE({},e),j),{},{payload:e,value:r?w:w[1],cx:y,cy:v,innerRadius:O,outerRadius:P,startAngle:E,endAngle:A},g&&g[t]&&g[t].props)}))}({angleAxis:e,angleAxisTicks:t,bandSize:o,baseValue:c,cells:f,cx:b,cy:x,dataKey:m,dataStartIndex:y,displayedData:h.slice(y,v+1),endAngle:O,layout:l,minPointSize:g,pos:d,radiusAxis:r,radiusAxisTicks:n,stackedData:p,stackedDomain:p?("centric"===l?r:e).scale.domain():null,startAngle:w})})),yE=Ge([cp,(e,t)=>t],((e,t)=>{var{chartData:r,dataStartIndex:n,dataEndIndex:i}=e;if(null==r)return[];var a=r.slice(n,i+1);return 0===a.length?[]:a.map((e=>({type:t,value:e.name,color:e.fill,payload:e})))})),vE=["shape","activeShape","cornerRadius"],mE=["onMouseEnter","onClick","onMouseLeave"],gE=["value","background"];function bE(){return bE=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bE.apply(null,arguments)}function xE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function wE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xE(Object(r),!0).forEach((function(t){OE(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xE(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function OE(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function PE(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var EE=[];function AE(e){var{sectors:r,allOtherRadialBarProps:n,showLabels:i}=e,{shape:a,activeShape:o,cornerRadius:l}=n,c=PE(n,vE),s=_(c,!1),u=Ue(lm),{onMouseEnter:f,onClick:d,onMouseLeave:p}=n,h=PE(n,mE),y=jw(f,n.dataKey),v=Sw(p),m=kw(d,n.dataKey);return null==r?null:t.createElement(t.Fragment,null,r.map(((e,r)=>{var n=o&&u===String(r),i=y(e,r),f=v(e,r),d=m(e,r),p=wE(wE(wE(wE({},s),{},{cornerRadius:_O(l)},e),k(h,e,r)),{},{onMouseEnter:i,onMouseLeave:f,onClick:d,key:"sector-".concat(r),className:"recharts-radial-bar-sector ".concat(e.className),forceCornerRadius:c.forceCornerRadius,cornerIsExternal:c.cornerIsExternal,isActive:n,option:n?o:a});return t.createElement(LO,p)})),i&&xb.renderCallByParent(n,r))}function jE(e){var{props:r,previousSectorsRef:n}=e,{data:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c,onAnimationEnd:s,onAnimationStart:u}=r,f=Iw(r,"recharts-radialbar-"),d=n.current,[p,h]=(0,t.useState)(!0),y=(0,t.useCallback)((()=>{"function"==typeof s&&s(),h(!1)}),[s]),v=(0,t.useCallback)((()=>{"function"==typeof u&&u(),h(!0)}),[u]);return t.createElement(Kl,{begin:o,duration:l,isActive:a,easing:c,from:{t:0},to:{t:1},onAnimationStart:v,onAnimationEnd:y,key:f},(e=>{var{t:a}=e,o=1===a?i:(null!=i?i:EE).map(((e,t)=>{var r=d&&d[t];if(r){var n=g(r.startAngle,e.startAngle),i=g(r.endAngle,e.endAngle);return wE(wE({},e),{},{startAngle:n(a),endAngle:i(a)})}var{endAngle:o,startAngle:l}=e,c=g(l,o);return wE(wE({},e),{},{endAngle:c(a)})}));return a>0&&(n.current=null!=o?o:null),t.createElement(F,null,t.createElement(AE,{sectors:null!=o?o:EE,allOtherRadialBarProps:r,showLabels:!p}))}))}function SE(e){var{data:r=[],isAnimationActive:n}=e,i=(0,t.useRef)(null),a=i.current;return n&&r&&r.length&&(!a||a!==r)?t.createElement(jE,{props:e,previousSectorsRef:i}):t.createElement(AE,{sectors:r,allOtherRadialBarProps:e,showLabels:!0})}function kE(e){var r=Ue((t=>yE(t,e.legendType)));return t.createElement(Dw,{legendPayload:null!=r?r:[]})}function ME(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,name:a,hide:o,fill:l,tooltipType:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:l,nameKey:void 0,dataKey:t,name:xi(a,t),hide:o,type:c,color:l,unit:""}}}class TE extends t.PureComponent{renderBackground(e){if(null==e)return null;var{cornerRadius:r}=this.props,i=_(this.props.background,!1);return e.map(((e,a)=>{var{value:o,background:l}=e,c=PE(e,gE);if(!l)return null;var s=wE(wE(wE(wE(wE({cornerRadius:_O(r)},c),{},{fill:"#eee"},l),i),k(this.props,e,a)),{},{index:a,key:"sector-".concat(a),className:n("recharts-radial-bar-background-sector",null==i?void 0:i.className),option:l,isActive:!1});return t.createElement(LO,s)}))}render(){var{hide:e,data:r,className:i,background:a}=this.props;if(e)return null;var o=n("recharts-area",i);return t.createElement(F,{className:o},a&&t.createElement(F,{className:"recharts-radial-bar-background"},this.renderBackground(r)),t.createElement(F,{className:"recharts-radial-bar-sectors"},t.createElement(SE,this.props)))}}function CE(e){var r,n=I(e.children,bg),i={dataKey:e.dataKey,minPointSize:e.minPointSize,stackId:e.stackId,maxBarSize:e.maxBarSize,barSize:e.barSize},a=null!==(r=Ue((t=>hE(t,e.radiusAxisId,e.angleAxisId,i,n))))&&void 0!==r?r:EE;return t.createElement(t.Fragment,null,t.createElement(Mw,{fn:ME,args:wE(wE({},e),{},{data:a})}),t.createElement(TE,bE({},e,{data:a})))}var DE={angleAxisId:0,radiusAxisId:0,minPointSize:0,hide:!1,legendType:"rect",data:[],isAnimationActive:!mo.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease",forceCornerRadius:!1,cornerIsExternal:!1};class IE extends t.PureComponent{render(){var e,r,n;return t.createElement(t.Fragment,null,t.createElement(KO,null),t.createElement(rO,{data:void 0,dataKey:this.props.dataKey,hide:null!==(e=this.props.hide)&&void 0!==e?e:DE.hide,angleAxisId:null!==(r=this.props.angleAxisId)&&void 0!==r?r:DE.angleAxisId,radiusAxisId:null!==(n=this.props.radiusAxisId)&&void 0!==n?n:DE.radiusAxisId,stackId:this.props.stackId,barSize:this.props.barSize,type:"radialBar"}),t.createElement(kE,this.props),t.createElement(CE,this.props))}}function NE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?NE(Object(r),!0).forEach((function(t){RE(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):NE(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function RE(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}OE(IE,"displayName","RadialBar"),OE(IE,"defaultProps",DE);var LE=["Webkit","Moz","O","ms"],KE=e=>{var{chartData:r}=e,n=ze(),i=Li();return(0,t.useEffect)((()=>i?()=>{}:(n(Qm(r)),()=>{n(Qm(void 0))})),[r,n,i]),null},zE=e=>{var{computedData:r}=e,n=ze();return(0,t.useEffect)((()=>(n(tg(r)),()=>{n(Qm(void 0))})),[r,n]),null},BE=e=>e.chartData.chartData,FE=e=>{var{dataStartIndex:t,dataEndIndex:r}=e.chartData;return{startIndex:t,endIndex:r}},WE=(0,t.createContext)((()=>{})),UE={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},XE=$r({name:"brush",initialState:UE,reducers:{setBrushSettings:(e,t)=>null==t.payload?UE:t.payload}}),{setBrushSettings:VE}=XE.actions,$E=XE.reducer;function HE(){return HE=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},HE.apply(null,arguments)}function qE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function YE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?qE(Object(r),!0).forEach((function(t){GE(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qE(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function GE(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ZE(e){var{x:r,y:n,width:i,height:a,stroke:o}=e,l=Math.floor(n+a/2)-1;return t.createElement(t.Fragment,null,t.createElement("rect",{x:r,y:n,width:i,height:a,fill:o,stroke:"none"}),t.createElement("line",{x1:r+1,y1:l,x2:r+i-1,y2:l,fill:"none",stroke:"#fff"}),t.createElement("line",{x1:r+1,y1:l+2,x2:r+i-1,y2:l+2,fill:"none",stroke:"#fff"}))}function JE(e){var{travellerProps:r,travellerType:n}=e;return t.isValidElement(n)?t.cloneElement(n,r):"function"==typeof n?n(r):t.createElement(ZE,r)}function QE(e){var r,n,{otherProps:i,travellerX:a,id:o,onMouseEnter:l,onMouseLeave:c,onMouseDown:s,onTouchStart:u,onTravellerMoveKeyboard:f,onFocus:d,onBlur:p}=e,{y:h,x:y,travellerWidth:v,height:m,traveller:g,ariaLabel:b,data:x,startIndex:w,endIndex:O}=i,P=Math.max(a,y),E=YE(YE({},_(i,!1)),{},{x:P,y:h,width:v,height:m}),A=b||"Min value: ".concat(null===(r=x[w])||void 0===r?void 0:r.name,", Max value: ").concat(null===(n=x[O])||void 0===n?void 0:n.name);return t.createElement(F,{tabIndex:0,role:"slider","aria-label":A,"aria-valuenow":a,className:"recharts-brush-traveller",onMouseEnter:l,onMouseLeave:c,onMouseDown:s,onTouchStart:u,onKeyDown:e=>{["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),f("ArrowRight"===e.key?1:-1,o))},onFocus:d,onBlur:p,style:{cursor:"col-resize"}},t.createElement(JE,{travellerType:g,travellerProps:E}))}function eA(e){var{index:t,data:r,tickFormatter:n,dataKey:i}=e,a=ni(r[t],i,t);return"function"==typeof n?n(a,t):a}function tA(e,t){for(var r=0,n=e.length-1;n-r>1;){var i=Math.floor((r+n)/2);e[i]>t?n=i:r=i}return t>=e[n]?n:r}function rA(e){var{startX:t,endX:r,scaleValues:n,gap:i,data:a}=e,o=a.length-1,l=Math.min(t,r),c=Math.max(t,r),s=tA(n,l),u=tA(n,c);return{startIndex:s-s%i,endIndex:u===o?o:u-u%i}}function nA(e){var{x:r,y:n,width:i,height:a,fill:o,stroke:l}=e;return t.createElement("rect",{stroke:l,fill:o,x:r,y:n,width:i,height:a})}function iA(e){var{startIndex:r,endIndex:n,y:i,height:a,travellerWidth:o,stroke:l,tickFormatter:c,dataKey:s,data:u,startX:f,endX:d}=e,p={pointerEvents:"none",fill:l};return t.createElement(F,{className:"recharts-brush-texts"},t.createElement(qg,HE({textAnchor:"end",verticalAnchor:"middle",x:Math.min(f,d)-5,y:i+a/2},p),eA({index:r,tickFormatter:c,dataKey:s,data:u})),t.createElement(qg,HE({textAnchor:"start",verticalAnchor:"middle",x:Math.max(f,d)+o+5,y:i+a/2},p),eA({index:n,tickFormatter:c,dataKey:s,data:u})))}function aA(e){var{y:r,height:n,stroke:i,travellerWidth:a,startX:o,endX:l,onMouseEnter:c,onMouseLeave:s,onMouseDown:u,onTouchStart:f}=e,d=Math.min(o,l)+a,p=Math.max(Math.abs(l-o)-a,0);return t.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:c,onMouseLeave:s,onMouseDown:u,onTouchStart:f,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:d,y:r,width:p,height:n})}function oA(e){var{x:r,y:n,width:i,height:a,data:o,children:l,padding:c}=e;if(!(1===t.Children.count(l)))return null;var s=t.Children.only(l);return s?t.cloneElement(s,{x:r,y:n,width:i,height:a,margin:c,compact:!0,data:o}):null}var lA=e=>e.changedTouches&&!!e.changedTouches.length;class cA extends t.PureComponent{constructor(e){super(e),GE(this,"handleDrag",(e=>{this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.state.isTravellerMoving?this.handleTravellerMove(e):this.state.isSlideMoving&&this.handleSlideDrag(e)})),GE(this,"handleTouchMove",(e=>{null!=e.changedTouches&&e.changedTouches.length>0&&this.handleDrag(e.changedTouches[0])})),GE(this,"handleDragEnd",(()=>{this.setState({isTravellerMoving:!1,isSlideMoving:!1},(()=>{var{endIndex:e,onDragEnd:t,startIndex:r}=this.props;null==t||t({endIndex:e,startIndex:r})})),this.detachDragEndListener()})),GE(this,"handleLeaveWrapper",(()=>{(this.state.isTravellerMoving||this.state.isSlideMoving)&&(this.leaveTimer=window.setTimeout(this.handleDragEnd,this.props.leaveTimeOut))})),GE(this,"handleEnterSlideOrTraveller",(()=>{this.setState({isTextActive:!0})})),GE(this,"handleLeaveSlideOrTraveller",(()=>{this.setState({isTextActive:!1})})),GE(this,"handleSlideDragStart",(e=>{var t=lA(e)?e.changedTouches[0]:e;this.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:t.pageX}),this.attachDragEndListener()})),GE(this,"handleTravellerMoveKeyboard",((e,t)=>{var{data:r,gap:n}=this.props,{scaleValues:i,startX:a,endX:o}=this.state,l=this.state[t],c=i.indexOf(l);if(-1!==c){var s=c+e;if(!(-1===s||s>=i.length)){var u=i[s];"startX"===t&&u>=o||"endX"===t&&u<=a||this.setState({[t]:u},(()=>{this.props.onChange(rA({startX:this.state.startX,endX:this.state.endX,data:r,gap:n,scaleValues:i}))}))}}})),this.travellerDragStartHandlers={startX:this.handleTravellerDragStart.bind(this,"startX"),endX:this.handleTravellerDragStart.bind(this,"endX")},this.state={}}static getDerivedStateFromProps(e,t){var{data:r,width:n,x:i,travellerWidth:a,startIndex:o,endIndex:l,startIndexControlledFromProps:c,endIndexControlledFromProps:s}=e;if(r!==t.prevData)return YE({prevData:r,prevTravellerWidth:a,prevX:i,prevWidth:n},r&&r.length?(e=>{var{data:t,startIndex:r,endIndex:n,x:i,width:a,travellerWidth:o}=e;if(!t||!t.length)return{};var l=t.length,c=sc().domain(Zl()(0,l)).range([i,i+a-o]),s=c.domain().map((e=>c(e)));return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:s}})({data:r,width:n,x:i,travellerWidth:a,startIndex:o,endIndex:l}):{scale:null,scaleValues:null});if(t.scale&&(n!==t.prevWidth||i!==t.prevX||a!==t.prevTravellerWidth)){t.scale.range([i,i+n-a]);var u=t.scale.domain().map((e=>t.scale(e)));return{prevData:r,prevTravellerWidth:a,prevX:i,prevWidth:n,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:u}}if(t.scale&&!t.isSlideMoving&&!t.isTravellerMoving&&!t.isTravellerFocused&&!t.isTextActive){if(null!=c&&t.prevStartIndexControlledFromProps!==c)return{startX:t.scale(c),prevStartIndexControlledFromProps:c};if(null!=s&&t.prevEndIndexControlledFromProps!==s)return{endX:t.scale(s),prevEndIndexControlledFromProps:s}}return null}componentWillUnmount(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}attachDragEndListener(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}detachDragEndListener(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}handleSlideDrag(e){var{slideMoveStartX:t,startX:r,endX:n,scaleValues:i}=this.state,{x:a,width:o,travellerWidth:l,startIndex:c,endIndex:s,onChange:u,data:f,gap:d}=this.props,p=e.pageX-t;p>0?p=Math.min(p,a+o-l-n,a+o-l-r):p<0&&(p=Math.max(p,a-r,a-n));var h=rA({startX:r+p,endX:n+p,data:f,gap:d,scaleValues:i});h.startIndex===c&&h.endIndex===s||!u||u(h),this.setState({startX:r+p,endX:n+p,slideMoveStartX:e.pageX})}handleTravellerDragStart(e,t){var r=lA(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:r.pageX}),this.attachDragEndListener()}handleTravellerMove(e){var{brushMoveStartX:t,movingTravellerId:r,endX:n,startX:i,scaleValues:a}=this.state,o=this.state[r],{x:l,width:c,travellerWidth:s,onChange:u,gap:f,data:d}=this.props,p={startX:this.state.startX,endX:this.state.endX,data:d,gap:f,scaleValues:a},h=e.pageX-t;h>0?h=Math.min(h,l+c-s-o):h<0&&(h=Math.max(h,l-o)),p[r]=o+h;var y=rA(p),{startIndex:v,endIndex:m}=y;this.setState({[r]:o+h,brushMoveStartX:e.pageX},(()=>{var e;u&&(e=d.length-1,("startX"===r&&(n>i?v%f==0:m%f==0)||n<i&&m===e||"endX"===r&&(n>i?m%f==0:v%f==0)||n>i&&m===e)&&u(y))}))}render(){var{data:e,className:r,children:i,x:a,y:o,dy:l,width:c,height:s,alwaysShowText:u,fill:f,stroke:p,startIndex:h,endIndex:y,travellerWidth:v,tickFormatter:m,dataKey:g,padding:b}=this.props,{startX:x,endX:w,isTextActive:O,isSlideMoving:P,isTravellerMoving:E,isTravellerFocused:A}=this.state;if(!e||!e.length||!d(a)||!d(o)||!d(c)||!d(s)||c<=0||s<=0)return null;var j=n("recharts-brush",r),S=((e,t)=>{if(!e)return null;var r=e.replace(/(\w)/,(e=>e.toUpperCase())),n=LE.reduce(((e,n)=>_E(_E({},e),{},{[n+r]:t})),{});return n[e]=t,n})("userSelect","none"),k=o+(null!=l?l:0);return t.createElement(F,{className:j,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:S},t.createElement(nA,{x:a,y:k,width:c,height:s,fill:f,stroke:p}),t.createElement(Ki,null,t.createElement(oA,{x:a,y:k,width:c,height:s,data:e,padding:b},i)),t.createElement(aA,{y:k,height:s,stroke:p,travellerWidth:v,startX:x,endX:w,onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart}),t.createElement(QE,{travellerX:x,id:"startX",otherProps:YE(YE({},this.props),{},{y:k}),onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers.startX,onTouchStart:this.travellerDragStartHandlers.startX,onTravellerMoveKeyboard:this.handleTravellerMoveKeyboard,onFocus:()=>{this.setState({isTravellerFocused:!0})},onBlur:()=>{this.setState({isTravellerFocused:!1})}}),t.createElement(QE,{travellerX:w,id:"endX",otherProps:YE(YE({},this.props),{},{y:k}),onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers.endX,onTouchStart:this.travellerDragStartHandlers.endX,onTravellerMoveKeyboard:this.handleTravellerMoveKeyboard,onFocus:()=>{this.setState({isTravellerFocused:!0})},onBlur:()=>{this.setState({isTravellerFocused:!1})}}),(O||P||E||A||u)&&t.createElement(iA,{startIndex:h,endIndex:y,y:k,height:s,travellerWidth:v,stroke:p,tickFormatter:m,dataKey:g,data:e,startX:x,endX:w}))}}function sA(e){var r,n,i,a,o=ze(),l=Ue(BE),{startIndex:c,endIndex:s}=Ue(FE),u=(0,t.useContext)(WE),f=e.onChange,{startIndex:d,endIndex:p}=e;(0,t.useEffect)((()=>{o(eg({startIndex:d,endIndex:p}))}),[o,p,d]),r=Ue(Kp),n=Ue(Bp),i=Ue((e=>e.chartData.dataStartIndex)),a=Ue((e=>e.chartData.dataEndIndex)),(0,t.useEffect)((()=>{if(null!=r&&null!=i&&null!=a&&null!=n){var e={startIndex:i,endIndex:a};Xm.emit($m,r,e,n)}}),[a,i,n,r]);var h=(0,t.useCallback)((e=>{e.startIndex===c&&e.endIndex===s||(null==u||u(e),null==f||f(e),o(eg(e)))}),[f,u,o,c,s]),{x:y,y:v,width:m}=Ue(Bi),g={data:l,x:y,y:v,width:m,startIndex:c,endIndex:s,onChange:h};return t.createElement(cA,HE({},e,g,{startIndexControlledFromProps:null!=d?d:void 0,endIndexControlledFromProps:null!=p?p:void 0}))}function uA(e){var r=ze();return(0,t.useEffect)((()=>(r(VE(e)),()=>{r(VE(null))})),[r,e]),null}class fA extends t.PureComponent{render(){return t.createElement(t.Fragment,null,t.createElement(uA,{height:this.props.height,x:this.props.x,y:this.props.y,width:this.props.width,padding:this.props.padding}),t.createElement(sA,this.props))}}function dA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function pA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dA(Object(r),!0).forEach((function(t){hA(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dA(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function hA(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}GE(fA,"displayName","Brush"),GE(fA,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var yA=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return{x:Math.min(r,i),y:Math.min(n,a),width:Math.abs(i-r),height:Math.abs(a-n)}};class vA{static create(e){return new vA(e)}constructor(e){this.scale=e}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}hA(vA,"EPS",1e-4);var mA=e=>{var t=Object.keys(e).reduce(((t,r)=>pA(pA({},t),{},{[r]:vA.create(e[r])})),{});return pA(pA({},t),{},{apply(e){var{bandAware:r,position:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.fromEntries(Object.entries(e).map((e=>{var[i,a]=e;return[i,t[i].apply(a,{bandAware:r,position:n})]})))},isInRange:e=>Object.keys(e).every((r=>t[r].isInRange(e[r])))})};var gA=$r({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=Lt(e).dots.findIndex((e=>e===t.payload));-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=Lt(e).areas.findIndex((e=>e===t.payload));-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=Lt(e).lines.findIndex((e=>e===t.payload));-1!==r&&e.lines.splice(r,1)}}}),{addDot:bA,removeDot:xA,addArea:wA,removeArea:OA,addLine:PA,removeLine:EA}=gA.actions,AA=gA.reducer,jA=(0,t.createContext)(void 0),SA=e=>{var{children:r}=e,[n]=(0,t.useState)("".concat(y("recharts"),"-clip")),i=Ui();if(null==i)return null;var{left:a,top:o,height:l,width:c}=i;return t.createElement(jA.Provider,{value:n},t.createElement("defs",null,t.createElement("clipPath",{id:n},t.createElement("rect",{x:a,y:o,height:l,width:c}))),r)},kA=()=>(0,t.useContext)(jA);function MA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function TA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?MA(Object(r),!0).forEach((function(t){CA(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):MA(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function CA(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function DA(){return DA=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},DA.apply(null,arguments)}function IA(e){var r=ze();return(0,t.useEffect)((()=>(r(PA(e)),()=>{r(EA(e))}))),null}function NA(e){var{x:r,y:i,segment:a,xAxisId:o,yAxisId:l,shape:c,className:s,ifOverflow:f}=e,d=Li(),h=kA(),y=Ue((e=>dh(e,o))),v=Ue((e=>hh(e,l))),m=Ue((e=>jy(e,"xAxis",o,d))),g=Ue((e=>jy(e,"yAxis",l,d))),b=Fi(),x=p(r),w=p(i);if(!h||!b||null==y||null==v||null==m||null==g)return null;var O=((e,t,r,n,i,a,o,l,c)=>{var{x:s,y:f,width:d,height:p}=i;if(r){var{y:h}=c,y=e.y.apply(h,{position:a});if(u(y))return null;if("discard"===c.ifOverflow&&!e.y.isInRange(y))return null;var v=[{x:s+d,y},{x:s,y}];return"left"===l?v.reverse():v}if(t){var{x:m}=c,g=e.x.apply(m,{position:a});if(u(g))return null;if("discard"===c.ifOverflow&&!e.x.isInRange(g))return null;var b=[{x:g,y:f+p},{x:g,y:f}];return"top"===o?b.reverse():b}if(n){var{segment:x}=c,w=x.map((t=>e.apply(t,{position:a})));return"discard"===c.ifOverflow&&w.some((t=>!e.isInRange(t)))?null:w}return null})(mA({x:m,y:g}),x,w,a&&2===a.length,b,e.position,y.orientation,v.orientation,e);if(!O)return null;var[{x:P,y:E},{x:A,y:j}]=O,S=TA(TA({clipPath:"hidden"===f?"url(#".concat(h,")"):void 0},_(e,!0)),{},{x1:P,y1:E,x2:A,y2:j});return t.createElement(F,{className:n("recharts-reference-line",s)},((e,r)=>t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?e(r):t.createElement("line",DA({},r,{className:"recharts-reference-line-line"})))(c,S),cb.renderCallByParent(e,(e=>{var{x1:t,y1:r,x2:n,y2:i}=e;return yA({x:t,y:r},{x:n,y:i})})({x1:P,y1:E,x2:A,y2:j})))}function _A(e){return t.createElement(t.Fragment,null,t.createElement(IA,{yAxisId:e.yAxisId,xAxisId:e.xAxisId,ifOverflow:e.ifOverflow,x:e.x,y:e.y}),t.createElement(NA,e))}class RA extends t.Component{render(){return t.createElement(_A,this.props)}}function LA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function KA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?LA(Object(r),!0).forEach((function(t){zA(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):LA(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function zA(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function BA(){return BA=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},BA.apply(null,arguments)}CA(RA,"displayName","ReferenceLine"),CA(RA,"defaultProps",{ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function FA(e){var r=ze();return(0,t.useEffect)((()=>(r(bA(e)),()=>{r(xA(e))}))),null}function WA(e){var{x:r,y:i,r:a}=e,o=kA(),l=((e,t,r,n,i)=>{var a=p(e),o=p(t),l=Li(),c=Ue((e=>jy(e,"xAxis",r,l))),s=Ue((e=>jy(e,"yAxis",n,l)));if(!a||!o||null==c||null==s)return null;var u=mA({x:c,y:s}),f=u.apply({x:e,y:t},{bandAware:!0});return"discard"!==i||u.isInRange(f)?f:null})(r,i,e.xAxisId,e.yAxisId,e.ifOverflow);if(!l)return null;var{x:c,y:s}=l,{shape:u,className:f,ifOverflow:d}=e,h=KA(KA({clipPath:"hidden"===d?"url(#".concat(o,")"):void 0},_(e,!0)),{},{cx:c,cy:s});return t.createElement(F,{className:n("recharts-reference-dot",f)},((e,r)=>t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?e(r):t.createElement(Mb,BA({},r,{cx:r.cx,cy:r.cy,className:"recharts-reference-dot-dot"})))(u,h),cb.renderCallByParent(e,{x:c-a,y:s-a,width:2*a,height:2*a}))}function UA(e){var{x:r,y:n,r:i,ifOverflow:a,yAxisId:o,xAxisId:l}=e;return t.createElement(t.Fragment,null,t.createElement(FA,{y:n,x:r,r:i,yAxisId:o,xAxisId:l,ifOverflow:a}),t.createElement(WA,e))}class XA extends t.Component{render(){return t.createElement(UA,this.props)}}function VA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function $A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?VA(Object(r),!0).forEach((function(t){HA(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):VA(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function HA(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qA(){return qA=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},qA.apply(null,arguments)}zA(XA,"displayName","ReferenceDot"),zA(XA,"defaultProps",{ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});function YA(e){var r=ze();return(0,t.useEffect)((()=>(r(wA(e)),()=>{r(OA(e))}))),null}function GA(e){var{x1:r,x2:i,y1:a,y2:o,className:l,shape:c,xAxisId:s,yAxisId:u}=e,f=kA(),d=Li(),h=Ue((e=>jy(e,"xAxis",s,d))),y=Ue((e=>jy(e,"yAxis",u,d)));if(null==h||null==!y)return null;var v=p(r),m=p(i),g=p(a),b=p(o);if(!(v||m||g||b||c))return null;var x=((e,t,r,n,i,a,o)=>{var{x1:l,x2:c,y1:s,y2:u}=o;if(null==i||null==a)return null;var f=mA({x:i,y:a}),d={x:e?f.x.apply(l,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(s,{position:"start"}):f.y.rangeMin},p={x:t?f.x.apply(c,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return"discard"!==o.ifOverflow||f.isInRange(d)&&f.isInRange(p)?yA(d,p):null})(v,m,g,b,h,y,e);if(!x&&!c)return null;var w="hidden"===e.ifOverflow?"url(#".concat(f,")"):void 0;return t.createElement(F,{className:n("recharts-reference-area",l)},((e,r)=>t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?e(r):t.createElement(Wl,qA({},r,{className:"recharts-reference-area-rect"})))(c,$A($A({clipPath:w},_(e,!0)),x)),cb.renderCallByParent(e,x))}function ZA(e){return t.createElement(t.Fragment,null,t.createElement(YA,{yAxisId:e.yAxisId,xAxisId:e.xAxisId,ifOverflow:e.ifOverflow,x1:e.x1,x2:e.x2,y1:e.y1,y2:e.y2}),t.createElement(GA,e))}class JA extends t.Component{render(){return t.createElement(ZA,this.props)}}function QA(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function ej(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t){if(void 0!==r&&!0!==r(e[i]))return;n.push(e[i])}return n}function tj(e,t,r){return function(e){var{width:t,height:r}=e,n=function(e){return(e%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),i=n*Math.PI/180,a=Math.atan(r/t),o=i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i);return Math.abs(o)}({width:e.width+t.width,height:e.height+t.height},r)}function rj(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function nj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ij(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nj(Object(r),!0).forEach((function(t){aj(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nj(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function aj(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oj(e,t,r){var n,{tick:i,ticks:a,viewBox:o,minTickGap:l,orientation:c,interval:u,tickFormatter:f,unit:p,angle:h}=e;if(!a||!a.length||!i)return[];if(d(u)||mo.isSsr)return null!==(n=function(e,t){return ej(e,t+1)}(a,d(u)?u:0))&&void 0!==n?n:[];var y=[],v="top"===c||"bottom"===c?"width":"height",m=p&&"width"===v?jg(p,{fontSize:t,letterSpacing:r}):{width:0,height:0},g=(e,n)=>{var i="function"==typeof f?f(e.value,n):e.value;return"width"===v?tj(jg(i,{fontSize:t,letterSpacing:r}),m,h):jg(i,{fontSize:t,letterSpacing:r})[v]},b=a.length>=2?s(a[1].coordinate-a[0].coordinate):1,x=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(o,b,v);return"equidistantPreserveStart"===u?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:c}=t,s=0,u=1,f=l,d=function(){var t=null==n?void 0:n[s];if(void 0===t)return{v:ej(n,u)};var a,o=s,d=()=>(void 0===a&&(a=r(t,o)),a),p=t.coordinate,h=0===s||rj(e,p,d,f,c);h||(s=0,f=l,u+=1),h&&(f=p+e*(d()/2+i),s+=u)};u<=o.length;)if(a=d())return a.v;return[]}(b,x,g,a,l):(y="preserveStart"===u||"preserveStartEnd"===u?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:c,end:s}=t;if(a){var u=n[l-1],f=r(u,l-1),d=e*(u.coordinate+e*f/2-s);o[l-1]=u=ij(ij({},u),{},{tickCoord:d>0?u.coordinate-d*e:u.coordinate}),rj(e,u.tickCoord,(()=>f),c,s)&&(s=u.tickCoord-e*(f/2+i),o[l-1]=ij(ij({},u),{},{isShow:!0}))}for(var p=a?l-1:l,h=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var u=e*(a.coordinate-e*l()/2-c);o[t]=a=ij(ij({},a),{},{tickCoord:u<0?a.coordinate-u*e:a.coordinate})}else o[t]=a=ij(ij({},a),{},{tickCoord:a.coordinate});rj(e,a.tickCoord,l,c,s)&&(c=a.tickCoord+e*(l()/2+i),o[t]=ij(ij({},a),{},{isShow:!0}))},y=0;y<p;y++)h(y);return o}(b,x,g,a,l,"preserveStartEnd"===u):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:c}=t,s=function(t){var n,s=a[t],u=()=>(void 0===n&&(n=r(s,t)),n);if(t===o-1){var f=e*(s.coordinate+e*u()/2-c);a[t]=s=ij(ij({},s),{},{tickCoord:f>0?s.coordinate-f*e:s.coordinate})}else a[t]=s=ij(ij({},s),{},{tickCoord:s.coordinate});rj(e,s.tickCoord,u,l,c)&&(c=s.tickCoord-e*(u()/2+i),a[t]=ij(ij({},s),{},{isShow:!0}))},u=o-1;u>=0;u--)s(u);return a}(b,x,g,a,l),y.filter((e=>e.isShow)))}HA(JA,"displayName","ReferenceArea"),HA(JA,"defaultProps",{ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});var lj=["viewBox"],cj=["viewBox"];function sj(){return sj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},sj.apply(null,arguments)}function uj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function fj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?uj(Object(r),!0).forEach((function(t){pj(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uj(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function dj(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function pj(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class hj extends t.Component{constructor(e){super(e),this.tickRefs=t.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(e,t){var{viewBox:r}=e,n=dj(e,lj),i=this.props,{viewBox:a}=i,o=dj(i,cj);return!QA(r,a)||!QA(n,o)||!QA(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:c,width:s,height:u,orientation:f,tickSize:p,mirror:h,tickMargin:y}=this.props,v=h?-1:1,m=e.tickSize||p,g=d(e.tickCoord)?e.tickCoord:e.coordinate;switch(f){case"top":t=r=e.coordinate,o=(n=(i=c+ +!h*u)-v*m)-v*y,a=g;break;case"left":n=i=e.coordinate,a=(t=(r=l+ +!h*s)-v*m)-v*y,o=g;break;case"right":n=i=e.coordinate,a=(t=(r=l+ +h*s)+v*m)+v*y,o=g;break;default:t=r=e.coordinate,o=(n=(i=c+ +h*u)+v*m)+v*y,a=g}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:r,width:i,height:a,orientation:o,mirror:c,axisLine:s}=this.props,u=fj(fj(fj({},_(this.props,!1)),_(s,!1)),{},{fill:"none"});if("top"===o||"bottom"===o){var f=+("top"===o&&!c||"bottom"===o&&c);u=fj(fj({},u),{},{x1:e,y1:r+f*a,x2:e+i,y2:r+f*a})}else{var d=+("left"===o&&!c||"right"===o&&c);u=fj(fj({},u),{},{x1:e+d*i,y1:r,x2:e+d*i,y2:r+a})}return t.createElement("line",sj({},u,{className:n("recharts-cartesian-axis-line",l()(s,"className"))}))}static renderTickItem(e,r,i){var a,o=n(r.className,"recharts-cartesian-axis-tick-value");if(t.isValidElement(e))a=t.cloneElement(e,fj(fj({},r),{},{className:o}));else if("function"==typeof e)a=e(fj(fj({},r),{},{className:o}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(l=n(l,e.className)),a=t.createElement(qg,sj({},r,{className:l}),i)}return a}renderTicks(e,r){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:a,stroke:o,tick:c,tickFormatter:s,unit:u}=this.props,f=oj(fj(fj({},this.props),{},{ticks:i}),e,r),d=this.getTickTextAnchor(),p=this.getTickVerticalAnchor(),h=_(this.props,!1),y=_(c,!1),v=fj(fj({},h),{},{fill:"none"},_(a,!1)),m=f.map(((e,r)=>{var{line:i,tick:m}=this.getTickLineCoord(e),g=fj(fj(fj(fj({textAnchor:d,verticalAnchor:p},h),{},{stroke:"none",fill:o},y),m),{},{index:r,payload:e,visibleTicksCount:f.length,tickFormatter:s});return t.createElement(F,sj({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},k(this.props,e,r)),a&&t.createElement("line",sj({},v,i,{className:n("recharts-cartesian-axis-tick-line",l()(a,"className"))})),c&&hj.renderTickItem(c,g,"".concat("function"==typeof s?s(e.value,r):e.value).concat(u||"")))}));return m.length>0?t.createElement("g",{className:"recharts-cartesian-axis-ticks"},m):null}render(){var{axisLine:e,width:r,height:i,className:a,hide:o}=this.props;if(o)return null;var{ticks:l}=this.props;return null!=r&&r<=0||null!=i&&i<=0?null:t.createElement(F,{className:n("recharts-cartesian-axis",a),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;n===this.state.fontSize&&i===this.state.letterSpacing||this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),cb.renderCallByParent(this.props))}}pj(hj,"displayName","CartesianAxis"),pj(hj,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var yj=["x1","y1","x2","y2","key"],vj=["offset"],mj=["xAxisId","yAxisId"],gj=["xAxisId","yAxisId"];function bj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function xj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?bj(Object(r),!0).forEach((function(t){wj(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bj(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function wj(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Oj(){return Oj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Oj.apply(null,arguments)}function Pj(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var Ej=e=>{var{fill:r}=e;if(!r||"none"===r)return null;var{fillOpacity:n,x:i,y:a,width:o,height:l,ry:c}=e;return t.createElement("rect",{x:i,y:a,ry:c,width:o,height:l,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function Aj(e,r){var n;if(t.isValidElement(e))n=t.cloneElement(e,r);else if("function"==typeof e)n=e(r);else{var{x1:i,y1:a,x2:o,y2:l,key:c}=r,s=Pj(r,yj),u=_(s,!1),{offset:f}=u,d=Pj(u,vj);n=t.createElement("line",Oj({},d,{x1:i,y1:a,x2:o,y2:l,fill:"none",key:c}))}return n}function jj(e){var{x:r,width:n,horizontal:i=!0,horizontalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,c=Pj(e,mj),s=a.map(((e,t)=>{var a=xj(xj({},c),{},{x1:r,y1:e,x2:r+n,y2:e,key:"line-".concat(t),index:t});return Aj(i,a)}));return t.createElement("g",{className:"recharts-cartesian-grid-horizontal"},s)}function Sj(e){var{y:r,height:n,vertical:i=!0,verticalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,c=Pj(e,gj),s=a.map(((e,t)=>{var a=xj(xj({},c),{},{x1:e,y1:r,x2:e,y2:r+n,key:"line-".concat(t),index:t});return Aj(i,a)}));return t.createElement("g",{className:"recharts-cartesian-grid-vertical"},s)}function kj(e){var{horizontalFill:r,fillOpacity:n,x:i,y:a,width:o,height:l,horizontalPoints:c,horizontal:s=!0}=e;if(!s||!r||!r.length)return null;var u=c.map((e=>Math.round(e+a-a))).sort(((e,t)=>e-t));a!==u[0]&&u.unshift(0);var f=u.map(((e,c)=>{var s=!u[c+1]?a+l-e:u[c+1]-e;if(s<=0)return null;var f=c%r.length;return t.createElement("rect",{key:"react-".concat(c),y:e,x:i,height:s,width:o,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})}));return t.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function Mj(e){var{vertical:r=!0,verticalFill:n,fillOpacity:i,x:a,y:o,width:l,height:c,verticalPoints:s}=e;if(!r||!n||!n.length)return null;var u=s.map((e=>Math.round(e+a-a))).sort(((e,t)=>e-t));a!==u[0]&&u.unshift(0);var f=u.map(((e,r)=>{var s=!u[r+1]?a+l-e:u[r+1]-e;if(s<=0)return null;var f=r%n.length;return t.createElement("rect",{key:"react-".concat(r),x:e,y:o,width:s,height:c,stroke:"none",fill:n[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})}));return t.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var Tj=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return ai(oj(xj(xj(xj({},hj.defaultProps),r),{},{ticks:oi(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},Cj=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return ai(oj(xj(xj(xj({},hj.defaultProps),r),{},{ticks:oi(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},Dj={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function Ij(e){var r=Xi(),n=Vi(),i=Ui(),a=xj(xj({},cl(e,Dj)),{},{x:d(e.x)?e.x:i.left,y:d(e.y)?e.y:i.top,width:d(e.width)?e.width:i.width,height:d(e.height)?e.height:i.height}),{xAxisId:o,yAxisId:l,x:c,y:s,width:u,height:f,syncWithTicks:p,horizontalValues:h,verticalValues:y}=a,v=Li(),m=Ue((e=>Wy(e,"xAxis",o,v))),g=Ue((e=>Wy(e,"yAxis",l,v)));if(!d(u)||u<=0||!d(f)||f<=0||!d(c)||c!==+c||!d(s)||s!==+s)return null;var b=a.verticalCoordinatesGenerator||Tj,x=a.horizontalCoordinatesGenerator||Cj,{horizontalPoints:w,verticalPoints:O}=a;if(!(w&&w.length||"function"!=typeof x)){var P=h&&h.length,E=x({yAxis:g?xj(xj({},g),{},{ticks:P?h:g.ticks}):void 0,width:r,height:n,offset:i},!!P||p);hg(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof E,"]")),Array.isArray(E)&&(w=E)}if(!(O&&O.length||"function"!=typeof b)){var A=y&&y.length,j=b({xAxis:m?xj(xj({},m),{},{ticks:A?y:m.ticks}):void 0,width:r,height:n,offset:i},!!A||p);hg(Array.isArray(j),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof j,"]")),Array.isArray(j)&&(O=j)}return t.createElement("g",{className:"recharts-cartesian-grid"},t.createElement(Ej,{fill:a.fill,fillOpacity:a.fillOpacity,x:a.x,y:a.y,width:a.width,height:a.height,ry:a.ry}),t.createElement(kj,Oj({},a,{horizontalPoints:w})),t.createElement(Mj,Oj({},a,{verticalPoints:O})),t.createElement(jj,Oj({},a,{offset:i,horizontalPoints:w,xAxis:m,yAxis:g})),t.createElement(Sj,Oj({},a,{offset:i,verticalPoints:O,xAxis:m,yAxis:g})))}Ij.displayName="CartesianGrid";var Nj=(e,t,r,n)=>Hy(e,"xAxis",t,n),_j=(e,t,r,n)=>$y(e,"xAxis",t,n),Rj=(e,t,r,n)=>Hy(e,"yAxis",r,n),Lj=(e,t,r,n)=>$y(e,"yAxis",r,n),Kj=Ge([Hi,Nj,Rj,_j,Lj],((e,t,r,n,i)=>ii(e,"xAxis")?gi(t,n,!1):gi(r,i,!1))),zj=Ge([wh,(e,t,r,n,i)=>i],((e,t)=>{if(e.some((e=>"line"===e.type&&t.dataKey===e.dataKey&&t.data===e.data)))return t})),Bj=Ge([Hi,Nj,Rj,_j,Lj,zj,Kj,sp],((e,t,r,n,i,a,o,l)=>{var{chartData:c,dataStartIndex:s,dataEndIndex:u}=l;if(null!=a&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=o){var f,{dataKey:d,data:p}=a;if(null!=(f=null!=p&&p.length>0?p:null==c?void 0:c.slice(s,u+1)))return function(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:i,yAxisTicks:a,dataKey:o,bandSize:l,displayedData:c}=e;return c.map(((e,c)=>{var s=ni(e,o);return"horizontal"===t?{x:di({axis:r,ticks:i,bandSize:l,entry:e,index:c}),y:w(s)?null:n.scale(s),value:s,payload:e}:{x:w(s)?null:r.scale(s),y:di({axis:n,ticks:a,bandSize:l,entry:e,index:c}),value:s,payload:e}}))}({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataKey:d,bandSize:o,displayedData:f})}})),Fj=["type","layout","connectNulls","needClip"],Wj=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function Uj(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Xj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Vj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Xj(Object(r),!0).forEach((function(t){$j(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xj(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function $j(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Hj(){return Hj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Hj.apply(null,arguments)}var qj=e=>{var{dataKey:t,name:r,stroke:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:xi(r,t),payload:e}]};function Yj(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:xi(o,t),hide:l,type:e.tooltipType,color:e.stroke,unit:c}}}var Gj=(e,t)=>"".concat(t,"px ").concat(e-t,"px");function Zj(e,t){for(var r=e.length%2!=0?[...e,0]:e,n=[],i=0;i<t;++i)n=[...n,...r];return n}function Jj(e){var{clipPathId:r,points:i,props:a}=e,{dot:o,dataKey:l,needClip:c}=a;if(!function(e,t){return null!=e&&(!!t||1===e.length)}(i,o))return null;var s=N(o),u=_(a,!1),f=_(o,!0),d=i.map(((e,r)=>{var a=Vj(Vj(Vj({key:"dot-".concat(r),r:3},u),f),{},{index:r,cx:e.x,cy:e.y,dataKey:l,value:e.value,payload:e.payload,points:i});return function(e,r){var i;if(t.isValidElement(e))i=t.cloneElement(e,r);else if("function"==typeof e)i=e(r);else{var a=n("recharts-line-dot","boolean"!=typeof e?e.className:"");i=t.createElement(Mb,Hj({},r,{className:a}))}return i}(o,a)})),p={clipPath:c?"url(#clipPath-".concat(s?"":"dots-").concat(r,")"):null};return t.createElement(F,Hj({className:"recharts-line-dots",key:"dots"},p),d)}function Qj(e){var{clipPathId:r,pathRef:n,points:i,strokeDasharray:a,props:o,showLabels:l}=e,{type:c,layout:s,connectNulls:u,needClip:f}=o,d=Uj(o,Fj),p=Vj(Vj({},_(d,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:f?"url(#clipPath-".concat(r,")"):null,points:i,type:c,layout:s,connectNulls:u,strokeDasharray:null!=a?a:o.strokeDasharray});return t.createElement(t.Fragment,null,(null==i?void 0:i.length)>1&&t.createElement(Qo,Hj({},p,{pathRef:n})),t.createElement(Jj,{points:i,clipPathId:r,props:o}),l&&xb.renderCallByParent(o,i))}function eS(e){var{clipPathId:r,props:n,pathRef:i,previousPointsRef:a,longestAnimatedLengthRef:o}=e,{points:l,strokeDasharray:c,isAnimationActive:s,animationBegin:u,animationDuration:f,animationEasing:d,animateNewValues:p,width:h,height:y,onAnimationEnd:v,onAnimationStart:m}=n,b=a.current,x=Iw(n,"recharts-line-"),[w,O]=(0,t.useState)(!1),P=(0,t.useCallback)((()=>{"function"==typeof v&&v(),O(!1)}),[v]),E=(0,t.useCallback)((()=>{"function"==typeof m&&m(),O(!0)}),[m]),A=function(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}(i.current),j=o.current;return t.createElement(Kl,{begin:u,duration:f,isActive:s,easing:d,from:{t:0},to:{t:1},onAnimationEnd:P,onAnimationStart:E,key:x},(e=>{var s,{t:u}=e,f=g(j,A+j),d=Math.min(f(u),A);if(c){var v="".concat(c).split(/[,\s]+/gim).map((e=>parseFloat(e)));s=((e,t,r)=>{var n=r.reduce(((e,t)=>e+t));if(!n)return Gj(t,e);for(var i=Math.floor(e/n),a=e%n,o=t-e,l=[],c=0,s=0;c<r.length;s+=r[c],++c)if(s+r[c]>a){l=[...r.slice(0,c),a-s];break}var u=l.length%2==0?[0,o]:[o];return[...Zj(r,i),...l,...u].map((e=>"".concat(e,"px"))).join(", ")})(d,A,v)}else s=Gj(A,d);if(b){var m=b.length/l.length,x=1===u?l:l.map(((e,t)=>{var r=Math.floor(t*m);if(b[r]){var n=b[r],i=g(n.x,e.x),a=g(n.y,e.y);return Vj(Vj({},e),{},{x:i(u),y:a(u)})}if(p){var o=g(2*h,e.x),l=g(y/2,e.y);return Vj(Vj({},e),{},{x:o(u),y:l(u)})}return Vj(Vj({},e),{},{x:e.x,y:e.y})}));return a.current=x,t.createElement(Qj,{props:n,points:x,clipPathId:r,pathRef:i,showLabels:!w,strokeDasharray:s})}return u>0&&A>0&&(a.current=l,o.current=d),t.createElement(Qj,{props:n,points:l,clipPathId:r,pathRef:i,showLabels:!w,strokeDasharray:s})}))}function tS(e){var{clipPathId:r,props:n}=e,{points:i,isAnimationActive:a}=n,o=(0,t.useRef)(null),l=(0,t.useRef)(0),c=(0,t.useRef)(null),s=o.current;return a&&i&&i.length&&s!==i?t.createElement(eS,{props:n,clipPathId:r,previousPointsRef:o,longestAnimatedLengthRef:l,pathRef:c}):t.createElement(Qj,{props:n,points:i,clipPathId:r,pathRef:c,showLabels:!0})}var rS=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:ni(e.payload,t)});class nS extends t.Component{constructor(){super(...arguments),$j(this,"id",y("recharts-line-"))}render(){var e,{hide:r,dot:i,points:a,className:o,xAxisId:l,yAxisId:c,top:s,left:u,width:f,height:d,id:p,needClip:h,layout:y}=this.props;if(r)return null;var v=n("recharts-line",o),m=w(p)?this.id:p,{r:g=3,strokeWidth:b=2}=null!==(e=_(i,!1))&&void 0!==e?e:{r:3,strokeWidth:2},x=N(i),O=2*g+b;return t.createElement(t.Fragment,null,t.createElement(F,{className:v},h&&t.createElement("defs",null,t.createElement(pP,{clipPathId:m,xAxisId:l,yAxisId:c}),!x&&t.createElement("clipPath",{id:"clipPath-dots-".concat(m)},t.createElement("rect",{x:u-O/2,y:s-O/2,width:f+O,height:d+O}))),t.createElement(tS,{props:this.props,clipPathId:m}),t.createElement(QO,{direction:"horizontal"===y?"y":"x"},t.createElement(XO,{xAxisId:l,yAxisId:c,data:a,dataPointFormatter:rS,errorBarOffset:0},this.props.children))),t.createElement(tO,{activeDot:this.props.activeDot,points:a,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}}var iS={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!mo.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function aS(e){var r=cl(e,iS),{activeDot:n,animateNewValues:i,animationBegin:a,animationDuration:o,animationEasing:l,connectNulls:c,dot:s,hide:u,isAnimationActive:f,label:d,legendType:p,xAxisId:h,yAxisId:y}=r,v=Uj(r,Wj),{needClip:m}=dP(h,y),{height:g,width:b,left:x,top:w}=Ui(),O=qi(),P=Li(),E=(0,t.useMemo)((()=>({dataKey:e.dataKey,data:e.data})),[e.dataKey,e.data]),A=Ue((e=>Bj(e,h,y,P,E)));return"horizontal"!==O&&"vertical"!==O?null:t.createElement(nS,Hj({},v,{connectNulls:c,dot:s,activeDot:n,animateNewValues:i,animationBegin:a,animationDuration:o,animationEasing:l,isAnimationActive:f,hide:u,label:d,legendType:p,xAxisId:h,yAxisId:y,points:A,layout:O,height:g,width:b,left:x,top:w,needClip:m}))}class oS extends t.PureComponent{render(){return t.createElement(VO,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},t.createElement(Cw,{legendPayload:qj(this.props)}),t.createElement(Mw,{fn:Yj,args:this.props}),t.createElement(aS,this.props))}}$j(oS,"displayName","Line"),$j(oS,"defaultProps",iS);var lS=(e,t,r,n)=>Hy(e,"xAxis",t,n),cS=(e,t,r,n)=>$y(e,"xAxis",t,n),sS=(e,t,r,n)=>Hy(e,"yAxis",r,n),uS=(e,t,r,n)=>$y(e,"yAxis",r,n),fS=Ge([Hi,lS,sS,cS,uS],((e,t,r,n,i)=>ii(e,"xAxis")?gi(t,n,!1):gi(r,i,!1))),dS=Ge([wh,(e,t,r,n,i)=>i],((e,t)=>{if(e.some((e=>"area"===e.type&&t.dataKey===e.dataKey&&fi(t.stackId)===e.stackId&&t.data===e.data)))return t})),pS=Ge([Hi,lS,sS,cS,uS,(e,t,r,n,i)=>{var a,o,l=Hi(e);if(null!=(o=ii(l,"xAxis")?Lh(e,"yAxis",r,n):Lh(e,"xAxis",t,n))){var{dataKey:c,stackId:s}=i;if(null!=s){var u=null===(a=o[s])||void 0===a?void 0:a.stackedData;return null==u?void 0:u.find((e=>e.key===c))}}},sp,fS,dS],((e,t,r,n,i,a,o,l,c)=>{var{chartData:s,dataStartIndex:u,dataEndIndex:f}=o;if(null!=c&&("horizontal"===e||"vertical"===e)&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=l){var d,{data:p}=c;if(null!=(d=p&&p.length>0?p:null==s?void 0:s.slice(u,f+1))){return function(e){var t,{areaSettings:{connectNulls:r,baseValue:n,dataKey:i},stackedData:a,layout:o,chartBaseValue:l,xAxis:c,yAxis:s,displayedData:u,dataStartIndex:f,xAxisTicks:d,yAxisTicks:p,bandSize:h}=e,y=a&&a.length,v=NS(o,l,n,c,s),m="horizontal"===o,g=!1,b=u.map(((e,t)=>{var n;y?n=a[f+t]:(n=ni(e,i),Array.isArray(n)?g=!0:n=[v,n]);var o=null==n[1]||y&&!r&&null==ni(e,i);return m?{x:di({axis:c,ticks:d,bandSize:h,entry:e,index:t}),y:o?null:s.scale(n[1]),value:n,payload:e}:{x:o?null:c.scale(n[1]),y:di({axis:s,ticks:p,bandSize:h,entry:e,index:t}),value:n,payload:e}}));t=y||g?b.map((e=>{var t=Array.isArray(e.value)?e.value[0]:null;return m?{x:e.x,y:null!=t&&null!=e.y?s.scale(t):null}:{x:null!=t?c.scale(t):null,y:e.y}})):m?s.scale(v):c.scale(v);return{points:b,baseLine:t,isRange:g}}({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataStartIndex:u,areaSettings:c,stackedData:a,displayedData:d,chartBaseValue:undefined,bandSize:l})}}})),hS=["layout","type","stroke","connectNulls","isRange"],yS=["activeDot","animationBegin","animationDuration","animationEasing","connectNulls","dot","fill","fillOpacity","hide","isAnimationActive","legendType","stroke","xAxisId","yAxisId"];function vS(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function mS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function gS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mS(Object(r),!0).forEach((function(t){bS(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mS(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function bS(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xS(){return xS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xS.apply(null,arguments)}function wS(e,t){return e&&"none"!==e?e:t}var OS=e=>{var{dataKey:t,name:r,stroke:n,fill:i,legendType:a,hide:o}=e;return[{inactive:o,dataKey:t,type:a,color:wS(n,i),value:xi(r,t),payload:e}]};function PS(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:xi(o,t),hide:l,type:e.tooltipType,color:wS(n,a),unit:c}}}function ES(e){var{clipPathId:r,points:i,props:a}=e,{needClip:o,dot:l,dataKey:c}=a;if(!function(e,t){return null!=e&&(!!t||1===e.length)}(i,l))return null;var s=N(l),u=_(a,!1),f=_(l,!0),d=i.map(((e,r)=>{var a=gS(gS(gS({key:"dot-".concat(r),r:3},u),f),{},{index:r,cx:e.x,cy:e.y,dataKey:c,value:e.value,payload:e.payload,points:i});return((e,r)=>{var i;if(t.isValidElement(e))i=t.cloneElement(e,r);else if("function"==typeof e)i=e(r);else{var a=n("recharts-area-dot","boolean"!=typeof e?e.className:"");i=t.createElement(Mb,xS({},r,{className:a}))}return i})(l,a)})),p={clipPath:o?"url(#clipPath-".concat(s?"":"dots-").concat(r,")"):void 0};return t.createElement(F,xS({className:"recharts-area-dots"},p),d)}function AS(e){var{points:r,baseLine:n,needClip:i,clipPathId:a,props:o,showLabels:l}=e,{layout:c,type:s,stroke:u,connectNulls:f,isRange:d}=o,p=vS(o,hS);return t.createElement(t.Fragment,null,(null==r?void 0:r.length)>1&&t.createElement(F,{clipPath:i?"url(#clipPath-".concat(a,")"):void 0},t.createElement(Qo,xS({},_(p,!0),{points:r,connectNulls:f,type:s,baseLine:n,layout:c,stroke:"none",className:"recharts-area-area"})),"none"!==u&&t.createElement(Qo,xS({},_(o,!1),{className:"recharts-area-curve",layout:c,type:s,connectNulls:f,fill:"none",points:r})),"none"!==u&&d&&t.createElement(Qo,xS({},_(o,!1),{className:"recharts-area-curve",layout:c,type:s,connectNulls:f,fill:"none",points:n}))),t.createElement(ES,{points:r,props:o,clipPathId:a}),l&&xb.renderCallByParent(o,r))}function jS(e){var{alpha:r,baseLine:n,points:i,strokeWidth:a}=e,o=i[0].y,l=i[i.length-1].y;if(!Wo(o)||!Wo(l))return null;var c=r*Math.abs(o-l),s=Math.max(...i.map((e=>e.x||0)));return d(n)?s=Math.max(n,s):n&&Array.isArray(n)&&n.length&&(s=Math.max(...n.map((e=>e.x||0)),s)),d(s)?t.createElement("rect",{x:0,y:o<l?o:o-c,width:s+(a?parseInt("".concat(a),10):1),height:Math.floor(c)}):null}function SS(e){var{alpha:r,baseLine:n,points:i,strokeWidth:a}=e,o=i[0].x,l=i[i.length-1].x;if(!Wo(o)||!Wo(l))return null;var c=r*Math.abs(o-l),s=Math.max(...i.map((e=>e.y||0)));return d(n)?s=Math.max(n,s):n&&Array.isArray(n)&&n.length&&(s=Math.max(...n.map((e=>e.y||0)),s)),d(s)?t.createElement("rect",{x:o<l?o:o-c,y:0,width:c,height:Math.floor(s+(a?parseInt("".concat(a),10):1))}):null}function kS(e){var{alpha:r,layout:n,points:i,baseLine:a,strokeWidth:o}=e;return"vertical"===n?t.createElement(jS,{alpha:r,points:i,baseLine:a,strokeWidth:o}):t.createElement(SS,{alpha:r,points:i,baseLine:a,strokeWidth:o})}function MS(e){var{needClip:r,clipPathId:n,props:i,previousPointsRef:a,previousBaselineRef:o}=e,{points:l,baseLine:c,isAnimationActive:s,animationBegin:f,animationDuration:p,animationEasing:h,onAnimationStart:y,onAnimationEnd:v}=i,m=Iw(i,"recharts-area-"),[g,x]=(0,t.useState)(!0),O=(0,t.useCallback)((()=>{"function"==typeof v&&v(),x(!1)}),[v]),P=(0,t.useCallback)((()=>{"function"==typeof y&&y(),x(!0)}),[y]),E=a.current,A=o.current;return t.createElement(Kl,{begin:f,duration:p,isActive:s,easing:h,from:{t:0},to:{t:1},onAnimationEnd:O,onAnimationStart:P,key:m},(e=>{var{t:s}=e;if(E){var f,p=E.length/l.length,h=1===s?l:l.map(((e,t)=>{var r=Math.floor(t*p);if(E[r]){var n=E[r];return gS(gS({},e),{},{x:b(n.x,e.x,s),y:b(n.y,e.y,s)})}return e}));return f=d(c)?b(A,c,s):w(c)||u(c)?b(A,0,s):c.map(((e,t)=>{var r=Math.floor(t*p);if(Array.isArray(A)&&A[r]){var n=A[r];return gS(gS({},e),{},{x:b(n.x,e.x,s),y:b(n.y,e.y,s)})}return e})),s>0&&(a.current=h,o.current=f),t.createElement(AS,{points:h,baseLine:f,needClip:r,clipPathId:n,props:i,showLabels:!g})}return s>0&&(a.current=l,o.current=c),t.createElement(F,null,t.createElement("defs",null,t.createElement("clipPath",{id:"animationClipPath-".concat(n)},t.createElement(kS,{alpha:s,points:l,baseLine:c,layout:i.layout,strokeWidth:i.strokeWidth}))),t.createElement(F,{clipPath:"url(#animationClipPath-".concat(n,")")},t.createElement(AS,{points:l,baseLine:c,needClip:r,clipPathId:n,props:i,showLabels:!0})))}))}function TS(e){var{needClip:r,clipPathId:n,props:i}=e,{points:a,baseLine:o,isAnimationActive:l}=i,c=(0,t.useRef)(null),s=(0,t.useRef)(),u=c.current,f=s.current;return l&&a&&a.length&&(u!==a||f!==o)?t.createElement(MS,{needClip:r,clipPathId:n,props:i,previousPointsRef:c,previousBaselineRef:s}):t.createElement(AS,{points:a,baseLine:o,needClip:r,clipPathId:n,props:i,showLabels:!0})}class CS extends t.PureComponent{constructor(){super(...arguments),bS(this,"id",y("recharts-area-"))}render(){var e,{hide:r,dot:i,points:a,className:o,top:l,left:c,needClip:s,xAxisId:u,yAxisId:f,width:d,height:p,id:h,baseLine:y}=this.props;if(r)return null;var v=n("recharts-area",o),m=w(h)?this.id:h,{r:g=3,strokeWidth:b=2}=null!==(e=_(i,!1))&&void 0!==e?e:{r:3,strokeWidth:2},x=N(i),O=2*g+b;return t.createElement(t.Fragment,null,t.createElement(F,{className:v},s&&t.createElement("defs",null,t.createElement(pP,{clipPathId:m,xAxisId:u,yAxisId:f}),!x&&t.createElement("clipPath",{id:"clipPath-dots-".concat(m)},t.createElement("rect",{x:c-O/2,y:l-O/2,width:d+O,height:p+O}))),t.createElement(TS,{needClip:s,clipPathId:m,props:this.props})),t.createElement(tO,{points:a,mainColor:wS(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}),this.props.isRange&&Array.isArray(y)&&t.createElement(tO,{points:y,mainColor:wS(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}}var DS={activeDot:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!1,fill:"#3182bd",fillOpacity:.6,hide:!1,isAnimationActive:!mo.isSsr,legendType:"line",stroke:"#3182bd",xAxisId:0,yAxisId:0};function IS(e){var r,n=cl(e,DS),{activeDot:i,animationBegin:a,animationDuration:o,animationEasing:l,connectNulls:c,dot:s,fill:u,fillOpacity:f,hide:d,isAnimationActive:p,legendType:h,stroke:y,xAxisId:v,yAxisId:m}=n,g=vS(n,yS),b=qi(),x=Om(),{needClip:w}=dP(v,m),O=Li(),P=(0,t.useMemo)((()=>({baseValue:e.baseValue,stackId:e.stackId,connectNulls:c,data:e.data,dataKey:e.dataKey})),[e.baseValue,e.stackId,c,e.data,e.dataKey]),{points:E,isRange:A,baseLine:j}=null!==(r=Ue((e=>pS(e,v,m,O,P))))&&void 0!==r?r:{},{height:S,width:k,left:M,top:T}=Ui();return"horizontal"!==b&&"vertical"!==b||"AreaChart"!==x&&"ComposedChart"!==x?null:t.createElement(CS,xS({},g,{activeDot:i,animationBegin:a,animationDuration:o,animationEasing:l,baseLine:j,connectNulls:c,dot:s,fill:u,fillOpacity:f,height:S,hide:d,layout:b,isAnimationActive:p,isRange:A,legendType:h,needClip:w,points:E,stroke:y,width:k,left:M,top:T,xAxisId:v,yAxisId:m}))}var NS=(e,t,r,n,i)=>{var a=null!=r?r:t;if(d(a))return a;var o="horizontal"===e?i:n,l=o.scale.domain();if("number"===o.type){var c=Math.max(l[0],l[1]),s=Math.min(l[0],l[1]);return"dataMin"===a?s:"dataMax"===a||c<0?c:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===a?l[0]:"dataMax"===a?l[1]:l[0]};class _S extends t.PureComponent{render(){return t.createElement(VO,{type:"area",data:this.props.data,dataKey:this.props.dataKey,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,stackId:this.props.stackId,hide:this.props.hide,barSize:void 0},t.createElement(Cw,{legendPayload:OS(this.props)}),t.createElement(Mw,{fn:PS,args:this.props}),t.createElement(IS,this.props))}}function RS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function LS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?RS(Object(r),!0).forEach((function(t){KS(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):RS(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function KS(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}bS(_S,"displayName","Area"),bS(_S,"defaultProps",DS);var zS=$r({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=t.payload},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=t.payload},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=t.payload},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=LS(LS({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:BS,removeXAxis:FS,addYAxis:WS,removeYAxis:US,addZAxis:XS,removeZAxis:VS,updateYAxisWidth:$S}=zS.actions,HS=zS.reducer;function qS(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function YS(e){var r=ze();return(0,t.useEffect)((()=>(r(XS(e)),()=>{r(VS(e))})),[e,r]),null}class GS extends t.Component{render(){return t.createElement(YS,{domain:this.props.domain,id:this.props.zAxisId,dataKey:this.props.dataKey,name:this.props.name,unit:this.props.unit,range:this.props.range,scale:this.props.scale,type:this.props.type,allowDuplicatedCategory:yh.allowDuplicatedCategory,allowDataOverflow:yh.allowDataOverflow,reversed:yh.reversed,includeHidden:yh.includeHidden})}}qS(GS,"displayName","ZAxis"),qS(GS,"defaultProps",{zAxisId:0,range:yh.range,scale:yh.scale,type:yh.type});var ZS=["option","isActive"];function JS(){return JS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},JS.apply(null,arguments)}function QS(e){var{option:r,isActive:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,ZS);return"string"==typeof r?t.createElement(Aw,JS({option:t.createElement(Se,JS({type:r},i)),isActive:n,shapeType:"symbols"},i)):t.createElement(Aw,JS({option:r,isActive:n,shapeType:"symbols"},i))}var ek=Ge([wh,(e,t,r,n,i)=>i],((e,t)=>{if(e.some((e=>"scatter"===e.type&&t.dataKey===e.dataKey&&t.data===e.data)))return t})),tk=Ge([(e,t,r,n,i,a,o)=>sp(e,0,0,o),(e,t,r,n,i,a,o)=>Hy(e,"xAxis",t,o),(e,t,r,n,i,a,o)=>$y(e,"xAxis",t,o),(e,t,r,n,i,a,o)=>Hy(e,"yAxis",r,o),(e,t,r,n,i,a,o)=>$y(e,"yAxis",r,o),(e,t,r,n)=>Yy(e,"zAxis",n,!1),ek,(e,t,r,n,i,a)=>a],((e,t,r,n,i,a,o,l)=>{var c,{chartData:s,dataStartIndex:u,dataEndIndex:f}=e;if(null!=o&&(null!=(c=null!=(null==o?void 0:o.data)&&o.data.length>0?o.data:null==s?void 0:s.slice(u,f+1))&&null!=t&&null!=n&&null!=r&&null!=i&&0!==(null==r?void 0:r.length)&&0!==(null==i?void 0:i.length)))return function(e){var{displayedData:t,xAxis:r,yAxis:n,zAxis:i,scatterSettings:a,xAxisTicks:o,yAxisTicks:l,cells:c}=e,s=w(r.dataKey)?a.dataKey:r.dataKey,u=w(n.dataKey)?a.dataKey:n.dataKey,f=i&&i.dataKey,d=i?i.range:GS.defaultProps.range,p=d&&d[0],h=r.scale.bandwidth?r.scale.bandwidth():0,y=n.scale.bandwidth?n.scale.bandwidth():0;return t.map(((e,t)=>{var d=ni(e,s),v=ni(e,u),m=!w(f)&&ni(e,f)||"-",g=[{name:w(r.dataKey)?a.name:r.name||r.dataKey,unit:r.unit||"",value:d,payload:e,dataKey:s,type:a.tooltipType},{name:w(n.dataKey)?a.name:n.name||n.dataKey,unit:n.unit||"",value:v,payload:e,dataKey:u,type:a.tooltipType}];"-"!==m&&g.push({name:i.name||i.dataKey,unit:i.unit||"",value:m,payload:e,dataKey:f,type:a.tooltipType});var b=di({axis:r,ticks:o,bandSize:h,entry:e,index:t,dataKey:s}),x=di({axis:n,ticks:l,bandSize:y,entry:e,index:t,dataKey:u}),O="-"!==m?i.scale(m):p,P=Math.sqrt(Math.max(O,0)/Math.PI);return lk(lk({},e),{},{cx:b,cy:x,x:b-P,y:x-P,width:2*P,height:2*P,size:O,node:{x:d,y:v,z:m},tooltipPayload:g,tooltipPosition:{x:b,y:x},payload:e},c&&c[t]&&c[t].props)}))}({displayedData:c,xAxis:t,yAxis:n,zAxis:a,scatterSettings:o,xAxisTicks:r,yAxisTicks:i,cells:l})})),rk=["onMouseEnter","onClick","onMouseLeave"],nk=["animationBegin","animationDuration","animationEasing","hide","isAnimationActive","legendType","lineJointType","lineType","shape","xAxisId","yAxisId","zAxisId"];function ik(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function ak(){return ak=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ak.apply(null,arguments)}function ok(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function lk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ok(Object(r),!0).forEach((function(t){ck(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ok(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ck(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var sk=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:xi(r,t),payload:e}]};function uk(e){var{points:r,props:n}=e,{line:i,lineType:a,lineJointType:o}=n;if(!i)return null;var l,c,s=_(n,!1),u=_(i,!1);if("joint"===a)l=r.map((e=>({x:e.cx,y:e.cy})));else if("fitting"===a){var{xmin:f,xmax:d,a:p,b:h}=(e=>{if(!e||!e.length)return null;for(var t=e.length,r=0,n=0,i=0,a=0,o=1/0,l=-1/0,c=0,s=0,u=0;u<t;u++)r+=c=e[u].cx||0,n+=s=e[u].cy||0,i+=c*s,a+=c*c,o=Math.min(o,c),l=Math.max(l,c);var f=t*a!=r*r?(t*i-r*n)/(t*a-r*r):0;return{xmin:o,xmax:l,a:f,b:(n-f*r)/t}})(r),y=e=>p*e+h;l=[{x:f,y:y(f)},{x:d,y:y(d)}]}var v=lk(lk(lk({},s),{},{fill:"none",stroke:s&&s.fill},u),{},{points:l});return c=t.isValidElement(i)?t.cloneElement(i,v):"function"==typeof i?i(v):t.createElement(Qo,ak({},v,{type:o})),t.createElement(F,{className:"recharts-scatter-line",key:"recharts-scatter-line"},c)}function fk(e){var{points:r,showLabels:n,allOtherScatterProps:i}=e,{shape:a,activeShape:o,dataKey:l}=i,c=_(i,!1),s=Ue(lm),{onMouseEnter:u,onClick:f,onMouseLeave:d}=i,p=ik(i,rk),h=jw(u,i.dataKey),y=Sw(d),v=kw(f,i.dataKey);return null==r?null:t.createElement(t.Fragment,null,t.createElement(uk,{points:r,props:i}),r.map(((e,r)=>{var n=o&&s===String(r),i=n?o:a,u=lk(lk(lk({key:"symbol-".concat(r)},c),e),{},{[ki]:r,[Mi]:String(l)});return t.createElement(F,ak({className:"recharts-scatter-symbol"},k(p,e,r),{onMouseEnter:h(e,r),onMouseLeave:y(e,r),onClick:v(e,r),key:"symbol-".concat(null==e?void 0:e.cx,"-").concat(null==e?void 0:e.cy,"-").concat(null==e?void 0:e.size,"-").concat(r)}),t.createElement(QS,ak({option:i,isActive:n},u)))})),n&&xb.renderCallByParent(i,r))}function dk(e){var{previousPointsRef:r,props:n}=e,{points:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c}=n,s=r.current,u=Iw(n,"recharts-scatter-"),[f,d]=(0,t.useState)(!1),p=(0,t.useCallback)((()=>{d(!1)}),[]),h=(0,t.useCallback)((()=>{d(!0)}),[]);return t.createElement(Kl,{begin:o,duration:l,isActive:a,easing:c,from:{t:0},to:{t:1},onAnimationEnd:p,onAnimationStart:h,key:u},(e=>{var{t:a}=e,o=1===a?i:i.map(((e,t)=>{var r=s&&s[t];if(r){var n=g(r.cx,e.cx),i=g(r.cy,e.cy),o=g(r.size,e.size);return lk(lk({},e),{},{cx:n(a),cy:i(a),size:o(a)})}var l=g(0,e.size);return lk(lk({},e),{},{size:l(a)})}));return a>0&&(r.current=o),t.createElement(F,null,t.createElement(fk,{points:o,allOtherScatterProps:n,showLabels:!f}))}))}function pk(e){var{points:r,isAnimationActive:n}=e,i=(0,t.useRef)(null),a=i.current;return n&&r&&r.length&&(!a||a!==r)?t.createElement(dk,{props:e,previousPointsRef:i}):t.createElement(fk,{points:r,allOtherScatterProps:e,showLabels:!0})}function hk(e){var{dataKey:t,points:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,tooltipType:c}=e;return{dataDefinedOnItem:null==r?void 0:r.map((e=>e.tooltipPayload)),positions:null==r?void 0:r.map((e=>e.tooltipPosition)),settings:{stroke:n,strokeWidth:i,fill:a,nameKey:void 0,dataKey:t,name:xi(o,t),hide:l,type:c,color:a,unit:""}}}var yk=(e,t,r)=>({x:e.cx,y:e.cy,value:"x"===r?+e.node.x:+e.node.y,errorVal:ni(e,t)});function vk(e){var r=(0,t.useRef)(y("recharts-scatter-")),{hide:i,points:a,className:o,needClip:l,xAxisId:c,yAxisId:s,id:u,children:f}=e;if(i)return null;var d=n("recharts-scatter",o),p=w(u)?r.current:u;return t.createElement(F,{className:d,clipPath:l?"url(#clipPath-".concat(p,")"):null},l&&t.createElement("defs",null,t.createElement(pP,{clipPathId:p,xAxisId:c,yAxisId:s})),t.createElement(XO,{xAxisId:c,yAxisId:s,data:a,dataPointFormatter:yk,errorBarOffset:0},f),t.createElement(F,{key:"recharts-scatter-symbols"},t.createElement(pk,e)))}var mk={xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!mo.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"};function gk(e){var r=cl(e,mk),{animationBegin:n,animationDuration:i,animationEasing:a,hide:o,isAnimationActive:l,legendType:c,lineJointType:s,lineType:u,shape:f,xAxisId:d,yAxisId:p,zAxisId:h}=r,y=ik(r,nk),{needClip:v}=dP(d,p),m=(0,t.useMemo)((()=>I(e.children,bg)),[e.children]),g=(0,t.useMemo)((()=>({name:e.name,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey})),[e.data,e.dataKey,e.name,e.tooltipType]),b=Li(),x=Ue((e=>tk(e,d,p,h,g,m,b)));return null==v?null:t.createElement(t.Fragment,null,t.createElement(Mw,{fn:hk,args:lk(lk({},e),{},{points:x})}),t.createElement(vk,ak({},y,{xAxisId:d,yAxisId:p,zAxisId:h,lineType:u,lineJointType:s,legendType:c,shape:f,hide:o,isAnimationActive:l,animationBegin:n,animationDuration:i,animationEasing:a,points:x,needClip:v})))}class bk extends t.Component{render(){return t.createElement(VO,{type:"scatter",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:this.props.zAxisId,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},t.createElement(Cw,{legendPayload:sk(this.props)}),t.createElement(gk,this.props))}}ck(bk,"displayName","Scatter"),ck(bk,"defaultProps",mk);var xk=["children"],wk=["dangerouslySetInnerHTML","ticks"];function Ok(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Pk(){return Pk=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pk.apply(null,arguments)}function Ek(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Ak(e){var r=ze(),n=(0,t.useMemo)((()=>{var{children:t}=e;return Ek(e,xk)}),[e]),i=Ue((e=>dh(e,n.id))),a=n===i;return(0,t.useEffect)((()=>(r(BS(n)),()=>{r(FS(n))})),[n,r]),a?e.children:null}var jk=e=>{var{xAxisId:r,className:i}=e,a=Ue(_i),o=Li(),l="xAxis",c=Ue((e=>jy(e,l,r,o))),s=Ue((e=>Xy(e,l,r,o))),u=Ue((e=>Iy(e,r))),f=Ue((e=>((e,t)=>{var r=Ii(e),n=dh(e,t);if(null!=n){var i=Ny(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}})(e,r)));if(null==u||null==f)return null;var{dangerouslySetInnerHTML:d,ticks:p}=e,h=Ek(e,wk);return t.createElement(hj,Pk({},h,{scale:c,x:f.x,y:f.y,width:u.width,height:u.height,className:n("recharts-".concat(l," ").concat(l),i),viewBox:a,ticks:s}))},Sk=e=>{var r,n,i,a,o;return t.createElement(Ak,{interval:null!==(r=e.interval)&&void 0!==r?r:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!==(n=e.includeHidden)&&void 0!==n&&n,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(i=e.angle)&&void 0!==i?i:0,minTickGap:null!==(a=e.minTickGap)&&void 0!==a?a:5,tick:null===(o=e.tick)||void 0===o||o,tickFormatter:e.tickFormatter},t.createElement(jk,e))};class kk extends t.Component{render(){return t.createElement(Sk,this.props)}}Ok(kk,"displayName","XAxis"),Ok(kk,"defaultProps",{allowDataOverflow:fh.allowDataOverflow,allowDecimals:fh.allowDecimals,allowDuplicatedCategory:fh.allowDuplicatedCategory,height:fh.height,hide:!1,mirror:fh.mirror,orientation:fh.orientation,padding:fh.padding,reversed:fh.reversed,scale:fh.scale,tickCount:fh.tickCount,type:fh.type,xAxisId:0});var Mk=["dangerouslySetInnerHTML","ticks"];function Tk(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ck(){return Ck=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ck.apply(null,arguments)}function Dk(e){var r=ze();return(0,t.useEffect)((()=>(r(WS(e)),()=>{r(US(e))})),[e,r]),null}var Ik=e=>{var r,{yAxisId:i,className:a,width:o,label:l}=e,c=(0,t.useRef)(null),s=(0,t.useRef)(null),u=Ue(_i),f=Li(),d=ze(),p="yAxis",h=Ue((e=>jy(e,p,i,f))),y=Ue((e=>Ry(e,i))),v=Ue((e=>((e,t)=>{var r=Ii(e),n=hh(e,t);if(null!=n){var i=_y(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}})(e,i))),m=Ue((e=>Xy(e,p,i,f)));if((0,t.useLayoutEffect)((()=>{var e;if("auto"===o&&y&&!nb(l)&&!(0,t.isValidElement)(l)){var r=c.current,n=null==r||null===(e=r.tickRefs)||void 0===e?void 0:e.current,{tickSize:a,tickMargin:u}=r.props,f=(e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach((e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}}));var l=r?r.getBoundingClientRect().width:0,c=o+(i+a)+l+(r?n:0);return Math.round(c)}return 0})({ticks:n,label:s.current,labelGapWithTick:5,tickSize:a,tickMargin:u});Math.round(y.width)!==Math.round(f)&&d($S({id:i,width:f}))}}),[c,null==c||null===(r=c.current)||void 0===r||null===(r=r.tickRefs)||void 0===r?void 0:r.current,null==y?void 0:y.width,y,d,l,i,o]),null==y||null==v)return null;var{dangerouslySetInnerHTML:g,ticks:b}=e,x=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Mk);return t.createElement(hj,Ck({},x,{ref:c,labelRef:s,scale:h,x:v.x,y:v.y,width:y.width,height:y.height,className:n("recharts-".concat(p," ").concat(p),a),viewBox:u,ticks:m}))},Nk=e=>{var r,n,i,a,o;return t.createElement(t.Fragment,null,t.createElement(Dk,{interval:null!==(r=e.interval)&&void 0!==r?r:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!==(n=e.includeHidden)&&void 0!==n&&n,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!==(i=e.angle)&&void 0!==i?i:0,minTickGap:null!==(a=e.minTickGap)&&void 0!==a?a:5,tick:null===(o=e.tick)||void 0===o||o,tickFormatter:e.tickFormatter}),t.createElement(Ik,e))},_k={allowDataOverflow:ph.allowDataOverflow,allowDecimals:ph.allowDecimals,allowDuplicatedCategory:ph.allowDuplicatedCategory,hide:!1,mirror:ph.mirror,orientation:ph.orientation,padding:ph.padding,reversed:ph.reversed,scale:ph.scale,tickCount:ph.tickCount,type:ph.type,width:ph.width,yAxisId:0};class Rk extends t.Component{render(){return t.createElement(Nk,this.props)}}Tk(Rk,"displayName","YAxis"),Tk(Rk,"defaultProps",_k);var Lk=a(9888);let Kk=function(e){e()};const zk=()=>Kk,Bk=Symbol.for("react-redux-context"),Fk="undefined"!=typeof globalThis?globalThis:{};function Wk(){var e;if(!t.createContext)return{};const r=null!=(e=Fk[Bk])?e:Fk[Bk]=new Map;let n=r.get(t.createContext);return n||(n=t.createContext(null),r.set(t.createContext,n)),n}const Uk=Wk();let Xk=null;a(4146);const Vk={notify(){},get:()=>[]};function $k(e,t){let r,n=Vk,i=0,a=!1;function o(){s.onStateChange&&s.onStateChange()}function l(){i++,r||(r=t?t.addNestedSub(o):e.subscribe(o),n=function(){const e=zk();let t=null,r=null;return{clear(){t=null,r=null},notify(){e((()=>{let e=t;for(;e;)e.callback(),e=e.next}))},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let n=!0,i=r={callback:e,next:null,prev:r};return i.prev?i.prev.next=i:t=i,function(){n&&null!==t&&(n=!1,i.next?i.next.prev=i.prev:r=i.prev,i.prev?i.prev.next=i.next:t=i.next)}}}}())}function c(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=Vk)}const s={addNestedSub:function(e){l();const t=n.subscribe(e);let r=!1;return()=>{r||(r=!0,t(),c())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,l())},tryUnsubscribe:function(){a&&(a=!1,c())},getListeners:()=>n};return s}const Hk=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement)?t.useLayoutEffect:t.useEffect;let qk=null;const Yk=function({store:e,context:r,children:n,serverState:i,stabilityCheck:a="once",noopCheck:o="once"}){const l=t.useMemo((()=>{const t=$k(e);return{store:e,subscription:t,getServerState:i?()=>i:void 0,stabilityCheck:a,noopCheck:o}}),[e,i,a,o]),c=t.useMemo((()=>e.getState()),[e]);Hk((()=>{const{subscription:t}=l;return t.onStateChange=t.notifyNestedSubs,t.trySubscribe(),c!==e.getState()&&t.notifyNestedSubs(),()=>{t.tryUnsubscribe(),t.onStateChange=void 0}}),[l,c]);const s=r||Uk;return t.createElement(s.Provider,{value:l},n)};var Gk;(e=>{Xk=e})(Re.useSyncExternalStoreWithSelector),(e=>{qk=e})(Lk.useSyncExternalStore),Gk=W.unstable_batchedUpdates,Kk=Gk;var Zk=Ge([(e,t)=>t,Hi,ih,Av,Jv,rm,jm,Ii],((e,t,r,n,i,a,o,l)=>{if(e&&t&&n&&i&&a){var c=function(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?Jn({x:e,y:t},n):null}(e.chartX,e.chartY,t,r,l);if(c){var u=((e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius)(c,t),f=((e,t,r,n,i)=>{var a,o=-1,l=null!==(a=null==t?void 0:t.length)&&void 0!==a?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&Math.abs(Math.abs(i[1]-i[0])-360)<=1e-6)for(var c=0;c<l;c++){var u=c>0?r[c-1].coordinate:r[l-1].coordinate,f=r[c].coordinate,d=c>=l-1?r[0].coordinate:r[c+1].coordinate,p=void 0;if(s(f-u)!==s(d-f)){var h=[];if(s(d-f)===s(i[1]-i[0])){p=d;var y=f+i[1]-i[0];h[0]=Math.min(y,(y+u)/2),h[1]=Math.max(y,(y+u)/2)}else{p=u;var v=d+i[1]-i[0];h[0]=Math.min(f,(v+f)/2),h[1]=Math.max(f,(v+f)/2)}var m=[Math.min(f,(p+f)/2),Math.max(f,(p+f)/2)];if(e>m[0]&&e<=m[1]||e>=h[0]&&e<=h[1]){({index:o}=r[c]);break}}else{var g=Math.min(u,d),b=Math.max(u,d);if(e>(g+f)/2&&e<=(b+f)/2){({index:o}=r[c]);break}}}else if(t)for(var x=0;x<l;x++)if(0===x&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x>0&&x<l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x===l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2){({index:o}=t[x]);break}return o})(u,o,a,n,i),d=((e,t,r,n)=>{var i=t.find((e=>e&&e.index===r));if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return ti(ti(ti({},n),qn(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:c}=n;return ti(ti(ti({},n),qn(n.cx,n.cy,l,c)),{},{angle:c,radius:l})}return{x:0,y:0}})(t,a,f,c);return{activeIndex:String(f),activeCoordinate:d}}}})),Jk=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},Qk=Kr("mouseClick"),eM=Mn();eM.startListening({actionCreator:Qk,effect:(e,t)=>{var r=e.payload,n=Zk(t.getState(),Jk(r));null!=(null==n?void 0:n.activeIndex)&&t.dispatch(dv({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var tM=Kr("mouseMove"),rM=Mn();function nM(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}rM.startListening({actionCreator:tM,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=ev(n,n.tooltip.settings.shared),a=Zk(n,Jk(r));"axis"===i&&(null!=(null==a?void 0:a.activeIndex)?t.dispatch(fv({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):t.dispatch(sv()))}});var iM={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},aM=$r({name:"rootProps",initialState:iM,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!==(r=t.payload.barGap)&&void 0!==r?r:iM.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),oM=aM.reducer,{updateOptions:lM}=aM.actions,cM=$r({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:sM}=cM.actions,uM=cM.reducer,fM=Kr("keyDown"),dM=Kr("focus"),pM=Mn();pM.startListening({actionCreator:fM,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,i=e.payload;if("ArrowRight"===i||"ArrowLeft"===i||"Enter"===i){var a=Number(xv(n,Iv(r))),o=rm(r);if("Enter"!==i){var l=a+("ArrowRight"===i?1:-1)*("left-to-right"===Gy(r)?1:-1);if(!(null==o||l>=o.length||l<0)){var c=Cm(r,"axis","hover",String(l));t.dispatch(hv({active:!0,activeIndex:l.toString(),activeDataKey:void 0,activeCoordinate:c}))}}else{var s=Cm(r,"axis","hover",String(n.index));t.dispatch(hv({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:s}))}}}}}),pM.startListening({actionCreator:dM,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=Cm(r,"axis","hover",String("0"));t.dispatch(hv({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}});var hM=Kr("externalEvent"),yM=Mn();yM.startListening({actionCreator:hM,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:dm(r),activeDataKey:sm(r),activeIndex:lm(r),activeLabel:cm(r),activeTooltipIndex:lm(r),isTooltipActive:pm(r)};e.payload.handler(n,e.payload.reactEvent)}}});var vM=Ge([Ev],(e=>e.tooltipItemPayloads)),mM=Ge([vM,Pv,(e,t,r)=>t,(e,t,r)=>r],((e,t,r,n)=>{var i=e.find((e=>e.settings.dataKey===n));if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}})),gM=Kr("touchMove"),bM=Mn();bM.startListening({actionCreator:gM,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=ev(n,n.tooltip.settings.shared);if("axis"===i){var a=Zk(n,Jk({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));null!=(null==a?void 0:a.activeIndex)&&t.dispatch(fv({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if("item"===i){var o,l=r.touches[0],c=document.elementFromPoint(l.clientX,l.clientY);if(!c||!c.getAttribute)return;var s=c.getAttribute(ki),u=null!==(o=c.getAttribute(Mi))&&void 0!==o?o:void 0,f=mM(t.getState(),s,u);t.dispatch(lv({activeDataKey:u,activeIndex:s,activeCoordinate:f}))}}});var xM=vr({brush:$E,cartesianAxis:HS,chartData:rg,graphicalItems:ow,layout:Ln,legend:Xa,options:Ym,polarAxis:vx,polarOptions:uM,referenceElements:AA,rootProps:oM,tooltip:yv}),wM=function(e){return Xr({reducer:xM,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([eM.middleware,rM.middleware,pM.middleware,yM.middleware,bM.middleware]),devTools:{serialize:{replacer:nM},name:"recharts-".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart")}})};function OM(e){var{preloadedState:r,children:n,reduxStoreName:i}=e,a=Li(),o=(0,t.useRef)(null);if(a)return n;null==o.current&&(o.current=wM(r,i));var l=Le;return t.createElement(Yk,{context:l,store:o.current},n)}function PM(e){var{layout:r,width:n,height:i,margin:a}=e,o=ze(),l=Li();return(0,t.useEffect)((()=>{l||(o(Nn(r)),o(_n({width:n,height:i})),o(In(a)))}),[o,l,r,n,i,a]),null}function EM(e){var r=ze();return(0,t.useEffect)((()=>{r(lM(e))}),[r,e]),null}var AM=["children"];function jM(){return jM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},jM.apply(null,arguments)}var SM={width:"100%",height:"100%"},kM=(0,t.forwardRef)(((e,r)=>{var n=Xi(),i=Vi(),a=go();if(!Uo(n)||!Uo(i))return null;var o,l,{children:c,otherAttributes:s,title:u,desc:f}=e;return o="number"==typeof s.tabIndex?s.tabIndex:a?0:void 0,l="string"==typeof s.role?s.role:a?"application":void 0,t.createElement(K,jM({},s,{title:u,desc:f,role:l,tabIndex:o,width:n,height:i,style:SM,ref:r}),c)})),MM=e=>{var{children:r}=e,n=Ue(Bi);if(!n)return null;var{width:i,height:a,y:o,x:l}=n;return t.createElement(K,{width:i,height:a,x:l,y:o},r)},TM=(0,t.forwardRef)(((e,r)=>{var{children:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,AM);return Li()?t.createElement(MM,null,n):t.createElement(kM,jM({ref:r},i),n)}));function CM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function DM(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?CM(Object(r),!0).forEach((function(t){IM(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):CM(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function IM(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var NM=(0,t.forwardRef)(((e,r)=>{var{children:i,className:a,height:o,onClick:l,onContextMenu:c,onDoubleClick:s,onMouseDown:u,onMouseEnter:f,onMouseLeave:d,onMouseMove:p,onMouseUp:h,onTouchEnd:y,onTouchMove:v,onTouchStart:m,style:g,width:b}=e,x=ze(),[w,O]=(0,t.useState)(null),[P,E]=(0,t.useState)(null);ig();var A=function(){var e=ze(),[r,n]=(0,t.useState)(null),i=Ue(Pi);return(0,t.useEffect)((()=>{if(null!=r){var t=r.getBoundingClientRect().width/r.offsetWidth;Wo(t)&&t!==i&&e(Rn(t))}}),[r,e,i]),n}(),j=(0,t.useCallback)((e=>{A(e),"function"==typeof r&&r(e),O(e),E(e)}),[A,r,O,E]),S=(0,t.useCallback)((e=>{x(Qk(e)),x(hM({handler:l,reactEvent:e}))}),[x,l]),k=(0,t.useCallback)((e=>{x(tM(e)),x(hM({handler:f,reactEvent:e}))}),[x,f]),M=(0,t.useCallback)((e=>{x(sv()),x(hM({handler:d,reactEvent:e}))}),[x,d]),T=(0,t.useCallback)((e=>{x(tM(e)),x(hM({handler:p,reactEvent:e}))}),[x,p]),C=(0,t.useCallback)((()=>{x(dM())}),[x]),D=(0,t.useCallback)((e=>{x(fM(e.key))}),[x]),I=(0,t.useCallback)((e=>{x(hM({handler:c,reactEvent:e}))}),[x,c]),N=(0,t.useCallback)((e=>{x(hM({handler:s,reactEvent:e}))}),[x,s]),_=(0,t.useCallback)((e=>{x(hM({handler:u,reactEvent:e}))}),[x,u]),R=(0,t.useCallback)((e=>{x(hM({handler:h,reactEvent:e}))}),[x,h]),L=(0,t.useCallback)((e=>{x(hM({handler:m,reactEvent:e}))}),[x,m]),K=(0,t.useCallback)((e=>{x(gM(e)),x(hM({handler:v,reactEvent:e}))}),[x,v]),z=(0,t.useCallback)((e=>{x(hM({handler:y,reactEvent:e}))}),[x,y]);return t.createElement(Wm.Provider,{value:w},t.createElement(U.Provider,{value:P},t.createElement("div",{className:n("recharts-wrapper",a),style:DM({position:"relative",cursor:"default",width:b,height:o},g),role:"application",onClick:S,onContextMenu:I,onDoubleClick:N,onFocus:C,onKeyDown:D,onMouseDown:_,onMouseEnter:k,onMouseLeave:M,onMouseMove:T,onMouseUp:R,onTouchEnd:z,onTouchMove:K,onTouchStart:L,ref:j},i)))})),_M=["children","className","width","height","style","compact","title","desc"];var RM=(0,t.forwardRef)(((e,r)=>{var{children:n,className:i,width:a,height:o,style:l,compact:c,title:s,desc:u}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,_M),d=_(f,!1);return c?t.createElement(TM,{otherAttributes:d,title:s,desc:u},n):t.createElement(NM,{className:i,style:l,width:a,height:o,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},t.createElement(TM,{otherAttributes:d,title:s,desc:u,ref:r},t.createElement(SA,null,n)))})),LM=["width","height"];function KM(){return KM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},KM.apply(null,arguments)}var zM={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},BM=(0,t.forwardRef)((function(e,r){var n,i=cl(e.categoricalChartProps,zM),{width:a,height:o}=i,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,LM);if(!Uo(a)||!Uo(o))return null;var{chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:u,tooltipPayloadSearcher:f,categoricalChartProps:d}=e,p={chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:u,tooltipPayloadSearcher:f,eventEmitter:void 0};return t.createElement(OM,{preloadedState:{options:p},reduxStoreName:null!==(n=d.id)&&void 0!==n?n:c},t.createElement(KE,{chartData:d.data}),t.createElement(PM,{width:a,height:o,layout:i.layout,margin:i.margin}),t.createElement(EM,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),t.createElement(RM,KM({},l,{width:a,height:o,ref:r})))})),FM=["axis"],WM=(0,t.forwardRef)(((e,r)=>t.createElement(BM,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:FM,tooltipPayloadSearcher:Hm,categoricalChartProps:e,ref:r}))),UM=["axis","item"],XM=(0,t.forwardRef)(((e,r)=>t.createElement(BM,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:UM,tooltipPayloadSearcher:Hm,categoricalChartProps:e,ref:r})));function VM(e){var r=ze();return(0,t.useEffect)((()=>{r(sM(e))}),[r,e]),null}var $M=["width","height","layout"];function HM(){return HM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},HM.apply(null,arguments)}var qM={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},YM=(0,t.forwardRef)((function(e,r){var n,i=cl(e.categoricalChartProps,qM),{width:a,height:o,layout:l}=i,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,$M);if(!Uo(a)||!Uo(o))return null;var{chartName:s,defaultTooltipEventType:u,validateTooltipEventTypes:f,tooltipPayloadSearcher:d}=e,p={chartName:s,defaultTooltipEventType:u,validateTooltipEventTypes:f,tooltipPayloadSearcher:d,eventEmitter:void 0};return t.createElement(OM,{preloadedState:{options:p},reduxStoreName:null!==(n=i.id)&&void 0!==n?n:s},t.createElement(KE,{chartData:i.data}),t.createElement(PM,{width:a,height:o,layout:l,margin:i.margin}),t.createElement(EM,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),t.createElement(VM,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),t.createElement(RM,HM({width:a,height:o},c,{ref:r})))})),GM=["item"],ZM={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},JM=(0,t.forwardRef)(((e,r)=>{var n=cl(e,ZM);return t.createElement(YM,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:GM,tooltipPayloadSearcher:Hm,categoricalChartProps:n,ref:r})})),QM=a(1576),eT=a.n(QM),tT=["width","height","className","style","children","type"];function rT(){return rT=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},rT.apply(null,arguments)}function nT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function iT(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nT(Object(r),!0).forEach((function(t){aT(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nT(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function aT(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var oT="value",lT=(e,t)=>l()(e,t),cT={chartName:"Treemap",defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],tooltipPayloadSearcher:lT,eventEmitter:void 0},sT=e=>{var t,{depth:r,node:n,index:i,dataKey:a,nameKey:o,nestedActiveTooltipIndex:l}=e,c=0===r?"":function(e){return"".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"","children[").concat(e,"]")}(i,l),{children:s}=n,f=r+1,d=s&&s.length?s.map(((e,t)=>sT({depth:f,node:e,index:t,dataKey:a,nameKey:o,nestedActiveTooltipIndex:c}))):null;return t=s&&s.length?d.reduce(((e,t)=>e+t[oT]),0):u(n[a])||n[a]<=0?0:n[a],iT(iT({},n),{},{children:d,name:ni(n,o,""),[oT]:t,depth:r,index:i,tooltipIndex:c})},uT=(e,t,r)=>{var n=t*t,i=e.area*e.area,{min:a,max:o}=e.reduce(((e,t)=>({min:Math.min(e.min,t.area),max:Math.max(e.max,t.area)})),{min:1/0,max:0});return i?Math.max(n*o*r/i,i/(n*a*r)):1/0},fT=(e,t,r,n)=>t===r.width?((e,t,r,n)=>{var i=t?Math.round(e.area/t):0;(n||i>r.height)&&(i=r.height);for(var a,o=r.x,l=0,c=e.length;l<c;l++)(a=e[l]).x=o,a.y=r.y,a.height=i,a.width=Math.min(i?Math.round(a.area/i):0,r.x+r.width-o),o+=a.width;return a.width+=r.x+r.width-o,iT(iT({},r),{},{y:r.y+i,height:r.height-i})})(e,t,r,n):((e,t,r,n)=>{var i=t?Math.round(e.area/t):0;(n||i>r.width)&&(i=r.width);for(var a,o=r.y,l=0,c=e.length;l<c;l++)(a=e[l]).x=r.x,a.y=o,a.width=i,a.height=Math.min(i?Math.round(a.area/i):0,r.y+r.height-o),o+=a.height;return a&&(a.height+=r.y+r.height-o),iT(iT({},r),{},{x:r.x+i,width:r.width-i})})(e,t,r,n),dT=(e,t)=>{var{children:r}=e;if(r&&r.length){var n,i,a=(e=>({x:e.x,y:e.y,width:e.width,height:e.height}))(e),o=[],l=1/0,c=Math.min(a.width,a.height),s=((e,t)=>{var r=t<0?0:t;return e.map((e=>{var t=e[oT]*r;return iT(iT({},e),{},{area:u(t)||t<=0?0:t})}))})(r,a.width*a.height/e[oT]),f=s.slice();for(o.area=0;f.length>0;)o.push(n=f[0]),o.area+=n.area,(i=uT(o,c,t))<=l?(f.shift(),l=i):(o.area-=o.pop().area,a=fT(o,c,a,!1),c=Math.min(a.width,a.height),o.length=o.area=0,l=1/0);return o.length&&(a=fT(o,c,a,!0),o.length=o.area=0),iT(iT({},e),{},{children:s.map((e=>dT(e,t)))})}return e},pT={isAnimationFinished:!1,formatRoot:null,currentRoot:null,nestIndex:[]};function hT(e){var{content:r,nodeProps:n,type:i,colorPanel:a,onMouseEnter:o,onMouseLeave:l,onClick:c}=e;if(t.isValidElement(r))return t.createElement(F,{onMouseEnter:o,onMouseLeave:l,onClick:c},t.cloneElement(r,n));if("function"==typeof r)return t.createElement(F,{onMouseEnter:o,onMouseLeave:l,onClick:c},r(n));var{x:s,y:u,width:f,height:d,index:p}=n,h=null;f>10&&d>10&&n.children&&"nest"===i&&(h=t.createElement(Sb,{points:[{x:s+2,y:u+d/2},{x:s+6,y:u+d/2+3},{x:s+2,y:u+d/2+6}]}));var y=null,v=jg(n.name);f>20&&d>20&&v.width<f&&v.height<d&&(y=t.createElement("text",{x:s+8,y:u+d/2+7,fontSize:14},n.name));var m=a||Si;return t.createElement("g",null,t.createElement(Wl,rT({fill:n.depth<2?m[p%m.length]:"rgba(255,255,255,0)",stroke:"#fff"},eT()(n,["children"]),{onMouseEnter:o,onMouseLeave:l,onClick:c,"data-recharts-item-index":n.tooltipIndex})),h,y)}function yT(e){var r=ze(),n=e.nodeProps?{x:e.nodeProps.x+e.nodeProps.width/2,y:e.nodeProps.y+e.nodeProps.height/2}:null;return t.createElement(hT,rT({},e,{onMouseEnter:()=>{r(lv({activeIndex:e.nodeProps.tooltipIndex,activeDataKey:e.dataKey,activeCoordinate:n}))},onMouseLeave:()=>{},onClick:()=>{r(uv({activeIndex:e.nodeProps.tooltipIndex,activeDataKey:e.dataKey,activeCoordinate:n}))}}))}function vT(e){var{props:t,currentRoot:r}=e,{dataKey:n,nameKey:i,stroke:a,fill:o}=t;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:a,strokeWidth:void 0,fill:o,dataKey:n,nameKey:i,name:void 0,hide:!1,type:void 0,color:o,unit:""}}}var mT={top:0,right:0,bottom:0,left:0};class gT extends t.PureComponent{constructor(){super(...arguments),aT(this,"state",iT({},pT)),aT(this,"handleAnimationEnd",(()=>{var{onAnimationEnd:e}=this.props;this.setState({isAnimationFinished:!0}),"function"==typeof e&&e()})),aT(this,"handleAnimationStart",(()=>{var{onAnimationStart:e}=this.props;this.setState({isAnimationFinished:!1}),"function"==typeof e&&e()})),aT(this,"handleTouchMove",((e,t)=>{var r=t.touches[0],n=document.elementFromPoint(r.clientX,r.clientY);if(n&&n.getAttribute){var i=n.getAttribute("data-recharts-item-index"),a=lT(this.state.formatRoot,i);if(a){var{dataKey:o,dispatch:l}=this.props,c={x:a.x+a.width/2,y:a.y+a.height/2};l(lv({activeIndex:i,activeDataKey:o,activeCoordinate:c}))}}}))}static getDerivedStateFromProps(e,t){if(e.data!==t.prevData||e.type!==t.prevType||e.width!==t.prevWidth||e.height!==t.prevHeight||e.dataKey!==t.prevDataKey||e.aspectRatio!==t.prevAspectRatio){var r=sT({depth:0,node:{children:e.data,x:0,y:0,width:e.width,height:e.height},index:0,dataKey:e.dataKey,nameKey:e.nameKey}),n=dT(r,e.aspectRatio);return iT(iT({},t),{},{formatRoot:n,currentRoot:r,nestIndex:[r],prevAspectRatio:e.aspectRatio,prevData:e.data,prevWidth:e.width,prevHeight:e.height,prevDataKey:e.dataKey,prevType:e.type})}return null}handleMouseEnter(e,t){t.persist();var{onMouseEnter:r}=this.props;r&&r(e,t)}handleMouseLeave(e,t){t.persist();var{onMouseLeave:r}=this.props;r&&r(e,t)}handleClick(e){var{onClick:t,type:r}=this.props;if("nest"===r&&e.children){var{width:n,height:i,dataKey:a,nameKey:o,aspectRatio:l}=this.props,c=sT({depth:0,node:iT(iT({},e),{},{x:0,y:0,width:n,height:i}),index:0,dataKey:a,nameKey:o,nestedActiveTooltipIndex:e.tooltipIndex}),s=dT(c,l),{nestIndex:u}=this.state;u.push(e),this.setState({formatRoot:s,currentRoot:c,nestIndex:u})}t&&t(e)}handleNestIndex(e,t){var{nestIndex:r}=this.state,{width:n,height:i,dataKey:a,nameKey:o,aspectRatio:l}=this.props,c=sT({depth:0,node:iT(iT({},e),{},{x:0,y:0,width:n,height:i}),index:0,dataKey:a,nameKey:o,nestedActiveTooltipIndex:e.tooltipIndex}),s=dT(c,l);r=r.slice(0,t+1),this.setState({formatRoot:s,currentRoot:e,nestIndex:r})}renderItem(e,r,n){var{isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:l,isUpdateAnimationActive:c,type:s,animationId:u,colorPanel:f,dataKey:d}=this.props,{isAnimationFinished:p}=this.state,{width:h,height:y,x:v,y:m,depth:g}=r,b=parseInt("".concat((2*Math.random()-1)*h),10),x={};return(n||"nest"===s)&&(x={onMouseEnter:this.handleMouseEnter.bind(this,r),onMouseLeave:this.handleMouseLeave.bind(this,r),onClick:this.handleClick.bind(this,r)}),i?t.createElement(Kl,{begin:a,duration:o,isActive:i,easing:l,key:"treemap-".concat(u),from:{x:v,y:m,width:h,height:y},to:{x:v,y:m,width:h,height:y},onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(n=>{var{x:u,y:h,width:y,height:v}=n;return t.createElement(Kl,{from:"translate(".concat(b,"px, ").concat(b,"px)"),to:"translate(0, 0)",attributeName:"transform",begin:a,easing:l,isActive:i,duration:o},t.createElement(F,x,g>2&&!p?null:t.createElement(yT,{content:e,dataKey:d,nodeProps:iT(iT({},r),{},{isAnimationActive:i,isUpdateAnimationActive:!c,width:y,height:v,x:u,y:h}),type:s,colorPanel:f})))})):t.createElement(F,x,t.createElement(yT,{content:e,dataKey:d,nodeProps:iT(iT({},r),{},{isAnimationActive:!1,isUpdateAnimationActive:!1,width:h,height:y,x:v,y:m}),type:s,colorPanel:f}))}renderNode(e,r){var{content:n,type:i}=this.props,a=iT(iT(iT({},_(this.props,!1)),r),{},{root:e}),o=!r.children||!r.children.length,{currentRoot:l}=this.state;return!(l.children||[]).filter((e=>e.depth===r.depth&&e.name===r.name)).length&&e.depth&&"nest"===i?null:t.createElement(F,{key:"recharts-treemap-node-".concat(a.x,"-").concat(a.y,"-").concat(a.name),className:"recharts-treemap-depth-".concat(r.depth)},this.renderItem(n,a,o),r.children&&r.children.length?r.children.map((e=>this.renderNode(r,e))):null)}renderAllNodes(){var{formatRoot:e}=this.state;return e?this.renderNode(e,e):null}renderNestIndex(){var{nameKey:e,nestIndexContent:r}=this.props,{nestIndex:n}=this.state;return t.createElement("div",{className:"recharts-treemap-nest-index-wrapper",style:{marginTop:"8px",textAlign:"center"}},n.map(((n,i)=>{var a=l()(n,e,"root"),o=null;return t.isValidElement(r)&&(o=t.cloneElement(r,n,i)),o="function"==typeof r?r(n,i):a,t.createElement("div",{onClick:this.handleNestIndex.bind(this,n,i),key:"nest-index-".concat(y()),className:"recharts-treemap-nest-index-box",style:{cursor:"pointer",display:"inline-block",padding:"0 7px",background:"#000",color:"#fff",marginRight:"3px"}},o)})))}render(){var e=this.props,{width:r,height:n,className:i,style:a,children:o,type:l}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,tT),s=_(c,!1);return t.createElement(Wm.Provider,{value:this.state.tooltipPortal},t.createElement(Mw,{fn:vT,args:{props:this.props,currentRoot:this.state.currentRoot}}),t.createElement(NM,{className:i,style:a,width:r,height:n,ref:e=>{null==this.state.tooltipPortal&&this.setState({tooltipPortal:e})},onMouseEnter:void 0,onMouseLeave:void 0,onClick:void 0,onMouseMove:void 0,onMouseDown:void 0,onMouseUp:void 0,onContextMenu:void 0,onDoubleClick:void 0,onTouchStart:void 0,onTouchMove:this.handleTouchMove,onTouchEnd:void 0},t.createElement(K,rT({},s,{width:r,height:"nest"===l?n-30:n}),this.renderAllNodes(),o),"nest"===l&&this.renderNestIndex()))}}function bT(e){var r=ze();return t.createElement(gT,rT({},e,{dispatch:r}))}function xT(e){var r,{width:n,height:i}=e;return Uo(n)&&Uo(i)?t.createElement(OM,{preloadedState:{options:cT},reduxStoreName:null!==(r=e.className)&&void 0!==r?r:"Treemap"},t.createElement(Yi,{width:n,height:i}),t.createElement(Gi,{margin:mT}),t.createElement(bT,e)):null}aT(gT,"displayName","Treemap"),aT(gT,"defaultProps",{aspectRatio:.5*(1+Math.sqrt(5)),dataKey:"value",nameKey:"name",type:"flat",isAnimationActive:!mo.isSsr,isUpdateAnimationActive:!mo.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"linear"});var wT=a(2067),OT=a.n(wT),PT=["sourceX","sourceY","sourceControlX","targetX","targetY","targetControlX","linkWidth"],ET=["width","height","className","style","children"];function AT(){return AT=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},AT.apply(null,arguments)}function jT(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function ST(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function kT(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ST(Object(r),!0).forEach((function(t){MT(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ST(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function MT(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var TT=e=>e.y+e.dy/2,CT=e=>e&&e.value||0,DT=(e,t)=>t.reduce(((t,r)=>t+CT(e[r])),0),IT=(e,t,r)=>r.reduce(((r,n)=>{var i=t[n],a=e[i.source];return r+TT(a)*CT(t[n])}),0),NT=(e,t,r)=>r.reduce(((r,n)=>{var i=t[n],a=e[i.target];return r+TT(a)*CT(t[n])}),0),_T=(e,t)=>e.y-t.y,RT=(e,t)=>{for(var{targetNodes:r}=t,n=0,i=r.length;n<i;n++){var a=e[r[n]];a&&(a.depth=Math.max(t.depth+1,a.depth),RT(e,a))}},LT=function(e,t,r){for(var n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],i=0,a=e.length;i<a;i++){var o=e[i],l=o.length;n&&o.sort(_T);for(var c=0,s=0;s<l;s++){var u=o[s],f=c-u.y;f>0&&(u.y+=f),c=u.y+u.dy+r}c=t+r;for(var d=l-1;d>=0;d--){var p=o[d],h=p.y+p.dy+r-c;if(!(h>0))break;p.y-=h,c=p.y}}},KT=(e,t,r,n)=>{for(var i=0,a=t.length;i<a;i++)for(var o=t[i],l=0,c=o.length;l<c;l++){var s=o[l];if(s.sourceLinks.length){var u=DT(r,s.sourceLinks),f=IT(e,r,s.sourceLinks)/u;s.y+=(f-TT(s))*n}}},zT=(e,t,r,n)=>{for(var i=t.length-1;i>=0;i--)for(var a=t[i],o=0,l=a.length;o<l;o++){var c=a[o];if(c.targetLinks.length){var s=DT(r,c.targetLinks),u=NT(e,r,c.targetLinks)/s;c.y+=(u-TT(c))*n}}},BT=e=>{var{data:t,width:r,height:n,iterations:i,nodeWidth:a,nodePadding:o,sort:l}=e,{links:c}=t,{tree:s}=((e,t,r)=>{for(var{nodes:n,links:i}=e,a=n.map(((e,t)=>{var r=((e,t)=>{for(var r=[],n=[],i=[],a=[],o=0,l=e.length;o<l;o++){var c=e[o];c.source===t&&(i.push(c.target),a.push(o)),c.target===t&&(r.push(c.source),n.push(o))}return{sourceNodes:r,sourceLinks:n,targetLinks:a,targetNodes:i}})(i,t);return kT(kT(kT({},e),r),{},{value:Math.max(DT(i,r.sourceLinks),DT(i,r.targetLinks)),depth:0})})),o=0,l=a.length;o<l;o++){var c=a[o];c.sourceNodes.length||RT(a,c)}var s=cx()(a,(e=>e.depth)).depth;if(s>=1)for(var u=(t-r)/s,f=0,d=a.length;f<d;f++){var p=a[f];p.targetNodes.length||(p.depth=s),p.x=p.depth*u,p.dx=r}return{tree:a,maxDepth:s}})(t,r,a),u=(e=>{for(var t=[],r=0,n=e.length;r<n;r++){var i=e[r];t[i.depth]||(t[i.depth]=[]),t[i.depth].push(i)}return t})(s),f=((e,t,r,n)=>{for(var i=Math.min(...e.map((e=>(t-(e.length-1)*r)/OT()(e,CT)))),a=0,o=e.length;a<o;a++)for(var l=0,c=e[a].length;l<c;l++){var s=e[a][l];s.y=l,s.dy=s.value*i}return n.map((e=>kT(kT({},e),{},{dy:CT(e)*i})))})(u,n,o,c);LT(u,n,o,l);for(var d=1,p=1;p<=i;p++)zT(s,u,f,d*=.99),LT(u,n,o,l),KT(s,u,f,d),LT(u,n,o,l);return((e,t)=>{for(var r=0,n=e.length;r<n;r++){var i=e[r],a=0,o=0;i.targetLinks.sort(((r,n)=>e[t[r].target].y-e[t[n].target].y)),i.sourceLinks.sort(((r,n)=>e[t[r].source].y-e[t[n].source].y));for(var l=0,c=i.targetLinks.length;l<c;l++){var s=t[i.targetLinks[l]];s&&(s.sy=a,a+=s.dy)}for(var u=0,f=i.sourceLinks.length;u<f;u++){var d=t[i.sourceLinks[u]];d&&(d.ty=o,o+=d.dy)}}})(s,f),{nodes:s,links:f}},FT=(e,t)=>"node"===t?{x:+e.x+ +e.width/2,y:+e.y+ +e.height/2}:"sourceX"in e&&{x:(e.sourceX+e.targetX)/2,y:(e.sourceY+e.targetY)/2},WT={chartName:"Sankey",defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],tooltipPayloadSearcher:(e,t,r,n)=>{if(null!=t&&"string"==typeof t){var i=t.split("-"),[a,o]=i,c=l()(r,"".concat(a,"s[").concat(o,"]"));if(c){var s=((e,t,r)=>{var{payload:n}=e;if("node"===t)return{payload:n,name:ni(n,r,""),value:ni(n,"value")};if("source"in n&&n.source&&n.target){var i=ni(n.source,r,""),a=ni(n.target,r,"");return{payload:n,name:"".concat(i," - ").concat(a),value:ni(n,"value")}}return null})(c,a,n);return s}}},eventEmitter:void 0};function UT(e){var{dataKey:t,nameKey:r,stroke:n,strokeWidth:i,fill:a,name:o,data:l}=e;return{dataDefinedOnItem:l,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,name:o,nameKey:r,color:a,unit:""}}}var XT={top:0,right:0,bottom:0,left:0};function VT(e){var{props:r,i:n,linkContent:i,onMouseEnter:a,onMouseLeave:o,onClick:l,dataKey:c}=e,s=FT(r,"link"),u="link-".concat(n),f=ze(),d={onMouseEnter:e=>{f(lv({activeIndex:u,activeDataKey:c,activeCoordinate:s})),a(r,e)},onMouseLeave:e=>{f(cv()),o(r,e)},onClick:e=>{f(uv({activeIndex:u,activeDataKey:c,activeCoordinate:s})),l(r,e)}};return t.createElement(F,d,function(e,r){if(t.isValidElement(e))return t.cloneElement(e,r);if("function"==typeof e)return e(r);var{sourceX:n,sourceY:i,sourceControlX:a,targetX:o,targetY:l,targetControlX:c,linkWidth:s}=r,u=jT(r,PT);return t.createElement("path",AT({className:"recharts-sankey-link",d:"\n          M".concat(n,",").concat(i,"\n          C").concat(a,",").concat(i," ").concat(c,",").concat(l," ").concat(o,",").concat(l,"\n        "),fill:"none",stroke:"#333",strokeWidth:s,strokeOpacity:"0.2"},_(u,!1)))}(i,r))}function $T(e){var{modifiedLinks:r,links:n,linkContent:i,onMouseEnter:a,onMouseLeave:o,onClick:l,dataKey:c}=e;return t.createElement(F,{className:"recharts-sankey-links",key:"recharts-sankey-links"},n.map(((e,n)=>{var s=r[n];return t.createElement(VT,{key:"link-".concat(e.source,"-").concat(e.target,"-").concat(e.value),props:s,linkContent:i,i:n,onMouseEnter:a,onMouseLeave:o,onClick:l,dataKey:c})})))}function HT(e){var{props:r,nodeContent:n,i,onMouseEnter:a,onMouseLeave:o,onClick:l,dataKey:c}=e,s=ze(),u=FT(r,"node"),f="node-".concat(i),d={onMouseEnter:e=>{s(lv({activeIndex:f,activeDataKey:c,activeCoordinate:u})),a(r,e)},onMouseLeave:e=>{s(cv()),o(r,e)},onClick:e=>{s(uv({activeIndex:f,activeDataKey:c,activeCoordinate:u})),l(r,e)}};return t.createElement(F,d,function(e,r){return t.isValidElement(e)?t.cloneElement(e,r):"function"==typeof e?e(r):t.createElement(Wl,AT({className:"recharts-sankey-node",fill:"#0088fe",fillOpacity:"0.8"},_(r,!1)))}(n,r))}function qT(e){var{modifiedNodes:r,nodeContent:n,onMouseEnter:i,onMouseLeave:a,onClick:o,dataKey:l}=e;return t.createElement(F,{className:"recharts-sankey-nodes",key:"recharts-sankey-nodes"},r.map(((e,r)=>t.createElement(HT,{props:e,nodeContent:n,i:r,onMouseEnter:i,onMouseLeave:a,onClick:o,dataKey:l}))))}class YT extends t.PureComponent{constructor(){super(...arguments),MT(this,"state",{nodes:[],links:[],modifiedLinks:[],modifiedNodes:[]})}static getDerivedStateFromProps(e,t){var{data:r,width:n,height:i,margin:a,iterations:o,nodeWidth:c,nodePadding:s,sort:u,linkCurvature:f}=e;if(r!==t.prevData||n!==t.prevWidth||i!==t.prevHeight||!QA(a,t.prevMargin)||o!==t.prevIterations||c!==t.prevNodeWidth||s!==t.prevNodePadding||u!==t.sort){var d=n-(a&&a.left||0)-(a&&a.right||0),p=i-(a&&a.top||0)-(a&&a.bottom||0),{links:h,nodes:y}=BT({data:r,width:d,height:p,iterations:o,nodeWidth:c,nodePadding:s,sort:u}),v=l()(a,"top")||0,m=l()(a,"left")||0,g=h.map(((t,r)=>(e=>{var{link:t,nodes:r,left:n,top:i,i:a,linkContent:o,linkCurvature:l}=e,{sy:c,ty:s,dy:u}=t,f=r[t.source],d=r[t.target],p=f.x+f.dx+n,h=d.x+n,y=((e,t)=>{var r=+e,n=t-r;return e=>r+n*e})(p,h),v=y(l),m=y(1-l);return kT({sourceX:p,targetX:h,sourceY:f.y+c+u/2+i,targetY:d.y+s+u/2+i,sourceControlX:v,targetControlX:m,sourceRelativeY:c,targetRelativeY:s,linkWidth:u,index:a,payload:kT(kT({},t),{},{source:f,target:d})},_(o,!1))})({link:t,nodes:y,i:r,top:v,left:m,linkContent:e.link,linkCurvature:f}))),b=y.map(((t,r)=>(e=>{var{node:t,nodeContent:r,top:n,left:i,i:a}=e,{x:o,y:l,dx:c,dy:s}=t;return kT(kT({},_(r,!1)),{},{x:o+i,y:l+n,width:c,height:s,index:a,payload:t})})({node:t,nodeContent:e.node,i:r,top:v,left:m})));return kT(kT({},t),{},{nodes:y,links:h,modifiedLinks:g,modifiedNodes:b,prevData:r,prevWidth:o,prevHeight:i,prevMargin:a,prevNodePadding:s,prevNodeWidth:c,prevIterations:o,prevSort:u})}return null}handleMouseEnter(e,t,r){var{onMouseEnter:n}=this.props;n&&n(e,t,r)}handleMouseLeave(e,t,r){var{onMouseLeave:n}=this.props;n&&n(e,t,r)}handleClick(e,t,r){var{onClick:n}=this.props;n&&n(e,t,r)}render(){var e=this.props,{width:r,height:n,className:i,style:a,children:o}=e,l=jT(e,ET);if(!Uo(r)||!Uo(n))return null;var{links:c,modifiedNodes:s,modifiedLinks:u}=this.state,f=_(l,!1);return t.createElement(OM,{preloadedState:{options:WT},reduxStoreName:null!=i?i:"Sankey"},t.createElement(Mw,{fn:UT,args:this.props}),t.createElement(zE,{computedData:{links:u,nodes:s}}),t.createElement(Yi,{width:r,height:n}),t.createElement(Gi,{margin:XT}),t.createElement(Wm.Provider,{value:this.state.tooltipPortal},t.createElement(NM,{className:i,style:a,width:r,height:n,ref:e=>{null==this.state.tooltipPortal&&this.setState({tooltipPortal:e})},onMouseEnter:void 0,onMouseLeave:void 0,onClick:void 0,onMouseMove:void 0,onMouseDown:void 0,onMouseUp:void 0,onContextMenu:void 0,onDoubleClick:void 0,onTouchStart:void 0,onTouchMove:void 0,onTouchEnd:void 0},t.createElement(K,AT({},f,{width:r,height:n}),o,t.createElement($T,{links:c,modifiedLinks:u,linkContent:this.props.link,dataKey:this.props.dataKey,onMouseEnter:(e,t)=>this.handleMouseEnter(e,"link",t),onMouseLeave:(e,t)=>this.handleMouseLeave(e,"link",t),onClick:(e,t)=>this.handleClick(e,"link",t)}),t.createElement(qT,{modifiedNodes:s,nodeContent:this.props.node,dataKey:this.props.dataKey,onMouseEnter:(e,t)=>this.handleMouseEnter(e,"node",t),onMouseLeave:(e,t)=>this.handleMouseLeave(e,"node",t),onClick:(e,t)=>this.handleClick(e,"node",t)})))))}}MT(YT,"displayName","Sankey"),MT(YT,"defaultProps",{nameKey:"name",dataKey:"value",nodePadding:10,nodeWidth:10,linkCurvature:.5,iterations:32,margin:{top:5,right:5,bottom:5,left:5},sort:!0});var GT=["axis"],ZT={layout:"centric",startAngle:90,endAngle:-270,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},JT=(0,t.forwardRef)(((e,r)=>{var n=cl(e,ZT);return t.createElement(YM,{chartName:"RadarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:GT,tooltipPayloadSearcher:Hm,categoricalChartProps:n,ref:r})})),QT=["item"],eC=(0,t.forwardRef)(((e,r)=>t.createElement(BM,{chartName:"ScatterChart",defaultTooltipEventType:"item",validateTooltipEventTypes:QT,tooltipPayloadSearcher:Hm,categoricalChartProps:e,ref:r}))),tC=["axis"],rC=(0,t.forwardRef)(((e,r)=>t.createElement(BM,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:tC,tooltipPayloadSearcher:Hm,categoricalChartProps:e,ref:r}))),nC=["axis","item"],iC={layout:"radial",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},aC=(0,t.forwardRef)(((e,r)=>{var n=cl(e,iC);return t.createElement(YM,{chartName:"RadialBarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:nC,tooltipPayloadSearcher:Hm,categoricalChartProps:n,ref:r})})),oC=["axis"],lC=(0,t.forwardRef)(((e,r)=>t.createElement(BM,{chartName:"ComposedChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:oC,tooltipPayloadSearcher:Hm,categoricalChartProps:e,ref:r})));function cC(){return cC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},cC.apply(null,arguments)}function sC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function uC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sC(Object(r),!0).forEach((function(t){fC(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sC(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function fC(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var dC={fontWeight:"bold",paintOrder:"stroke fill",fontSize:".75rem",stroke:"#FFF",fill:"black",pointerEvents:"none"};function pC(e){if(!e.children||0===e.children.length)return 1;var t=e.children.map((e=>pC(e)));return 1+Math.max(...t)}function hC(e){var t={};return e.forEach(((e,r)=>{t[r]=e})),t}function yC(e){var{dataKey:t,nameKey:r,data:n,stroke:i,fill:a,positions:o}=e;return{dataDefinedOnItem:n.children,positions:hC(o),settings:{stroke:i,strokeWidth:void 0,fill:a,nameKey:r,dataKey:t,name:r?void 0:t,hide:!1,type:void 0,color:a,unit:""}}}var vC={top:0,right:0,bottom:0,left:0},mC={options:{validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",chartName:"Sunburst",tooltipPayloadSearcher:(e,t)=>l()(e,t),eventEmitter:void 0}},gC=e=>{var{className:r,data:i,children:a,width:o,height:l,padding:c=2,dataKey:s="value",nameKey:u="name",ringPadding:f=2,innerRadius:d=50,fill:p="#333",stroke:h="#FFF",textOptions:y=dC,outerRadius:v=Math.min(o,l)/2,cx:m=o/2,cy:g=l/2,startAngle:b=0,endAngle:x=360,onClick:w,onMouseEnter:O,onMouseLeave:P}=e,E=ze(),A=qs([0,i[s]],[0,x]),j=(v-d)/pC(i),S=[],k=new Map([]),[M,T]=(0,t.useState)(null);!function e(r,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,{radius:a,innerR:o,initialAngle:l,childColor:u,nestedActiveTooltipIndex:d}=n,v=l;r&&r.forEach(((r,n)=>{var l,b,x=1===i?"[".concat(n,"]"):function(e){return"".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"","children[").concat(e,"]")}(n,d),j=uC(uC({},r),{},{tooltipIndex:x}),M=A(r[s]),T=v,C=null!==(l=null!==(b=null==r?void 0:r.fill)&&void 0!==b?b:u)&&void 0!==l?l:p,{x:D,y:I}=qn(0,0,o+a/2,-(T+M-M/2));v+=M,S.push(t.createElement("g",{key:"sunburst-sector-".concat(r.name,"-").concat(n)},t.createElement(ql,{onClick:()=>{return e=j,w&&w(e),void E(uv({activeIndex:e.tooltipIndex,activeDataKey:s,activeCoordinate:k.get(e.name)}));var e},onMouseEnter:e=>function(e,t){O&&O(e,t),E(lv({activeIndex:e.tooltipIndex,activeDataKey:s,activeCoordinate:k.get(e.name)}))}(j,e),onMouseLeave:e=>function(e,t){P&&P(e,t),E(cv())}(j,e),fill:C,stroke:h,strokeWidth:c,startAngle:T,endAngle:T+M,innerRadius:o,outerRadius:o+a,cx:m,cy:g}),t.createElement(qg,cC({},y,{alignmentBaseline:"middle",textAnchor:"middle",x:D+m,y:g-I}),r[s])));var{x:N,y:_}=qn(m,g,o+a/2,T);return k.set(r.name,{x:N,y:_}),e(r.children,{radius:a,innerR:o+a+f,initialAngle:T,childColor:C,nestedActiveTooltipIndex:x},i+1)}))}(i.children,{radius:j,innerR:d,initialAngle:b});var C=n("recharts-sunburst",r);return t.createElement(Wm.Provider,{value:M},t.createElement(NM,{className:r,width:o,height:l,ref:e=>{null==M&&null!=e&&T(e)},onMouseEnter:void 0,onMouseLeave:void 0,onClick:void 0,onMouseMove:void 0,onMouseDown:void 0,onMouseUp:void 0,onContextMenu:void 0,onDoubleClick:void 0,onTouchStart:void 0,onTouchMove:void 0,onTouchEnd:void 0},t.createElement(K,{width:o,height:l},t.createElement(F,{className:C},S),t.createElement(Mw,{fn:yC,args:{dataKey:s,data:i,stroke:h,fill:p,nameKey:u,positions:k}}),a)))},bC=e=>{var r;return t.createElement(OM,{preloadedState:mC,reduxStoreName:null!==(r=e.className)&&void 0!==r?r:"SunburstChart"},t.createElement(Yi,{width:e.width,height:e.height}),t.createElement(Gi,{margin:vC}),t.createElement(gC,e))};function xC(){return xC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xC.apply(null,arguments)}function wC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function OC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?wC(Object(r),!0).forEach((function(t){PC(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wC(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function PC(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function EC(e,t){var r="".concat(t.x||e.x),n=parseInt(r,10),i="".concat(t.y||e.y),a=parseInt(i,10),o="".concat((null==t?void 0:t.height)||(null==e?void 0:e.height)),l=parseInt(o,10);return OC(OC(OC({},t),Ew(e)),{},{height:l,x:n,y:a})}function AC(e){return t.createElement(Aw,xC({shapeType:"trapezoid",propTransformer:EC},e))}function jC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function SC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?jC(Object(r),!0).forEach((function(t){kC(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jC(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function kC(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var MC=Ge([Ii,(e,t)=>t,cp],((e,t,r)=>{var n,{data:i,dataKey:a,nameKey:o,tooltipType:l,lastShapeType:c,reversed:s,customWidth:u,cells:f,presentationProps:d}=t,{chartData:p}=r;if(null!=i&&i.length>0?n=i:null!=p&&p.length>0&&(n=p),n&&n.length)n=n.map(((e,t)=>SC(SC(SC({payload:e},d),e),f&&f[t]&&f[t].props)));else{if(!f||!f.length)return{trapezoids:[],data:n};n=f.map((e=>SC(SC({},d),e.props)))}return function(e){var{dataKey:t,nameKey:r,displayedData:n,tooltipType:i,lastShapeType:a,reversed:o,offset:l,customWidth:c}=e,{left:s,top:u}=l,{realHeight:f,realWidth:d,offsetX:p,offsetY:h}=WC({customWidth:c},l),y=Math.max.apply(null,n.map((e=>ni(e,t,0)))),v=n.length,m=f/v,g={x:l.left,y:l.top,width:l.width,height:l.height},b=n.map(((e,o)=>{var l,c=ni(e,t,0),f=ni(e,r,o),b=c;o!==v-1?(l=ni(n[o+1],t,0))instanceof Array&&([l]=l):c instanceof Array&&2===c.length?[b,l]=c:l="rectangle"===a?b:0;var x=(y-b)*d/(2*y)+u+25+p,w=m*o+s+h,O=b/y*d,P=l/y*d,E=[{name:f,value:b,payload:e,dataKey:t,type:i}],A={x:x+O/2,y:w+m/2};return NC(NC({x,y:w,width:Math.max(O,P),upperWidth:O,lowerWidth:P,height:m,name:f,val:b,tooltipPayload:E,tooltipPosition:A},eT()(e,["width"])),{},{payload:e,parentViewBox:g,labelViewBox:{x:x+(O-P)/4,y:w,width:Math.abs(O-P)/2+Math.min(O,P),height:m}})}));o&&(b=b.map(((e,t)=>{var r=e.y-t*m+(v-1-t)*m;return NC(NC({},e),{},{upperWidth:e.lowerWidth,lowerWidth:e.upperWidth,x:e.x-(e.lowerWidth-e.upperWidth)/2,y:e.y-t*m+(v-1-t)*m,tooltipPosition:NC(NC({},e.tooltipPosition),{},{y:r+m/2}),labelViewBox:NC(NC({},e.labelViewBox),{},{y:r})})})));return{trapezoids:b,data:n}}({dataKey:a,nameKey:o,displayedData:n,tooltipType:l,lastShapeType:c,reversed:s,offset:e,customWidth:u})})),TC=["onMouseEnter","onClick","onMouseLeave","shape","activeShape"],CC=["stroke","fill","legendType","hide","isAnimationActive","animationBegin","animationDuration","animationEasing","nameKey","lastShapeType"];function DC(){return DC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},DC.apply(null,arguments)}function IC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function NC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?IC(Object(r),!0).forEach((function(t){_C(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):IC(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function _C(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function RC(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function LC(e){var{dataKey:t,nameKey:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,tooltipType:c,data:s}=e;return{dataDefinedOnItem:s,positions:e.trapezoids.map((e=>{var{tooltipPosition:t}=e;return t})),settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,name:o,nameKey:r,hide:l,type:c,color:a,unit:""}}}function KC(e){var{trapezoids:r,allOtherFunnelProps:n,showLabels:i}=e,a=Ue((e=>km(e,"item",e.tooltip.settings.trigger,void 0))),{onMouseEnter:o,onClick:l,onMouseLeave:c,shape:s,activeShape:u}=n,f=RC(n,TC),d=jw(o,n.dataKey),p=Sw(c),h=kw(l,n.dataKey);return t.createElement(t.Fragment,null,r.map(((e,r)=>{var n=u&&a===String(r),i=n?u:s,o=NC(NC({},e),{},{option:i,isActive:n,stroke:e.stroke});return t.createElement(F,DC({className:"recharts-funnel-trapezoid"},k(f,e,r),{onMouseEnter:d(e,r),onMouseLeave:p(e,r),onClick:h(e,r),key:"trapezoid-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.name,"-").concat(null==e?void 0:e.value)}),t.createElement(AC,o))})),i&&xb.renderCallByParent(n,r))}var zC=0;function BC(e){var r,n,i,{previousTrapezoidsRef:a,props:o}=e,{trapezoids:l,isAnimationActive:c,animationBegin:s,animationDuration:u,animationEasing:f,onAnimationEnd:d,onAnimationStart:p}=o,h=a.current,[y,v]=(0,t.useState)(!0),m=(r=l,n=(0,t.useRef)(zC),(i=(0,t.useRef)(r)).current!==r&&(n.current+=1,zC=n.current,i.current=r),n.current),b=(0,t.useCallback)((()=>{"function"==typeof d&&d(),v(!1)}),[d]),x=(0,t.useCallback)((()=>{"function"==typeof p&&p(),v(!0)}),[p]);return t.createElement(Kl,{begin:s,duration:u,isActive:c,easing:f,from:{t:0},to:{t:1},key:m,onAnimationStart:x,onAnimationEnd:b},(e=>{var{t:r}=e,n=1===r?l:l.map(((e,t)=>{var n=h&&h[t];if(n){var i=g(n.x,e.x),a=g(n.y,e.y),o=g(n.upperWidth,e.upperWidth),l=g(n.lowerWidth,e.lowerWidth),c=g(n.height,e.height);return NC(NC({},e),{},{x:i(r),y:a(r),upperWidth:o(r),lowerWidth:l(r),height:c(r)})}var s=g(e.x+e.upperWidth/2,e.x),u=g(e.y+e.height/2,e.y),f=g(0,e.upperWidth),d=g(0,e.lowerWidth),p=g(0,e.height);return NC(NC({},e),{},{x:s(r),y:u(r),upperWidth:f(r),lowerWidth:d(r),height:p(r)})}));return r>0&&(a.current=n),t.createElement(F,null,t.createElement(KC,{trapezoids:n,allOtherFunnelProps:o,showLabels:!y}))}))}function FC(e){var{trapezoids:r,isAnimationActive:n}=e,i=(0,t.useRef)(null),a=i.current;return n&&r&&r.length&&(!a||a!==r)?t.createElement(BC,{props:e,previousTrapezoidsRef:i}):t.createElement(KC,{trapezoids:r,allOtherFunnelProps:e,showLabels:!0})}var WC=(e,t)=>{var{customWidth:r}=e,{width:n,height:i,left:a,right:o,top:l,bottom:c}=t,s=i,u=n;return d(r)?u=r:"string"==typeof r&&(u=u*parseFloat(r)/100),{realWidth:u-a-o-50,realHeight:s-c-l,offsetX:(n-u)/2,offsetY:(i-s)/2}};class UC extends t.PureComponent{render(){var{className:e}=this.props,r=n("recharts-trapezoids",e);return t.createElement(F,{className:r},t.createElement(FC,this.props))}}var XC={stroke:"#fff",fill:"#808080",legendType:"rect",hide:!1,isAnimationActive:!mo.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",lastShapeType:"triangle"};function VC(e){var{height:r,width:n}=Ui(),i=cl(e,XC),{stroke:a,fill:o,legendType:l,hide:c,isAnimationActive:s,animationBegin:u,animationDuration:f,animationEasing:d,nameKey:p,lastShapeType:h}=i,y=RC(i,CC),v=_(e,!1),m=I(e.children,bg),g=(0,t.useMemo)((()=>({dataKey:e.dataKey,nameKey:p,data:e.data,tooltipType:e.tooltipType,lastShapeType:h,reversed:e.reversed,customWidth:e.width,cells:m,presentationProps:v})),[e.dataKey,p,e.data,e.tooltipType,h,e.reversed,e.width,m,v]),{trapezoids:b}=Ue((e=>MC(e,g)));return t.createElement(t.Fragment,null,t.createElement(Mw,{fn:LC,args:NC(NC({},e),{},{trapezoids:b})}),c?null:t.createElement(UC,DC({},y,{stroke:a,fill:o,nameKey:p,lastShapeType:h,animationBegin:u,animationDuration:f,animationEasing:d,isAnimationActive:s,hide:c,legendType:l,height:r,width:n,trapezoids:b})))}class $C extends t.PureComponent{render(){return t.createElement(VC,this.props)}}_C($C,"displayName","Funnel"),_C($C,"defaultProps",XC);var HC=["item"],qC=(0,t.forwardRef)(((e,r)=>t.createElement(BM,{chartName:"FunnelChart",defaultTooltipEventType:"item",validateTooltipEventTypes:HC,tooltipPayloadSearcher:Hm,categoricalChartProps:e,ref:r})))})(),o})()));
//# sourceMappingURL=Recharts.js.map