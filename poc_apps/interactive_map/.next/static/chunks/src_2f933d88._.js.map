{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\n\nconst Navigation: React.FC = () => {\n  const pathname = usePathname();\n\n  const navItems = [\n    {\n      href: '/',\n      label: 'Live Tracking',\n      icon: '🗺️',\n      description: 'Real-time vehicle tracking'\n    },\n    {\n      href: '/dashboard/monitoring',\n      label: 'Monitoring',\n      icon: '📊',\n      description: 'Analytics and logs'\n    }\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo/Brand */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <h1 className=\"text-xl font-bold text-gray-900\">\n                🚛 Vehicle Tracking System\n              </h1>\n            </div>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navItems.map((item) => {\n                const isActive = pathname === item.href;\n                return (\n                  <Link\n                    key={item.href}\n                    href={item.href}\n                    className={`\n                      px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\n                      ${isActive\n                        ? 'bg-blue-100 text-blue-700 border border-blue-200'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                      }\n                    `}\n                  >\n                    <span className=\"mr-2\">{item.icon}</span>\n                    {item.label}\n                  </Link>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"bg-gray-100 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n              aria-expanded=\"false\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              <svg\n                className=\"block h-6 w-6\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n                aria-hidden=\"true\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth=\"2\"\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3\">\n            {navItems.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`\n                    block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200\n                    ${isActive\n                      ? 'bg-blue-100 text-blue-700 border border-blue-200'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                    }\n                  `}\n                >\n                  <div className=\"flex items-center\">\n                    <span className=\"mr-3\">{item.icon}</span>\n                    <div>\n                      <div>{item.label}</div>\n                      <div className=\"text-xs text-gray-500\">{item.description}</div>\n                    </div>\n                  </div>\n                </Link>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAMA,MAAM,aAAuB;;IAC3B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;;;;;;sCAOpD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC;oCACb,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC;;sBAEV,EAAE,WACE,qDACA,sDACH;oBACH,CAAC;;0DAED,6LAAC;gDAAK,WAAU;0DAAQ,KAAK,IAAI;;;;;;4CAChC,KAAK,KAAK;;uCAXN,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;sCAKJ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,iBAAc;;kDAEd,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCACC,WAAU;wCACV,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAY;4CACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC;;oBAEV,EAAE,WACE,qDACA,sDACH;kBACH,CAAC;0CAED,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ,KAAK,IAAI;;;;;;sDACjC,6LAAC;;8DACC,6LAAC;8DAAK,KAAK,KAAK;;;;;;8DAChB,6LAAC;oDAAI,WAAU;8DAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;+BAdvD,KAAK,IAAI;;;;;wBAmBpB;;;;;;;;;;;;;;;;;;;;;;AAMZ;GAnHM;;QACa,qIAAA,CAAA,cAAW;;;KADxB;uCAqHS", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/AlertSummary.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { AlertSummaryProps, AlertStats, TimeFilter, ApiResponse } from '@/types';\n\nconst AlertSummary: React.FC<AlertSummaryProps> = ({\n  timeFilter,\n  onTimeFilterChange,\n}) => {\n  const [stats, setStats] = useState<AlertStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Fetch alert statistics\n  useEffect(() => {\n    const fetchStats = async () => {\n      setLoading(true);\n      setError(null);\n      \n      try {\n        const response = await fetch(`/api/alerts?timeFilter=${timeFilter}&statsOnly=true`);\n        const result: ApiResponse<AlertStats> = await response.json();\n        \n        if (result.success) {\n          setStats(result.data);\n        } else {\n          setError(result.error || 'Failed to fetch alert statistics');\n        }\n      } catch (err) {\n        setError('Network error occurred');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStats();\n  }, [timeFilter]);\n\n  // Time filter options\n  const timeFilterOptions: { value: TimeFilter; label: string }[] = [\n    { value: '24h', label: 'Last 24 Hours' },\n    { value: '7d', label: 'Last 7 Days' },\n    { value: '30d', label: 'Last 30 Days' },\n    { value: 'all', label: 'All Time' },\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-6 bg-gray-200 rounded mb-4 w-1/3\"></div>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            {[...Array(4)].map((_, i) => (\n              <div key={i} className=\"h-24 bg-gray-200 rounded\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <div className=\"text-center text-red-600\">\n          <p>Error loading alert statistics: {error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!stats) return null;\n\n  return (\n    <div className=\"bg-white rounded-lg shadow p-6\">\n      {/* Header with time filter */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4 sm:mb-0\">\n          Alert Summary\n        </h2>\n        <div className=\"flex items-center space-x-2\">\n          <label className=\"text-sm font-medium text-gray-700\">Time Period:</label>\n          <select\n            value={timeFilter}\n            onChange={(e) => onTimeFilterChange(e.target.value as TimeFilter)}\n            className=\"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            {timeFilterOptions.map((option) => (\n              <option key={option.value} value={option.value}>\n                {option.label}\n              </option>\n            ))}\n          </select>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n        {/* Total Alerts */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-sm font-bold\">📊</span>\n              </div>\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-blue-800\">Total Alerts</p>\n              <p className=\"text-2xl font-bold text-blue-900\">{stats.total}</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Active Alerts */}\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-sm font-bold\">🚨</span>\n              </div>\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-red-800\">Active Alerts</p>\n              <p className=\"text-2xl font-bold text-red-900\">{stats.byStatus.active}</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Resolved Alerts */}\n        <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-sm font-bold\">✅</span>\n              </div>\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-green-800\">Resolved</p>\n              <p className=\"text-2xl font-bold text-green-900\">{stats.byStatus.resolved}</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Critical Alerts */}\n        <div className=\"bg-orange-50 border border-orange-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-sm font-bold\">⚠️</span>\n              </div>\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-orange-800\">Critical</p>\n              <p className=\"text-2xl font-bold text-orange-900\">{stats.byLevel.critical}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Alert Type Breakdown */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* By Type */}\n        <div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-3\">By Type</h3>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-yellow-500 rounded-full mr-2\"></div>\n                <span className=\"text-sm text-gray-700\">Warnings</span>\n              </div>\n              <span className=\"text-sm font-medium text-gray-900\">{stats.byType.warning}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-red-500 rounded-full mr-2\"></div>\n                <span className=\"text-sm text-gray-700\">Errors</span>\n              </div>\n              <span className=\"text-sm font-medium text-gray-900\">{stats.byType.error}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-blue-500 rounded-full mr-2\"></div>\n                <span className=\"text-sm text-gray-700\">Info</span>\n              </div>\n              <span className=\"text-sm font-medium text-gray-900\">{stats.byType.info}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* By Level */}\n        <div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-3\">By Level</h3>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full mr-2\"></div>\n                <span className=\"text-sm text-gray-700\">Low</span>\n              </div>\n              <span className=\"text-sm font-medium text-gray-900\">{stats.byLevel.low}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-yellow-500 rounded-full mr-2\"></div>\n                <span className=\"text-sm text-gray-700\">Medium</span>\n              </div>\n              <span className=\"text-sm font-medium text-gray-900\">{stats.byLevel.medium}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-orange-500 rounded-full mr-2\"></div>\n                <span className=\"text-sm text-gray-700\">High</span>\n              </div>\n              <span className=\"text-sm font-medium text-gray-900\">{stats.byLevel.high}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-red-500 rounded-full mr-2\"></div>\n                <span className=\"text-sm text-gray-700\">Critical</span>\n              </div>\n              <span className=\"text-sm font-medium text-gray-900\">{stats.byLevel.critical}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AlertSummary;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAKA,MAAM,eAA4C,CAAC,EACjD,UAAU,EACV,kBAAkB,EACnB;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;qDAAa;oBACjB,WAAW;oBACX,SAAS;oBAET,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,WAAW,eAAe,CAAC;wBAClF,MAAM,SAAkC,MAAM,SAAS,IAAI;wBAE3D,IAAI,OAAO,OAAO,EAAE;4BAClB,SAAS,OAAO,IAAI;wBACtB,OAAO;4BACL,SAAS,OAAO,KAAK,IAAI;wBAC3B;oBACF,EAAE,OAAO,KAAK;wBACZ,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;iCAAG;QAAC;KAAW;IAEf,sBAAsB;IACtB,MAAM,oBAA4D;QAChE;YAAE,OAAO;YAAO,OAAO;QAAgB;QACvC;YAAE,OAAO;YAAM,OAAO;QAAc;QACpC;YAAE,OAAO;YAAO,OAAO;QAAe;QACtC;YAAE,OAAO;YAAO,OAAO;QAAW;KACnC;IAED,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gCAAY,WAAU;+BAAb;;;;;;;;;;;;;;;;;;;;;IAMtB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;wBAAE;wBAAiC;;;;;;;;;;;;;;;;;IAI5C;IAEA,IAAI,CAAC,OAAO,OAAO;IAEnB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmD;;;;;;kCAGjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CAAoC;;;;;;0CACrD,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;0CAET,kBAAkB,GAAG,CAAC,CAAC,uBACtB,6LAAC;wCAA0B,OAAO,OAAO,KAAK;kDAC3C,OAAO,KAAK;uCADF,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;0BASjC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;;;;;8CAGnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;kCAMlE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;;;;;8CAGnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAmC;;;;;;sDAChD,6LAAC;4CAAE,WAAU;sDAAmC,MAAM,QAAQ,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAM3E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;;;;;8CAGnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAqC,MAAM,QAAQ,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAM/E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;;;;;8CAGnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAsC;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAAsC,MAAM,OAAO,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,6LAAC;gDAAK,WAAU;0DAAqC,MAAM,MAAM,CAAC,OAAO;;;;;;;;;;;;kDAE3E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,6LAAC;gDAAK,WAAU;0DAAqC,MAAM,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAEzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,6LAAC;gDAAK,WAAU;0DAAqC,MAAM,MAAM,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;kCAM5E,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,6LAAC;gDAAK,WAAU;0DAAqC,MAAM,OAAO,CAAC,GAAG;;;;;;;;;;;;kDAExE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,6LAAC;gDAAK,WAAU;0DAAqC,MAAM,OAAO,CAAC,MAAM;;;;;;;;;;;;kDAE3E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,6LAAC;gDAAK,WAAU;0DAAqC,MAAM,OAAO,CAAC,IAAI;;;;;;;;;;;;kDAEzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,6LAAC;gDAAK,WAAU;0DAAqC,MAAM,OAAO,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzF;GA7NM;KAAA;uCA+NS", "debugId": null}}, {"offset": {"line": 1066, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/LogsTable.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { LogsTableProps, VehicleLog, EventType, AlertLevel, ApiResponse, PaginatedResponse, SortConfig } from '@/types';\n\nconst LogsTable: React.FC<LogsTableProps> = ({\n  timeFilter,\n  searchQuery = '',\n  onSearchChange,\n}) => {\n  const [logs, setLogs] = useState<VehicleLog[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [total, setTotal] = useState(0);\n  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: 'timestamp', direction: 'desc' });\n  const [filters, setFilters] = useState({\n    vehicleId: '',\n    eventType: '' as EventType | '',\n    status: '',\n    alertLevel: '' as AlertLevel | '',\n  });\n\n  const pageSize = 20;\n\n  // Fetch logs data\n  const fetchLogs = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const params = new URLSearchParams({\n        timeFilter,\n        page: currentPage.toString(),\n        pageSize: pageSize.toString(),\n      });\n\n      if (searchQuery) params.append('search', searchQuery);\n      if (filters.vehicleId) params.append('vehicleId', filters.vehicleId);\n      if (filters.eventType) params.append('eventType', filters.eventType);\n      if (filters.status) params.append('status', filters.status);\n      if (filters.alertLevel) params.append('alertLevel', filters.alertLevel);\n\n      const response = await fetch(`/api/logs?${params.toString()}`);\n      const result: ApiResponse<PaginatedResponse<VehicleLog>> = await response.json();\n\n      if (result.success) {\n        setLogs(result.data.items);\n        setTotalPages(result.data.totalPages);\n        setTotal(result.data.total);\n      } else {\n        setError(result.error || 'Failed to fetch logs');\n      }\n    } catch (err) {\n      setError('Network error occurred');\n    } finally {\n      setLoading(false);\n    }\n  }, [timeFilter, currentPage, searchQuery, filters]);\n\n  useEffect(() => {\n    fetchLogs();\n  }, [fetchLogs]);\n\n  // Reset page when filters change\n  useEffect(() => {\n    setCurrentPage(1);\n  }, [timeFilter, searchQuery, filters]);\n\n  // Handle sorting\n  const handleSort = (key: string) => {\n    setSortConfig(prev => ({\n      key,\n      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'\n    }));\n  };\n\n  // Handle filter changes\n  const handleFilterChange = (filterKey: string, value: string) => {\n    setFilters(prev => ({ ...prev, [filterKey]: value }));\n  };\n\n  // Format timestamp\n  const formatTimestamp = (timestamp: Date) => {\n    return new Date(timestamp).toLocaleString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit',\n    });\n  };\n\n  // Get event type badge color\n  const getEventTypeBadge = (eventType: EventType) => {\n    const colors = {\n      position_update: 'bg-blue-100 text-blue-800',\n      alert_triggered: 'bg-red-100 text-red-800',\n      route_deviation: 'bg-orange-100 text-orange-800',\n      route_started: 'bg-green-100 text-green-800',\n      route_completed: 'bg-green-100 text-green-800',\n      waypoint_reached: 'bg-blue-100 text-blue-800',\n      emergency_stop: 'bg-red-100 text-red-800',\n      speed_violation: 'bg-yellow-100 text-yellow-800',\n      maintenance_due: 'bg-purple-100 text-purple-800',\n      vehicle_stopped: 'bg-gray-100 text-gray-800',\n      vehicle_resumed: 'bg-green-100 text-green-800',\n    };\n    return colors[eventType] || 'bg-gray-100 text-gray-800';\n  };\n\n  // Get status badge color\n  const getStatusBadge = (status: string) => {\n    const colors = {\n      'on-route': 'bg-green-100 text-green-800',\n      'off-route': 'bg-red-100 text-red-800',\n      'stopped': 'bg-yellow-100 text-yellow-800',\n    };\n    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';\n  };\n\n  // Get alert level badge color\n  const getAlertLevelBadge = (level: AlertLevel) => {\n    const colors = {\n      low: 'bg-green-100 text-green-800',\n      medium: 'bg-yellow-100 text-yellow-800',\n      high: 'bg-orange-100 text-orange-800',\n      critical: 'bg-red-100 text-red-800',\n    };\n    return colors[level];\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow\">\n        <div className=\"p-6\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-6 bg-gray-200 rounded mb-4 w-1/4\"></div>\n            <div className=\"space-y-3\">\n              {[...Array(5)].map((_, i) => (\n                <div key={i} className=\"h-12 bg-gray-200 rounded\"></div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <div className=\"text-center text-red-600\">\n          <p>Error loading logs: {error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow\">\n      {/* Header with search and filters */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4 lg:mb-0\">\n            Vehicle Tracking Logs ({total} entries)\n          </h2>\n          \n          {/* Search */}\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"Search logs...\"\n                value={searchQuery}\n                onChange={(e) => onSearchChange?.(e.target.value)}\n                className=\"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                </svg>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <select\n            value={filters.vehicleId}\n            onChange={(e) => handleFilterChange('vehicleId', e.target.value)}\n            className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"\">All Vehicles</option>\n            <option value=\"VH-001\">VH-001</option>\n            <option value=\"VH-002\">VH-002</option>\n            <option value=\"VH-003\">VH-003</option>\n            <option value=\"VH-004\">VH-004</option>\n            <option value=\"VH-005\">VH-005</option>\n          </select>\n\n          <select\n            value={filters.eventType}\n            onChange={(e) => handleFilterChange('eventType', e.target.value)}\n            className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"\">All Event Types</option>\n            <option value=\"position_update\">Position Update</option>\n            <option value=\"alert_triggered\">Alert Triggered</option>\n            <option value=\"route_deviation\">Route Deviation</option>\n            <option value=\"route_started\">Route Started</option>\n            <option value=\"route_completed\">Route Completed</option>\n            <option value=\"waypoint_reached\">Waypoint Reached</option>\n            <option value=\"emergency_stop\">Emergency Stop</option>\n            <option value=\"speed_violation\">Speed Violation</option>\n            <option value=\"maintenance_due\">Maintenance Due</option>\n            <option value=\"vehicle_stopped\">Vehicle Stopped</option>\n            <option value=\"vehicle_resumed\">Vehicle Resumed</option>\n          </select>\n\n          <select\n            value={filters.status}\n            onChange={(e) => handleFilterChange('status', e.target.value)}\n            className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"\">All Status</option>\n            <option value=\"on-route\">On Route</option>\n            <option value=\"off-route\">Off Route</option>\n            <option value=\"stopped\">Stopped</option>\n          </select>\n\n          <select\n            value={filters.alertLevel}\n            onChange={(e) => handleFilterChange('alertLevel', e.target.value)}\n            className=\"border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"\">All Alert Levels</option>\n            <option value=\"low\">Low</option>\n            <option value=\"medium\">Medium</option>\n            <option value=\"high\">High</option>\n            <option value=\"critical\">Critical</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Table */}\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th \n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                onClick={() => handleSort('timestamp')}\n              >\n                <div className=\"flex items-center\">\n                  Timestamp\n                  {sortConfig.key === 'timestamp' && (\n                    <span className=\"ml-1\">\n                      {sortConfig.direction === 'asc' ? '↑' : '↓'}\n                    </span>\n                  )}\n                </div>\n              </th>\n              <th \n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                onClick={() => handleSort('vehicleId')}\n              >\n                <div className=\"flex items-center\">\n                  Vehicle ID\n                  {sortConfig.key === 'vehicleId' && (\n                    <span className=\"ml-1\">\n                      {sortConfig.direction === 'asc' ? '↑' : '↓'}\n                    </span>\n                  )}\n                </div>\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Event Type\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Location\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Status\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Alert Level\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {logs.map((log) => (\n              <tr key={log.id} className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  {formatTimestamp(log.timestamp)}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                  {log.vehicleId}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getEventTypeBadge(log.eventType)}`}>\n                    {log.eventType.replace('_', ' ')}\n                  </span>\n                </td>\n                <td className=\"px-6 py-4 text-sm text-gray-900 max-w-xs truncate\">\n                  {log.address || `${log.location.lat.toFixed(4)}, ${log.location.lng.toFixed(4)}`}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadge(log.status)}`}>\n                    {log.status}\n                  </span>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  {log.alertLevel ? (\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getAlertLevelBadge(log.alertLevel)}`}>\n                      {log.alertLevel}\n                    </span>\n                  ) : (\n                    <span className=\"text-gray-400 text-sm\">-</span>\n                  )}\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <div className=\"px-6 py-3 border-t border-gray-200 flex items-center justify-between\">\n          <div className=\"text-sm text-gray-700\">\n            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, total)} of {total} results\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n              disabled={currentPage === 1}\n              className=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n            >\n              Previous\n            </button>\n            <span className=\"text-sm text-gray-700\">\n              Page {currentPage} of {totalPages}\n            </span>\n            <button\n              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}\n              disabled={currentPage === totalPages}\n              className=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n            >\n              Next\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default LogsTable;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAKA,MAAM,YAAsC,CAAC,EAC3C,UAAU,EACV,cAAc,EAAE,EAChB,cAAc,EACf;;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QAAE,KAAK;QAAa,WAAW;IAAO;IAC/F,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,WAAW;QACX,WAAW;QACX,QAAQ;QACR,YAAY;IACd;IAEA,MAAM,WAAW;IAEjB,kBAAkB;IAClB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YAC5B,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,SAAS,IAAI,gBAAgB;oBACjC;oBACA,MAAM,YAAY,QAAQ;oBAC1B,UAAU,SAAS,QAAQ;gBAC7B;gBAEA,IAAI,aAAa,OAAO,MAAM,CAAC,UAAU;gBACzC,IAAI,QAAQ,SAAS,EAAE,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;gBACnE,IAAI,QAAQ,SAAS,EAAE,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;gBACnE,IAAI,QAAQ,MAAM,EAAE,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;gBAC1D,IAAI,QAAQ,UAAU,EAAE,OAAO,MAAM,CAAC,cAAc,QAAQ,UAAU;gBAEtE,MAAM,WAAW,MAAM,MAAM,CAAC,UAAU,EAAE,OAAO,QAAQ,IAAI;gBAC7D,MAAM,SAAqD,MAAM,SAAS,IAAI;gBAE9E,IAAI,OAAO,OAAO,EAAE;oBAClB,QAAQ,OAAO,IAAI,CAAC,KAAK;oBACzB,cAAc,OAAO,IAAI,CAAC,UAAU;oBACpC,SAAS,OAAO,IAAI,CAAC,KAAK;gBAC5B,OAAO;oBACL,SAAS,OAAO,KAAK,IAAI;gBAC3B;YACF,EAAE,OAAO,KAAK;gBACZ,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;2CAAG;QAAC;QAAY;QAAa;QAAa;KAAQ;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG;QAAC;KAAU;IAEd,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,eAAe;QACjB;8BAAG;QAAC;QAAY;QAAa;KAAQ;IAErC,iBAAiB;IACjB,MAAM,aAAa,CAAC;QAClB,cAAc,CAAA,OAAQ,CAAC;gBACrB;gBACA,WAAW,KAAK,GAAG,KAAK,OAAO,KAAK,SAAS,KAAK,QAAQ,SAAS;YACrE,CAAC;IACH;IAEA,wBAAwB;IACxB,MAAM,qBAAqB,CAAC,WAAmB;QAC7C,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,UAAU,EAAE;YAAM,CAAC;IACrD;IAEA,mBAAmB;IACnB,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,KAAK,WAAW,cAAc,CAAC,SAAS;YACjD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,6BAA6B;IAC7B,MAAM,oBAAoB,CAAC;QACzB,MAAM,SAAS;YACb,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB;YACjB,eAAe;YACf,iBAAiB;YACjB,kBAAkB;YAClB,gBAAgB;YAChB,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB;QACnB;QACA,OAAO,MAAM,CAAC,UAAU,IAAI;IAC9B;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,YAAY;YACZ,aAAa;YACb,WAAW;QACb;QACA,OAAO,MAAM,CAAC,OAA8B,IAAI;IAClD;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB,CAAC;QAC1B,MAAM,SAAS;YACb,KAAK;YACL,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;QACA,OAAO,MAAM,CAAC,MAAM;IACtB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;oCAAY,WAAU;mCAAb;;;;;;;;;;;;;;;;;;;;;;;;;;IAOxB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;wBAAE;wBAAqB;;;;;;;;;;;;;;;;;IAIhC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAmD;oCACvC;oCAAM;;;;;;;0CAIhC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC5E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,OAAO,QAAQ,SAAS;gCACxB,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC/D,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;0CAGzB,6LAAC;gCACC,OAAO,QAAQ,SAAS;gCACxB,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC/D,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAkB;;;;;;kDAChC,6LAAC;wCAAO,OAAM;kDAAkB;;;;;;kDAChC,6LAAC;wCAAO,OAAM;kDAAkB;;;;;;kDAChC,6LAAC;wCAAO,OAAM;kDAAgB;;;;;;kDAC9B,6LAAC;wCAAO,OAAM;kDAAkB;;;;;;kDAChC,6LAAC;wCAAO,OAAM;kDAAmB;;;;;;kDACjC,6LAAC;wCAAO,OAAM;kDAAiB;;;;;;kDAC/B,6LAAC;wCAAO,OAAM;kDAAkB;;;;;;kDAChC,6LAAC;wCAAO,OAAM;kDAAkB;;;;;;kDAChC,6LAAC;wCAAO,OAAM;kDAAkB;;;;;;kDAChC,6LAAC;wCAAO,OAAM;kDAAkB;;;;;;;;;;;;0CAGlC,6LAAC;gCACC,OAAO,QAAQ,MAAM;gCACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAU;;;;;;;;;;;;0CAG1B,6LAAC;gCACC,OAAO,QAAQ,UAAU;gCACzB,UAAU,CAAC,IAAM,mBAAmB,cAAc,EAAE,MAAM,CAAC,KAAK;gCAChE,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;;kDACC,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,WAAW;kDAE1B,cAAA,6LAAC;4CAAI,WAAU;;gDAAoB;gDAEhC,WAAW,GAAG,KAAK,6BAClB,6LAAC;oDAAK,WAAU;8DACb,WAAW,SAAS,KAAK,QAAQ,MAAM;;;;;;;;;;;;;;;;;kDAKhD,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,WAAW;kDAE1B,cAAA,6LAAC;4CAAI,WAAU;;gDAAoB;gDAEhC,WAAW,GAAG,KAAK,6BAClB,6LAAC;oDAAK,WAAU;8DACb,WAAW,SAAS,KAAK,QAAQ,MAAM;;;;;;;;;;;;;;;;;kDAKhD,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;;;;;;;;;;;;sCAKnG,6LAAC;4BAAM,WAAU;sCACd,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAG,WAAU;sDACX,gBAAgB,IAAI,SAAS;;;;;;sDAEhC,6LAAC;4CAAG,WAAU;sDACX,IAAI,SAAS;;;;;;sDAEhB,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAK,WAAW,CAAC,yDAAyD,EAAE,kBAAkB,IAAI,SAAS,GAAG;0DAC5G,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;sDAGhC,6LAAC;4CAAG,WAAU;sDACX,IAAI,OAAO,IAAI,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI;;;;;;sDAElF,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,IAAI,MAAM,GAAG;0DACtG,IAAI,MAAM;;;;;;;;;;;sDAGf,6LAAC;4CAAG,WAAU;sDACX,IAAI,UAAU,iBACb,6LAAC;gDAAK,WAAW,CAAC,yDAAyD,EAAE,mBAAmB,IAAI,UAAU,GAAG;0DAC9G,IAAI,UAAU;;;;;qEAGjB,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;mCA1BrC,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;YAoCtB,aAAa,mBACZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BAAwB;4BAC3B,CAAC,cAAc,CAAC,IAAI,WAAY;4BAAE;4BAAK,KAAK,GAAG,CAAC,cAAc,UAAU;4BAAO;4BAAK;4BAAM;;;;;;;kCAEtG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;gCACzD,UAAU,gBAAgB;gCAC1B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCAAK,WAAU;;oCAAwB;oCAChC;oCAAY;oCAAK;;;;;;;0CAEzB,6LAAC;gCACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;gCACzD,UAAU,gBAAgB;gCAC1B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAlWM;KAAA;uCAoWS", "debugId": null}}, {"offset": {"line": 1934, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/app/dashboard/monitoring/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Navigation from '@/components/Navigation';\nimport AlertSummary from '@/components/AlertSummary';\nimport LogsTable from '@/components/LogsTable';\nimport { TimeFilter } from '@/types';\n\nexport default function MonitoringDashboard() {\n  const [timeFilter, setTimeFilter] = useState<TimeFilter>('7d');\n  const [searchQuery, setSearchQuery] = useState('');\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation */}\n      <Navigation />\n\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                Monitoring Dashboard\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"text-sm text-gray-600\">\n                Real-time fleet monitoring and analytics\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"space-y-8\">\n          {/* Alert Summary Section */}\n          <AlertSummary\n            timeFilter={timeFilter}\n            onTimeFilterChange={setTimeFilter}\n          />\n\n          {/* Logs Table Section */}\n          <LogsTable\n            timeFilter={timeFilter}\n            searchQuery={searchQuery}\n            onSearchChange={setSearchQuery}\n          />\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,UAAU;;;;;0BAGX,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;0CAInD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/C,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,qIAAA,CAAA,UAAY;4BACX,YAAY;4BACZ,oBAAoB;;;;;;sCAItB,6LAAC,kIAAA,CAAA,UAAS;4BACR,YAAY;4BACZ,aAAa;4BACb,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAM5B;GA9CwB;KAAA", "debugId": null}}]}