{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/TrackingMap.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useRef, useState, useEffect } from 'react';\nimport { GoogleMap, LoadScript, <PERSON>yline, Marker } from '@react-google-maps/api';\nimport { TrackingMapProps, MapConfig, Coordinate } from '@/types';\n\n// Default map configuration\nconst defaultMapConfig: MapConfig = {\n  center: { lat: 24.7136, lng: 46.6753 }, // Riyadh, Saudi Arabia\n  zoom: 12,\n  disableDefaultUI: false,\n  zoomControl: true,\n  streetViewControl: false,\n  fullscreenControl: true,\n};\n\n// Google Maps libraries to load\nconst libraries: ('geometry' | 'places' | 'drawing' | 'visualization')[] = ['geometry'];\n\n// Map container style\nconst mapContainerStyle = {\n  width: '100%',\n  height: '100%',\n  minHeight: '500px',\n};\n\nconst TrackingMap: React.FC<TrackingMapProps> = ({\n  route,\n  vehicle,\n  onVehicleUpdate,\n  onAlert,\n  mapConfig = {},\n  geofenceConfig = {},\n}) => {\n  const mapRef = useRef<google.maps.Map | null>(null);\n  const [isMapLoaded, setIsMapLoaded] = useState(false);\n  const [loadError, setLoadError] = useState<string | null>(null);\n  const [isMounted, setIsMounted] = useState(false);\n\n  // Merge default config with provided config\n  const finalMapConfig = { ...defaultMapConfig, ...mapConfig };\n\n  // Get API key from environment\n  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;\n\n  // Ensure component only renders on client side\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  // Handle map load\n  const onMapLoad = useCallback((map: google.maps.Map) => {\n    mapRef.current = map;\n    setIsMapLoaded(true);\n\n    // Fit map to show the entire route\n    if (route.waypoints.length > 0 && window.google) {\n      const bounds = new window.google.maps.LatLngBounds();\n      route.waypoints.forEach(point => {\n        bounds.extend(new window.google.maps.LatLng(point.lat, point.lng));\n      });\n      map.fitBounds(bounds);\n    }\n  }, [route.waypoints]);\n\n  // Handle map unmount\n  const onMapUnmount = useCallback(() => {\n    mapRef.current = null;\n    setIsMapLoaded(false);\n  }, []);\n\n  // Handle load error\n  const onLoadError = useCallback((error: Error) => {\n    console.error('Google Maps load error:', error);\n    setLoadError(error.message);\n  }, []);\n\n  // Calculate route polyline options\n  const routeOptions = {\n    path: route.waypoints,\n    geodesic: true,\n    strokeColor: route.color || '#2563eb',\n    strokeOpacity: 1.0,\n    strokeWeight: route.strokeWeight || 4,\n  };\n\n  // Custom vehicle icon path (truck/car shape)\n  const vehicleIconPath = \"M12 2C13.1 2 14 2.9 14 4V6H18C19.1 6 20 6.9 20 8V14H18.5C18.5 15.9 16.9 17.5 15 17.5S11.5 15.9 11.5 14H8.5C8.5 15.9 6.9 17.5 5 17.5S1.5 15.9 1.5 14H0V8C0 6.9 0.9 6 2 6H6V4C6 2.9 6.9 2 8 2H12M8 4V6H12V4H8M5 12.5C6.4 12.5 7.5 13.6 7.5 15S6.4 17.5 5 17.5 2.5 16.4 2.5 15 3.6 12.5 5 12.5M15 12.5C16.4 12.5 17.5 13.6 17.5 15S16.4 17.5 15 17.5 12.5 16.4 12.5 15 13.6 12.5 15 12.5Z\";\n\n  // Vehicle marker options\n  const vehicleIcon = isMounted && isMapLoaded && typeof window !== 'undefined' && window.google ? {\n    path: vehicleIconPath,\n    scale: 1.5,\n    fillColor: vehicle.isOnRoute ? '#10b981' : '#ef4444',\n    fillOpacity: 1,\n    strokeColor: '#ffffff',\n    strokeWeight: 2,\n    rotation: vehicle.position.heading || 0,\n    anchor: new window.google.maps.Point(10, 10), // Center the icon\n  } : undefined;\n\n  // Show loading state during hydration\n  if (!isMounted) {\n    return (\n      <div className=\"flex items-center justify-center h-full bg-gray-100 rounded-lg\">\n        <div className=\"text-center p-6\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">Initializing map...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state\n  if (!apiKey) {\n    return (\n      <div className=\"flex items-center justify-center h-full bg-gray-100 rounded-lg\">\n        <div className=\"text-center p-6\">\n          <div className=\"text-red-500 text-xl mb-2\">⚠️</div>\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n            Google Maps API Key Missing\n          </h3>\n          <p className=\"text-gray-600 text-sm\">\n            Please add your Google Maps API key to the .env.local file\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  if (loadError) {\n    return (\n      <div className=\"flex items-center justify-center h-full bg-gray-100 rounded-lg\">\n        <div className=\"text-center p-6\">\n          <div className=\"text-red-500 text-xl mb-2\">❌</div>\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n            Map Load Error\n          </h3>\n          <p className=\"text-gray-600 text-sm\">{loadError}</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"w-full h-full relative\">\n      <LoadScript\n        googleMapsApiKey={apiKey}\n        libraries={libraries}\n        onError={onLoadError}\n        loadingElement={\n          <div className=\"flex items-center justify-center h-full bg-gray-100 rounded-lg\">\n            <div className=\"text-center p-6\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n              <p className=\"text-gray-600\">Loading Google Maps...</p>\n            </div>\n          </div>\n        }\n      >\n        <GoogleMap\n          mapContainerStyle={mapContainerStyle}\n          center={finalMapConfig.center}\n          zoom={finalMapConfig.zoom}\n          onLoad={onMapLoad}\n          onUnmount={onMapUnmount}\n          options={{\n            disableDefaultUI: finalMapConfig.disableDefaultUI,\n            zoomControl: finalMapConfig.zoomControl,\n            streetViewControl: finalMapConfig.streetViewControl,\n            fullscreenControl: finalMapConfig.fullscreenControl,\n            mapTypeControl: false,\n            scaleControl: true,\n          }}\n        >\n          {/* Route polyline */}\n          {route.waypoints.length > 1 && (\n            <Polyline options={routeOptions} />\n          )}\n\n          {/* Vehicle marker */}\n          {vehicleIcon && (\n            <Marker\n              position={vehicle.position}\n              icon={vehicleIcon}\n              title={`Vehicle ${vehicle.id} - ${vehicle.status}`}\n            />\n          )}\n\n          {/* Route waypoint markers */}\n          {isMounted && isMapLoaded && typeof window !== 'undefined' && window.google && route.waypoints.map((waypoint, index) => (\n            <Marker\n              key={`waypoint-${index}`}\n              position={waypoint}\n              icon={{\n                path: window.google.maps.SymbolPath.CIRCLE,\n                scale: 4,\n                fillColor: index === 0 ? '#10b981' : index === route.waypoints.length - 1 ? '#ef4444' : '#6b7280',\n                fillOpacity: 1,\n                strokeColor: '#ffffff',\n                strokeWeight: 2,\n              }}\n              title={\n                index === 0\n                  ? 'Start Point'\n                  : index === route.waypoints.length - 1\n                    ? 'End Point'\n                    : `Waypoint ${index + 1}`\n              }\n            />\n          ))}\n        </GoogleMap>\n      </LoadScript>\n\n      {/* Map overlay with vehicle status */}\n      <div className=\"absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 z-10\">\n        <div className=\"flex items-center space-x-2\">\n          <div \n            className={`w-3 h-3 rounded-full ${\n              vehicle.isOnRoute ? 'bg-green-500' : 'bg-red-500'\n            }`}\n          />\n          <span className=\"text-sm font-medium\">\n            {vehicle.isOnRoute ? 'On Route' : 'Off Route'}\n          </span>\n        </div>\n        <div className=\"text-xs text-gray-500 mt-1\">\n          Distance from route: {Math.round(vehicle.distanceFromRoute)}m\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TrackingMap;\n"], "names": [], "mappings": ";;;AA2CiB;;AAzCjB;AACA;;;AAHA;;;AAMA,4BAA4B;AAC5B,MAAM,mBAA8B;IAClC,QAAQ;QAAE,KAAK;QAAS,KAAK;IAAQ;IACrC,MAAM;IACN,kBAAkB;IAClB,aAAa;IACb,mBAAmB;IACnB,mBAAmB;AACrB;AAEA,gCAAgC;AAChC,MAAM,YAAqE;IAAC;CAAW;AAEvF,sBAAsB;AACtB,MAAM,oBAAoB;IACxB,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAEA,MAAM,cAA0C,CAAC,EAC/C,KAAK,EACL,OAAO,EACP,eAAe,EACf,OAAO,EACP,YAAY,CAAC,CAAC,EACd,iBAAiB,CAAC,CAAC,EACpB;;IACC,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA0B;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,4CAA4C;IAC5C,MAAM,iBAAiB;QAAE,GAAG,gBAAgB;QAAE,GAAG,SAAS;IAAC;IAE3D,+BAA+B;IAC/B,MAAM;IAEN,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,aAAa;QACf;gCAAG,EAAE;IAEL,kBAAkB;IAClB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,CAAC;YAC7B,OAAO,OAAO,GAAG;YACjB,eAAe;YAEf,mCAAmC;YACnC,IAAI,MAAM,SAAS,CAAC,MAAM,GAAG,KAAK,OAAO,MAAM,EAAE;gBAC/C,MAAM,SAAS,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY;gBAClD,MAAM,SAAS,CAAC,OAAO;0DAAC,CAAA;wBACtB,OAAO,MAAM,CAAC,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,MAAM,GAAG;oBAClE;;gBACA,IAAI,SAAS,CAAC;YAChB;QACF;6CAAG;QAAC,MAAM,SAAS;KAAC;IAEpB,qBAAqB;IACrB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC/B,OAAO,OAAO,GAAG;YACjB,eAAe;QACjB;gDAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YAC/B,QAAQ,KAAK,CAAC,2BAA2B;YACzC,aAAa,MAAM,OAAO;QAC5B;+CAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,eAAe;QACnB,MAAM,MAAM,SAAS;QACrB,UAAU;QACV,aAAa,MAAM,KAAK,IAAI;QAC5B,eAAe;QACf,cAAc,MAAM,YAAY,IAAI;IACtC;IAEA,6CAA6C;IAC7C,MAAM,kBAAkB;IAExB,yBAAyB;IACzB,MAAM,cAAc,aAAa,eAAe,aAAkB,eAAe,OAAO,MAAM,GAAG;QAC/F,MAAM;QACN,OAAO;QACP,WAAW,QAAQ,SAAS,GAAG,YAAY;QAC3C,aAAa;QACb,aAAa;QACb,cAAc;QACd,UAAU,QAAQ,QAAQ,CAAC,OAAO,IAAI;QACtC,QAAQ,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;IAC3C,IAAI;IAEJ,sCAAsC;IACtC,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,mBAAmB;IACnB,uCAAa;;IAcb;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA4B;;;;;;kCAC3C,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;;;;;;IAI9C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,kKAAA,CAAA,aAAU;gBACT,kBAAkB;gBAClB,WAAW;gBACX,SAAS;gBACT,8BACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;0BAKnC,cAAA,6LAAC,kKAAA,CAAA,YAAS;oBACR,mBAAmB;oBACnB,QAAQ,eAAe,MAAM;oBAC7B,MAAM,eAAe,IAAI;oBACzB,QAAQ;oBACR,WAAW;oBACX,SAAS;wBACP,kBAAkB,eAAe,gBAAgB;wBACjD,aAAa,eAAe,WAAW;wBACvC,mBAAmB,eAAe,iBAAiB;wBACnD,mBAAmB,eAAe,iBAAiB;wBACnD,gBAAgB;wBAChB,cAAc;oBAChB;;wBAGC,MAAM,SAAS,CAAC,MAAM,GAAG,mBACxB,6LAAC,kKAAA,CAAA,WAAQ;4BAAC,SAAS;;;;;;wBAIpB,6BACC,6LAAC,kKAAA,CAAA,SAAM;4BACL,UAAU,QAAQ,QAAQ;4BAC1B,MAAM;4BACN,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,QAAQ,MAAM,EAAE;;;;;;wBAKrD,aAAa,eAAe,aAAkB,eAAe,OAAO,MAAM,IAAI,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC5G,6LAAC,kKAAA,CAAA,SAAM;gCAEL,UAAU;gCACV,MAAM;oCACJ,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM;oCAC1C,OAAO;oCACP,WAAW,UAAU,IAAI,YAAY,UAAU,MAAM,SAAS,CAAC,MAAM,GAAG,IAAI,YAAY;oCACxF,aAAa;oCACb,aAAa;oCACb,cAAc;gCAChB;gCACA,OACE,UAAU,IACN,gBACA,UAAU,MAAM,SAAS,CAAC,MAAM,GAAG,IACjC,cACA,CAAC,SAAS,EAAE,QAAQ,GAAG;+BAf1B,CAAC,SAAS,EAAE,OAAO;;;;;;;;;;;;;;;;0BAuBhC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAW,CAAC,qBAAqB,EAC/B,QAAQ,SAAS,GAAG,iBAAiB,cACrC;;;;;;0CAEJ,6LAAC;gCAAK,WAAU;0CACb,QAAQ,SAAS,GAAG,aAAa;;;;;;;;;;;;kCAGtC,6LAAC;wBAAI,WAAU;;4BAA6B;4BACpB,KAAK,KAAK,CAAC,QAAQ,iBAAiB;4BAAE;;;;;;;;;;;;;;;;;;;AAKtE;GA7MM;KAAA;uCA+MS", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/VehicleTracker.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useCallback } from 'react';\nimport { VehicleTrackerProps, VehiclePosition, Coordinate } from '@/types';\n\nconst VehicleTracker: React.FC<VehicleTrackerProps> = ({\n  route,\n  onPositionUpdate,\n  onStatusChange,\n  isActive,\n  speed = 1,\n}) => {\n  const currentWaypointIndex = useRef(0);\n  const progress = useRef(0); // Progress between current and next waypoint (0-1)\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n  const isOffRoute = useRef(false);\n  const offRouteCounter = useRef(0);\n  const totalUpdateCounter = useRef(0);\n  const offRouteTestTriggered = useRef(false);\n\n\n\n  // Calculate bearing between two coordinates\n  const calculateBearing = useCallback((coord1: Coordinate, coord2: Coordinate): number => {\n    const dLng = (coord2.lng - coord1.lng) * Math.PI / 180;\n    const lat1 = coord1.lat * Math.PI / 180;\n    const lat2 = coord2.lat * Math.PI / 180;\n    \n    const y = Math.sin(dLng) * Math.cos(lat2);\n    const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLng);\n    \n    const bearing = Math.atan2(y, x) * 180 / Math.PI;\n    return (bearing + 360) % 360;\n  }, []);\n\n  // Interpolate between two coordinates\n  const interpolatePosition = useCallback((\n    start: Coordinate,\n    end: Coordinate,\n    progress: number\n  ): Coordinate => {\n    // Calculate base position along the route\n    let lat = start.lat + (end.lat - start.lat) * progress;\n    let lng = start.lng + (end.lng - start.lng) * progress;\n\n    // Increment counters\n    totalUpdateCounter.current++;\n    offRouteCounter.current++;\n\n    // Controlled off-route testing: trigger once after 30 updates, then stay off-route for 10 updates\n    if (!offRouteTestTriggered.current && totalUpdateCounter.current > 30 && totalUpdateCounter.current < 45) {\n      if (totalUpdateCounter.current === 31) {\n        // Start off-route test\n        isOffRoute.current = true;\n        offRouteTestTriggered.current = true;\n        offRouteCounter.current = 0;\n      }\n\n      if (isOffRoute.current) {\n        // Apply consistent offset while off-route\n        const offsetDistance = 0.002; // Roughly 200 meters\n        lat += offsetDistance; // Move north consistently\n        lng += offsetDistance * 0.5; // Move slightly east\n      }\n    } else if (isOffRoute.current && offRouteCounter.current > 10) {\n      // Return to route after being off-route for 10 updates\n      isOffRoute.current = false;\n      offRouteCounter.current = 0;\n    }\n\n    return { lat, lng };\n  }, []);\n\n  // Update vehicle position\n  const updatePosition = useCallback(() => {\n    if (!route.waypoints || route.waypoints.length < 2) return;\n\n    const currentWaypoint = route.waypoints[currentWaypointIndex.current];\n    const nextWaypointIndex = currentWaypointIndex.current + 1;\n\n    // Check if we've reached the end of the route\n    if (nextWaypointIndex >= route.waypoints.length) {\n      // Restart from the beginning\n      currentWaypointIndex.current = 0;\n      progress.current = 0;\n      onStatusChange('on-route');\n      return;\n    }\n\n    const nextWaypoint = route.waypoints[nextWaypointIndex];\n\n    // Calculate new position\n    const newPosition = interpolatePosition(currentWaypoint, nextWaypoint, progress.current);\n    \n    // Calculate bearing for vehicle orientation\n    const bearing = calculateBearing(currentWaypoint, nextWaypoint);\n\n    // Create vehicle position object\n    const vehiclePosition: VehiclePosition = {\n      lat: newPosition.lat,\n      lng: newPosition.lng,\n      timestamp: new Date(),\n      speed: 50 + Math.random() * 20, // Random speed between 50-70 km/h\n      heading: bearing,\n    };\n\n    // Update progress\n    progress.current += 0.02 * speed; // Adjust speed multiplier\n\n    // Check if we've reached the next waypoint\n    if (progress.current >= 1) {\n      currentWaypointIndex.current = nextWaypointIndex;\n      progress.current = 0;\n    }\n\n    // Update status based on whether vehicle is off-route\n    // Note: The actual route status will be determined by the distance calculation in the parent component\n    const status = isOffRoute.current ? 'off-route' : 'on-route';\n    onStatusChange(status);\n\n    // Notify parent component\n    onPositionUpdate(vehiclePosition);\n  }, [route.waypoints, speed, onPositionUpdate, onStatusChange, interpolatePosition, calculateBearing]);\n\n  // Start/stop simulation\n  useEffect(() => {\n    if (isActive && route.waypoints.length >= 2) {\n      // Start simulation\n      intervalRef.current = setInterval(updatePosition, 2000); // Update every 2 seconds\n    } else {\n      // Stop simulation\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n        intervalRef.current = null;\n      }\n    }\n\n    // Cleanup on unmount\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [isActive, route.waypoints.length, updatePosition]);\n\n  // Reset simulation when route changes\n  useEffect(() => {\n    currentWaypointIndex.current = 0;\n    progress.current = 0;\n    isOffRoute.current = false;\n    offRouteCounter.current = 0;\n    totalUpdateCounter.current = 0;\n    offRouteTestTriggered.current = false;\n  }, [route]);\n\n  // This component doesn't render anything visible\n  return null;\n};\n\nexport default VehicleTracker;\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAKA,MAAM,iBAAgD,CAAC,EACrD,KAAK,EACL,gBAAgB,EAChB,cAAc,EACd,QAAQ,EACR,QAAQ,CAAC,EACV;;IACC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACpC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,IAAI,mDAAmD;IAC/E,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAClD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAClC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAIrC,4CAA4C;IAC5C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC,QAAoB;YACxD,MAAM,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG,IAAI,KAAK,EAAE,GAAG;YACnD,MAAM,OAAO,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG;YACpC,MAAM,OAAO,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG;YAEpC,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC;YACpC,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC;YAEvF,MAAM,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,KAAK,EAAE;YAChD,OAAO,CAAC,UAAU,GAAG,IAAI;QAC3B;uDAAG,EAAE;IAEL,sCAAsC;IACtC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CACtC,OACA,KACA;YAEA,0CAA0C;YAC1C,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI;YAC9C,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI;YAE9C,qBAAqB;YACrB,mBAAmB,OAAO;YAC1B,gBAAgB,OAAO;YAEvB,kGAAkG;YAClG,IAAI,CAAC,sBAAsB,OAAO,IAAI,mBAAmB,OAAO,GAAG,MAAM,mBAAmB,OAAO,GAAG,IAAI;gBACxG,IAAI,mBAAmB,OAAO,KAAK,IAAI;oBACrC,uBAAuB;oBACvB,WAAW,OAAO,GAAG;oBACrB,sBAAsB,OAAO,GAAG;oBAChC,gBAAgB,OAAO,GAAG;gBAC5B;gBAEA,IAAI,WAAW,OAAO,EAAE;oBACtB,0CAA0C;oBAC1C,MAAM,iBAAiB,OAAO,qBAAqB;oBACnD,OAAO,gBAAgB,0BAA0B;oBACjD,OAAO,iBAAiB,KAAK,qBAAqB;gBACpD;YACF,OAAO,IAAI,WAAW,OAAO,IAAI,gBAAgB,OAAO,GAAG,IAAI;gBAC7D,uDAAuD;gBACvD,WAAW,OAAO,GAAG;gBACrB,gBAAgB,OAAO,GAAG;YAC5B;YAEA,OAAO;gBAAE;gBAAK;YAAI;QACpB;0DAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACjC,IAAI,CAAC,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,MAAM,GAAG,GAAG;YAEpD,MAAM,kBAAkB,MAAM,SAAS,CAAC,qBAAqB,OAAO,CAAC;YACrE,MAAM,oBAAoB,qBAAqB,OAAO,GAAG;YAEzD,8CAA8C;YAC9C,IAAI,qBAAqB,MAAM,SAAS,CAAC,MAAM,EAAE;gBAC/C,6BAA6B;gBAC7B,qBAAqB,OAAO,GAAG;gBAC/B,SAAS,OAAO,GAAG;gBACnB,eAAe;gBACf;YACF;YAEA,MAAM,eAAe,MAAM,SAAS,CAAC,kBAAkB;YAEvD,yBAAyB;YACzB,MAAM,cAAc,oBAAoB,iBAAiB,cAAc,SAAS,OAAO;YAEvF,4CAA4C;YAC5C,MAAM,UAAU,iBAAiB,iBAAiB;YAElD,iCAAiC;YACjC,MAAM,kBAAmC;gBACvC,KAAK,YAAY,GAAG;gBACpB,KAAK,YAAY,GAAG;gBACpB,WAAW,IAAI;gBACf,OAAO,KAAK,KAAK,MAAM,KAAK;gBAC5B,SAAS;YACX;YAEA,kBAAkB;YAClB,SAAS,OAAO,IAAI,OAAO,OAAO,0BAA0B;YAE5D,2CAA2C;YAC3C,IAAI,SAAS,OAAO,IAAI,GAAG;gBACzB,qBAAqB,OAAO,GAAG;gBAC/B,SAAS,OAAO,GAAG;YACrB;YAEA,sDAAsD;YACtD,uGAAuG;YACvG,MAAM,SAAS,WAAW,OAAO,GAAG,cAAc;YAClD,eAAe;YAEf,0BAA0B;YAC1B,iBAAiB;QACnB;qDAAG;QAAC,MAAM,SAAS;QAAE;QAAO;QAAkB;QAAgB;QAAqB;KAAiB;IAEpG,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,YAAY,MAAM,SAAS,CAAC,MAAM,IAAI,GAAG;gBAC3C,mBAAmB;gBACnB,YAAY,OAAO,GAAG,YAAY,gBAAgB,OAAO,yBAAyB;YACpF,OAAO;gBACL,kBAAkB;gBAClB,IAAI,YAAY,OAAO,EAAE;oBACvB,cAAc,YAAY,OAAO;oBACjC,YAAY,OAAO,GAAG;gBACxB;YACF;YAEA,qBAAqB;YACrB;4CAAO;oBACL,IAAI,YAAY,OAAO,EAAE;wBACvB,cAAc,YAAY,OAAO;oBACnC;gBACF;;QACF;mCAAG;QAAC;QAAU,MAAM,SAAS,CAAC,MAAM;QAAE;KAAe;IAErD,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,qBAAqB,OAAO,GAAG;YAC/B,SAAS,OAAO,GAAG;YACnB,WAAW,OAAO,GAAG;YACrB,gBAAgB,OAAO,GAAG;YAC1B,mBAAmB,OAAO,GAAG;YAC7B,sBAAsB,OAAO,GAAG;QAClC;mCAAG;QAAC;KAAM;IAEV,iDAAiD;IACjD,OAAO;AACT;GAxJM;KAAA;uCA0JS", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/AlertBanner.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { AlertBannerProps, Alert } from '@/types';\n\nconst AlertBanner: React.FC<AlertBannerProps> = ({\n  alerts,\n  onDismiss,\n  maxVisible = 3,\n}) => {\n  // Filter active alerts and limit to maxVisible\n  const activeAlerts = alerts\n    .filter(alert => alert.isActive)\n    .slice(0, maxVisible);\n\n  if (activeAlerts.length === 0) {\n    return null;\n  }\n\n  // Get alert styling based on type\n  const getAlertStyles = (type: Alert['type']) => {\n    switch (type) {\n      case 'error':\n        return {\n          container: 'bg-red-50 border-red-200 text-red-800',\n          icon: '🚨',\n          iconBg: 'bg-red-100',\n        };\n      case 'warning':\n        return {\n          container: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n          icon: '⚠️',\n          iconBg: 'bg-yellow-100',\n        };\n      case 'info':\n        return {\n          container: 'bg-blue-50 border-blue-200 text-blue-800',\n          icon: 'ℹ️',\n          iconBg: 'bg-blue-100',\n        };\n      default:\n        return {\n          container: 'bg-gray-50 border-gray-200 text-gray-800',\n          icon: '📢',\n          iconBg: 'bg-gray-100',\n        };\n    }\n  };\n\n  // Format timestamp\n  const formatTime = (timestamp: Date) => {\n    return timestamp.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit',\n    });\n  };\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-md\">\n      {activeAlerts.map((alert) => {\n        const styles = getAlertStyles(alert.type);\n        \n        return (\n          <div\n            key={alert.id}\n            className={`\n              ${styles.container}\n              border rounded-lg shadow-lg p-4 \n              transform transition-all duration-300 ease-in-out\n              animate-slide-in-right\n            `}\n            role=\"alert\"\n          >\n            <div className=\"flex items-start space-x-3\">\n              {/* Alert Icon */}\n              <div className={`\n                ${styles.iconBg} \n                rounded-full p-1 flex-shrink-0 mt-0.5\n              `}>\n                <span className=\"text-sm\">{styles.icon}</span>\n              </div>\n\n              {/* Alert Content */}\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium leading-5\">\n                      {alert.message}\n                    </p>\n                    <div className=\"mt-1 flex items-center space-x-2 text-xs opacity-75\">\n                      <span>Vehicle: {alert.vehicleId}</span>\n                      <span>•</span>\n                      <span>{formatTime(alert.timestamp)}</span>\n                    </div>\n                  </div>\n\n                  {/* Dismiss Button */}\n                  <button\n                    onClick={() => onDismiss(alert.id)}\n                    className=\"\n                      ml-2 flex-shrink-0 rounded-md p-1.5 \n                      hover:bg-black hover:bg-opacity-10 \n                      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent\n                      transition-colors duration-200\n                    \"\n                    aria-label=\"Dismiss alert\"\n                  >\n                    <svg\n                      className=\"h-4 w-4\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M6 18L18 6M6 6l12 12\"\n                      />\n                    </svg>\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Progress bar for auto-dismiss (optional) */}\n            {alert.type === 'info' && (\n              <div className=\"mt-3 w-full bg-black bg-opacity-10 rounded-full h-1\">\n                <div \n                  className=\"bg-current h-1 rounded-full transition-all duration-1000 ease-linear\"\n                  style={{ width: '100%' }}\n                />\n              </div>\n            )}\n          </div>\n        );\n      })}\n\n      {/* Alert counter if there are more alerts */}\n      {alerts.filter(a => a.isActive).length > maxVisible && (\n        <div className=\"text-center\">\n          <div className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n            +{alerts.filter(a => a.isActive).length - maxVisible} more alerts\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AlertBanner;\n"], "names": [], "mappings": ";;;;AAAA;;AAKA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,SAAS,EACT,aAAa,CAAC,EACf;IACC,+CAA+C;IAC/C,MAAM,eAAe,OAClB,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,EAC9B,KAAK,CAAC,GAAG;IAEZ,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,QAAQ;gBACV;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,QAAQ;gBACV;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,QAAQ;gBACV;YACF;gBACE,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,QAAQ;gBACV;QACJ;IACF;IAEA,mBAAmB;IACnB,MAAM,aAAa,CAAC;QAClB,OAAO,UAAU,kBAAkB,CAAC,SAAS;YAC3C,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,aAAa,GAAG,CAAC,CAAC;gBACjB,MAAM,SAAS,eAAe,MAAM,IAAI;gBAExC,qBACE,6LAAC;oBAEC,WAAW,CAAC;cACV,EAAE,OAAO,SAAS,CAAC;;;;YAIrB,CAAC;oBACD,MAAK;;sCAEL,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAW,CAAC;gBACf,EAAE,OAAO,MAAM,CAAC;;cAElB,CAAC;8CACC,cAAA,6LAAC;wCAAK,WAAU;kDAAW,OAAO,IAAI;;;;;;;;;;;8CAIxC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,MAAM,OAAO;;;;;;kEAEhB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAK;oEAAU,MAAM,SAAS;;;;;;;0EAC/B,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAM,WAAW,MAAM,SAAS;;;;;;;;;;;;;;;;;;0DAKrC,6LAAC;gDACC,SAAS,IAAM,UAAU,MAAM,EAAE;gDACjC,WAAU;gDAMV,cAAW;0DAEX,cAAA,6LAAC;oDACC,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,QAAO;8DAEP,cAAA,6LAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASb,MAAM,IAAI,KAAK,wBACd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAO;;;;;;;;;;;;mBAlExB,MAAM,EAAE;;;;;YAwEnB;YAGC,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM,GAAG,4BACvC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBAAgG;wBAC3G,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM,GAAG;wBAAW;;;;;;;;;;;;;;;;;;AAMjE;KAjJM;uCAmJS", "debugId": null}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/ClientTimestamp.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\n\ninterface ClientTimestampProps {\n  timestamp: Date;\n  className?: string;\n}\n\nconst ClientTimestamp: React.FC<ClientTimestampProps> = ({ timestamp, className }) => {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return <span className={className}>Loading...</span>;\n  }\n\n  return (\n    <span className={className}>\n      {timestamp.toLocaleTimeString()}\n    </span>\n  );\n};\n\nexport default ClientTimestamp;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASA,MAAM,kBAAkD,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE;;IAC/E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,WAAW;QACb;oCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBAAO,6LAAC;YAAK,WAAW;sBAAW;;;;;;IACrC;IAEA,qBACE,6LAAC;QAAK,WAAW;kBACd,UAAU,kBAAkB;;;;;;AAGnC;GAhBM;KAAA;uCAkBS", "debugId": null}}, {"offset": {"line": 823, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\n\nconst Navigation: React.FC = () => {\n  const pathname = usePathname();\n\n  const navItems = [\n    {\n      href: '/',\n      label: 'Live Tracking',\n      icon: '🗺️',\n      description: 'Real-time vehicle tracking'\n    },\n    {\n      href: '/dashboard/monitoring',\n      label: 'Monitoring',\n      icon: '📊',\n      description: 'Analytics and logs'\n    },\n    {\n      href: '/dashboard/analytics',\n      label: 'Analytics',\n      icon: '📈',\n      description: 'Data visualization and insights'\n    }\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo/Brand */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <h1 className=\"text-xl font-bold text-gray-900\">\n                🚛 Vehicle Tracking System\n              </h1>\n            </div>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navItems.map((item) => {\n                const isActive = pathname === item.href;\n                return (\n                  <Link\n                    key={item.href}\n                    href={item.href}\n                    className={`\n                      px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\n                      ${isActive\n                        ? 'bg-blue-100 text-blue-700 border border-blue-200'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                      }\n                    `}\n                  >\n                    <span className=\"mr-2\">{item.icon}</span>\n                    {item.label}\n                  </Link>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"bg-gray-100 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n              aria-expanded=\"false\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              <svg\n                className=\"block h-6 w-6\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n                aria-hidden=\"true\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth=\"2\"\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3\">\n            {navItems.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`\n                    block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200\n                    ${isActive\n                      ? 'bg-blue-100 text-blue-700 border border-blue-200'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                    }\n                  `}\n                >\n                  <div className=\"flex items-center\">\n                    <span className=\"mr-3\">{item.icon}</span>\n                    <div>\n                      <div>{item.label}</div>\n                      <div className=\"text-xs text-gray-500\">{item.description}</div>\n                    </div>\n                  </div>\n                </Link>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAMA,MAAM,aAAuB;;IAC3B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;;;;;;sCAOpD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC;oCACb,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC;;sBAEV,EAAE,WACE,qDACA,sDACH;oBACH,CAAC;;0DAED,6LAAC;gDAAK,WAAU;0DAAQ,KAAK,IAAI;;;;;;4CAChC,KAAK,KAAK;;uCAXN,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;sCAKJ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,iBAAc;;kDAEd,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCACC,WAAU;wCACV,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAY;4CACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC;;oBAEV,EAAE,WACE,qDACA,sDACH;kBACH,CAAC;0CAED,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAQ,KAAK,IAAI;;;;;;sDACjC,6LAAC;;8DACC,6LAAC;8DAAK,KAAK,KAAK;;;;;;8DAChB,6LAAC;oDAAI,WAAU;8DAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;+BAdvD,KAAK,IAAI;;;;;wBAmBpB;;;;;;;;;;;;;;;;;;;;;;AAMZ;GAzHM;;QACa,qIAAA,CAAA,cAAW;;;KADxB;uCA2HS", "debugId": null}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback, useEffect } from 'react';\nimport TrackingMap from '@/components/TrackingMap';\nimport VehicleTracker from '@/components/VehicleTracker';\nimport AlertBanner from '@/components/AlertBanner';\nimport ClientTimestamp from '@/components/ClientTimestamp';\nimport Navigation from '@/components/Navigation';\nimport { Route, VehicleState, VehiclePosition, Alert, Coordinate } from '@/types';\n\n// Sample route data (Riyadh City Route)\nconst sampleRoute: Route = {\n  id: 'route-1',\n  name: 'Riyadh City Route',\n  waypoints: [\n    { lat: 24.7136, lng: 46.6753 }, // King Fahd Road (Start)\n    { lat: 24.7200, lng: 46.6800 }, // Olaya District\n    { lat: 24.7280, lng: 46.6850 }, // Al Malaz\n    { lat: 24.7350, lng: 46.6900 }, // King Abdulaziz Road\n    { lat: 24.7420, lng: 46.6950 }, // Diplomatic Quarter\n    { lat: 24.7480, lng: 46.7000 }, // Al Muhammadiya<PERSON>\n    { lat: 24.7540, lng: 46.7050 }, // Al Naseem\n    { lat: 24.7600, lng: 46.7100 }, // King Khalid Airport Road\n    { lat: 24.7660, lng: 46.7150 }, // Al Rawdah\n    { lat: 24.7720, lng: 46.7200 }, // Northern Ring Road (End)\n  ],\n  color: '#2563eb',\n  strokeWeight: 4,\n};\n\nexport default function VehicleTrackingDashboard() {\n  const [vehicle, setVehicle] = useState<VehicleState>({\n    id: 'vehicle-001',\n    position: {\n      lat: sampleRoute.waypoints[0].lat,\n      lng: sampleRoute.waypoints[0].lng,\n      timestamp: new Date(),\n    },\n    isOnRoute: true,\n    currentWaypointIndex: 0,\n    distanceFromRoute: 0,\n    status: 'on-route',\n  });\n\n  const [alerts, setAlerts] = useState<Alert[]>([]);\n  const [isSimulationActive, setIsSimulationActive] = useState(true);\n\n  // Calculate distance from route using proper point-to-line segment distance\n  const calculateDistanceFromRoute = useCallback((position: VehiclePosition): number => {\n    let minDistance = Infinity;\n\n    // Helper function to calculate distance between two coordinates using Haversine formula\n    const haversineDistance = (coord1: Coordinate, coord2: Coordinate): number => {\n      const R = 6371000; // Earth's radius in meters\n      const dLat = (coord2.lat - coord1.lat) * Math.PI / 180;\n      const dLng = (coord2.lng - coord1.lng) * Math.PI / 180;\n      const a =\n        Math.sin(dLat/2) * Math.sin(dLat/2) +\n        Math.cos(coord1.lat * Math.PI / 180) * Math.cos(coord2.lat * Math.PI / 180) *\n        Math.sin(dLng/2) * Math.sin(dLng/2);\n      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n      return R * c;\n    };\n\n    // Helper function to calculate distance from point to line segment\n    const pointToLineDistance = (point: Coordinate, lineStart: Coordinate, lineEnd: Coordinate): number => {\n      const A = point.lat - lineStart.lat;\n      const B = point.lng - lineStart.lng;\n      const C = lineEnd.lat - lineStart.lat;\n      const D = lineEnd.lng - lineStart.lng;\n\n      const dot = A * C + B * D;\n      const lenSq = C * C + D * D;\n\n      if (lenSq === 0) {\n        // Line segment is actually a point\n        return haversineDistance(point, lineStart);\n      }\n\n      let param = dot / lenSq;\n\n      let closestPoint: Coordinate;\n      if (param < 0) {\n        closestPoint = lineStart;\n      } else if (param > 1) {\n        closestPoint = lineEnd;\n      } else {\n        closestPoint = {\n          lat: lineStart.lat + param * C,\n          lng: lineStart.lng + param * D\n        };\n      }\n\n      return haversineDistance(point, closestPoint);\n    };\n\n    // Calculate minimum distance to any route segment\n    for (let i = 0; i < sampleRoute.waypoints.length - 1; i++) {\n      const start = sampleRoute.waypoints[i];\n      const end = sampleRoute.waypoints[i + 1];\n\n      const distance = pointToLineDistance(position, start, end);\n      minDistance = Math.min(minDistance, distance);\n    }\n\n    return minDistance;\n  }, []);\n\n  // Handle vehicle position updates\n  const handleVehicleUpdate = useCallback((newVehicle: VehicleState) => {\n    setVehicle(newVehicle);\n  }, []);\n\n  // Handle position updates from tracker\n  const handlePositionUpdate = useCallback((position: VehiclePosition) => {\n    const distanceFromRoute = calculateDistanceFromRoute(position);\n    const isOnRoute = distanceFromRoute <= 150; // Increased tolerance to 150 meters\n\n    // Debug logging (remove in production)\n    if (distanceFromRoute > 100) {\n      console.log(`Vehicle distance from route: ${Math.round(distanceFromRoute)}m, Status: ${isOnRoute ? 'On Route' : 'Off Route'}`);\n    }\n\n    setVehicle(prev => {\n      const wasOnRoute = prev.isOnRoute;\n      const newVehicle = {\n        ...prev,\n        position,\n        distanceFromRoute,\n        isOnRoute,\n      };\n\n      // Create alert only when vehicle goes from on-route to off-route\n      if (wasOnRoute && !isOnRoute) {\n        const newAlert: Alert = {\n          id: `alert-${Date.now()}`,\n          type: 'warning',\n          message: 'Vehicle is outside allowed route',\n          timestamp: new Date(),\n          vehicleId: prev.id,\n          isActive: true,\n        };\n\n        setAlerts(prevAlerts => [...prevAlerts, newAlert]);\n      }\n\n      return newVehicle;\n    });\n  }, [calculateDistanceFromRoute]);\n\n  // Handle alerts based on vehicle position (simplified)\n  const handleStatusChange = useCallback(() => {\n    // This function is called by VehicleTracker but we determine status based on distance only\n    // Alerts are now handled in handlePositionUpdate when distance changes\n  }, []);\n\n  // Handle alert creation\n  const handleAlert = useCallback((alert: Alert) => {\n    setAlerts(prev => [...prev, alert]);\n  }, []);\n\n  // Handle alert dismissal\n  const handleAlertDismiss = useCallback((alertId: string) => {\n    setAlerts(prev =>\n      prev.map(alert =>\n        alert.id === alertId\n          ? { ...alert, isActive: false }\n          : alert\n      )\n    );\n  }, []);\n\n  // Auto-dismiss info alerts after 5 seconds\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setAlerts(prev =>\n        prev.map(alert =>\n          alert.type === 'info' && alert.isActive\n            ? { ...alert, isActive: false }\n            : alert\n        )\n      );\n    }, 5000);\n\n    return () => clearTimeout(timer);\n  }, [alerts]);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation */}\n      <Navigation />\n\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                Live Vehicle Tracking\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => setIsSimulationActive(!isSimulationActive)}\n                className={`\n                  px-4 py-2 rounded-md text-sm font-medium transition-colors\n                  ${isSimulationActive\n                    ? 'bg-red-600 text-white hover:bg-red-700'\n                    : 'bg-green-600 text-white hover:bg-green-700'\n                  }\n                `}\n              >\n                {isSimulationActive ? 'Stop Simulation' : 'Start Simulation'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n          {/* Status Panel */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                Vehicle Status\n              </h2>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Vehicle ID</label>\n                  <p className=\"text-lg font-mono\">{vehicle.id}</p>\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Status</label>\n                  <div className=\"flex items-center space-x-2\">\n                    <div\n                      className={`w-3 h-3 rounded-full ${\n                        vehicle.isOnRoute ? 'bg-green-500' : 'bg-red-500'\n                      }`}\n                    />\n                    <span className={`font-medium ${\n                      vehicle.isOnRoute ? 'text-green-700' : 'text-red-700'\n                    }`}>\n                      {vehicle.isOnRoute ? 'On Route' : 'Off Route'}\n                    </span>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Distance from Route</label>\n                  <p className=\"text-lg\">{Math.round(vehicle.distanceFromRoute)}m</p>\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Last Update</label>\n                  <ClientTimestamp\n                    timestamp={vehicle.position.timestamp}\n                    className=\"text-sm text-gray-600\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Speed</label>\n                  <p className=\"text-lg\">\n                    {vehicle.position.speed ? `${Math.round(vehicle.position.speed)} km/h` : 'N/A'}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Map */}\n          <div className=\"lg:col-span-3\">\n            <div className=\"bg-white rounded-lg shadow overflow-hidden\" style={{ height: '600px' }}>\n              <TrackingMap\n                route={sampleRoute}\n                vehicle={vehicle}\n                onVehicleUpdate={handleVehicleUpdate}\n                onAlert={handleAlert}\n              />\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Vehicle Tracker (invisible component) */}\n      <VehicleTracker\n        route={sampleRoute}\n        onPositionUpdate={handlePositionUpdate}\n        onStatusChange={handleStatusChange}\n        isActive={isSimulationActive}\n        speed={1}\n      />\n\n      {/* Alert Banner */}\n      <AlertBanner\n        alerts={alerts}\n        onDismiss={handleAlertDismiss}\n        maxVisible={3}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAUA,wCAAwC;AACxC,MAAM,cAAqB;IACzB,IAAI;IACJ,MAAM;IACN,WAAW;QACT;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;KAC9B;IACD,OAAO;IACP,cAAc;AAChB;AAEe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QACnD,IAAI;QACJ,UAAU;YACR,KAAK,YAAY,SAAS,CAAC,EAAE,CAAC,GAAG;YACjC,KAAK,YAAY,SAAS,CAAC,EAAE,CAAC,GAAG;YACjC,WAAW,IAAI;QACjB;QACA,WAAW;QACX,sBAAsB;QACtB,mBAAmB;QACnB,QAAQ;IACV;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,4EAA4E;IAC5E,MAAM,6BAA6B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4EAAE,CAAC;YAC9C,IAAI,cAAc;YAElB,wFAAwF;YACxF,MAAM;sGAAoB,CAAC,QAAoB;oBAC7C,MAAM,IAAI,SAAS,2BAA2B;oBAC9C,MAAM,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG,IAAI,KAAK,EAAE,GAAG;oBACnD,MAAM,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG,IAAI,KAAK,EAAE,GAAG;oBACnD,MAAM,IACJ,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK,KACjC,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,OACvE,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK;oBACnC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAE;oBACnD,OAAO,IAAI;gBACb;;YAEA,mEAAmE;YACnE,MAAM;wGAAsB,CAAC,OAAmB,WAAuB;oBACrE,MAAM,IAAI,MAAM,GAAG,GAAG,UAAU,GAAG;oBACnC,MAAM,IAAI,MAAM,GAAG,GAAG,UAAU,GAAG;oBACnC,MAAM,IAAI,QAAQ,GAAG,GAAG,UAAU,GAAG;oBACrC,MAAM,IAAI,QAAQ,GAAG,GAAG,UAAU,GAAG;oBAErC,MAAM,MAAM,IAAI,IAAI,IAAI;oBACxB,MAAM,QAAQ,IAAI,IAAI,IAAI;oBAE1B,IAAI,UAAU,GAAG;wBACf,mCAAmC;wBACnC,OAAO,kBAAkB,OAAO;oBAClC;oBAEA,IAAI,QAAQ,MAAM;oBAElB,IAAI;oBACJ,IAAI,QAAQ,GAAG;wBACb,eAAe;oBACjB,OAAO,IAAI,QAAQ,GAAG;wBACpB,eAAe;oBACjB,OAAO;wBACL,eAAe;4BACb,KAAK,UAAU,GAAG,GAAG,QAAQ;4BAC7B,KAAK,UAAU,GAAG,GAAG,QAAQ;wBAC/B;oBACF;oBAEA,OAAO,kBAAkB,OAAO;gBAClC;;YAEA,kDAAkD;YAClD,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,SAAS,CAAC,MAAM,GAAG,GAAG,IAAK;gBACzD,MAAM,QAAQ,YAAY,SAAS,CAAC,EAAE;gBACtC,MAAM,MAAM,YAAY,SAAS,CAAC,IAAI,EAAE;gBAExC,MAAM,WAAW,oBAAoB,UAAU,OAAO;gBACtD,cAAc,KAAK,GAAG,CAAC,aAAa;YACtC;YAEA,OAAO;QACT;2EAAG,EAAE;IAEL,kCAAkC;IAClC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qEAAE,CAAC;YACvC,WAAW;QACb;oEAAG,EAAE;IAEL,uCAAuC;IACvC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sEAAE,CAAC;YACxC,MAAM,oBAAoB,2BAA2B;YACrD,MAAM,YAAY,qBAAqB,KAAK,oCAAoC;YAEhF,uCAAuC;YACvC,IAAI,oBAAoB,KAAK;gBAC3B,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,KAAK,KAAK,CAAC,mBAAmB,WAAW,EAAE,YAAY,aAAa,aAAa;YAC/H;YAEA;8EAAW,CAAA;oBACT,MAAM,aAAa,KAAK,SAAS;oBACjC,MAAM,aAAa;wBACjB,GAAG,IAAI;wBACP;wBACA;wBACA;oBACF;oBAEA,iEAAiE;oBACjE,IAAI,cAAc,CAAC,WAAW;wBAC5B,MAAM,WAAkB;4BACtB,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;4BACzB,MAAM;4BACN,SAAS;4BACT,WAAW,IAAI;4BACf,WAAW,KAAK,EAAE;4BAClB,UAAU;wBACZ;wBAEA;0FAAU,CAAA,aAAc;uCAAI;oCAAY;iCAAS;;oBACnD;oBAEA,OAAO;gBACT;;QACF;qEAAG;QAAC;KAA2B;IAE/B,uDAAuD;IACvD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oEAAE;QACrC,2FAA2F;QAC3F,uEAAuE;QACzE;mEAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YAC/B;qEAAU,CAAA,OAAQ;2BAAI;wBAAM;qBAAM;;QACpC;4DAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oEAAE,CAAC;YACtC;4EAAU,CAAA,OACR,KAAK,GAAG;oFAAC,CAAA,QACP,MAAM,EAAE,KAAK,UACT;gCAAE,GAAG,KAAK;gCAAE,UAAU;4BAAM,IAC5B;;;QAGV;mEAAG,EAAE;IAEL,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,MAAM,QAAQ;4DAAW;oBACvB;oEAAU,CAAA,OACR,KAAK,GAAG;4EAAC,CAAA,QACP,MAAM,IAAI,KAAK,UAAU,MAAM,QAAQ,GACnC;wCAAE,GAAG,KAAK;wCAAE,UAAU;oCAAM,IAC5B;;;gBAGV;2DAAG;YAEH;sDAAO,IAAM,aAAa;;QAC5B;6CAAG;QAAC;KAAO;IAEX,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,UAAU;;;;;0BAGX,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;0CAInD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS,IAAM,sBAAsB,CAAC;oCACtC,WAAW,CAAC;;kBAEV,EAAE,qBACE,2CACA,6CACH;gBACH,CAAC;8CAEA,qBAAqB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpD,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAIzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6LAAC;wDAAE,WAAU;kEAAqB,QAAQ,EAAE;;;;;;;;;;;;0DAG9C,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAW,CAAC,qBAAqB,EAC/B,QAAQ,SAAS,GAAG,iBAAiB,cACrC;;;;;;0EAEJ,6LAAC;gEAAK,WAAW,CAAC,YAAY,EAC5B,QAAQ,SAAS,GAAG,mBAAmB,gBACvC;0EACC,QAAQ,SAAS,GAAG,aAAa;;;;;;;;;;;;;;;;;;0DAKxC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6LAAC;wDAAE,WAAU;;4DAAW,KAAK,KAAK,CAAC,QAAQ,iBAAiB;4DAAE;;;;;;;;;;;;;0DAGhE,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6LAAC,wIAAA,CAAA,UAAe;wDACd,WAAW,QAAQ,QAAQ,CAAC,SAAS;wDACrC,WAAU;;;;;;;;;;;;0DAId,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6LAAC;wDAAE,WAAU;kEACV,QAAQ,QAAQ,CAAC,KAAK,GAAG,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQnF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAA6C,OAAO;oCAAE,QAAQ;gCAAQ;0CACnF,cAAA,6LAAC,oIAAA,CAAA,UAAW;oCACV,OAAO;oCACP,SAAS;oCACT,iBAAiB;oCACjB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnB,6LAAC,uIAAA,CAAA,UAAc;gBACb,OAAO;gBACP,kBAAkB;gBAClB,gBAAgB;gBAChB,UAAU;gBACV,OAAO;;;;;;0BAIT,6LAAC,oIAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,WAAW;gBACX,YAAY;;;;;;;;;;;;AAIpB;GAnRwB;KAAA", "debugId": null}}]}