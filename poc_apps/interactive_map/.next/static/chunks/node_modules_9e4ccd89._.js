(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return (type.displayName || "Context") + ".Provider";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler");
    Symbol.for("react.provider");
    var REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        "react-stack-bottom-frame": function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React["react-stack-bottom-frame"].bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/@react-google-maps/api/dist/esm.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Autocomplete": (()=>Autocomplete),
    "BicyclingLayer": (()=>BicyclingLayer),
    "BicyclingLayerF": (()=>BicyclingLayerF),
    "Circle": (()=>Circle),
    "CircleF": (()=>CircleF),
    "Data": (()=>Data),
    "DataF": (()=>DataF),
    "DirectionsRenderer": (()=>DirectionsRenderer),
    "DirectionsService": (()=>DirectionsService),
    "DistanceMatrixService": (()=>DistanceMatrixService),
    "DrawingManager": (()=>DrawingManager),
    "DrawingManagerF": (()=>DrawingManagerF),
    "FLOAT_PANE": (()=>FLOAT_PANE),
    "GoogleMap": (()=>GoogleMap),
    "GoogleMapsMarkerClusterer": (()=>index_esm),
    "GoogleMarkerClusterer": (()=>GoogleMarkerClusterer$1),
    "GroundOverlay": (()=>GroundOverlay),
    "GroundOverlayF": (()=>GroundOverlayF),
    "HeatmapLayer": (()=>HeatmapLayer),
    "HeatmapLayerF": (()=>HeatmapLayerF),
    "InfoBox": (()=>InfoBoxComponent),
    "InfoBoxF": (()=>InfoBoxF),
    "InfoWindow": (()=>InfoWindow),
    "InfoWindowF": (()=>InfoWindowF),
    "KmlLayer": (()=>KmlLayer),
    "LoadScript": (()=>LoadScript),
    "LoadScriptNext": (()=>LoadScriptNext$1),
    "MAP_PANE": (()=>MAP_PANE),
    "MARKER_LAYER": (()=>MARKER_LAYER),
    "MapContext": (()=>MapContext),
    "Marker": (()=>Marker),
    "MarkerClusterer": (()=>ClustererComponent),
    "MarkerClustererF": (()=>MarkerClustererF),
    "MarkerF": (()=>MarkerF),
    "OVERLAY_LAYER": (()=>OVERLAY_LAYER),
    "OVERLAY_MOUSE_TARGET": (()=>OVERLAY_MOUSE_TARGET),
    "OverlayView": (()=>OverlayView),
    "OverlayViewF": (()=>OverlayViewF),
    "Polygon": (()=>Polygon),
    "PolygonF": (()=>PolygonF),
    "Polyline": (()=>Polyline),
    "PolylineF": (()=>PolylineF),
    "Rectangle": (()=>Rectangle),
    "RectangleF": (()=>RectangleF),
    "StandaloneSearchBox": (()=>StandaloneSearchBox),
    "StreetViewPanorama": (()=>StreetViewPanorama),
    "StreetViewService": (()=>StreetViewService),
    "TrafficLayer": (()=>TrafficLayer),
    "TrafficLayerF": (()=>TrafficLayerF),
    "TransitLayer": (()=>TransitLayer),
    "TransitLayerF": (()=>TransitLayerF),
    "useGoogleMap": (()=>useGoogleMap),
    "useJsApiLoader": (()=>useJsApiLoader),
    "useLoadScript": (()=>useLoadScript)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-dom/index.js [app-client] (ecmascript)");
;
;
;
;
function _typeof(o) {
    "@babel/helpers - typeof";
    return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o) {
        return typeof o;
    } : function(o) {
        return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;
    }, _typeof(o);
}
function toPrimitive(t, r) {
    if ("object" != _typeof(t) || !t) return t;
    var e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        var i = e.call(t, r || "default");
        if ("object" != _typeof(i)) return i;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return ("string" === r ? String : Number)(t);
}
function toPropertyKey(t) {
    var i = toPrimitive(t, "string");
    return "symbol" == _typeof(i) ? i : i + "";
}
function _defineProperty(e, r, t) {
    return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) : e[r] = t, e;
}
function getDefaultExportFromCjs$1(x) {
    return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
}
/**
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var invariant_1;
var hasRequiredInvariant;
function requireInvariant() {
    if (hasRequiredInvariant) return invariant_1;
    hasRequiredInvariant = 1;
    /**
   * Use invariant() to assert state which your program assumes to be true.
   *
   * Provide sprintf-style format (only %s is supported) and arguments
   * to provide information about what broke and what you were
   * expecting.
   *
   * The invariant message will be stripped in production, but the invariant
   * will remain to ensure logic does not differ in production.
   */ var NODE_ENV = ("TURBOPACK compile-time value", "development");
    var invariant = function invariant(condition, format, a, b, c, d, e, f) {
        if ("TURBOPACK compile-time truthy", 1) {
            if (format === undefined) {
                throw new Error('invariant requires an error message argument');
            }
        }
        if (!condition) {
            var error;
            if (format === undefined) {
                error = new Error('Minified exception occurred; use the non-minified dev environment ' + 'for the full error message and additional helpful warnings.');
            } else {
                var args = [
                    a,
                    b,
                    c,
                    d,
                    e,
                    f
                ];
                var argIndex = 0;
                error = new Error(format.replace(/%s/g, function() {
                    return args[argIndex++];
                }));
                error.name = 'Invariant Violation';
            }
            error.framesToPop = 1; // we don't care about invariant's own frame
            throw error;
        }
    };
    invariant_1 = invariant;
    return invariant_1;
}
var invariantExports = requireInvariant();
var invariant = /*@__PURE__*/ getDefaultExportFromCjs$1(invariantExports);
var MapContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
function useGoogleMap() {
    invariant(!!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"], 'useGoogleMap is React hook and requires React version 16.8+');
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    invariant(!!map, 'useGoogleMap needs a GoogleMap available up in the tree');
    return map;
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function reduce(obj, fn, acc) {
    return Object.keys(obj).reduce(function reducer(newAcc, key) {
        return fn(newAcc, obj[key], key);
    }, acc);
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function forEach(obj, fn) {
    Object.keys(obj).forEach((key)=>{
        return fn(obj[key], key);
    });
}
/* global google */ /* eslint-disable filenames/match-regex */ function applyUpdaterToNextProps(// eslint-disable-next-line @typescript-eslint/no-explicit-any
updaterMap, // eslint-disable-next-line @typescript-eslint/no-explicit-any
prevProps, // eslint-disable-next-line @typescript-eslint/no-explicit-any
nextProps, // eslint-disable-next-line @typescript-eslint/no-explicit-any
instance) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    var map = {};
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    var iter = (fn, key)=>{
        var nextValue = nextProps[key];
        if (nextValue !== prevProps[key]) {
            map[key] = nextValue;
            fn(instance, nextValue);
        }
    };
    forEach(updaterMap, iter);
    return map;
}
function registerEvents(// eslint-disable-next-line @typescript-eslint/no-explicit-any
props, // eslint-disable-next-line @typescript-eslint/no-explicit-any
instance, eventMap) {
    var registeredList = reduce(eventMap, function reducer(acc, googleEventName, // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onEventName) {
        if (typeof props[onEventName] === 'function') {
            acc.push(google.maps.event.addListener(instance, googleEventName, props[onEventName]));
        }
        return acc;
    }, []);
    return registeredList;
}
function unregisterEvent(registered) {
    google.maps.event.removeListener(registered);
}
function unregisterEvents() {
    var events = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
    events.forEach(unregisterEvent);
}
function applyUpdatersToPropsAndRegisterEvents(_ref) {
    var { updaterMap, eventMap, prevProps, nextProps, instance } = _ref;
    var registeredEvents = registerEvents(nextProps, instance, eventMap);
    applyUpdaterToNextProps(updaterMap, prevProps, nextProps, instance);
    return registeredEvents;
}
var eventMap$i = {
    onDblClick: 'dblclick',
    onDragEnd: 'dragend',
    onDragStart: 'dragstart',
    onMapTypeIdChanged: 'maptypeid_changed',
    onMouseMove: 'mousemove',
    onMouseOut: 'mouseout',
    onMouseOver: 'mouseover',
    onMouseDown: 'mousedown',
    onMouseUp: 'mouseup',
    onRightClick: 'rightclick',
    onTilesLoaded: 'tilesloaded',
    onBoundsChanged: 'bounds_changed',
    onCenterChanged: 'center_changed',
    onClick: 'click',
    onDrag: 'drag',
    onHeadingChanged: 'heading_changed',
    onIdle: 'idle',
    onProjectionChanged: 'projection_changed',
    onResize: 'resize',
    onTiltChanged: 'tilt_changed',
    onZoomChanged: 'zoom_changed'
};
var updaterMap$i = {
    extraMapTypes (map, extra) {
        extra.forEach(function forEachExtra(it, i) {
            map.mapTypes.set(String(i), it);
        });
    },
    center (map, center) {
        map.setCenter(center);
    },
    clickableIcons (map, clickable) {
        map.setClickableIcons(clickable);
    },
    heading (map, heading) {
        map.setHeading(heading);
    },
    mapTypeId (map, mapTypeId) {
        map.setMapTypeId(mapTypeId);
    },
    options (map, options) {
        map.setOptions(options);
    },
    streetView (map, streetView) {
        map.setStreetView(streetView);
    },
    tilt (map, tilt) {
        map.setTilt(tilt);
    },
    zoom (map, zoom) {
        map.setZoom(zoom);
    }
};
// TODO: unfinished!
function GoogleMapFunctional(_ref) {
    var { children, options, id, mapContainerStyle, mapContainerClassName, center, // clickableIcons,
    // extraMapTypes,
    // heading,
    // mapTypeId,
    onClick, onDblClick, onDrag, onDragEnd, onDragStart, onMouseMove, onMouseOut, onMouseOver, onMouseDown, onMouseUp, onRightClick, // onMapTypeIdChanged,
    // onTilesLoaded,
    // onBoundsChanged,
    onCenterChanged, // onHeadingChanged,
    // onIdle,
    // onProjectionChanged,
    // onResize,
    // onTiltChanged,
    // onZoomChanged,
    onLoad, onUnmount } = _ref;
    var [map, setMap] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // const [extraMapTypesListener, setExtraMapTypesListener] = useState<google.maps.MapsEventListener | null>(null)
    var [centerChangedListener, setCenterChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dblclickListener, setDblclickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragendListener, setDragendListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragstartListener, setDragstartListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mousedownListener, setMousedownListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mousemoveListener, setMousemoveListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoutListener, setMouseoutListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoverListener, setMouseoverListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseupListener, setMouseupListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [rightclickListener, setRightclickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [clickListener, setClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragListener, setDragListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (options && map !== null) {
                map.setOptions(options);
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        map,
        options
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (map !== null && typeof center !== 'undefined') {
                map.setCenter(center);
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        map,
        center
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (map && onDblClick) {
                if (dblclickListener !== null) {
                    google.maps.event.removeListener(dblclickListener);
                }
                setDblclickListener(google.maps.event.addListener(map, 'dblclick', onDblClick));
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        onDblClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (map && onDragEnd) {
                if (dragendListener !== null) {
                    google.maps.event.removeListener(dragendListener);
                }
                setDragendListener(google.maps.event.addListener(map, 'dragend', onDragEnd));
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        onDragEnd
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (map && onDragStart) {
                if (dragstartListener !== null) {
                    google.maps.event.removeListener(dragstartListener);
                }
                setDragstartListener(google.maps.event.addListener(map, 'dragstart', onDragStart));
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        onDragStart
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (map && onMouseDown) {
                if (mousedownListener !== null) {
                    google.maps.event.removeListener(mousedownListener);
                }
                setMousedownListener(google.maps.event.addListener(map, 'mousedown', onMouseDown));
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        onMouseDown
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (map && onMouseMove) {
                if (mousemoveListener !== null) {
                    google.maps.event.removeListener(mousemoveListener);
                }
                setMousemoveListener(google.maps.event.addListener(map, 'mousemove', onMouseMove));
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        onMouseMove
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (map && onMouseOut) {
                if (mouseoutListener !== null) {
                    google.maps.event.removeListener(mouseoutListener);
                }
                setMouseoutListener(google.maps.event.addListener(map, 'mouseout', onMouseOut));
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        onMouseOut
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (map && onMouseOver) {
                if (mouseoverListener !== null) {
                    google.maps.event.removeListener(mouseoverListener);
                }
                setMouseoverListener(google.maps.event.addListener(map, 'mouseover', onMouseOver));
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        onMouseOver
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (map && onMouseUp) {
                if (mouseupListener !== null) {
                    google.maps.event.removeListener(mouseupListener);
                }
                setMouseupListener(google.maps.event.addListener(map, 'mouseup', onMouseUp));
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        onMouseUp
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (map && onRightClick) {
                if (rightclickListener !== null) {
                    google.maps.event.removeListener(rightclickListener);
                }
                setRightclickListener(google.maps.event.addListener(map, 'rightclick', onRightClick));
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        onRightClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (map && onClick) {
                if (clickListener !== null) {
                    google.maps.event.removeListener(clickListener);
                }
                setClickListener(google.maps.event.addListener(map, 'click', onClick));
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        onClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (map && onDrag) {
                if (dragListener !== null) {
                    google.maps.event.removeListener(dragListener);
                }
                setDragListener(google.maps.event.addListener(map, 'drag', onDrag));
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        onDrag
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            if (map && onCenterChanged) {
                if (centerChangedListener !== null) {
                    google.maps.event.removeListener(centerChangedListener);
                }
                setCenterChangedListener(google.maps.event.addListener(map, 'center_changed', onCenterChanged));
            }
        }
    }["GoogleMapFunctional.useEffect"], [
        onClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleMapFunctional.useEffect": ()=>{
            var map = ref.current === null ? null : new google.maps.Map(ref.current, options);
            setMap(map);
            if (map !== null && onLoad) {
                onLoad(map);
            }
            return ({
                "GoogleMapFunctional.useEffect": ()=>{
                    if (map !== null) {
                        if (onUnmount) {
                            onUnmount(map);
                        }
                    }
                }
            })["GoogleMapFunctional.useEffect"];
        }
    }["GoogleMapFunctional.useEffect"], []);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        id: id,
        ref: ref,
        style: mapContainerStyle,
        className: mapContainerClassName,
        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(MapContext.Provider, {
            value: map,
            children: map !== null ? children : null
        })
    });
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(GoogleMapFunctional);
class GoogleMap extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "state", {
            map: null
        });
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "mapRef", null);
        _defineProperty(this, "getInstance", ()=>{
            if (this.mapRef === null) {
                return null;
            }
            return new google.maps.Map(this.mapRef, this.props.options);
        });
        _defineProperty(this, "panTo", (latLng)=>{
            var map = this.getInstance();
            if (map) {
                map.panTo(latLng);
            }
        });
        _defineProperty(this, "setMapCallback", ()=>{
            if (this.state.map !== null) {
                if (this.props.onLoad) {
                    this.props.onLoad(this.state.map);
                }
            }
        });
        _defineProperty(this, "getRef", (ref)=>{
            this.mapRef = ref;
        });
    }
    componentDidMount() {
        var map = this.getInstance();
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$i,
            eventMap: eventMap$i,
            prevProps: {},
            nextProps: this.props,
            instance: map
        });
        this.setState(function setMap() {
            return {
                map
            };
        }, this.setMapCallback);
    }
    componentDidUpdate(prevProps) {
        if (this.state.map !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$i,
                eventMap: eventMap$i,
                prevProps,
                nextProps: this.props,
                instance: this.state.map
            });
        }
    }
    componentWillUnmount() {
        if (this.state.map !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.map);
            }
            unregisterEvents(this.registeredEvents);
        }
    }
    render() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
            id: this.props.id,
            ref: this.getRef,
            style: this.props.mapContainerStyle,
            className: this.props.mapContainerClassName,
            children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(MapContext.Provider, {
                value: this.state.map,
                children: this.state.map !== null ? this.props.children : null
            })
        });
    }
}
function asyncGeneratorStep(n, t, e, r, o, a, c) {
    try {
        var i = n[a](c), u = i.value;
    } catch (n) {
        return void e(n);
    }
    i.done ? t(u) : Promise.resolve(u).then(r, o);
}
function _asyncToGenerator(n) {
    return function() {
        var t = this, e = arguments;
        return new Promise(function(r, o) {
            var a = n.apply(t, e);
            function _next(n) {
                asyncGeneratorStep(a, r, o, _next, _throw, "next", n);
            }
            function _throw(n) {
                asyncGeneratorStep(a, r, o, _next, _throw, "throw", n);
            }
            _next(void 0);
        });
    };
}
function makeLoadScriptUrl(_ref) {
    var { googleMapsApiKey, googleMapsClientId, version = 'weekly', language, region, libraries, channel, mapIds, authReferrerPolicy, apiUrl = 'https://maps.googleapis.com' } = _ref;
    var params = [];
    invariant(googleMapsApiKey && googleMapsClientId || !(googleMapsApiKey && googleMapsClientId), 'You need to specify either googleMapsApiKey or googleMapsClientId for @react-google-maps/api load script to work. You cannot use both at the same time.');
    if (googleMapsApiKey) {
        params.push("key=".concat(googleMapsApiKey));
    } else if (googleMapsClientId) {
        params.push("client=".concat(googleMapsClientId));
    }
    if (version) {
        params.push("v=".concat(version));
    }
    if (language) {
        params.push("language=".concat(language));
    }
    if (region) {
        params.push("region=".concat(region));
    }
    if (libraries && libraries.length) {
        params.push("libraries=".concat(libraries.sort().join(',')));
    }
    if (channel) {
        params.push("channel=".concat(channel));
    }
    if (mapIds && mapIds.length) {
        params.push("map_ids=".concat(mapIds.join(',')));
    }
    if (authReferrerPolicy) {
        params.push("auth_referrer_policy=".concat(authReferrerPolicy));
    }
    params.push('loading=async');
    params.push('callback=initMap');
    return "".concat(apiUrl, "/maps/api/js?").concat(params.join('&'));
}
var isBrowser = typeof document !== 'undefined';
function injectScript(_ref) {
    var { url, id, nonce } = _ref;
    if (!isBrowser) {
        return Promise.reject(new Error('document is undefined'));
    }
    return new Promise(function injectScriptCallback(resolve, reject) {
        var existingScript = document.getElementById(id);
        var windowWithGoogleMap = window;
        if (existingScript) {
            // Same script id/url: keep same script
            var dataStateAttribute = existingScript.getAttribute('data-state');
            if (existingScript.src === url && dataStateAttribute !== 'error') {
                if (dataStateAttribute === 'ready') {
                    return resolve(id);
                } else {
                    var originalInitMap = windowWithGoogleMap.initMap;
                    var originalErrorCallback = existingScript.onerror;
                    windowWithGoogleMap.initMap = function initMap() {
                        if (originalInitMap) {
                            originalInitMap();
                        }
                        resolve(id);
                    };
                    existingScript.onerror = function(err) {
                        if (originalErrorCallback) {
                            originalErrorCallback(err);
                        }
                        reject(err);
                    };
                    return;
                }
            } else {
                existingScript.remove();
            }
        }
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        script.id = id;
        script.async = true;
        script.nonce = nonce || '';
        script.onerror = function onerror(err) {
            script.setAttribute('data-state', 'error');
            reject(err);
        };
        windowWithGoogleMap.initMap = function onload() {
            script.setAttribute('data-state', 'ready');
            resolve(id);
        };
        document.head.appendChild(script);
    }).catch((err)=>{
        console.error('injectScript error: ', err);
        throw err;
    });
}
function isGoogleFontStyle(element) {
    // 'Roboto' or 'Google Sans Text' font download
    var href = element.href;
    if (href && (href.indexOf('https://fonts.googleapis.com/css?family=Roboto') === 0 || href.indexOf('https://fonts.googleapis.com/css?family=Google+Sans+Text') === 0)) {
        return true;
    }
    // font style elements
    if (// eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    element.tagName.toLowerCase() === 'style' && // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    element.styleSheet && // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    element.styleSheet.cssText && // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    element.styleSheet.cssText.replace('\r\n', '').indexOf('.gm-style') === 0) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        element.styleSheet.cssText = '';
        return true;
    }
    // font style elements for other browsers
    if (// eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    element.tagName.toLowerCase() === 'style' && // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    element.innerHTML && // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    element.innerHTML.replace('\r\n', '').indexOf('.gm-style') === 0) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        element.innerHTML = '';
        return true;
    }
    // when google tries to add empty style
    if (// eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    element.tagName.toLowerCase() === 'style' && // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    !element.styleSheet && // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    !element.innerHTML) {
        return true;
    }
    return false;
}
// Preventing the Google Maps library from downloading an extra font
function preventGoogleFonts() {
    // we override these methods only for one particular head element
    // default methods for other elements are not affected
    var head = document.getElementsByTagName('head')[0];
    if (head) {
        var trueInsertBefore = head.insertBefore.bind(head);
        // TODO: adding return before reflect solves the TS issue
        head.insertBefore = function insertBefore(newElement, referenceElement) {
            if (!isGoogleFontStyle(newElement)) {
                Reflect.apply(trueInsertBefore, head, [
                    newElement,
                    referenceElement
                ]);
            }
            return newElement;
        };
        var trueAppend = head.appendChild.bind(head);
        // TODO: adding return before reflect solves the TS issue
        head.appendChild = function appendChild(textNode) {
            if (!isGoogleFontStyle(textNode)) {
                Reflect.apply(trueAppend, head, [
                    textNode
                ]);
            }
            return textNode;
        };
    }
}
var cleaningUp = false;
function DefaultLoadingElement() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        children: "Loading..."
    });
}
var defaultLoadScriptProps = {
    id: 'script-loader',
    version: 'weekly'
};
class LoadScript extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "check", null);
        _defineProperty(this, "state", {
            loaded: false
        });
        _defineProperty(this, "cleanupCallback", ()=>{
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            delete window.google.maps;
            this.injectScript();
        });
        _defineProperty(this, "isCleaningUp", /*#__PURE__*/ _asyncToGenerator(function*() {
            function promiseCallback(resolve) {
                if (!cleaningUp) {
                    resolve();
                } else {
                    if (isBrowser) {
                        var timer = window.setInterval(function interval() {
                            if (!cleaningUp) {
                                window.clearInterval(timer);
                                resolve();
                            }
                        }, 1);
                    }
                }
                return;
            }
            return new Promise(promiseCallback);
        }));
        _defineProperty(this, "cleanup", ()=>{
            cleaningUp = true;
            var script = document.getElementById(this.props.id);
            if (script && script.parentNode) {
                script.parentNode.removeChild(script);
            }
            Array.prototype.slice.call(document.getElementsByTagName('script')).filter(function filter(script) {
                return typeof script.src === 'string' && script.src.includes('maps.googleapis');
            }).forEach(function forEach(script) {
                if (script.parentNode) {
                    script.parentNode.removeChild(script);
                }
            });
            Array.prototype.slice.call(document.getElementsByTagName('link')).filter(function filter(link) {
                return link.href === 'https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Google+Sans';
            }).forEach(function forEach(link) {
                if (link.parentNode) {
                    link.parentNode.removeChild(link);
                }
            });
            Array.prototype.slice.call(document.getElementsByTagName('style')).filter(function filter(style) {
                return style.innerText !== undefined && style.innerText.length > 0 && style.innerText.includes('.gm-');
            }).forEach(function forEach(style) {
                if (style.parentNode) {
                    style.parentNode.removeChild(style);
                }
            });
        });
        _defineProperty(this, "injectScript", ()=>{
            if (this.props.preventGoogleFontsLoading) {
                preventGoogleFonts();
            }
            invariant(!!this.props.id, 'LoadScript requires "id" prop to be a string: %s', this.props.id);
            var injectScriptOptions = {
                id: this.props.id,
                nonce: this.props.nonce,
                url: makeLoadScriptUrl(this.props)
            };
            injectScript(injectScriptOptions).then(()=>{
                if (this.props.onLoad) {
                    this.props.onLoad();
                }
                this.setState(function setLoaded() {
                    return {
                        loaded: true
                    };
                });
                return;
            }).catch((err)=>{
                if (this.props.onError) {
                    this.props.onError(err);
                }
                console.error("\n          There has been an Error with loading Google Maps API script, please check that you provided correct google API key (".concat(this.props.googleMapsApiKey || '-', ") or Client ID (").concat(this.props.googleMapsClientId || '-', ") to <LoadScript />\n          Otherwise it is a Network issue.\n        "));
            });
        });
        _defineProperty(this, "getRef", (el)=>{
            this.check = el;
        });
    }
    componentDidMount() {
        if (isBrowser) {
            if (window.google && window.google.maps && !cleaningUp) {
                console.error('google api is already presented');
                return;
            }
            this.isCleaningUp().then(this.injectScript).catch(function error(err) {
                console.error('Error at injecting script after cleaning up: ', err);
            });
        }
    }
    componentDidUpdate(prevProps) {
        if (this.props.libraries !== prevProps.libraries) {
            console.warn('Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables');
        }
        if (isBrowser && prevProps.language !== this.props.language) {
            this.cleanup();
            // TODO: refactor to use gDSFP maybe... wait for hooks refactoring.
            this.setState(function setLoaded() {
                return {
                    loaded: false
                };
            }, this.cleanupCallback);
        }
    }
    componentWillUnmount() {
        if (isBrowser) {
            this.cleanup();
            var timeoutCallback = ()=>{
                if (!this.check) {
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    delete window.google;
                    cleaningUp = false;
                }
            };
            window.setTimeout(timeoutCallback, 1);
            if (this.props.onUnmount) {
                this.props.onUnmount();
            }
        }
    }
    render() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                    ref: this.getRef
                }),
                this.state.loaded ? this.props.children : this.props.loadingElement || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(DefaultLoadingElement, {})
            ]
        });
    }
}
_defineProperty(LoadScript, "defaultProps", defaultLoadScriptProps);
function _objectWithoutPropertiesLoose(r, e) {
    if (null == r) return {};
    var t = {};
    for(var n in r)if (({}).hasOwnProperty.call(r, n)) {
        if (e.includes(n)) continue;
        t[n] = r[n];
    }
    return t;
}
function _objectWithoutProperties(e, t) {
    if (null == e) return {};
    var o, r, i = _objectWithoutPropertiesLoose(e, t);
    if (Object.getOwnPropertySymbols) {
        var s = Object.getOwnPropertySymbols(e);
        for(r = 0; r < s.length; r++)o = s[r], t.includes(o) || ({}).propertyIsEnumerable.call(e, o) && (i[o] = e[o]);
    }
    return i;
}
/* eslint-disable filenames/match-regex */ var previouslyLoadedUrl;
function useLoadScript(_ref) {
    var { id = defaultLoadScriptProps.id, version = defaultLoadScriptProps.version, nonce, googleMapsApiKey, googleMapsClientId, language, region, libraries, preventGoogleFontsLoading, channel, mapIds, authReferrerPolicy, apiUrl = "https://maps.googleapis.com" } = _ref;
    var isMounted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    var [isLoaded, setLoaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    var [loadError, setLoadError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(undefined);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function trackMountedState() {
        isMounted.current = true;
        return ({
            "useLoadScript.useEffect.trackMountedState": ()=>{
                isMounted.current = false;
            }
        })["useLoadScript.useEffect.trackMountedState"];
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function applyPreventGoogleFonts() {
        if (isBrowser && preventGoogleFontsLoading) {
            preventGoogleFonts();
        }
    }, [
        preventGoogleFontsLoading
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function validateLoadedState() {
        if (isLoaded) {
            invariant(!!window.google, 'useLoadScript was marked as loaded, but window.google is not present. Something went wrong.');
        }
    }, [
        isLoaded
    ]);
    var url = makeLoadScriptUrl({
        version,
        googleMapsApiKey,
        googleMapsClientId,
        language,
        region,
        libraries,
        channel,
        mapIds,
        authReferrerPolicy,
        apiUrl
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function loadScriptAndModifyLoadedState() {
        if (!isBrowser) {
            return;
        }
        function setLoadedIfMounted() {
            if (isMounted.current) {
                setLoaded(true);
                previouslyLoadedUrl = url;
            }
        }
        if (window.google && window.google.maps && previouslyLoadedUrl === url) {
            setLoadedIfMounted();
            return;
        }
        injectScript({
            id,
            url,
            nonce
        }).then(setLoadedIfMounted).catch(function handleInjectError(err) {
            if (isMounted.current) {
                setLoadError(err);
            }
            console.warn("\n        There has been an Error with loading Google Maps API script, please check that you provided correct google API key (".concat(googleMapsApiKey || '-', ") or Client ID (").concat(googleMapsClientId || '-', ")\n        Otherwise it is a Network issue.\n      "));
            console.error(err);
        });
    }, [
        id,
        url,
        nonce
    ]);
    var prevLibraries = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(undefined);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function checkPerformance() {
        if (prevLibraries.current && libraries !== prevLibraries.current) {
            console.warn('Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables');
        }
        prevLibraries.current = libraries;
    }, [
        libraries
    ]);
    return {
        isLoaded,
        loadError,
        url
    };
}
var _excluded$1 = [
    "loadingElement",
    "onLoad",
    "onError",
    "onUnmount",
    "children"
];
var defaultLoadingElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(DefaultLoadingElement, {});
function LoadScriptNext(_ref) {
    var { loadingElement, onLoad, onError, onUnmount, children } = _ref, hookOptions = _objectWithoutProperties(_ref, _excluded$1);
    var { isLoaded, loadError } = useLoadScript(hookOptions);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function handleOnLoad() {
        if (isLoaded && typeof onLoad === 'function') {
            onLoad();
        }
    }, [
        isLoaded,
        onLoad
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function handleOnError() {
        if (loadError && typeof onError === 'function') {
            onError(loadError);
        }
    }, [
        loadError,
        onError
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function handleOnUnmount() {
        return ({
            "LoadScriptNext.useEffect.handleOnUnmount": ()=>{
                if (onUnmount) {
                    onUnmount();
                }
            }
        })["LoadScriptNext.useEffect.handleOnUnmount"];
    }, [
        onUnmount
    ]);
    return isLoaded ? children : loadingElement || defaultLoadingElement;
}
var LoadScriptNext$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(LoadScriptNext);
/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */ /* global Reflect, Promise, SuppressedError, Symbol */ function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, [])).next());
    });
}
typeof SuppressedError === "function" ? SuppressedError : function(error, suppressed, message) {
    var e = new Error(message);
    return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
};
function getDefaultExportFromCjs(x) {
    return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
}
// do not edit .js files directly - edit src/index.jst
var fastDeepEqual$1 = function equal(a, b) {
    if (a === b) return true;
    if (a && b && typeof a == 'object' && typeof b == 'object') {
        if (a.constructor !== b.constructor) return false;
        var length, i, keys;
        if (Array.isArray(a)) {
            length = a.length;
            if (length != b.length) return false;
            for(i = length; i-- !== 0;)if (!equal(a[i], b[i])) return false;
            return true;
        }
        if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;
        if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();
        if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();
        keys = Object.keys(a);
        length = keys.length;
        if (length !== Object.keys(b).length) return false;
        for(i = length; i-- !== 0;)if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;
        for(i = length; i-- !== 0;){
            var key = keys[i];
            if (!equal(a[key], b[key])) return false;
        }
        return true;
    }
    // true if both NaN, false otherwise
    return a !== a && b !== b;
};
var isEqual = /*@__PURE__*/ getDefaultExportFromCjs(fastDeepEqual$1);
/**
 * Copyright 2019 Google LLC. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at.
 *
 *      Http://www.apache.org/licenses/LICENSE-2.0.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ var DEFAULT_ID = "__googleMapsScriptId";
/**
 * The status of the [[Loader]].
 */ var LoaderStatus;
(function(LoaderStatus) {
    LoaderStatus[LoaderStatus["INITIALIZED"] = 0] = "INITIALIZED";
    LoaderStatus[LoaderStatus["LOADING"] = 1] = "LOADING";
    LoaderStatus[LoaderStatus["SUCCESS"] = 2] = "SUCCESS";
    LoaderStatus[LoaderStatus["FAILURE"] = 3] = "FAILURE";
})(LoaderStatus || (LoaderStatus = {}));
/**
 * [[Loader]] makes it easier to add Google Maps JavaScript API to your application
 * dynamically using
 * [Promises](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise).
 * It works by dynamically creating and appending a script node to the the
 * document head and wrapping the callback function so as to return a promise.
 *
 * ```
 * const loader = new Loader({
 *   apiKey: "",
 *   version: "weekly",
 *   libraries: ["places"]
 * });
 *
 * loader.load().then((google) => {
 *   const map = new google.maps.Map(...)
 * })
 * ```
 */ class Loader {
    /**
   * Creates an instance of Loader using [[LoaderOptions]]. No defaults are set
   * using this library, instead the defaults are set by the Google Maps
   * JavaScript API server.
   *
   * ```
   * const loader = Loader({apiKey, version: 'weekly', libraries: ['places']});
   * ```
   */ constructor(_ref){
        var { apiKey, authReferrerPolicy, channel, client, id = DEFAULT_ID, language, libraries = [], mapIds, nonce, region, retries = 3, url = "https://maps.googleapis.com/maps/api/js", version } = _ref;
        this.callbacks = [];
        this.done = false;
        this.loading = false;
        this.errors = [];
        this.apiKey = apiKey;
        this.authReferrerPolicy = authReferrerPolicy;
        this.channel = channel;
        this.client = client;
        this.id = id || DEFAULT_ID; // Do not allow empty string
        this.language = language;
        this.libraries = libraries;
        this.mapIds = mapIds;
        this.nonce = nonce;
        this.region = region;
        this.retries = retries;
        this.url = url;
        this.version = version;
        if (Loader.instance) {
            if (!isEqual(this.options, Loader.instance.options)) {
                throw new Error("Loader must not be called again with different options. ".concat(JSON.stringify(this.options), " !== ").concat(JSON.stringify(Loader.instance.options)));
            }
            return Loader.instance;
        }
        Loader.instance = this;
    }
    get options() {
        return {
            version: this.version,
            apiKey: this.apiKey,
            channel: this.channel,
            client: this.client,
            id: this.id,
            libraries: this.libraries,
            language: this.language,
            region: this.region,
            mapIds: this.mapIds,
            nonce: this.nonce,
            url: this.url,
            authReferrerPolicy: this.authReferrerPolicy
        };
    }
    get status() {
        if (this.errors.length) {
            return LoaderStatus.FAILURE;
        }
        if (this.done) {
            return LoaderStatus.SUCCESS;
        }
        if (this.loading) {
            return LoaderStatus.LOADING;
        }
        return LoaderStatus.INITIALIZED;
    }
    get failed() {
        return this.done && !this.loading && this.errors.length >= this.retries + 1;
    }
    /**
   * CreateUrl returns the Google Maps JavaScript API script url given the [[LoaderOptions]].
   *
   * @ignore
   * @deprecated
   */ createUrl() {
        var url = this.url;
        url += "?callback=__googleMapsCallback&loading=async";
        if (this.apiKey) {
            url += "&key=".concat(this.apiKey);
        }
        if (this.channel) {
            url += "&channel=".concat(this.channel);
        }
        if (this.client) {
            url += "&client=".concat(this.client);
        }
        if (this.libraries.length > 0) {
            url += "&libraries=".concat(this.libraries.join(","));
        }
        if (this.language) {
            url += "&language=".concat(this.language);
        }
        if (this.region) {
            url += "&region=".concat(this.region);
        }
        if (this.version) {
            url += "&v=".concat(this.version);
        }
        if (this.mapIds) {
            url += "&map_ids=".concat(this.mapIds.join(","));
        }
        if (this.authReferrerPolicy) {
            url += "&auth_referrer_policy=".concat(this.authReferrerPolicy);
        }
        return url;
    }
    deleteScript() {
        var script = document.getElementById(this.id);
        if (script) {
            script.remove();
        }
    }
    /**
   * Load the Google Maps JavaScript API script and return a Promise.
   * @deprecated, use importLibrary() instead.
   */ load() {
        return this.loadPromise();
    }
    /**
   * Load the Google Maps JavaScript API script and return a Promise.
   *
   * @ignore
   * @deprecated, use importLibrary() instead.
   */ loadPromise() {
        return new Promise((resolve, reject)=>{
            this.loadCallback((err)=>{
                if (!err) {
                    resolve(window.google);
                } else {
                    reject(err.error);
                }
            });
        });
    }
    importLibrary(name) {
        this.execute();
        return google.maps.importLibrary(name);
    }
    /**
   * Load the Google Maps JavaScript API script with a callback.
   * @deprecated, use importLibrary() instead.
   */ loadCallback(fn) {
        this.callbacks.push(fn);
        this.execute();
    }
    /**
   * Set the script on document.
   */ setScript() {
        var _a, _b;
        if (document.getElementById(this.id)) {
            // TODO wrap onerror callback for cases where the script was loaded elsewhere
            this.callback();
            return;
        }
        var params = {
            key: this.apiKey,
            channel: this.channel,
            client: this.client,
            libraries: this.libraries.length && this.libraries,
            v: this.version,
            mapIds: this.mapIds,
            language: this.language,
            region: this.region,
            authReferrerPolicy: this.authReferrerPolicy
        };
        // keep the URL minimal:
        Object.keys(params).forEach(// eslint-disable-next-line @typescript-eslint/no-explicit-any
        (key)=>!params[key] && delete params[key]);
        if (!((_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.maps) === null || _b === void 0 ? void 0 : _b.importLibrary)) {
            // tweaked copy of https://developers.google.com/maps/documentation/javascript/load-maps-js-api#dynamic-library-import
            // which also sets the base url, the id, and the nonce
            /* eslint-disable */ ((g)=>{
                // @ts-ignore
                var h, a, k, p = "The Google Maps JavaScript API", c = "google", l = "importLibrary", q = "__ib__", m = document, b = window;
                // @ts-ignore
                b = b[c] || (b[c] = {});
                // @ts-ignore
                var d = b.maps || (b.maps = {}), r = new Set(), e = new URLSearchParams(), u = ()=>// @ts-ignore
                    h || (h = new Promise((f, n)=>__awaiter(this, void 0, void 0, function*() {
                            var _a;
                            yield a = m.createElement("script");
                            a.id = this.id;
                            e.set("libraries", [
                                ...r
                            ] + "");
                            // @ts-ignore
                            for(k in g)e.set(k.replace(/[A-Z]/g, (t)=>"_" + t[0].toLowerCase()), g[k]);
                            e.set("callback", c + ".maps." + q);
                            a.src = this.url + "?" + e;
                            d[q] = f;
                            a.onerror = ()=>h = n(Error(p + " could not load."));
                            // @ts-ignore
                            a.nonce = this.nonce || ((_a = m.querySelector("script[nonce]")) === null || _a === void 0 ? void 0 : _a.nonce) || "";
                            m.head.append(a);
                        })));
                // @ts-ignore
                d[l] ? console.warn(p + " only loads once. Ignoring:", g) : d[l] = function(f) {
                    for(var _len = arguments.length, n = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
                        n[_key - 1] = arguments[_key];
                    }
                    return r.add(f) && u().then(()=>d[l](f, ...n));
                };
            })(params);
        /* eslint-enable */ }
        // While most libraries populate the global namespace when loaded via bootstrap params,
        // this is not the case for "marker" when used with the inline bootstrap loader
        // (and maybe others in the future). So ensure there is an importLibrary for each:
        var libraryPromises = this.libraries.map((library)=>this.importLibrary(library));
        // ensure at least one library, to kick off loading...
        if (!libraryPromises.length) {
            libraryPromises.push(this.importLibrary("core"));
        }
        Promise.all(libraryPromises).then(()=>this.callback(), (error)=>{
            var event = new ErrorEvent("error", {
                error
            }); // for backwards compat
            this.loadErrorCallback(event);
        });
    }
    /**
   * Reset the loader state.
   */ reset() {
        this.deleteScript();
        this.done = false;
        this.loading = false;
        this.errors = [];
        this.onerrorEvent = null;
    }
    resetIfRetryingFailed() {
        if (this.failed) {
            this.reset();
        }
    }
    loadErrorCallback(e) {
        this.errors.push(e);
        if (this.errors.length <= this.retries) {
            var delay = this.errors.length * Math.pow(2, this.errors.length);
            console.error("Failed to load Google Maps script, retrying in ".concat(delay, " ms."));
            setTimeout(()=>{
                this.deleteScript();
                this.setScript();
            }, delay);
        } else {
            this.onerrorEvent = e;
            this.callback();
        }
    }
    callback() {
        this.done = true;
        this.loading = false;
        this.callbacks.forEach((cb)=>{
            cb(this.onerrorEvent);
        });
        this.callbacks = [];
    }
    execute() {
        this.resetIfRetryingFailed();
        if (this.loading) {
            // do nothing but wait
            return;
        }
        if (this.done) {
            this.callback();
        } else {
            // short circuit and warn if google.maps is already loaded
            if (window.google && window.google.maps && window.google.maps.version) {
                console.warn("Google Maps already loaded outside @googlemaps/js-api-loader. " + "This may result in undesirable behavior as options and script parameters may not match.");
                this.callback();
                return;
            }
            this.loading = true;
            this.setScript();
        }
    }
}
var defaultLibraries = [
    'maps'
];
function useJsApiLoader(_ref) {
    var { id = defaultLoadScriptProps.id, version = defaultLoadScriptProps.version, nonce, googleMapsApiKey, // googleMapsClientId,
    language, region, libraries = defaultLibraries, preventGoogleFontsLoading, // channel,
    mapIds, authReferrerPolicy } = _ref;
    var isMounted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    var [isLoaded, setLoaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    var [loadError, setLoadError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(undefined);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function trackMountedState() {
        isMounted.current = true;
        return ({
            "useJsApiLoader.useEffect.trackMountedState": ()=>{
                isMounted.current = false;
            }
        })["useJsApiLoader.useEffect.trackMountedState"];
    }, []);
    var loader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useJsApiLoader.useMemo[loader]": ()=>{
            return new Loader({
                id,
                apiKey: googleMapsApiKey,
                version,
                libraries,
                language: language || 'en',
                region: region || 'US',
                mapIds: mapIds || [],
                nonce: nonce || '',
                authReferrerPolicy: authReferrerPolicy || 'origin'
            });
        }
    }["useJsApiLoader.useMemo[loader]"], [
        id,
        googleMapsApiKey,
        version,
        libraries,
        language,
        region,
        mapIds,
        nonce,
        authReferrerPolicy
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function effect() {
        if (isLoaded) {
            return;
        } else {
            loader.load().then({
                "useJsApiLoader.useEffect.effect": ()=>{
                    if (isMounted.current) {
                        setLoaded(true);
                    }
                    return;
                }
            }["useJsApiLoader.useEffect.effect"]).catch({
                "useJsApiLoader.useEffect.effect": (error)=>{
                    setLoadError(error);
                }
            }["useJsApiLoader.useEffect.effect"]);
        }
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useJsApiLoader.useEffect": ()=>{
            if (isBrowser && preventGoogleFontsLoading) {
                preventGoogleFonts();
            }
        }
    }["useJsApiLoader.useEffect"], [
        preventGoogleFontsLoading
    ]);
    var prevLibraries = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useJsApiLoader.useEffect": ()=>{
            if (prevLibraries.current && libraries !== prevLibraries.current) {
                console.warn('Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables');
            }
            prevLibraries.current = libraries;
        }
    }["useJsApiLoader.useEffect"], [
        libraries
    ]);
    return {
        isLoaded,
        loadError
    };
}
function ownKeys$f(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$f(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$f(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$f(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
var eventMap$h = {};
var updaterMap$h = {
    options (instance, options) {
        instance.setOptions(options);
    }
};
function TrafficLayerFunctional(_ref) {
    var { options, onLoad, onUnmount } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TrafficLayerFunctional.useEffect": ()=>{
            if (instance !== null) {
                instance.setMap(map);
            }
        }
    }["TrafficLayerFunctional.useEffect"], [
        map
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TrafficLayerFunctional.useEffect": ()=>{
            if (options && instance !== null) {
                instance.setOptions(options);
            }
        }
    }["TrafficLayerFunctional.useEffect"], [
        instance,
        options
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TrafficLayerFunctional.useEffect": ()=>{
            var trafficLayer = new google.maps.TrafficLayer(_objectSpread$f(_objectSpread$f({}, options), {}, {
                map
            }));
            setInstance(trafficLayer);
            if (onLoad) {
                onLoad(trafficLayer);
            }
            return ({
                "TrafficLayerFunctional.useEffect": ()=>{
                    if (instance !== null) {
                        if (onUnmount) {
                            onUnmount(instance);
                        }
                        instance.setMap(null);
                    }
                }
            })["TrafficLayerFunctional.useEffect"];
        }
    }["TrafficLayerFunctional.useEffect"], []);
    return null;
}
var TrafficLayerF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(TrafficLayerFunctional);
class TrafficLayer extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "state", {
            trafficLayer: null
        });
        _defineProperty(this, "setTrafficLayerCallback", ()=>{
            if (this.state.trafficLayer !== null && this.props.onLoad) {
                this.props.onLoad(this.state.trafficLayer);
            }
        });
        _defineProperty(this, "registeredEvents", []);
    }
    componentDidMount() {
        var trafficLayer = new google.maps.TrafficLayer(_objectSpread$f(_objectSpread$f({}, this.props.options), {}, {
            map: this.context
        }));
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$h,
            eventMap: eventMap$h,
            prevProps: {},
            nextProps: this.props,
            instance: trafficLayer
        });
        this.setState(function setTrafficLayer() {
            return {
                trafficLayer
            };
        }, this.setTrafficLayerCallback);
    }
    componentDidUpdate(prevProps) {
        if (this.state.trafficLayer !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$h,
                eventMap: eventMap$h,
                prevProps,
                nextProps: this.props,
                instance: this.state.trafficLayer
            });
        }
    }
    componentWillUnmount() {
        if (this.state.trafficLayer !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.trafficLayer);
            }
            unregisterEvents(this.registeredEvents);
            this.state.trafficLayer.setMap(null);
        }
    }
    render() {
        return null;
    }
}
_defineProperty(TrafficLayer, "contextType", MapContext);
function BicyclingLayerFunctional(_ref) {
    var { onLoad, onUnmount } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BicyclingLayerFunctional.useEffect": ()=>{
            if (instance !== null) {
                instance.setMap(map);
            }
        }
    }["BicyclingLayerFunctional.useEffect"], [
        map
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BicyclingLayerFunctional.useEffect": ()=>{
            var bicyclingLayer = new google.maps.BicyclingLayer();
            setInstance(bicyclingLayer);
            bicyclingLayer.setMap(map);
            if (onLoad) {
                onLoad(bicyclingLayer);
            }
            return ({
                "BicyclingLayerFunctional.useEffect": ()=>{
                    if (bicyclingLayer !== null) {
                        if (onUnmount) {
                            onUnmount(bicyclingLayer);
                        }
                        bicyclingLayer.setMap(null);
                    }
                }
            })["BicyclingLayerFunctional.useEffect"];
        }
    }["BicyclingLayerFunctional.useEffect"], []);
    return null;
}
var BicyclingLayerF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(BicyclingLayerFunctional);
class BicyclingLayer extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "state", {
            bicyclingLayer: null
        });
        _defineProperty(this, "setBicyclingLayerCallback", ()=>{
            if (this.state.bicyclingLayer !== null) {
                this.state.bicyclingLayer.setMap(this.context);
                if (this.props.onLoad) {
                    this.props.onLoad(this.state.bicyclingLayer);
                }
            }
        });
    }
    componentDidMount() {
        var bicyclingLayer = new google.maps.BicyclingLayer();
        this.setState(()=>{
            return {
                bicyclingLayer
            };
        }, this.setBicyclingLayerCallback);
    }
    componentWillUnmount() {
        if (this.state.bicyclingLayer !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.bicyclingLayer);
            }
            this.state.bicyclingLayer.setMap(null);
        }
    }
    render() {
        return null;
    }
}
_defineProperty(BicyclingLayer, "contextType", MapContext);
function TransitLayerFunctional(_ref) {
    var { onLoad, onUnmount } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TransitLayerFunctional.useEffect": ()=>{
            if (instance !== null) {
                instance.setMap(map);
            }
        }
    }["TransitLayerFunctional.useEffect"], [
        map
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TransitLayerFunctional.useEffect": ()=>{
            var transitLayer = new google.maps.TransitLayer();
            setInstance(transitLayer);
            transitLayer.setMap(map);
            if (onLoad) {
                onLoad(transitLayer);
            }
            return ({
                "TransitLayerFunctional.useEffect": ()=>{
                    if (instance !== null) {
                        if (onUnmount) {
                            onUnmount(instance);
                        }
                        instance.setMap(null);
                    }
                }
            })["TransitLayerFunctional.useEffect"];
        }
    }["TransitLayerFunctional.useEffect"], []);
    return null;
}
var TransitLayerF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(TransitLayerFunctional);
class TransitLayer extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "state", {
            transitLayer: null
        });
        _defineProperty(this, "setTransitLayerCallback", ()=>{
            if (this.state.transitLayer !== null) {
                this.state.transitLayer.setMap(this.context);
                if (this.props.onLoad) {
                    this.props.onLoad(this.state.transitLayer);
                }
            }
        });
    }
    componentDidMount() {
        var transitLayer = new google.maps.TransitLayer();
        this.setState(function setTransitLayer() {
            return {
                transitLayer
            };
        }, this.setTransitLayerCallback);
    }
    componentWillUnmount() {
        if (this.state.transitLayer !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.transitLayer);
            }
            this.state.transitLayer.setMap(null);
        }
    }
    render() {
        return null;
    }
}
_defineProperty(TransitLayer, "contextType", MapContext);
function ownKeys$e(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$e(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$e(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$e(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
var eventMap$g = {
    onCircleComplete: 'circlecomplete',
    onMarkerComplete: 'markercomplete',
    onOverlayComplete: 'overlaycomplete',
    onPolygonComplete: 'polygoncomplete',
    onPolylineComplete: 'polylinecomplete',
    onRectangleComplete: 'rectanglecomplete'
};
var updaterMap$g = {
    drawingMode (instance, drawingMode) {
        instance.setDrawingMode(drawingMode);
    },
    options (instance, options) {
        instance.setOptions(options);
    }
};
function DrawingManagerFunctional(_ref) {
    var { options, drawingMode, onCircleComplete, onMarkerComplete, onOverlayComplete, onPolygonComplete, onPolylineComplete, onRectangleComplete, onLoad, onUnmount } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [circlecompleteListener, setCircleCompleteListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [markercompleteListener, setMarkerCompleteListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [overlaycompleteListener, setOverlayCompleteListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [polygoncompleteListener, setPolygonCompleteListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [polylinecompleteListener, setPolylineCompleteListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [rectanglecompleteListener, setRectangleCompleteListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DrawingManagerFunctional.useEffect": ()=>{
            if (instance !== null) {
                instance.setMap(map);
            }
        }
    }["DrawingManagerFunctional.useEffect"], [
        map
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DrawingManagerFunctional.useEffect": ()=>{
            if (options && instance !== null) {
                instance.setOptions(options);
            }
        }
    }["DrawingManagerFunctional.useEffect"], [
        instance,
        options
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DrawingManagerFunctional.useEffect": ()=>{
            if (instance !== null) {
                instance.setDrawingMode(drawingMode !== null && drawingMode !== void 0 ? drawingMode : null);
            }
        }
    }["DrawingManagerFunctional.useEffect"], [
        instance,
        drawingMode
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DrawingManagerFunctional.useEffect": ()=>{
            if (instance && onCircleComplete) {
                if (circlecompleteListener !== null) {
                    google.maps.event.removeListener(circlecompleteListener);
                }
                setCircleCompleteListener(google.maps.event.addListener(instance, 'circlecomplete', onCircleComplete));
            }
        }
    }["DrawingManagerFunctional.useEffect"], [
        instance,
        onCircleComplete
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DrawingManagerFunctional.useEffect": ()=>{
            if (instance && onMarkerComplete) {
                if (markercompleteListener !== null) {
                    google.maps.event.removeListener(markercompleteListener);
                }
                setMarkerCompleteListener(google.maps.event.addListener(instance, 'markercomplete', onMarkerComplete));
            }
        }
    }["DrawingManagerFunctional.useEffect"], [
        instance,
        onMarkerComplete
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DrawingManagerFunctional.useEffect": ()=>{
            if (instance && onOverlayComplete) {
                if (overlaycompleteListener !== null) {
                    google.maps.event.removeListener(overlaycompleteListener);
                }
                setOverlayCompleteListener(google.maps.event.addListener(instance, 'overlaycomplete', onOverlayComplete));
            }
        }
    }["DrawingManagerFunctional.useEffect"], [
        instance,
        onOverlayComplete
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DrawingManagerFunctional.useEffect": ()=>{
            if (instance && onPolygonComplete) {
                if (polygoncompleteListener !== null) {
                    google.maps.event.removeListener(polygoncompleteListener);
                }
                setPolygonCompleteListener(google.maps.event.addListener(instance, 'polygoncomplete', onPolygonComplete));
            }
        }
    }["DrawingManagerFunctional.useEffect"], [
        instance,
        onPolygonComplete
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DrawingManagerFunctional.useEffect": ()=>{
            if (instance && onPolylineComplete) {
                if (polylinecompleteListener !== null) {
                    google.maps.event.removeListener(polylinecompleteListener);
                }
                setPolylineCompleteListener(google.maps.event.addListener(instance, 'polylinecomplete', onPolylineComplete));
            }
        }
    }["DrawingManagerFunctional.useEffect"], [
        instance,
        onPolylineComplete
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DrawingManagerFunctional.useEffect": ()=>{
            if (instance && onRectangleComplete) {
                if (rectanglecompleteListener !== null) {
                    google.maps.event.removeListener(rectanglecompleteListener);
                }
                setRectangleCompleteListener(google.maps.event.addListener(instance, 'rectanglecomplete', onRectangleComplete));
            }
        }
    }["DrawingManagerFunctional.useEffect"], [
        instance,
        onRectangleComplete
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DrawingManagerFunctional.useEffect": ()=>{
            invariant(!!google.maps.drawing, "Did you include prop libraries={['drawing']} in the URL? %s", google.maps.drawing);
            var drawingManager = new google.maps.drawing.DrawingManager(_objectSpread$e(_objectSpread$e({}, options), {}, {
                map
            }));
            if (drawingMode) {
                drawingManager.setDrawingMode(drawingMode);
            }
            if (onCircleComplete) {
                setCircleCompleteListener(google.maps.event.addListener(drawingManager, 'circlecomplete', onCircleComplete));
            }
            if (onMarkerComplete) {
                setMarkerCompleteListener(google.maps.event.addListener(drawingManager, 'markercomplete', onMarkerComplete));
            }
            if (onOverlayComplete) {
                setOverlayCompleteListener(google.maps.event.addListener(drawingManager, 'overlaycomplete', onOverlayComplete));
            }
            if (onPolygonComplete) {
                setPolygonCompleteListener(google.maps.event.addListener(drawingManager, 'polygoncomplete', onPolygonComplete));
            }
            if (onPolylineComplete) {
                setPolylineCompleteListener(google.maps.event.addListener(drawingManager, 'polylinecomplete', onPolylineComplete));
            }
            if (onRectangleComplete) {
                setRectangleCompleteListener(google.maps.event.addListener(drawingManager, 'rectanglecomplete', onRectangleComplete));
            }
            setInstance(drawingManager);
            if (onLoad) {
                onLoad(drawingManager);
            }
            return ({
                "DrawingManagerFunctional.useEffect": ()=>{
                    if (instance !== null) {
                        if (circlecompleteListener) {
                            google.maps.event.removeListener(circlecompleteListener);
                        }
                        if (markercompleteListener) {
                            google.maps.event.removeListener(markercompleteListener);
                        }
                        if (overlaycompleteListener) {
                            google.maps.event.removeListener(overlaycompleteListener);
                        }
                        if (polygoncompleteListener) {
                            google.maps.event.removeListener(polygoncompleteListener);
                        }
                        if (polylinecompleteListener) {
                            google.maps.event.removeListener(polylinecompleteListener);
                        }
                        if (rectanglecompleteListener) {
                            google.maps.event.removeListener(rectanglecompleteListener);
                        }
                        if (onUnmount) {
                            onUnmount(instance);
                        }
                        instance.setMap(null);
                    }
                }
            })["DrawingManagerFunctional.useEffect"];
        }
    }["DrawingManagerFunctional.useEffect"], []);
    return null;
}
var DrawingManagerF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(DrawingManagerFunctional);
class DrawingManager extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(props){
        super(props);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "state", {
            drawingManager: null
        });
        _defineProperty(this, "setDrawingManagerCallback", ()=>{
            if (this.state.drawingManager !== null && this.props.onLoad) {
                this.props.onLoad(this.state.drawingManager);
            }
        });
        invariant(!!google.maps.drawing, "Did you include prop libraries={['drawing']} in the URL? %s", google.maps.drawing);
    }
    componentDidMount() {
        var drawingManager = new google.maps.drawing.DrawingManager(_objectSpread$e(_objectSpread$e({}, this.props.options), {}, {
            map: this.context
        }));
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$g,
            eventMap: eventMap$g,
            prevProps: {},
            nextProps: this.props,
            instance: drawingManager
        });
        this.setState(function setDrawingManager() {
            return {
                drawingManager
            };
        }, this.setDrawingManagerCallback);
    }
    componentDidUpdate(prevProps) {
        if (this.state.drawingManager !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$g,
                eventMap: eventMap$g,
                prevProps,
                nextProps: this.props,
                instance: this.state.drawingManager
            });
        }
    }
    componentWillUnmount() {
        if (this.state.drawingManager !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.drawingManager);
            }
            unregisterEvents(this.registeredEvents);
            this.state.drawingManager.setMap(null);
        }
    }
    render() {
        return null;
    }
}
_defineProperty(DrawingManager, "contextType", MapContext);
function ownKeys$d(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$d(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$d(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$d(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
var eventMap$f = {
    onAnimationChanged: 'animation_changed',
    onClick: 'click',
    onClickableChanged: 'clickable_changed',
    onCursorChanged: 'cursor_changed',
    onDblClick: 'dblclick',
    onDrag: 'drag',
    onDragEnd: 'dragend',
    onDraggableChanged: 'draggable_changed',
    onDragStart: 'dragstart',
    onFlatChanged: 'flat_changed',
    onIconChanged: 'icon_changed',
    onMouseDown: 'mousedown',
    onMouseOut: 'mouseout',
    onMouseOver: 'mouseover',
    onMouseUp: 'mouseup',
    onPositionChanged: 'position_changed',
    onRightClick: 'rightclick',
    onShapeChanged: 'shape_changed',
    onTitleChanged: 'title_changed',
    onVisibleChanged: 'visible_changed',
    onZindexChanged: 'zindex_changed'
};
var updaterMap$f = {
    animation (instance, animation) {
        instance.setAnimation(animation);
    },
    clickable (instance, clickable) {
        instance.setClickable(clickable);
    },
    cursor (instance, cursor) {
        instance.setCursor(cursor);
    },
    draggable (instance, draggable) {
        instance.setDraggable(draggable);
    },
    icon (instance, icon) {
        instance.setIcon(icon);
    },
    label (instance, label) {
        instance.setLabel(label);
    },
    map (instance, map) {
        instance.setMap(map);
    },
    opacity (instance, opacity) {
        instance.setOpacity(opacity);
    },
    options (instance, options) {
        instance.setOptions(options);
    },
    position (instance, position) {
        instance.setPosition(position);
    },
    shape (instance, shape) {
        instance.setShape(shape);
    },
    title (instance, title) {
        instance.setTitle(title);
    },
    visible (instance, visible) {
        instance.setVisible(visible);
    },
    zIndex (instance, zIndex) {
        instance.setZIndex(zIndex);
    }
};
var defaultOptions$5 = {};
function MarkerFunctional(_ref) {
    var { position, options, clusterer, noClustererRedraw, children, draggable, visible, animation, clickable, cursor, icon, label, opacity, shape, title, zIndex, onClick, onDblClick, onDrag, onDragEnd, onDragStart, onMouseOut, onMouseOver, onMouseUp, onMouseDown, onRightClick, onClickableChanged, onCursorChanged, onAnimationChanged, onDraggableChanged, onFlatChanged, onIconChanged, onPositionChanged, onShapeChanged, onTitleChanged, onVisibleChanged, onZindexChanged, onLoad, onUnmount } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dblclickListener, setDblclickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragendListener, setDragendListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragstartListener, setDragstartListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mousedownListener, setMousedownListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoutListener, setMouseoutListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoverListener, setMouseoverListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseupListener, setMouseupListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [rightclickListener, setRightclickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [clickListener, setClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragListener, setDragListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [clickableChangedListener, setClickableChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [cursorChangedListener, setCursorChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [animationChangedListener, setAnimationChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [draggableChangedListener, setDraggableChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [flatChangedListener, setFlatChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [iconChangedListener, setIconChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [positionChangedListener, setPositionChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [shapeChangedListener, setShapeChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [titleChangedListener, setTitleChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [visibleChangedListener, setVisibleChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [zIndexChangedListener, setZindexChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance !== null) {
                instance.setMap(map);
            }
        }
    }["MarkerFunctional.useEffect"], [
        map
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (typeof options !== 'undefined' && instance !== null) {
                instance.setOptions(options);
            }
        }
    }["MarkerFunctional.useEffect"], [
        instance,
        options
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (typeof draggable !== 'undefined' && instance !== null) {
                instance.setDraggable(draggable);
            }
        }
    }["MarkerFunctional.useEffect"], [
        instance,
        draggable
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (position && instance !== null) {
                instance.setPosition(position);
            }
        }
    }["MarkerFunctional.useEffect"], [
        instance,
        position
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (typeof visible !== 'undefined' && instance !== null) {
                instance.setVisible(visible);
            }
        }
    }["MarkerFunctional.useEffect"], [
        instance,
        visible
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            instance === null || instance === void 0 || instance.setAnimation(animation);
        }
    }["MarkerFunctional.useEffect"], [
        instance,
        animation
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && clickable !== undefined) {
                instance.setClickable(clickable);
            }
        }
    }["MarkerFunctional.useEffect"], [
        instance,
        clickable
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && cursor !== undefined) {
                instance.setCursor(cursor);
            }
        }
    }["MarkerFunctional.useEffect"], [
        instance,
        cursor
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && icon !== undefined) {
                instance.setIcon(icon);
            }
        }
    }["MarkerFunctional.useEffect"], [
        instance,
        icon
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && label !== undefined) {
                instance.setLabel(label);
            }
        }
    }["MarkerFunctional.useEffect"], [
        instance,
        label
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && opacity !== undefined) {
                instance.setOpacity(opacity);
            }
        }
    }["MarkerFunctional.useEffect"], [
        instance,
        opacity
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && shape !== undefined) {
                instance.setShape(shape);
            }
        }
    }["MarkerFunctional.useEffect"], [
        instance,
        shape
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && title !== undefined) {
                instance.setTitle(title);
            }
        }
    }["MarkerFunctional.useEffect"], [
        instance,
        title
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && zIndex !== undefined) {
                instance.setZIndex(zIndex);
            }
        }
    }["MarkerFunctional.useEffect"], [
        instance,
        zIndex
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onDblClick) {
                if (dblclickListener !== null) {
                    google.maps.event.removeListener(dblclickListener);
                }
                setDblclickListener(google.maps.event.addListener(instance, 'dblclick', onDblClick));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onDblClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onDragEnd) {
                if (dragendListener !== null) {
                    google.maps.event.removeListener(dragendListener);
                }
                setDragendListener(google.maps.event.addListener(instance, 'dragend', onDragEnd));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onDragEnd
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onDragStart) {
                if (dragstartListener !== null) {
                    google.maps.event.removeListener(dragstartListener);
                }
                setDragstartListener(google.maps.event.addListener(instance, 'dragstart', onDragStart));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onDragStart
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onMouseDown) {
                if (mousedownListener !== null) {
                    google.maps.event.removeListener(mousedownListener);
                }
                setMousedownListener(google.maps.event.addListener(instance, 'mousedown', onMouseDown));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onMouseDown
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onMouseOut) {
                if (mouseoutListener !== null) {
                    google.maps.event.removeListener(mouseoutListener);
                }
                setMouseoutListener(google.maps.event.addListener(instance, 'mouseout', onMouseOut));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onMouseOut
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onMouseOver) {
                if (mouseoverListener !== null) {
                    google.maps.event.removeListener(mouseoverListener);
                }
                setMouseoverListener(google.maps.event.addListener(instance, 'mouseover', onMouseOver));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onMouseOver
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onMouseUp) {
                if (mouseupListener !== null) {
                    google.maps.event.removeListener(mouseupListener);
                }
                setMouseupListener(google.maps.event.addListener(instance, 'mouseup', onMouseUp));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onMouseUp
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onRightClick) {
                if (rightclickListener !== null) {
                    google.maps.event.removeListener(rightclickListener);
                }
                setRightclickListener(google.maps.event.addListener(instance, 'rightclick', onRightClick));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onRightClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onClick) {
                if (clickListener !== null) {
                    google.maps.event.removeListener(clickListener);
                }
                setClickListener(google.maps.event.addListener(instance, 'click', onClick));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onDrag) {
                if (dragListener !== null) {
                    google.maps.event.removeListener(dragListener);
                }
                setDragListener(google.maps.event.addListener(instance, 'drag', onDrag));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onDrag
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onClickableChanged) {
                if (clickableChangedListener !== null) {
                    google.maps.event.removeListener(clickableChangedListener);
                }
                setClickableChangedListener(google.maps.event.addListener(instance, 'clickable_changed', onClickableChanged));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onClickableChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onCursorChanged) {
                if (cursorChangedListener !== null) {
                    google.maps.event.removeListener(cursorChangedListener);
                }
                setCursorChangedListener(google.maps.event.addListener(instance, 'cursor_changed', onCursorChanged));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onCursorChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onAnimationChanged) {
                if (animationChangedListener !== null) {
                    google.maps.event.removeListener(animationChangedListener);
                }
                setAnimationChangedListener(google.maps.event.addListener(instance, 'animation_changed', onAnimationChanged));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onAnimationChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onDraggableChanged) {
                if (draggableChangedListener !== null) {
                    google.maps.event.removeListener(draggableChangedListener);
                }
                setDraggableChangedListener(google.maps.event.addListener(instance, 'draggable_changed', onDraggableChanged));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onDraggableChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onFlatChanged) {
                if (flatChangedListener !== null) {
                    google.maps.event.removeListener(flatChangedListener);
                }
                setFlatChangedListener(google.maps.event.addListener(instance, 'flat_changed', onFlatChanged));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onFlatChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onIconChanged) {
                if (iconChangedListener !== null) {
                    google.maps.event.removeListener(iconChangedListener);
                }
                setIconChangedListener(google.maps.event.addListener(instance, 'icon_changed', onIconChanged));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onIconChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onPositionChanged) {
                if (positionChangedListener !== null) {
                    google.maps.event.removeListener(positionChangedListener);
                }
                setPositionChangedListener(google.maps.event.addListener(instance, 'position_changed', onPositionChanged));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onPositionChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onShapeChanged) {
                if (shapeChangedListener !== null) {
                    google.maps.event.removeListener(shapeChangedListener);
                }
                setShapeChangedListener(google.maps.event.addListener(instance, 'shape_changed', onShapeChanged));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onShapeChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onTitleChanged) {
                if (titleChangedListener !== null) {
                    google.maps.event.removeListener(titleChangedListener);
                }
                setTitleChangedListener(google.maps.event.addListener(instance, 'title_changed', onTitleChanged));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onTitleChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onVisibleChanged) {
                if (visibleChangedListener !== null) {
                    google.maps.event.removeListener(visibleChangedListener);
                }
                setVisibleChangedListener(google.maps.event.addListener(instance, 'visible_changed', onVisibleChanged));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onVisibleChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            if (instance && onZindexChanged) {
                if (zIndexChangedListener !== null) {
                    google.maps.event.removeListener(zIndexChangedListener);
                }
                setZindexChangedListener(google.maps.event.addListener(instance, 'zindex_changed', onZindexChanged));
            }
        }
    }["MarkerFunctional.useEffect"], [
        onZindexChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerFunctional.useEffect": ()=>{
            var markerOptions = _objectSpread$d(_objectSpread$d(_objectSpread$d({}, options || defaultOptions$5), clusterer ? defaultOptions$5 : {
                map
            }), {}, {
                position
            });
            var marker = new google.maps.Marker(markerOptions);
            if (clusterer) {
                clusterer.addMarker(marker, !!noClustererRedraw);
            } else {
                marker.setMap(map);
            }
            if (position) {
                marker.setPosition(position);
            }
            if (typeof visible !== 'undefined') {
                marker.setVisible(visible);
            }
            if (typeof draggable !== 'undefined') {
                marker.setDraggable(draggable);
            }
            if (typeof clickable !== 'undefined') {
                marker.setClickable(clickable);
            }
            if (typeof cursor === 'string') {
                marker.setCursor(cursor);
            }
            if (icon) {
                marker.setIcon(icon);
            }
            if (typeof label !== 'undefined') {
                marker.setLabel(label);
            }
            if (typeof opacity !== 'undefined') {
                marker.setOpacity(opacity);
            }
            if (shape) {
                marker.setShape(shape);
            }
            if (typeof title === 'string') {
                marker.setTitle(title);
            }
            if (typeof zIndex === 'number') {
                marker.setZIndex(zIndex);
            }
            if (onDblClick) {
                setDblclickListener(google.maps.event.addListener(marker, 'dblclick', onDblClick));
            }
            if (onDragEnd) {
                setDragendListener(google.maps.event.addListener(marker, 'dragend', onDragEnd));
            }
            if (onDragStart) {
                setDragstartListener(google.maps.event.addListener(marker, 'dragstart', onDragStart));
            }
            if (onMouseDown) {
                setMousedownListener(google.maps.event.addListener(marker, 'mousedown', onMouseDown));
            }
            if (onMouseOut) {
                setMouseoutListener(google.maps.event.addListener(marker, 'mouseout', onMouseOut));
            }
            if (onMouseOver) {
                setMouseoverListener(google.maps.event.addListener(marker, 'mouseover', onMouseOver));
            }
            if (onMouseUp) {
                setMouseupListener(google.maps.event.addListener(marker, 'mouseup', onMouseUp));
            }
            if (onRightClick) {
                setRightclickListener(google.maps.event.addListener(marker, 'rightclick', onRightClick));
            }
            if (onClick) {
                setClickListener(google.maps.event.addListener(marker, 'click', onClick));
            }
            if (onDrag) {
                setDragListener(google.maps.event.addListener(marker, 'drag', onDrag));
            }
            if (onClickableChanged) {
                setClickableChangedListener(google.maps.event.addListener(marker, 'clickable_changed', onClickableChanged));
            }
            if (onCursorChanged) {
                setCursorChangedListener(google.maps.event.addListener(marker, 'cursor_changed', onCursorChanged));
            }
            if (onAnimationChanged) {
                setAnimationChangedListener(google.maps.event.addListener(marker, 'animation_changed', onAnimationChanged));
            }
            if (onDraggableChanged) {
                setDraggableChangedListener(google.maps.event.addListener(marker, 'draggable_changed', onDraggableChanged));
            }
            if (onFlatChanged) {
                setFlatChangedListener(google.maps.event.addListener(marker, 'flat_changed', onFlatChanged));
            }
            if (onIconChanged) {
                setIconChangedListener(google.maps.event.addListener(marker, 'icon_changed', onIconChanged));
            }
            if (onPositionChanged) {
                setPositionChangedListener(google.maps.event.addListener(marker, 'position_changed', onPositionChanged));
            }
            if (onShapeChanged) {
                setShapeChangedListener(google.maps.event.addListener(marker, 'shape_changed', onShapeChanged));
            }
            if (onTitleChanged) {
                setTitleChangedListener(google.maps.event.addListener(marker, 'title_changed', onTitleChanged));
            }
            if (onVisibleChanged) {
                setVisibleChangedListener(google.maps.event.addListener(marker, 'visible_changed', onVisibleChanged));
            }
            if (onZindexChanged) {
                setZindexChangedListener(google.maps.event.addListener(marker, 'zindex_changed', onZindexChanged));
            }
            setInstance(marker);
            if (onLoad) {
                onLoad(marker);
            }
            return ({
                "MarkerFunctional.useEffect": ()=>{
                    if (dblclickListener !== null) {
                        google.maps.event.removeListener(dblclickListener);
                    }
                    if (dragendListener !== null) {
                        google.maps.event.removeListener(dragendListener);
                    }
                    if (dragstartListener !== null) {
                        google.maps.event.removeListener(dragstartListener);
                    }
                    if (mousedownListener !== null) {
                        google.maps.event.removeListener(mousedownListener);
                    }
                    if (mouseoutListener !== null) {
                        google.maps.event.removeListener(mouseoutListener);
                    }
                    if (mouseoverListener !== null) {
                        google.maps.event.removeListener(mouseoverListener);
                    }
                    if (mouseupListener !== null) {
                        google.maps.event.removeListener(mouseupListener);
                    }
                    if (rightclickListener !== null) {
                        google.maps.event.removeListener(rightclickListener);
                    }
                    if (clickListener !== null) {
                        google.maps.event.removeListener(clickListener);
                    }
                    if (clickableChangedListener !== null) {
                        google.maps.event.removeListener(clickableChangedListener);
                    }
                    if (cursorChangedListener !== null) {
                        google.maps.event.removeListener(cursorChangedListener);
                    }
                    if (animationChangedListener !== null) {
                        google.maps.event.removeListener(animationChangedListener);
                    }
                    if (draggableChangedListener !== null) {
                        google.maps.event.removeListener(draggableChangedListener);
                    }
                    if (flatChangedListener !== null) {
                        google.maps.event.removeListener(flatChangedListener);
                    }
                    if (iconChangedListener !== null) {
                        google.maps.event.removeListener(iconChangedListener);
                    }
                    if (positionChangedListener !== null) {
                        google.maps.event.removeListener(positionChangedListener);
                    }
                    if (titleChangedListener !== null) {
                        google.maps.event.removeListener(titleChangedListener);
                    }
                    if (visibleChangedListener !== null) {
                        google.maps.event.removeListener(visibleChangedListener);
                    }
                    if (zIndexChangedListener !== null) {
                        google.maps.event.removeListener(zIndexChangedListener);
                    }
                    if (onUnmount) {
                        onUnmount(marker);
                    }
                    if (clusterer) {
                        clusterer.removeMarker(marker, !!noClustererRedraw);
                    } else if (marker) {
                        marker.setMap(null);
                    }
                }
            })["MarkerFunctional.useEffect"];
        }
    }["MarkerFunctional.useEffect"], []);
    var chx = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "MarkerFunctional.useMemo[chx]": ()=>{
            return children ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].map(children, {
                "MarkerFunctional.useMemo[chx]": (child)=>{
                    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(child)) {
                        return child;
                    }
                    var elementChild = child;
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cloneElement"])(elementChild, {
                        anchor: instance
                    });
                }
            }["MarkerFunctional.useMemo[chx]"]) : null;
        }
    }["MarkerFunctional.useMemo[chx]"], [
        children,
        instance
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: chx
    }) || null;
}
var MarkerF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(MarkerFunctional);
class Marker extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
    }
    componentDidMount() {
        var _this = this;
        return _asyncToGenerator(function*() {
            var markerOptions = _objectSpread$d(_objectSpread$d(_objectSpread$d({}, _this.props.options || defaultOptions$5), _this.props.clusterer ? defaultOptions$5 : {
                map: _this.context
            }), {}, {
                position: _this.props.position
            });
            // Unfortunately we can't just do this in the contstructor, because the
            // `MapContext` might not be filled in yet.
            _this.marker = new google.maps.Marker(markerOptions);
            if (_this.props.clusterer) {
                _this.props.clusterer.addMarker(_this.marker, !!_this.props.noClustererRedraw);
            } else {
                _this.marker.setMap(_this.context);
            }
            _this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$f,
                eventMap: eventMap$f,
                prevProps: {},
                nextProps: _this.props,
                instance: _this.marker
            });
            if (_this.props.onLoad) {
                _this.props.onLoad(_this.marker);
            }
        })();
    }
    componentDidUpdate(prevProps) {
        if (this.marker) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$f,
                eventMap: eventMap$f,
                prevProps,
                nextProps: this.props,
                instance: this.marker
            });
        }
    }
    componentWillUnmount() {
        if (!this.marker) {
            return;
        }
        if (this.props.onUnmount) {
            this.props.onUnmount(this.marker);
        }
        unregisterEvents(this.registeredEvents);
        if (this.props.clusterer) {
            this.props.clusterer.removeMarker(this.marker, !!this.props.noClustererRedraw);
        } else if (this.marker) {
            this.marker.setMap(null);
        }
    }
    render() {
        var children = this.props.children ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].map(this.props.children, (child)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(child)) {
                return child;
            }
            var elementChild = child;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cloneElement"])(elementChild, {
                anchor: this.marker
            });
        }) : null;
        return children || null;
    }
}
_defineProperty(Marker, "contextType", MapContext);
var ClusterIcon = /** @class */ function() {
    function ClusterIcon(cluster, styles) {
        cluster.getClusterer().extend(ClusterIcon, google.maps.OverlayView);
        this.cluster = cluster;
        this.clusterClassName = this.cluster.getClusterer().getClusterClass();
        this.className = this.clusterClassName;
        this.styles = styles;
        this.center = undefined;
        this.div = null;
        this.sums = null;
        this.visible = false;
        this.boundsChangedListener = null;
        this.url = '';
        this.height = 0;
        this.width = 0;
        this.anchorText = [
            0,
            0
        ];
        this.anchorIcon = [
            0,
            0
        ];
        this.textColor = 'black';
        this.textSize = 11;
        this.textDecoration = 'none';
        this.fontWeight = 'bold';
        this.fontStyle = 'normal';
        this.fontFamily = 'Arial,sans-serif';
        this.backgroundPosition = '0 0';
        this.cMouseDownInCluster = null;
        this.cDraggingMapByCluster = null;
        this.timeOut = null;
        this.setMap(cluster.getMap()); // Note: this causes onAdd to be called
        this.onBoundsChanged = this.onBoundsChanged.bind(this);
        this.onMouseDown = this.onMouseDown.bind(this);
        this.onClick = this.onClick.bind(this);
        this.onMouseOver = this.onMouseOver.bind(this);
        this.onMouseOut = this.onMouseOut.bind(this);
        this.onAdd = this.onAdd.bind(this);
        this.onRemove = this.onRemove.bind(this);
        this.draw = this.draw.bind(this);
        this.hide = this.hide.bind(this);
        this.show = this.show.bind(this);
        this.useStyle = this.useStyle.bind(this);
        this.setCenter = this.setCenter.bind(this);
        this.getPosFromLatLng = this.getPosFromLatLng.bind(this);
    }
    ClusterIcon.prototype.onBoundsChanged = function() {
        this.cDraggingMapByCluster = this.cMouseDownInCluster;
    };
    ClusterIcon.prototype.onMouseDown = function() {
        this.cMouseDownInCluster = true;
        this.cDraggingMapByCluster = false;
    };
    ClusterIcon.prototype.onClick = function(event) {
        this.cMouseDownInCluster = false;
        if (!this.cDraggingMapByCluster) {
            var markerClusterer_1 = this.cluster.getClusterer();
            /**
       * This event is fired when a cluster marker is clicked.
       * @name MarkerClusterer#click
       * @param {Cluster} c The cluster that was clicked.
       * @event
       */ google.maps.event.trigger(markerClusterer_1, 'click', this.cluster);
            google.maps.event.trigger(markerClusterer_1, 'clusterclick', this.cluster); // deprecated name
            // The default click handler follows. Disable it by setting
            // the zoomOnClick property to false.
            if (markerClusterer_1.getZoomOnClick()) {
                // Zoom into the cluster.
                var maxZoom_1 = markerClusterer_1.getMaxZoom();
                var bounds_1 = this.cluster.getBounds();
                var map = markerClusterer_1.getMap();
                if (map !== null && 'fitBounds' in map) {
                    map.fitBounds(bounds_1);
                }
                // There is a fix for Issue 170 here:
                this.timeOut = window.setTimeout(function() {
                    var map = markerClusterer_1.getMap();
                    if (map !== null) {
                        if ('fitBounds' in map) {
                            map.fitBounds(bounds_1);
                        }
                        var zoom = map.getZoom() || 0;
                        // Don't zoom beyond the max zoom level
                        if (maxZoom_1 !== null && zoom > maxZoom_1) {
                            map.setZoom(maxZoom_1 + 1);
                        }
                    }
                }, 100);
            }
            // Prevent event propagation to the map:
            event.cancelBubble = true;
            if (event.stopPropagation) {
                event.stopPropagation();
            }
        }
    };
    ClusterIcon.prototype.onMouseOver = function() {
        /**
     * This event is fired when the mouse moves over a cluster marker.
     * @name MarkerClusterer#mouseover
     * @param {Cluster} c The cluster that the mouse moved over.
     * @event
     */ google.maps.event.trigger(this.cluster.getClusterer(), 'mouseover', this.cluster);
    };
    ClusterIcon.prototype.onMouseOut = function() {
        /**
     * This event is fired when the mouse moves out of a cluster marker.
     * @name MarkerClusterer#mouseout
     * @param {Cluster} c The cluster that the mouse moved out of.
     * @event
     */ google.maps.event.trigger(this.cluster.getClusterer(), 'mouseout', this.cluster);
    };
    ClusterIcon.prototype.onAdd = function() {
        var _a;
        this.div = document.createElement('div');
        this.div.className = this.className;
        if (this.visible) {
            this.show();
        }
        (_a = this.getPanes()) === null || _a === void 0 ? void 0 : _a.overlayMouseTarget.appendChild(this.div);
        var map = this.getMap();
        if (map !== null) {
            // Fix for Issue 157
            this.boundsChangedListener = google.maps.event.addListener(map, 'bounds_changed', this.onBoundsChanged);
            this.div.addEventListener('mousedown', this.onMouseDown);
            this.div.addEventListener('click', this.onClick);
            this.div.addEventListener('mouseover', this.onMouseOver);
            this.div.addEventListener('mouseout', this.onMouseOut);
        }
    };
    ClusterIcon.prototype.onRemove = function() {
        if (this.div && this.div.parentNode) {
            this.hide();
            if (this.boundsChangedListener !== null) {
                google.maps.event.removeListener(this.boundsChangedListener);
            }
            this.div.removeEventListener('mousedown', this.onMouseDown);
            this.div.removeEventListener('click', this.onClick);
            this.div.removeEventListener('mouseover', this.onMouseOver);
            this.div.removeEventListener('mouseout', this.onMouseOut);
            this.div.parentNode.removeChild(this.div);
            if (this.timeOut !== null) {
                window.clearTimeout(this.timeOut);
                this.timeOut = null;
            }
            this.div = null;
        }
    };
    ClusterIcon.prototype.draw = function() {
        if (this.visible && this.div !== null && this.center) {
            var pos = this.getPosFromLatLng(this.center);
            this.div.style.top = pos !== null ? "".concat(pos.y, "px") : '0';
            this.div.style.left = pos !== null ? "".concat(pos.x, "px") : '0';
        }
    };
    ClusterIcon.prototype.hide = function() {
        if (this.div) {
            this.div.style.display = 'none';
        }
        this.visible = false;
    };
    ClusterIcon.prototype.show = function() {
        var _a, _b, _c, _d, _e, _f;
        if (this.div && this.center) {
            var divTitle = this.sums === null || typeof this.sums.title === 'undefined' || this.sums.title === '' ? this.cluster.getClusterer().getTitle() : this.sums.title;
            // NOTE: values must be specified in px units
            var bp = this.backgroundPosition.split(' ');
            var spriteH = parseInt(((_a = bp[0]) === null || _a === void 0 ? void 0 : _a.replace(/^\s+|\s+$/g, '')) || '0', 10);
            var spriteV = parseInt(((_b = bp[1]) === null || _b === void 0 ? void 0 : _b.replace(/^\s+|\s+$/g, '')) || '0', 10);
            var pos = this.getPosFromLatLng(this.center);
            this.div.className = this.className;
            this.div.setAttribute('style', "cursor: pointer; position: absolute; top: ".concat(pos !== null ? "".concat(pos.y, "px") : '0', "; left: ").concat(pos !== null ? "".concat(pos.x, "px") : '0', "; width: ").concat(this.width, "px; height: ").concat(this.height, "px; "));
            var img = document.createElement('img');
            img.alt = divTitle;
            img.src = this.url;
            img.width = this.width;
            img.height = this.height;
            img.setAttribute('style', "position: absolute; top: ".concat(spriteV, "px; left: ").concat(spriteH, "px"));
            if (!this.cluster.getClusterer().enableRetinaIcons) {
                img.style.clip = "rect(-".concat(spriteV, "px, -").concat(spriteH + this.width, "px, -").concat(spriteV + this.height, ", -").concat(spriteH, ")");
            }
            var textElm = document.createElement('div');
            textElm.setAttribute('style', "position: absolute; top: ".concat(this.anchorText[0], "px; left: ").concat(this.anchorText[1], "px; color: ").concat(this.textColor, "; font-size: ").concat(this.textSize, "px; font-family: ").concat(this.fontFamily, "; font-weight: ").concat(this.fontWeight, "; fontStyle: ").concat(this.fontStyle, "; text-decoration: ").concat(this.textDecoration, "; text-align: center; width: ").concat(this.width, "px; line-height: ").concat(this.height, "px"));
            if ((_c = this.sums) === null || _c === void 0 ? void 0 : _c.text) textElm.innerText = "".concat((_d = this.sums) === null || _d === void 0 ? void 0 : _d.text);
            if ((_e = this.sums) === null || _e === void 0 ? void 0 : _e.html) textElm.innerHTML = "".concat((_f = this.sums) === null || _f === void 0 ? void 0 : _f.html);
            this.div.innerHTML = '';
            this.div.appendChild(img);
            this.div.appendChild(textElm);
            this.div.title = divTitle;
            this.div.style.display = '';
        }
        this.visible = true;
    };
    ClusterIcon.prototype.useStyle = function(sums) {
        this.sums = sums;
        var styles = this.cluster.getClusterer().getStyles();
        var style = styles[Math.min(styles.length - 1, Math.max(0, sums.index - 1))];
        if (style) {
            this.url = style.url;
            this.height = style.height;
            this.width = style.width;
            if (style.className) {
                this.className = "".concat(this.clusterClassName, " ").concat(style.className);
            }
            this.anchorText = style.anchorText || [
                0,
                0
            ];
            this.anchorIcon = style.anchorIcon || [
                this.height / 2,
                this.width / 2
            ];
            this.textColor = style.textColor || 'black';
            this.textSize = style.textSize || 11;
            this.textDecoration = style.textDecoration || 'none';
            this.fontWeight = style.fontWeight || 'bold';
            this.fontStyle = style.fontStyle || 'normal';
            this.fontFamily = style.fontFamily || 'Arial,sans-serif';
            this.backgroundPosition = style.backgroundPosition || '0 0';
        }
    };
    ClusterIcon.prototype.setCenter = function(center) {
        this.center = center;
    };
    ClusterIcon.prototype.getPosFromLatLng = function(latlng) {
        var pos = this.getProjection().fromLatLngToDivPixel(latlng);
        if (pos !== null) {
            pos.x -= this.anchorIcon[1];
            pos.y -= this.anchorIcon[0];
        }
        return pos;
    };
    return ClusterIcon;
}();
/* global google */ var Cluster$1 = /** @class */ function() {
    function Cluster(markerClusterer) {
        this.markerClusterer = markerClusterer;
        this.map = this.markerClusterer.getMap();
        this.gridSize = this.markerClusterer.getGridSize();
        this.minClusterSize = this.markerClusterer.getMinimumClusterSize();
        this.averageCenter = this.markerClusterer.getAverageCenter();
        this.markers = [];
        this.center = undefined;
        this.bounds = null;
        this.clusterIcon = new ClusterIcon(this, this.markerClusterer.getStyles());
        this.getSize = this.getSize.bind(this);
        this.getMarkers = this.getMarkers.bind(this);
        this.getCenter = this.getCenter.bind(this);
        this.getMap = this.getMap.bind(this);
        this.getClusterer = this.getClusterer.bind(this);
        this.getBounds = this.getBounds.bind(this);
        this.remove = this.remove.bind(this);
        this.addMarker = this.addMarker.bind(this);
        this.isMarkerInClusterBounds = this.isMarkerInClusterBounds.bind(this);
        this.calculateBounds = this.calculateBounds.bind(this);
        this.updateIcon = this.updateIcon.bind(this);
        this.isMarkerAlreadyAdded = this.isMarkerAlreadyAdded.bind(this);
    }
    Cluster.prototype.getSize = function() {
        return this.markers.length;
    };
    Cluster.prototype.getMarkers = function() {
        return this.markers;
    };
    Cluster.prototype.getCenter = function() {
        return this.center;
    };
    Cluster.prototype.getMap = function() {
        return this.map;
    };
    Cluster.prototype.getClusterer = function() {
        return this.markerClusterer;
    };
    Cluster.prototype.getBounds = function() {
        var bounds = new google.maps.LatLngBounds(this.center, this.center);
        var markers = this.getMarkers();
        for(var _i = 0, markers_1 = markers; _i < markers_1.length; _i++){
            var marker = markers_1[_i];
            var position = marker.getPosition();
            if (position) {
                bounds.extend(position);
            }
        }
        return bounds;
    };
    Cluster.prototype.remove = function() {
        this.clusterIcon.setMap(null);
        this.markers = [];
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        delete this.markers;
    };
    Cluster.prototype.addMarker = function(marker) {
        var _a;
        if (this.isMarkerAlreadyAdded(marker)) {
            return false;
        }
        if (!this.center) {
            var position = marker.getPosition();
            if (position) {
                this.center = position;
                this.calculateBounds();
            }
        } else {
            if (this.averageCenter) {
                var position = marker.getPosition();
                if (position) {
                    var length_1 = this.markers.length + 1;
                    this.center = new google.maps.LatLng((this.center.lat() * (length_1 - 1) + position.lat()) / length_1, (this.center.lng() * (length_1 - 1) + position.lng()) / length_1);
                    this.calculateBounds();
                }
            }
        }
        marker.isAdded = true;
        this.markers.push(marker);
        var mCount = this.markers.length;
        var maxZoom = this.markerClusterer.getMaxZoom();
        var zoom = (_a = this.map) === null || _a === void 0 ? void 0 : _a.getZoom();
        if (maxZoom !== null && typeof zoom !== 'undefined' && zoom > maxZoom) {
            // Zoomed in past max zoom, so show the marker.
            if (marker.getMap() !== this.map) {
                marker.setMap(this.map);
            }
        } else if (mCount < this.minClusterSize) {
            // Min cluster size not reached so show the marker.
            if (marker.getMap() !== this.map) {
                marker.setMap(this.map);
            }
        } else if (mCount === this.minClusterSize) {
            // Hide the markers that were showing.
            for(var _i = 0, _b = this.markers; _i < _b.length; _i++){
                var markerElement = _b[_i];
                markerElement.setMap(null);
            }
        } else {
            marker.setMap(null);
        }
        return true;
    };
    Cluster.prototype.isMarkerInClusterBounds = function(marker) {
        if (this.bounds !== null) {
            var position = marker.getPosition();
            if (position) {
                return this.bounds.contains(position);
            }
        }
        return false;
    };
    Cluster.prototype.calculateBounds = function() {
        this.bounds = this.markerClusterer.getExtendedBounds(new google.maps.LatLngBounds(this.center, this.center));
    };
    Cluster.prototype.updateIcon = function() {
        var _a;
        var mCount = this.markers.length;
        var maxZoom = this.markerClusterer.getMaxZoom();
        var zoom = (_a = this.map) === null || _a === void 0 ? void 0 : _a.getZoom();
        if (maxZoom !== null && typeof zoom !== 'undefined' && zoom > maxZoom) {
            this.clusterIcon.hide();
            return;
        }
        if (mCount < this.minClusterSize) {
            // Min cluster size not yet reached.
            this.clusterIcon.hide();
            return;
        }
        if (this.center) {
            this.clusterIcon.setCenter(this.center);
        }
        this.clusterIcon.useStyle(this.markerClusterer.getCalculator()(this.markers, this.markerClusterer.getStyles().length));
        this.clusterIcon.show();
    };
    Cluster.prototype.isMarkerAlreadyAdded = function(marker) {
        if (this.markers.includes) {
            return this.markers.includes(marker);
        }
        for(var i = 0; i < this.markers.length; i++){
            if (marker === this.markers[i]) {
                return true;
            }
        }
        return false;
    };
    return Cluster;
}();
/* global google */ /* eslint-disable filenames/match-regex */ /**
 * Supports up to 9007199254740991 (Number.MAX_SAFE_INTEGER) markers
 * which is not a problem as max array length is 4294967296 (2**32)
 */ function CALCULATOR(markers, numStyles) {
    var count = markers.length;
    var numberOfDigits = count.toString().length;
    var index = Math.min(numberOfDigits, numStyles);
    return {
        text: count.toString(),
        index: index,
        title: ''
    };
}
var BATCH_SIZE = 2000;
var BATCH_SIZE_IE = 500;
var IMAGE_PATH = 'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m';
var IMAGE_EXTENSION = 'png';
var IMAGE_SIZES = [
    53,
    56,
    66,
    78,
    90
];
var CLUSTERER_CLASS = 'cluster';
var Clusterer = /** @class */ function() {
    function Clusterer(map, optMarkers, optOptions) {
        if (optMarkers === void 0) {
            optMarkers = [];
        }
        if (optOptions === void 0) {
            optOptions = {};
        }
        this.getMinimumClusterSize = this.getMinimumClusterSize.bind(this);
        this.setMinimumClusterSize = this.setMinimumClusterSize.bind(this);
        this.getEnableRetinaIcons = this.getEnableRetinaIcons.bind(this);
        this.setEnableRetinaIcons = this.setEnableRetinaIcons.bind(this);
        this.addToClosestCluster = this.addToClosestCluster.bind(this);
        this.getImageExtension = this.getImageExtension.bind(this);
        this.setImageExtension = this.setImageExtension.bind(this);
        this.getExtendedBounds = this.getExtendedBounds.bind(this);
        this.getAverageCenter = this.getAverageCenter.bind(this);
        this.setAverageCenter = this.setAverageCenter.bind(this);
        this.getTotalClusters = this.getTotalClusters.bind(this);
        this.fitMapToMarkers = this.fitMapToMarkers.bind(this);
        this.getIgnoreHidden = this.getIgnoreHidden.bind(this);
        this.setIgnoreHidden = this.setIgnoreHidden.bind(this);
        this.getClusterClass = this.getClusterClass.bind(this);
        this.setClusterClass = this.setClusterClass.bind(this);
        this.getTotalMarkers = this.getTotalMarkers.bind(this);
        this.getZoomOnClick = this.getZoomOnClick.bind(this);
        this.setZoomOnClick = this.setZoomOnClick.bind(this);
        this.getBatchSizeIE = this.getBatchSizeIE.bind(this);
        this.setBatchSizeIE = this.setBatchSizeIE.bind(this);
        this.createClusters = this.createClusters.bind(this);
        this.onZoomChanged = this.onZoomChanged.bind(this);
        this.getImageSizes = this.getImageSizes.bind(this);
        this.setImageSizes = this.setImageSizes.bind(this);
        this.getCalculator = this.getCalculator.bind(this);
        this.setCalculator = this.setCalculator.bind(this);
        this.removeMarkers = this.removeMarkers.bind(this);
        this.resetViewport = this.resetViewport.bind(this);
        this.getImagePath = this.getImagePath.bind(this);
        this.setImagePath = this.setImagePath.bind(this);
        this.pushMarkerTo = this.pushMarkerTo.bind(this);
        this.removeMarker = this.removeMarker.bind(this);
        this.clearMarkers = this.clearMarkers.bind(this);
        this.setupStyles = this.setupStyles.bind(this);
        this.getGridSize = this.getGridSize.bind(this);
        this.setGridSize = this.setGridSize.bind(this);
        this.getClusters = this.getClusters.bind(this);
        this.getMaxZoom = this.getMaxZoom.bind(this);
        this.setMaxZoom = this.setMaxZoom.bind(this);
        this.getMarkers = this.getMarkers.bind(this);
        this.addMarkers = this.addMarkers.bind(this);
        this.getStyles = this.getStyles.bind(this);
        this.setStyles = this.setStyles.bind(this);
        this.addMarker = this.addMarker.bind(this);
        this.onRemove = this.onRemove.bind(this);
        this.getTitle = this.getTitle.bind(this);
        this.setTitle = this.setTitle.bind(this);
        this.repaint = this.repaint.bind(this);
        this.onIdle = this.onIdle.bind(this);
        this.redraw = this.redraw.bind(this);
        this.onAdd = this.onAdd.bind(this);
        this.draw = this.draw.bind(this);
        this.extend = this.extend.bind(this);
        this.extend(Clusterer, google.maps.OverlayView);
        this.markers = [];
        this.clusters = [];
        this.listeners = [];
        this.activeMap = null;
        this.ready = false;
        this.gridSize = optOptions.gridSize || 60;
        this.minClusterSize = optOptions.minimumClusterSize || 2;
        this.maxZoom = optOptions.maxZoom || null;
        this.styles = optOptions.styles || [];
        this.title = optOptions.title || '';
        this.zoomOnClick = true;
        if (optOptions.zoomOnClick !== undefined) {
            this.zoomOnClick = optOptions.zoomOnClick;
        }
        this.averageCenter = false;
        if (optOptions.averageCenter !== undefined) {
            this.averageCenter = optOptions.averageCenter;
        }
        this.ignoreHidden = false;
        if (optOptions.ignoreHidden !== undefined) {
            this.ignoreHidden = optOptions.ignoreHidden;
        }
        this.enableRetinaIcons = false;
        if (optOptions.enableRetinaIcons !== undefined) {
            this.enableRetinaIcons = optOptions.enableRetinaIcons;
        }
        this.imagePath = optOptions.imagePath || IMAGE_PATH;
        this.imageExtension = optOptions.imageExtension || IMAGE_EXTENSION;
        this.imageSizes = optOptions.imageSizes || IMAGE_SIZES;
        this.calculator = optOptions.calculator || CALCULATOR;
        this.batchSize = optOptions.batchSize || BATCH_SIZE;
        this.batchSizeIE = optOptions.batchSizeIE || BATCH_SIZE_IE;
        this.clusterClass = optOptions.clusterClass || CLUSTERER_CLASS;
        if (navigator.userAgent.toLowerCase().indexOf('msie') !== -1) {
            // Try to avoid IE timeout when processing a huge number of markers:
            this.batchSize = this.batchSizeIE;
        }
        this.timerRefStatic = null;
        this.setupStyles();
        this.addMarkers(optMarkers, true);
        this.setMap(map); // Note: this causes onAdd to be called
    }
    Clusterer.prototype.onZoomChanged = function() {
        var _a, _b;
        this.resetViewport(false);
        // Workaround for this Google bug: when map is at level 0 and "-" of
        // zoom slider is clicked, a "zoom_changed" event is fired even though
        // the map doesn't zoom out any further. In this situation, no "idle"
        // event is triggered so the cluster markers that have been removed
        // do not get redrawn. Same goes for a zoom in at maxZoom.
        if (((_a = this.getMap()) === null || _a === void 0 ? void 0 : _a.getZoom()) === (this.get('minZoom') || 0) || ((_b = this.getMap()) === null || _b === void 0 ? void 0 : _b.getZoom()) === this.get('maxZoom')) {
            google.maps.event.trigger(this, 'idle');
        }
    };
    Clusterer.prototype.onIdle = function() {
        this.redraw();
    };
    Clusterer.prototype.onAdd = function() {
        var map = this.getMap();
        this.activeMap = map;
        this.ready = true;
        this.repaint();
        if (map !== null) {
            // Add the map event listeners
            this.listeners = [
                google.maps.event.addListener(map, 'zoom_changed', this.onZoomChanged),
                google.maps.event.addListener(map, 'idle', this.onIdle)
            ];
        }
    };
    Clusterer.prototype.onRemove = function() {
        // Put all the managed markers back on the map:
        for(var _i = 0, _a = this.markers; _i < _a.length; _i++){
            var marker = _a[_i];
            if (marker.getMap() !== this.activeMap) {
                marker.setMap(this.activeMap);
            }
        }
        // Remove all clusters:
        for(var _b = 0, _c = this.clusters; _b < _c.length; _b++){
            var cluster = _c[_b];
            cluster.remove();
        }
        this.clusters = [];
        // Remove map event listeners:
        for(var _d = 0, _e = this.listeners; _d < _e.length; _d++){
            var listener = _e[_d];
            google.maps.event.removeListener(listener);
        }
        this.listeners = [];
        this.activeMap = null;
        this.ready = false;
    };
    Clusterer.prototype.draw = function() {
        return;
    };
    Clusterer.prototype.getMap = function() {
        return null;
    };
    Clusterer.prototype.getPanes = function() {
        return null;
    };
    Clusterer.prototype.getProjection = function() {
        return {
            fromContainerPixelToLatLng: function fromContainerPixelToLatLng() {
                return null;
            },
            fromDivPixelToLatLng: function fromDivPixelToLatLng() {
                return null;
            },
            fromLatLngToContainerPixel: function fromLatLngToContainerPixel() {
                return null;
            },
            fromLatLngToDivPixel: function fromLatLngToDivPixel() {
                return null;
            },
            getVisibleRegion: function getVisibleRegion() {
                return null;
            },
            getWorldWidth: function getWorldWidth() {
                return 0;
            }
        };
    };
    Clusterer.prototype.setMap = function() {
        return;
    };
    Clusterer.prototype.addListener = function() {
        return {
            remove: function remove() {
                return;
            }
        };
    };
    Clusterer.prototype.bindTo = function() {
        return;
    };
    Clusterer.prototype.get = function() {
        return;
    };
    Clusterer.prototype.notify = function() {
        return;
    };
    Clusterer.prototype.set = function() {
        return;
    };
    Clusterer.prototype.setValues = function() {
        return;
    };
    Clusterer.prototype.unbind = function() {
        return;
    };
    Clusterer.prototype.unbindAll = function() {
        return;
    };
    Clusterer.prototype.setupStyles = function() {
        if (this.styles.length > 0) {
            return;
        }
        for(var i = 0; i < this.imageSizes.length; i++){
            this.styles.push({
                url: "".concat(this.imagePath + (i + 1), ".").concat(this.imageExtension),
                height: this.imageSizes[i] || 0,
                width: this.imageSizes[i] || 0
            });
        }
    };
    Clusterer.prototype.fitMapToMarkers = function() {
        var markers = this.getMarkers();
        var bounds = new google.maps.LatLngBounds();
        for(var _i = 0, markers_1 = markers; _i < markers_1.length; _i++){
            var marker = markers_1[_i];
            var position = marker.getPosition();
            if (position) {
                bounds.extend(position);
            }
        }
        var map = this.getMap();
        if (map !== null && 'fitBounds' in map) {
            map.fitBounds(bounds);
        }
    };
    Clusterer.prototype.getGridSize = function() {
        return this.gridSize;
    };
    Clusterer.prototype.setGridSize = function(gridSize) {
        this.gridSize = gridSize;
    };
    Clusterer.prototype.getMinimumClusterSize = function() {
        return this.minClusterSize;
    };
    Clusterer.prototype.setMinimumClusterSize = function(minimumClusterSize) {
        this.minClusterSize = minimumClusterSize;
    };
    Clusterer.prototype.getMaxZoom = function() {
        return this.maxZoom;
    };
    Clusterer.prototype.setMaxZoom = function(maxZoom) {
        this.maxZoom = maxZoom;
    };
    Clusterer.prototype.getStyles = function() {
        return this.styles;
    };
    Clusterer.prototype.setStyles = function(styles) {
        this.styles = styles;
    };
    Clusterer.prototype.getTitle = function() {
        return this.title;
    };
    Clusterer.prototype.setTitle = function(title) {
        this.title = title;
    };
    Clusterer.prototype.getZoomOnClick = function() {
        return this.zoomOnClick;
    };
    Clusterer.prototype.setZoomOnClick = function(zoomOnClick) {
        this.zoomOnClick = zoomOnClick;
    };
    Clusterer.prototype.getAverageCenter = function() {
        return this.averageCenter;
    };
    Clusterer.prototype.setAverageCenter = function(averageCenter) {
        this.averageCenter = averageCenter;
    };
    Clusterer.prototype.getIgnoreHidden = function() {
        return this.ignoreHidden;
    };
    Clusterer.prototype.setIgnoreHidden = function(ignoreHidden) {
        this.ignoreHidden = ignoreHidden;
    };
    Clusterer.prototype.getEnableRetinaIcons = function() {
        return this.enableRetinaIcons;
    };
    Clusterer.prototype.setEnableRetinaIcons = function(enableRetinaIcons) {
        this.enableRetinaIcons = enableRetinaIcons;
    };
    Clusterer.prototype.getImageExtension = function() {
        return this.imageExtension;
    };
    Clusterer.prototype.setImageExtension = function(imageExtension) {
        this.imageExtension = imageExtension;
    };
    Clusterer.prototype.getImagePath = function() {
        return this.imagePath;
    };
    Clusterer.prototype.setImagePath = function(imagePath) {
        this.imagePath = imagePath;
    };
    Clusterer.prototype.getImageSizes = function() {
        return this.imageSizes;
    };
    Clusterer.prototype.setImageSizes = function(imageSizes) {
        this.imageSizes = imageSizes;
    };
    Clusterer.prototype.getCalculator = function() {
        return this.calculator;
    };
    Clusterer.prototype.setCalculator = function(calculator) {
        this.calculator = calculator;
    };
    Clusterer.prototype.getBatchSizeIE = function() {
        return this.batchSizeIE;
    };
    Clusterer.prototype.setBatchSizeIE = function(batchSizeIE) {
        this.batchSizeIE = batchSizeIE;
    };
    Clusterer.prototype.getClusterClass = function() {
        return this.clusterClass;
    };
    Clusterer.prototype.setClusterClass = function(clusterClass) {
        this.clusterClass = clusterClass;
    };
    Clusterer.prototype.getMarkers = function() {
        return this.markers;
    };
    Clusterer.prototype.getTotalMarkers = function() {
        return this.markers.length;
    };
    Clusterer.prototype.getClusters = function() {
        return this.clusters;
    };
    Clusterer.prototype.getTotalClusters = function() {
        return this.clusters.length;
    };
    Clusterer.prototype.addMarker = function(marker, optNoDraw) {
        this.pushMarkerTo(marker);
        if (!optNoDraw) {
            this.redraw();
        }
    };
    Clusterer.prototype.addMarkers = function(markers, optNoDraw) {
        for(var key in markers){
            if (Object.prototype.hasOwnProperty.call(markers, key)) {
                var marker = markers[key];
                if (marker) {
                    this.pushMarkerTo(marker);
                }
            }
        }
        if (!optNoDraw) {
            this.redraw();
        }
    };
    Clusterer.prototype.pushMarkerTo = function(marker) {
        var _this = this;
        // If the marker is draggable add a listener so we can update the clusters on the dragend:
        if (marker.getDraggable()) {
            google.maps.event.addListener(marker, 'dragend', function() {
                if (_this.ready) {
                    marker.isAdded = false;
                    _this.repaint();
                }
            });
        }
        marker.isAdded = false;
        this.markers.push(marker);
    };
    Clusterer.prototype.removeMarker_ = function(marker) {
        var index = -1;
        if (this.markers.indexOf) {
            index = this.markers.indexOf(marker);
        } else {
            for(var i = 0; i < this.markers.length; i++){
                if (marker === this.markers[i]) {
                    index = i;
                    break;
                }
            }
        }
        if (index === -1) {
            // Marker is not in our list of markers, so do nothing:
            return false;
        }
        marker.setMap(null);
        this.markers.splice(index, 1); // Remove the marker from the list of managed markers
        return true;
    };
    Clusterer.prototype.removeMarker = function(marker, optNoDraw) {
        var removed = this.removeMarker_(marker);
        if (!optNoDraw && removed) {
            this.repaint();
        }
        return removed;
    };
    Clusterer.prototype.removeMarkers = function(markers, optNoDraw) {
        var removed = false;
        for(var _i = 0, markers_2 = markers; _i < markers_2.length; _i++){
            var marker = markers_2[_i];
            removed = removed || this.removeMarker_(marker);
        }
        if (!optNoDraw && removed) {
            this.repaint();
        }
        return removed;
    };
    Clusterer.prototype.clearMarkers = function() {
        this.resetViewport(true);
        this.markers = [];
    };
    Clusterer.prototype.repaint = function() {
        var oldClusters = this.clusters.slice();
        this.clusters = [];
        this.resetViewport(false);
        this.redraw();
        // Remove the old clusters.
        // Do it in a timeout to prevent blinking effect.
        setTimeout(function timeout() {
            for(var _i = 0, oldClusters_1 = oldClusters; _i < oldClusters_1.length; _i++){
                var oldCluster = oldClusters_1[_i];
                oldCluster.remove();
            }
        }, 0);
    };
    Clusterer.prototype.getExtendedBounds = function(bounds) {
        var projection = this.getProjection();
        // Convert the points to pixels and the extend out by the grid size.
        var trPix = projection.fromLatLngToDivPixel(// Turn the bounds into latlng.
        new google.maps.LatLng(bounds.getNorthEast().lat(), bounds.getNorthEast().lng()));
        if (trPix !== null) {
            trPix.x += this.gridSize;
            trPix.y -= this.gridSize;
        }
        var blPix = projection.fromLatLngToDivPixel(// Turn the bounds into latlng.
        new google.maps.LatLng(bounds.getSouthWest().lat(), bounds.getSouthWest().lng()));
        if (blPix !== null) {
            blPix.x -= this.gridSize;
            blPix.y += this.gridSize;
        }
        // Extend the bounds to contain the new bounds.
        if (trPix !== null) {
            // Convert the pixel points back to LatLng nw
            var point1 = projection.fromDivPixelToLatLng(trPix);
            if (point1 !== null) {
                bounds.extend(point1);
            }
        }
        if (blPix !== null) {
            // Convert the pixel points back to LatLng sw
            var point2 = projection.fromDivPixelToLatLng(blPix);
            if (point2 !== null) {
                bounds.extend(point2);
            }
        }
        return bounds;
    };
    Clusterer.prototype.redraw = function() {
        // Redraws all the clusters.
        this.createClusters(0);
    };
    Clusterer.prototype.resetViewport = function(optHide) {
        // Remove all the clusters
        for(var _i = 0, _a = this.clusters; _i < _a.length; _i++){
            var cluster = _a[_i];
            cluster.remove();
        }
        this.clusters = [];
        // Reset the markers to not be added and to be removed from the map.
        for(var _b = 0, _c = this.markers; _b < _c.length; _b++){
            var marker = _c[_b];
            marker.isAdded = false;
            if (optHide) {
                marker.setMap(null);
            }
        }
    };
    Clusterer.prototype.distanceBetweenPoints = function(p1, p2) {
        var R = 6371; // Radius of the Earth in km
        var dLat = (p2.lat() - p1.lat()) * Math.PI / 180;
        var dLon = (p2.lng() - p1.lng()) * Math.PI / 180;
        var a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(p1.lat() * Math.PI / 180) * Math.cos(p2.lat() * Math.PI / 180) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
        return R * (2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)));
    };
    Clusterer.prototype.isMarkerInBounds = function(marker, bounds) {
        var position = marker.getPosition();
        if (position) {
            return bounds.contains(position);
        }
        return false;
    };
    Clusterer.prototype.addToClosestCluster = function(marker) {
        var cluster;
        var distance = 40000; // Some large number
        var clusterToAddTo = null;
        for(var _i = 0, _a = this.clusters; _i < _a.length; _i++){
            var clusterElement = _a[_i];
            cluster = clusterElement;
            var center = cluster.getCenter();
            var position = marker.getPosition();
            if (center && position) {
                var d = this.distanceBetweenPoints(center, position);
                if (d < distance) {
                    distance = d;
                    clusterToAddTo = cluster;
                }
            }
        }
        if (clusterToAddTo && clusterToAddTo.isMarkerInClusterBounds(marker)) {
            clusterToAddTo.addMarker(marker);
        } else {
            cluster = new Cluster$1(this);
            cluster.addMarker(marker);
            this.clusters.push(cluster);
        }
    };
    Clusterer.prototype.createClusters = function(iFirst) {
        var _this = this;
        if (!this.ready) {
            return;
        }
        // Cancel previous batch processing if we're working on the first batch:
        if (iFirst === 0) {
            /**
       * This event is fired when the <code>Clusterer</code> begins
       *  clustering markers.
       * @name Clusterer#clusteringbegin
       * @param {Clusterer} mc The Clusterer whose markers are being clustered.
       * @event
       */ google.maps.event.trigger(this, 'clusteringbegin', this);
            if (this.timerRefStatic !== null) {
                window.clearTimeout(this.timerRefStatic);
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                delete this.timerRefStatic;
            }
        }
        var map = this.getMap();
        var bounds = map !== null && 'getBounds' in map ? map.getBounds() : null;
        var zoom = (map === null || map === void 0 ? void 0 : map.getZoom()) || 0;
        // Get our current map view bounds.
        // Create a new bounds object so we don't affect the map.
        //
        // See Comments 9 & 11 on Issue 3651 relating to this workaround for a Google Maps bug:
        var mapBounds = zoom > 3 ? new google.maps.LatLngBounds(bounds === null || bounds === void 0 ? void 0 : bounds.getSouthWest(), bounds === null || bounds === void 0 ? void 0 : bounds.getNorthEast()) : new google.maps.LatLngBounds(new google.maps.LatLng(85.02070771743472, -178.48388434375), new google.maps.LatLng(-85.08136444384544, 178.00048865625));
        var extendedMapBounds = this.getExtendedBounds(mapBounds);
        var iLast = Math.min(iFirst + this.batchSize, this.markers.length);
        for(var i = iFirst; i < iLast; i++){
            var marker = this.markers[i];
            if (marker && !marker.isAdded && this.isMarkerInBounds(marker, extendedMapBounds) && (!this.ignoreHidden || this.ignoreHidden && marker.getVisible())) {
                this.addToClosestCluster(marker);
            }
        }
        if (iLast < this.markers.length) {
            this.timerRefStatic = window.setTimeout(function() {
                _this.createClusters(iLast);
            }, 0);
        } else {
            this.timerRefStatic = null;
            /**
       * This event is fired when the <code>Clusterer</code> stops
       *  clustering markers.
       * @name Clusterer#clusteringend
       * @param {Clusterer} mc The Clusterer whose markers are being clustered.
       * @event
       */ google.maps.event.trigger(this, 'clusteringend', this);
            for(var _i = 0, _a = this.clusters; _i < _a.length; _i++){
                var cluster = _a[_i];
                cluster.updateIcon();
            }
        }
    };
    Clusterer.prototype.extend = function(obj1, obj2) {
        return (function applyExtend(object) {
            for(var property in object.prototype){
                // eslint-disable-next-line @typescript-eslint/ban-types
                var prop = property;
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                this.prototype[prop] = object.prototype[prop];
            }
            return this;
        }).apply(obj1, [
            obj2
        ]);
    };
    return Clusterer;
}();
function ownKeys$c(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$c(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$c(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$c(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
var eventMap$e = {
    onClick: 'click',
    onClusteringBegin: 'clusteringbegin',
    onClusteringEnd: 'clusteringend',
    onMouseOut: 'mouseout',
    onMouseOver: 'mouseover'
};
var updaterMap$e = {
    averageCenter (instance, averageCenter) {
        instance.setAverageCenter(averageCenter);
    },
    batchSizeIE (instance, batchSizeIE) {
        instance.setBatchSizeIE(batchSizeIE);
    },
    calculator (instance, calculator) {
        instance.setCalculator(calculator);
    },
    clusterClass (instance, clusterClass) {
        instance.setClusterClass(clusterClass);
    },
    enableRetinaIcons (instance, enableRetinaIcons) {
        instance.setEnableRetinaIcons(enableRetinaIcons);
    },
    gridSize (instance, gridSize) {
        instance.setGridSize(gridSize);
    },
    ignoreHidden (instance, ignoreHidden) {
        instance.setIgnoreHidden(ignoreHidden);
    },
    imageExtension (instance, imageExtension) {
        instance.setImageExtension(imageExtension);
    },
    imagePath (instance, imagePath) {
        instance.setImagePath(imagePath);
    },
    imageSizes (instance, imageSizes) {
        instance.setImageSizes(imageSizes);
    },
    maxZoom (instance, maxZoom) {
        instance.setMaxZoom(maxZoom);
    },
    minimumClusterSize (instance, minimumClusterSize) {
        instance.setMinimumClusterSize(minimumClusterSize);
    },
    styles (instance, styles) {
        instance.setStyles(styles);
    },
    title (instance, title) {
        instance.setTitle(title);
    },
    zoomOnClick (instance, zoomOnClick) {
        instance.setZoomOnClick(zoomOnClick);
    }
};
var defaultOptions$4 = {};
function MarkerClustererFunctional(props) {
    var { children, options, averageCenter, batchSizeIE, calculator, clusterClass, enableRetinaIcons, gridSize, ignoreHidden, imageExtension, imagePath, imageSizes, maxZoom, minimumClusterSize, styles, title, zoomOnClick, onClick, onClusteringBegin, onClusteringEnd, onMouseOver, onMouseOut, onLoad, onUnmount } = props;
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [clickListener, setClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [clusteringBeginListener, setClusteringBeginListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [clusteringEndListener, setClusteringEndListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoutListener, setMouseoutListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoverListener, setMouseoverListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (instance && onMouseOut) {
                if (mouseoutListener !== null) {
                    google.maps.event.removeListener(mouseoutListener);
                }
                setMouseoutListener(google.maps.event.addListener(instance, eventMap$e.onMouseOut, onMouseOut));
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        onMouseOut
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (instance && onMouseOver) {
                if (mouseoverListener !== null) {
                    google.maps.event.removeListener(mouseoverListener);
                }
                setMouseoverListener(google.maps.event.addListener(instance, eventMap$e.onMouseOver, onMouseOver));
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        onMouseOver
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (instance && onClick) {
                if (clickListener !== null) {
                    google.maps.event.removeListener(clickListener);
                }
                setClickListener(google.maps.event.addListener(instance, eventMap$e.onClick, onClick));
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        onClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (instance && onClusteringBegin) {
                if (clusteringBeginListener !== null) {
                    google.maps.event.removeListener(clusteringBeginListener);
                }
                setClusteringBeginListener(google.maps.event.addListener(instance, eventMap$e.onClusteringBegin, onClusteringBegin));
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        onClusteringBegin
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (instance && onClusteringEnd) {
                if (clusteringEndListener !== null) {
                    google.maps.event.removeListener(clusteringEndListener);
                }
                setClusteringBeginListener(google.maps.event.addListener(instance, eventMap$e.onClusteringEnd, onClusteringEnd));
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        onClusteringEnd
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof averageCenter !== 'undefined' && instance !== null) {
                updaterMap$e.averageCenter(instance, averageCenter);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        averageCenter
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof batchSizeIE !== 'undefined' && instance !== null) {
                updaterMap$e.batchSizeIE(instance, batchSizeIE);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        batchSizeIE
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof calculator !== 'undefined' && instance !== null) {
                updaterMap$e.calculator(instance, calculator);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        calculator
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof clusterClass !== 'undefined' && instance !== null) {
                updaterMap$e.clusterClass(instance, clusterClass);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        clusterClass
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof enableRetinaIcons !== 'undefined' && instance !== null) {
                updaterMap$e.enableRetinaIcons(instance, enableRetinaIcons);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        enableRetinaIcons
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof gridSize !== 'undefined' && instance !== null) {
                updaterMap$e.gridSize(instance, gridSize);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        gridSize
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof ignoreHidden !== 'undefined' && instance !== null) {
                updaterMap$e.ignoreHidden(instance, ignoreHidden);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        ignoreHidden
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof imageExtension !== 'undefined' && instance !== null) {
                updaterMap$e.imageExtension(instance, imageExtension);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        imageExtension
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof imagePath !== 'undefined' && instance !== null) {
                updaterMap$e.imagePath(instance, imagePath);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        imagePath
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof imageSizes !== 'undefined' && instance !== null) {
                updaterMap$e.imageSizes(instance, imageSizes);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        imageSizes
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof maxZoom !== 'undefined' && instance !== null) {
                updaterMap$e.maxZoom(instance, maxZoom);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        maxZoom
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof minimumClusterSize !== 'undefined' && instance !== null) {
                updaterMap$e.minimumClusterSize(instance, minimumClusterSize);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        minimumClusterSize
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof styles !== 'undefined' && instance !== null) {
                updaterMap$e.styles(instance, styles);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        styles
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof title !== 'undefined' && instance !== null) {
                updaterMap$e.title(instance, title);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        title
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (typeof zoomOnClick !== 'undefined' && instance !== null) {
                updaterMap$e.zoomOnClick(instance, zoomOnClick);
            }
        }
    }["MarkerClustererFunctional.useEffect"], [
        instance,
        zoomOnClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MarkerClustererFunctional.useEffect": ()=>{
            if (!map) return;
            var clustererOptions = _objectSpread$c({}, options || defaultOptions$4);
            var clusterer = new Clusterer(map, [], clustererOptions);
            if (averageCenter) {
                updaterMap$e.averageCenter(clusterer, averageCenter);
            }
            if (batchSizeIE) {
                updaterMap$e.batchSizeIE(clusterer, batchSizeIE);
            }
            if (calculator) {
                updaterMap$e.calculator(clusterer, calculator);
            }
            if (clusterClass) {
                updaterMap$e.clusterClass(clusterer, clusterClass);
            }
            if (enableRetinaIcons) {
                updaterMap$e.enableRetinaIcons(clusterer, enableRetinaIcons);
            }
            if (gridSize) {
                updaterMap$e.gridSize(clusterer, gridSize);
            }
            if (ignoreHidden) {
                updaterMap$e.ignoreHidden(clusterer, ignoreHidden);
            }
            if (imageExtension) {
                updaterMap$e.imageExtension(clusterer, imageExtension);
            }
            if (imagePath) {
                updaterMap$e.imagePath(clusterer, imagePath);
            }
            if (imageSizes) {
                updaterMap$e.imageSizes(clusterer, imageSizes);
            }
            if (maxZoom) {
                updaterMap$e.maxZoom(clusterer, maxZoom);
            }
            if (minimumClusterSize) {
                updaterMap$e.minimumClusterSize(clusterer, minimumClusterSize);
            }
            if (styles) {
                updaterMap$e.styles(clusterer, styles);
            }
            if (title) {
                updaterMap$e.title(clusterer, title);
            }
            if (zoomOnClick) {
                updaterMap$e.zoomOnClick(clusterer, zoomOnClick);
            }
            if (onMouseOut) {
                setMouseoutListener(google.maps.event.addListener(clusterer, eventMap$e.onMouseOut, onMouseOut));
            }
            if (onMouseOver) {
                setMouseoverListener(google.maps.event.addListener(clusterer, eventMap$e.onMouseOver, onMouseOver));
            }
            if (onClick) {
                setClickListener(google.maps.event.addListener(clusterer, eventMap$e.onClick, onClick));
            }
            if (onClusteringBegin) {
                setClusteringBeginListener(google.maps.event.addListener(clusterer, eventMap$e.onClusteringBegin, onClusteringBegin));
            }
            if (onClusteringEnd) {
                setClusteringEndListener(google.maps.event.addListener(clusterer, eventMap$e.onClusteringEnd, onClusteringEnd));
            }
            setInstance(clusterer);
            if (onLoad) {
                onLoad(clusterer);
            }
            return ({
                "MarkerClustererFunctional.useEffect": ()=>{
                    if (mouseoutListener !== null) {
                        google.maps.event.removeListener(mouseoutListener);
                    }
                    if (mouseoverListener !== null) {
                        google.maps.event.removeListener(mouseoverListener);
                    }
                    if (clickListener !== null) {
                        google.maps.event.removeListener(clickListener);
                    }
                    if (clusteringBeginListener !== null) {
                        google.maps.event.removeListener(clusteringBeginListener);
                    }
                    if (clusteringEndListener !== null) {
                        google.maps.event.removeListener(clusteringEndListener);
                    }
                    if (onUnmount) {
                        onUnmount(clusterer);
                    }
                }
            })["MarkerClustererFunctional.useEffect"];
        }
    }["MarkerClustererFunctional.useEffect"], []);
    return instance !== null ? children(instance) || null : null;
}
var MarkerClustererF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(MarkerClustererFunctional);
class ClustererComponent extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "state", {
            markerClusterer: null
        });
        _defineProperty(this, "setClustererCallback", ()=>{
            if (this.state.markerClusterer !== null && this.props.onLoad) {
                this.props.onLoad(this.state.markerClusterer);
            }
        });
    }
    componentDidMount() {
        if (this.context) {
            var markerClusterer = new Clusterer(this.context, [], this.props.options);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$e,
                eventMap: eventMap$e,
                prevProps: {},
                nextProps: this.props,
                instance: markerClusterer
            });
            this.setState(()=>{
                return {
                    markerClusterer
                };
            }, this.setClustererCallback);
        }
    }
    componentDidUpdate(prevProps) {
        if (this.state.markerClusterer) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$e,
                eventMap: eventMap$e,
                prevProps,
                nextProps: this.props,
                instance: this.state.markerClusterer
            });
        }
    }
    componentWillUnmount() {
        if (this.state.markerClusterer !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.markerClusterer);
            }
            unregisterEvents(this.registeredEvents);
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            this.state.markerClusterer.setMap(null);
        }
    }
    render() {
        return this.state.markerClusterer !== null ? this.props.children(this.state.markerClusterer) : null;
    }
}
_defineProperty(ClustererComponent, "contextType", MapContext);
// This handler prevents an event in the InfoBox from being passed on to the map.
function cancelHandler(event) {
    event.cancelBubble = true;
    if (event.stopPropagation) {
        event.stopPropagation();
    }
}
var InfoBox = /** @class */ function() {
    function InfoBox(options) {
        if (options === void 0) {
            options = {};
        }
        this.getCloseClickHandler = this.getCloseClickHandler.bind(this);
        this.closeClickHandler = this.closeClickHandler.bind(this);
        this.createInfoBoxDiv = this.createInfoBoxDiv.bind(this);
        this.addClickHandler = this.addClickHandler.bind(this);
        this.getCloseBoxImg = this.getCloseBoxImg.bind(this);
        this.getBoxWidths = this.getBoxWidths.bind(this);
        this.setBoxStyle = this.setBoxStyle.bind(this);
        this.setPosition = this.setPosition.bind(this);
        this.getPosition = this.getPosition.bind(this);
        this.setOptions = this.setOptions.bind(this);
        this.setContent = this.setContent.bind(this);
        this.setVisible = this.setVisible.bind(this);
        this.getContent = this.getContent.bind(this);
        this.getVisible = this.getVisible.bind(this);
        this.setZIndex = this.setZIndex.bind(this);
        this.getZIndex = this.getZIndex.bind(this);
        this.onRemove = this.onRemove.bind(this);
        this.panBox = this.panBox.bind(this);
        this.extend = this.extend.bind(this);
        this.close = this.close.bind(this);
        this.draw = this.draw.bind(this);
        this.show = this.show.bind(this);
        this.hide = this.hide.bind(this);
        this.open = this.open.bind(this);
        this.extend(InfoBox, google.maps.OverlayView);
        // Standard options (in common with google.maps.InfoWindow):
        this.content = options.content || '';
        this.disableAutoPan = options.disableAutoPan || false;
        this.maxWidth = options.maxWidth || 0;
        this.pixelOffset = options.pixelOffset || new google.maps.Size(0, 0);
        this.position = options.position || new google.maps.LatLng(0, 0);
        this.zIndex = options.zIndex || null;
        // Additional options (unique to InfoBox):
        this.boxClass = options.boxClass || 'infoBox';
        this.boxStyle = options.boxStyle || {};
        this.closeBoxMargin = options.closeBoxMargin || '2px';
        this.closeBoxURL = options.closeBoxURL || 'http://www.google.com/intl/en_us/mapfiles/close.gif';
        if (options.closeBoxURL === '') {
            this.closeBoxURL = '';
        }
        this.infoBoxClearance = options.infoBoxClearance || new google.maps.Size(1, 1);
        if (typeof options.visible === 'undefined') {
            if (typeof options.isHidden === 'undefined') {
                options.visible = true;
            } else {
                options.visible = !options.isHidden;
            }
        }
        this.isHidden = !options.visible;
        this.alignBottom = options.alignBottom || false;
        this.pane = options.pane || 'floatPane';
        this.enableEventPropagation = options.enableEventPropagation || false;
        this.div = null;
        this.closeListener = null;
        this.moveListener = null;
        this.mapListener = null;
        this.contextListener = null;
        this.eventListeners = null;
        this.fixedWidthSet = null;
    }
    InfoBox.prototype.createInfoBoxDiv = function() {
        var _this = this;
        // This handler ignores the current event in the InfoBox and conditionally prevents
        // the event from being passed on to the map. It is used for the contextmenu event.
        var ignoreHandler = function ignoreHandler(event) {
            event.returnValue = false;
            if (event.preventDefault) {
                event.preventDefault();
            }
            if (!_this.enableEventPropagation) {
                cancelHandler(event);
            }
        };
        if (!this.div) {
            this.div = document.createElement('div');
            this.setBoxStyle();
            if (typeof this.content === 'string') {
                this.div.innerHTML = this.getCloseBoxImg() + this.content;
            } else {
                this.div.innerHTML = this.getCloseBoxImg();
                this.div.appendChild(this.content);
            }
            var panes = this.getPanes();
            if (panes !== null) {
                panes[this.pane].appendChild(this.div); // Add the InfoBox div to the DOM
            }
            this.addClickHandler();
            if (this.div.style.width) {
                this.fixedWidthSet = true;
            } else {
                if (this.maxWidth !== 0 && this.div.offsetWidth > this.maxWidth) {
                    this.div.style.width = this.maxWidth + 'px';
                    this.fixedWidthSet = true;
                } else {
                    // The following code is needed to overcome problems with MSIE
                    var bw = this.getBoxWidths();
                    this.div.style.width = this.div.offsetWidth - bw.left - bw.right + 'px';
                    this.fixedWidthSet = false;
                }
            }
            this.panBox(this.disableAutoPan);
            if (!this.enableEventPropagation) {
                this.eventListeners = [];
                // Cancel event propagation.
                // Note: mousemove not included (to resolve Issue 152)
                var events = [
                    'mousedown',
                    'mouseover',
                    'mouseout',
                    'mouseup',
                    'click',
                    'dblclick',
                    'touchstart',
                    'touchend',
                    'touchmove'
                ];
                for(var _i = 0, events_1 = events; _i < events_1.length; _i++){
                    var event_1 = events_1[_i];
                    this.eventListeners.push(google.maps.event.addListener(this.div, event_1, cancelHandler));
                }
                // Workaround for Google bug that causes the cursor to change to a pointer
                // when the mouse moves over a marker underneath InfoBox.
                this.eventListeners.push(google.maps.event.addListener(this.div, 'mouseover', function() {
                    if (_this.div) {
                        _this.div.style.cursor = 'default';
                    }
                }));
            }
            this.contextListener = google.maps.event.addListener(this.div, 'contextmenu', ignoreHandler);
            /**
       * This event is fired when the DIV containing the InfoBox's content is attached to the DOM.
       * @name InfoBox#domready
       * @event
       */ google.maps.event.trigger(this, 'domready');
        }
    };
    InfoBox.prototype.getCloseBoxImg = function() {
        var img = '';
        if (this.closeBoxURL !== '') {
            img = '<img alt=""';
            img += ' aria-hidden="true"';
            img += " src='" + this.closeBoxURL + "'";
            img += ' align=right'; // Do this because Opera chokes on style='float: right;'
            img += " style='";
            img += ' position: relative;'; // Required by MSIE
            img += ' cursor: pointer;';
            img += ' margin: ' + this.closeBoxMargin + ';';
            img += "'>";
        }
        return img;
    };
    InfoBox.prototype.addClickHandler = function() {
        this.closeListener = this.div && this.div.firstChild && this.closeBoxURL !== '' ? google.maps.event.addListener(this.div.firstChild, 'click', this.getCloseClickHandler()) : null;
    };
    InfoBox.prototype.closeClickHandler = function(event) {
        // 1.0.3 fix: Always prevent propagation of a close box click to the map:
        event.cancelBubble = true;
        if (event.stopPropagation) {
            event.stopPropagation();
        }
        /**
     * This event is fired when the InfoBox's close box is clicked.
     * @name InfoBox#closeclick
     * @event
     */ google.maps.event.trigger(this, 'closeclick');
        this.close();
    };
    InfoBox.prototype.getCloseClickHandler = function() {
        return this.closeClickHandler;
    };
    InfoBox.prototype.panBox = function(disablePan) {
        if (this.div && !disablePan) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            var map = this.getMap();
            // Only pan if attached to map, not panorama
            if (map instanceof google.maps.Map) {
                var xOffset = 0;
                var yOffset = 0;
                var bounds = map.getBounds();
                if (bounds && !bounds.contains(this.position)) {
                    // Marker not in visible area of map, so set center
                    // of map to the marker position first.
                    map.setCenter(this.position);
                }
                var mapDiv = map.getDiv();
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                var mapWidth = mapDiv.offsetWidth;
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                var mapHeight = mapDiv.offsetHeight;
                var iwOffsetX = this.pixelOffset.width;
                var iwOffsetY = this.pixelOffset.height;
                var iwWidth = this.div.offsetWidth;
                var iwHeight = this.div.offsetHeight;
                var padX = this.infoBoxClearance.width;
                var padY = this.infoBoxClearance.height;
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                var projection = this.getProjection();
                var pixPosition = projection.fromLatLngToContainerPixel(this.position);
                if (pixPosition !== null) {
                    if (pixPosition.x < -iwOffsetX + padX) {
                        xOffset = pixPosition.x + iwOffsetX - padX;
                    } else if (pixPosition.x + iwWidth + iwOffsetX + padX > mapWidth) {
                        xOffset = pixPosition.x + iwWidth + iwOffsetX + padX - mapWidth;
                    }
                    if (this.alignBottom) {
                        if (pixPosition.y < -iwOffsetY + padY + iwHeight) {
                            yOffset = pixPosition.y + iwOffsetY - padY - iwHeight;
                        } else if (pixPosition.y + iwOffsetY + padY > mapHeight) {
                            yOffset = pixPosition.y + iwOffsetY + padY - mapHeight;
                        }
                    } else {
                        if (pixPosition.y < -iwOffsetY + padY) {
                            yOffset = pixPosition.y + iwOffsetY - padY;
                        } else if (pixPosition.y + iwHeight + iwOffsetY + padY > mapHeight) {
                            yOffset = pixPosition.y + iwHeight + iwOffsetY + padY - mapHeight;
                        }
                    }
                }
                if (!(xOffset === 0 && yOffset === 0)) {
                    // Move the map to the shifted center.
                    map.panBy(xOffset, yOffset);
                }
            }
        }
    };
    InfoBox.prototype.setBoxStyle = function() {
        if (this.div) {
            // Apply style values from the style sheet defined in the boxClass parameter:
            this.div.className = this.boxClass;
            // Clear existing inline style values:
            this.div.style.cssText = '';
            // Apply style values defined in the boxStyle parameter:
            var boxStyle = this.boxStyle;
            for(var i in boxStyle){
                if (Object.prototype.hasOwnProperty.call(boxStyle, i)) {
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    this.div.style[i] = boxStyle[i];
                }
            }
            // Fix for iOS disappearing InfoBox problem
            // See http://stackoverflow.com/questions/9229535/google-maps-markers-disappear-at-certain-zoom-level-only-on-iphone-ipad
            this.div.style.webkitTransform = 'translateZ(0)';
            // Fix up opacity style for benefit of MSIE
            if (typeof this.div.style.opacity !== 'undefined' && this.div.style.opacity !== '') {
                // See http://www.quirksmode.org/css/opacity.html
                var opacity = parseFloat(this.div.style.opacity || '');
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                this.div.style.msFilter = '"progid:DXImageTransform.Microsoft.Alpha(Opacity=' + opacity * 100 + ')"';
                this.div.style.filter = 'alpha(opacity=' + opacity * 100 + ')';
            }
            // Apply required styles
            this.div.style.position = 'absolute';
            this.div.style.visibility = 'hidden';
            if (this.zIndex !== null) {
                this.div.style.zIndex = this.zIndex + '';
            }
            if (!this.div.style.overflow) {
                this.div.style.overflow = 'auto';
            }
        }
    };
    InfoBox.prototype.getBoxWidths = function() {
        var bw = {
            top: 0,
            bottom: 0,
            left: 0,
            right: 0
        };
        if (!this.div) {
            return bw;
        }
        if (document.defaultView) {
            var ownerDocument = this.div.ownerDocument;
            var computedStyle = ownerDocument && ownerDocument.defaultView ? ownerDocument.defaultView.getComputedStyle(this.div, '') : null;
            if (computedStyle) {
                // The computed styles are always in pixel units (good!)
                bw.top = parseInt(computedStyle.borderTopWidth || '', 10) || 0;
                bw.bottom = parseInt(computedStyle.borderBottomWidth || '', 10) || 0;
                bw.left = parseInt(computedStyle.borderLeftWidth || '', 10) || 0;
                bw.right = parseInt(computedStyle.borderRightWidth || '', 10) || 0;
            }
        } else if (// eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        document.documentElement.currentStyle // MSIE
        ) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            var currentStyle = this.div.currentStyle;
            if (currentStyle) {
                // The current styles may not be in pixel units, but assume they are (bad!)
                bw.top = parseInt(currentStyle.borderTopWidth || '', 10) || 0;
                bw.bottom = parseInt(currentStyle.borderBottomWidth || '', 10) || 0;
                bw.left = parseInt(currentStyle.borderLeftWidth || '', 10) || 0;
                bw.right = parseInt(currentStyle.borderRightWidth || '', 10) || 0;
            }
        }
        return bw;
    };
    InfoBox.prototype.onRemove = function() {
        if (this.div && this.div.parentNode) {
            this.div.parentNode.removeChild(this.div);
            this.div = null;
        }
    };
    InfoBox.prototype.draw = function() {
        this.createInfoBoxDiv();
        if (this.div) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            var projection = this.getProjection();
            var pixPosition = projection.fromLatLngToDivPixel(this.position);
            if (pixPosition !== null) {
                this.div.style.left = pixPosition.x + this.pixelOffset.width + 'px';
                if (this.alignBottom) {
                    this.div.style.bottom = -(pixPosition.y + this.pixelOffset.height) + 'px';
                } else {
                    this.div.style.top = pixPosition.y + this.pixelOffset.height + 'px';
                }
            }
            if (this.isHidden) {
                this.div.style.visibility = 'hidden';
            } else {
                this.div.style.visibility = 'visible';
            }
        }
    };
    InfoBox.prototype.setOptions = function(options) {
        if (options === void 0) {
            options = {};
        }
        if (typeof options.boxClass !== 'undefined') {
            // Must be first
            this.boxClass = options.boxClass;
            this.setBoxStyle();
        }
        if (typeof options.boxStyle !== 'undefined') {
            // Must be second
            this.boxStyle = options.boxStyle;
            this.setBoxStyle();
        }
        if (typeof options.content !== 'undefined') {
            this.setContent(options.content);
        }
        if (typeof options.disableAutoPan !== 'undefined') {
            this.disableAutoPan = options.disableAutoPan;
        }
        if (typeof options.maxWidth !== 'undefined') {
            this.maxWidth = options.maxWidth;
        }
        if (typeof options.pixelOffset !== 'undefined') {
            this.pixelOffset = options.pixelOffset;
        }
        if (typeof options.alignBottom !== 'undefined') {
            this.alignBottom = options.alignBottom;
        }
        if (typeof options.position !== 'undefined') {
            this.setPosition(options.position);
        }
        if (typeof options.zIndex !== 'undefined') {
            this.setZIndex(options.zIndex);
        }
        if (typeof options.closeBoxMargin !== 'undefined') {
            this.closeBoxMargin = options.closeBoxMargin;
        }
        if (typeof options.closeBoxURL !== 'undefined') {
            this.closeBoxURL = options.closeBoxURL;
        }
        if (typeof options.infoBoxClearance !== 'undefined') {
            this.infoBoxClearance = options.infoBoxClearance;
        }
        if (typeof options.isHidden !== 'undefined') {
            this.isHidden = options.isHidden;
        }
        if (typeof options.visible !== 'undefined') {
            this.isHidden = !options.visible;
        }
        if (typeof options.enableEventPropagation !== 'undefined') {
            this.enableEventPropagation = options.enableEventPropagation;
        }
        if (this.div) {
            this.draw();
        }
    };
    InfoBox.prototype.setContent = function(content) {
        this.content = content;
        if (this.div) {
            if (this.closeListener) {
                google.maps.event.removeListener(this.closeListener);
                this.closeListener = null;
            }
            // Odd code required to make things work with MSIE.
            if (!this.fixedWidthSet) {
                this.div.style.width = '';
            }
            if (typeof content === 'string') {
                this.div.innerHTML = this.getCloseBoxImg() + content;
            } else {
                this.div.innerHTML = this.getCloseBoxImg();
                this.div.appendChild(content);
            }
            // Perverse code required to make things work with MSIE.
            // (Ensures the close box does, in fact, float to the right.)
            if (!this.fixedWidthSet) {
                this.div.style.width = this.div.offsetWidth + 'px';
                if (typeof content === 'string') {
                    this.div.innerHTML = this.getCloseBoxImg() + content;
                } else {
                    this.div.innerHTML = this.getCloseBoxImg();
                    this.div.appendChild(content);
                }
            }
            this.addClickHandler();
        }
        /**
     * This event is fired when the content of the InfoBox changes.
     * @name InfoBox#content_changed
     * @event
     */ google.maps.event.trigger(this, 'content_changed');
    };
    InfoBox.prototype.setPosition = function(latLng) {
        this.position = latLng;
        if (this.div) {
            this.draw();
        }
        /**
     * This event is fired when the position of the InfoBox changes.
     * @name InfoBox#position_changed
     * @event
     */ google.maps.event.trigger(this, 'position_changed');
    };
    InfoBox.prototype.setVisible = function(isVisible) {
        this.isHidden = !isVisible;
        if (this.div) {
            this.div.style.visibility = this.isHidden ? 'hidden' : 'visible';
        }
    };
    InfoBox.prototype.setZIndex = function(index) {
        this.zIndex = index;
        if (this.div) {
            this.div.style.zIndex = index + '';
        }
        /**
     * This event is fired when the zIndex of the InfoBox changes.
     * @name InfoBox#zindex_changed
     * @event
     */ google.maps.event.trigger(this, 'zindex_changed');
    };
    InfoBox.prototype.getContent = function() {
        return this.content;
    };
    InfoBox.prototype.getPosition = function() {
        return this.position;
    };
    InfoBox.prototype.getZIndex = function() {
        return this.zIndex;
    };
    InfoBox.prototype.getVisible = function() {
        var map = this.getMap();
        return typeof map === 'undefined' || map === null ? false : !this.isHidden;
    };
    InfoBox.prototype.show = function() {
        this.isHidden = false;
        if (this.div) {
            this.div.style.visibility = 'visible';
        }
    };
    InfoBox.prototype.hide = function() {
        this.isHidden = true;
        if (this.div) {
            this.div.style.visibility = 'hidden';
        }
    };
    InfoBox.prototype.open = function(map, anchor) {
        var _this = this;
        if (anchor) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            this.position = anchor.getPosition();
            this.moveListener = google.maps.event.addListener(anchor, 'position_changed', function() {
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                var position = anchor.getPosition();
                _this.setPosition(position);
            });
            this.mapListener = google.maps.event.addListener(anchor, 'map_changed', function() {
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                _this.setMap(anchor.map);
            });
        }
        this.setMap(map);
        if (this.div) {
            this.panBox();
        }
    };
    InfoBox.prototype.close = function() {
        if (this.closeListener) {
            google.maps.event.removeListener(this.closeListener);
            this.closeListener = null;
        }
        if (this.eventListeners) {
            for(var _i = 0, _a = this.eventListeners; _i < _a.length; _i++){
                var eventListener = _a[_i];
                google.maps.event.removeListener(eventListener);
            }
            this.eventListeners = null;
        }
        if (this.moveListener) {
            google.maps.event.removeListener(this.moveListener);
            this.moveListener = null;
        }
        if (this.mapListener) {
            google.maps.event.removeListener(this.mapListener);
            this.mapListener = null;
        }
        if (this.contextListener) {
            google.maps.event.removeListener(this.contextListener);
            this.contextListener = null;
        }
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        this.setMap(null);
    };
    InfoBox.prototype.extend = function(obj1, obj2) {
        return (function applyExtend(object) {
            for(var property in object.prototype){
                if (!Object.prototype.hasOwnProperty.call(this, property)) {
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    this.prototype[property] = object.prototype[property];
                }
            }
            return this;
        }).apply(obj1, [
            obj2
        ]);
    };
    return InfoBox;
}();
var _excluded = [
    "position"
], _excluded2 = [
    "position"
];
function ownKeys$b(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$b(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$b(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$b(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
var eventMap$d = {
    onCloseClick: 'closeclick',
    onContentChanged: 'content_changed',
    onDomReady: 'domready',
    onPositionChanged: 'position_changed',
    onZindexChanged: 'zindex_changed'
};
var updaterMap$d = {
    options (instance, options) {
        instance.setOptions(options);
    },
    position (instance, position) {
        if (position instanceof google.maps.LatLng) {
            instance.setPosition(position);
        } else {
            instance.setPosition(new google.maps.LatLng(position.lat, position.lng));
        }
    },
    visible (instance, visible) {
        instance.setVisible(visible);
    },
    zIndex (instance, zIndex) {
        instance.setZIndex(zIndex);
    }
};
var defaultOptions$3 = {};
function InfoBoxFunctional(_ref) {
    var { children, anchor, options, position, zIndex, onCloseClick, onDomReady, onContentChanged, onPositionChanged, onZindexChanged, onLoad, onUnmount } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [closeClickListener, setCloseClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [domReadyClickListener, setDomReadyClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [contentChangedClickListener, setContentChangedClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [positionChangedClickListener, setPositionChangedClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [zIndexChangedClickListener, setZindexChangedClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var containerElementRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoBoxFunctional.useEffect": ()=>{
            if (map && instance !== null) {
                instance.close();
                if (anchor) {
                    instance.open(map, anchor);
                } else if (instance.getPosition()) {
                    instance.open(map);
                }
            }
        }
    }["InfoBoxFunctional.useEffect"], [
        map,
        instance,
        anchor
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoBoxFunctional.useEffect": ()=>{
            if (options && instance !== null) {
                instance.setOptions(options);
            }
        }
    }["InfoBoxFunctional.useEffect"], [
        instance,
        options
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoBoxFunctional.useEffect": ()=>{
            if (position && instance !== null) {
                var positionLatLng = position instanceof google.maps.LatLng ? position : // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                new google.maps.LatLng(position.lat, position.lng);
                instance.setPosition(positionLatLng);
            }
        }
    }["InfoBoxFunctional.useEffect"], [
        position
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoBoxFunctional.useEffect": ()=>{
            if (typeof zIndex === 'number' && instance !== null) {
                instance.setZIndex(zIndex);
            }
        }
    }["InfoBoxFunctional.useEffect"], [
        zIndex
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoBoxFunctional.useEffect": ()=>{
            if (instance && onCloseClick) {
                if (closeClickListener !== null) {
                    google.maps.event.removeListener(closeClickListener);
                }
                setCloseClickListener(google.maps.event.addListener(instance, 'closeclick', onCloseClick));
            }
        }
    }["InfoBoxFunctional.useEffect"], [
        onCloseClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoBoxFunctional.useEffect": ()=>{
            if (instance && onDomReady) {
                if (domReadyClickListener !== null) {
                    google.maps.event.removeListener(domReadyClickListener);
                }
                setDomReadyClickListener(google.maps.event.addListener(instance, 'domready', onDomReady));
            }
        }
    }["InfoBoxFunctional.useEffect"], [
        onDomReady
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoBoxFunctional.useEffect": ()=>{
            if (instance && onContentChanged) {
                if (contentChangedClickListener !== null) {
                    google.maps.event.removeListener(contentChangedClickListener);
                }
                setContentChangedClickListener(google.maps.event.addListener(instance, 'content_changed', onContentChanged));
            }
        }
    }["InfoBoxFunctional.useEffect"], [
        onContentChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoBoxFunctional.useEffect": ()=>{
            if (instance && onPositionChanged) {
                if (positionChangedClickListener !== null) {
                    google.maps.event.removeListener(positionChangedClickListener);
                }
                setPositionChangedClickListener(google.maps.event.addListener(instance, 'position_changed', onPositionChanged));
            }
        }
    }["InfoBoxFunctional.useEffect"], [
        onPositionChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoBoxFunctional.useEffect": ()=>{
            if (instance && onZindexChanged) {
                if (zIndexChangedClickListener !== null) {
                    google.maps.event.removeListener(zIndexChangedClickListener);
                }
                setZindexChangedClickListener(google.maps.event.addListener(instance, 'zindex_changed', onZindexChanged));
            }
        }
    }["InfoBoxFunctional.useEffect"], [
        onZindexChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoBoxFunctional.useEffect": ()=>{
            if (map) {
                var _ref2 = options || defaultOptions$3, { position: _position } = _ref2, infoBoxOptions = _objectWithoutProperties(_ref2, _excluded);
                var positionLatLng;
                if (_position && !(_position instanceof google.maps.LatLng)) {
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    positionLatLng = new google.maps.LatLng(_position.lat, _position.lng);
                }
                var infoBox = new InfoBox(_objectSpread$b(_objectSpread$b({}, infoBoxOptions), positionLatLng ? {
                    position: positionLatLng
                } : {}));
                containerElementRef.current = document.createElement('div');
                setInstance(infoBox);
                if (onCloseClick) {
                    setCloseClickListener(google.maps.event.addListener(infoBox, 'closeclick', onCloseClick));
                }
                if (onDomReady) {
                    setDomReadyClickListener(google.maps.event.addListener(infoBox, 'domready', onDomReady));
                }
                if (onContentChanged) {
                    setContentChangedClickListener(google.maps.event.addListener(infoBox, 'content_changed', onContentChanged));
                }
                if (onPositionChanged) {
                    setPositionChangedClickListener(google.maps.event.addListener(infoBox, 'position_changed', onPositionChanged));
                }
                if (onZindexChanged) {
                    setZindexChangedClickListener(google.maps.event.addListener(infoBox, 'zindex_changed', onZindexChanged));
                }
                infoBox.setContent(containerElementRef.current);
                if (anchor) {
                    infoBox.open(map, anchor);
                } else if (infoBox.getPosition()) {
                    infoBox.open(map);
                } else {
                    invariant(false, 'You must provide either an anchor or a position prop for <InfoBox>.');
                }
                if (onLoad) {
                    onLoad(infoBox);
                }
            }
            return ({
                "InfoBoxFunctional.useEffect": ()=>{
                    if (instance !== null) {
                        if (closeClickListener) {
                            google.maps.event.removeListener(closeClickListener);
                        }
                        if (contentChangedClickListener) {
                            google.maps.event.removeListener(contentChangedClickListener);
                        }
                        if (domReadyClickListener) {
                            google.maps.event.removeListener(domReadyClickListener);
                        }
                        if (positionChangedClickListener) {
                            google.maps.event.removeListener(positionChangedClickListener);
                        }
                        if (zIndexChangedClickListener) {
                            google.maps.event.removeListener(zIndexChangedClickListener);
                        }
                        if (onUnmount) {
                            onUnmount(instance);
                        }
                        instance.close();
                    }
                }
            })["InfoBoxFunctional.useEffect"];
        }
    }["InfoBoxFunctional.useEffect"], []);
    return containerElementRef.current ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPortal"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].only(children), containerElementRef.current) : null;
}
var InfoBoxF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(InfoBoxFunctional);
class InfoBoxComponent extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "containerElement", null);
        _defineProperty(this, "state", {
            infoBox: null
        });
        _defineProperty(this, "open", (infoBox, anchor)=>{
            if (anchor) {
                if (this.context !== null) {
                    infoBox.open(this.context, anchor);
                }
            } else if (infoBox.getPosition()) {
                if (this.context !== null) {
                    infoBox.open(this.context);
                }
            } else {
                invariant(false, 'You must provide either an anchor or a position prop for <InfoBox>.');
            }
        });
        _defineProperty(this, "setInfoBoxCallback", ()=>{
            if (this.state.infoBox !== null && this.containerElement !== null) {
                this.state.infoBox.setContent(this.containerElement);
                this.open(this.state.infoBox, this.props.anchor);
                if (this.props.onLoad) {
                    this.props.onLoad(this.state.infoBox);
                }
            }
        });
    }
    componentDidMount() {
        var _ref3 = this.props.options || {}, { position } = _ref3, infoBoxOptions = _objectWithoutProperties(_ref3, _excluded2);
        var positionLatLng;
        if (position && !(position instanceof google.maps.LatLng)) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            positionLatLng = new google.maps.LatLng(position.lat, position.lng);
        }
        var infoBox = new InfoBox(_objectSpread$b(_objectSpread$b({}, infoBoxOptions), positionLatLng ? {
            position: positionLatLng
        } : {}));
        this.containerElement = document.createElement('div');
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$d,
            eventMap: eventMap$d,
            prevProps: {},
            nextProps: this.props,
            instance: infoBox
        });
        this.setState({
            infoBox
        }, this.setInfoBoxCallback);
    }
    componentDidUpdate(prevProps) {
        var { infoBox } = this.state;
        if (infoBox !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$d,
                eventMap: eventMap$d,
                prevProps,
                nextProps: this.props,
                instance: infoBox
            });
        }
    }
    componentWillUnmount() {
        var { onUnmount } = this.props;
        var { infoBox } = this.state;
        if (infoBox !== null) {
            if (onUnmount) {
                onUnmount(infoBox);
            }
            unregisterEvents(this.registeredEvents);
            infoBox.close();
        }
    }
    render() {
        return this.containerElement ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPortal"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].only(this.props.children), this.containerElement) : null;
    }
}
_defineProperty(InfoBoxComponent, "contextType", MapContext);
var fastDeepEqual;
var hasRequiredFastDeepEqual;
function requireFastDeepEqual() {
    if (hasRequiredFastDeepEqual) return fastDeepEqual;
    hasRequiredFastDeepEqual = 1;
    // do not edit .js files directly - edit src/index.jst
    fastDeepEqual = function equal(a, b) {
        if (a === b) return true;
        if (a && b && typeof a == 'object' && typeof b == 'object') {
            if (a.constructor !== b.constructor) return false;
            var length, i, keys;
            if (Array.isArray(a)) {
                length = a.length;
                if (length != b.length) return false;
                for(i = length; i-- !== 0;)if (!equal(a[i], b[i])) return false;
                return true;
            }
            if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;
            if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();
            if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();
            keys = Object.keys(a);
            length = keys.length;
            if (length !== Object.keys(b).length) return false;
            for(i = length; i-- !== 0;)if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;
            for(i = length; i-- !== 0;){
                var key = keys[i];
                if (!equal(a[key], b[key])) return false;
            }
            return true;
        }
        // true if both NaN, false otherwise
        return a !== a && b !== b;
    };
    return fastDeepEqual;
}
var fastDeepEqualExports = requireFastDeepEqual();
var equal = /*@__PURE__*/ getDefaultExportFromCjs$1(fastDeepEqualExports);
var ARRAY_TYPES = [
    Int8Array,
    Uint8Array,
    Uint8ClampedArray,
    Int16Array,
    Uint16Array,
    Int32Array,
    Uint32Array,
    Float32Array,
    Float64Array
];
/** @typedef {Int8ArrayConstructor | Uint8ArrayConstructor | Uint8ClampedArrayConstructor | Int16ArrayConstructor | Uint16ArrayConstructor | Int32ArrayConstructor | Uint32ArrayConstructor | Float32ArrayConstructor | Float64ArrayConstructor} TypedArrayConstructor */ var VERSION = 1; // serialized format version
var HEADER_SIZE = 8;
class KDBush {
    /**
   * Creates an index from raw `ArrayBuffer` data.
   * @param {ArrayBuffer} data
   */ static from(data) {
        if (!(data instanceof ArrayBuffer)) {
            throw new Error('Data must be an instance of ArrayBuffer.');
        }
        var [magic, versionAndType] = new Uint8Array(data, 0, 2);
        if (magic !== 0xdb) {
            throw new Error('Data does not appear to be in a KDBush format.');
        }
        var version = versionAndType >> 4;
        if (version !== VERSION) {
            throw new Error("Got v".concat(version, " data when expected v").concat(VERSION, "."));
        }
        var ArrayType = ARRAY_TYPES[versionAndType & 0x0f];
        if (!ArrayType) {
            throw new Error('Unrecognized array type.');
        }
        var [nodeSize] = new Uint16Array(data, 2, 1);
        var [numItems] = new Uint32Array(data, 4, 1);
        return new KDBush(numItems, nodeSize, ArrayType, data);
    }
    /**
   * Creates an index that will hold a given number of items.
   * @param {number} numItems
   * @param {number} [nodeSize=64] Size of the KD-tree node (64 by default).
   * @param {TypedArrayConstructor} [ArrayType=Float64Array] The array type used for coordinates storage (`Float64Array` by default).
   * @param {ArrayBuffer} [data] (For internal use only)
   */ constructor(numItems){
        var nodeSize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 64;
        var ArrayType = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Float64Array;
        var data = arguments.length > 3 ? arguments[3] : undefined;
        if (isNaN(numItems) || numItems < 0) throw new Error("Unpexpected numItems value: ".concat(numItems, "."));
        this.numItems = +numItems;
        this.nodeSize = Math.min(Math.max(+nodeSize, 2), 65535);
        this.ArrayType = ArrayType;
        this.IndexArrayType = numItems < 65536 ? Uint16Array : Uint32Array;
        var arrayTypeIndex = ARRAY_TYPES.indexOf(this.ArrayType);
        var coordsByteSize = numItems * 2 * this.ArrayType.BYTES_PER_ELEMENT;
        var idsByteSize = numItems * this.IndexArrayType.BYTES_PER_ELEMENT;
        var padCoords = (8 - idsByteSize % 8) % 8;
        if (arrayTypeIndex < 0) {
            throw new Error("Unexpected typed array class: ".concat(ArrayType, "."));
        }
        if (data && data instanceof ArrayBuffer) {
            // reconstruct an index from a buffer
            this.data = data;
            this.ids = new this.IndexArrayType(this.data, HEADER_SIZE, numItems);
            this.coords = new this.ArrayType(this.data, HEADER_SIZE + idsByteSize + padCoords, numItems * 2);
            this._pos = numItems * 2;
            this._finished = true;
        } else {
            // initialize a new index
            this.data = new ArrayBuffer(HEADER_SIZE + coordsByteSize + idsByteSize + padCoords);
            this.ids = new this.IndexArrayType(this.data, HEADER_SIZE, numItems);
            this.coords = new this.ArrayType(this.data, HEADER_SIZE + idsByteSize + padCoords, numItems * 2);
            this._pos = 0;
            this._finished = false;
            // set header
            new Uint8Array(this.data, 0, 2).set([
                0xdb,
                (VERSION << 4) + arrayTypeIndex
            ]);
            new Uint16Array(this.data, 2, 1)[0] = nodeSize;
            new Uint32Array(this.data, 4, 1)[0] = numItems;
        }
    }
    /**
   * Add a point to the index.
   * @param {number} x
   * @param {number} y
   * @returns {number} An incremental index associated with the added item (starting from `0`).
   */ add(x, y) {
        var index = this._pos >> 1;
        this.ids[index] = index;
        this.coords[this._pos++] = x;
        this.coords[this._pos++] = y;
        return index;
    }
    /**
   * Perform indexing of the added points.
   */ finish() {
        var numAdded = this._pos >> 1;
        if (numAdded !== this.numItems) {
            throw new Error("Added ".concat(numAdded, " items when expected ").concat(this.numItems, "."));
        }
        // kd-sort both arrays for efficient search
        sort(this.ids, this.coords, this.nodeSize, 0, this.numItems - 1, 0);
        this._finished = true;
        return this;
    }
    /**
   * Search the index for items within a given bounding box.
   * @param {number} minX
   * @param {number} minY
   * @param {number} maxX
   * @param {number} maxY
   * @returns {number[]} An array of indices correponding to the found items.
   */ range(minX, minY, maxX, maxY) {
        if (!this._finished) throw new Error('Data not yet indexed - call index.finish().');
        var { ids, coords, nodeSize } = this;
        var stack = [
            0,
            ids.length - 1,
            0
        ];
        var result = [];
        // recursively search for items in range in the kd-sorted arrays
        while(stack.length){
            var axis = stack.pop() || 0;
            var right = stack.pop() || 0;
            var left = stack.pop() || 0;
            // if we reached "tree node", search linearly
            if (right - left <= nodeSize) {
                for(var i = left; i <= right; i++){
                    var _x = coords[2 * i];
                    var _y = coords[2 * i + 1];
                    if (_x >= minX && _x <= maxX && _y >= minY && _y <= maxY) result.push(ids[i]);
                }
                continue;
            }
            // otherwise find the middle index
            var m = left + right >> 1;
            // include the middle item if it's in range
            var x = coords[2 * m];
            var y = coords[2 * m + 1];
            if (x >= minX && x <= maxX && y >= minY && y <= maxY) result.push(ids[m]);
            // queue search in halves that intersect the query
            if (axis === 0 ? minX <= x : minY <= y) {
                stack.push(left);
                stack.push(m - 1);
                stack.push(1 - axis);
            }
            if (axis === 0 ? maxX >= x : maxY >= y) {
                stack.push(m + 1);
                stack.push(right);
                stack.push(1 - axis);
            }
        }
        return result;
    }
    /**
   * Search the index for items within a given radius.
   * @param {number} qx
   * @param {number} qy
   * @param {number} r Query radius.
   * @returns {number[]} An array of indices correponding to the found items.
   */ within(qx, qy, r) {
        if (!this._finished) throw new Error('Data not yet indexed - call index.finish().');
        var { ids, coords, nodeSize } = this;
        var stack = [
            0,
            ids.length - 1,
            0
        ];
        var result = [];
        var r2 = r * r;
        // recursively search for items within radius in the kd-sorted arrays
        while(stack.length){
            var axis = stack.pop() || 0;
            var right = stack.pop() || 0;
            var left = stack.pop() || 0;
            // if we reached "tree node", search linearly
            if (right - left <= nodeSize) {
                for(var i = left; i <= right; i++){
                    if (sqDist(coords[2 * i], coords[2 * i + 1], qx, qy) <= r2) result.push(ids[i]);
                }
                continue;
            }
            // otherwise find the middle index
            var m = left + right >> 1;
            // include the middle item if it's in range
            var x = coords[2 * m];
            var y = coords[2 * m + 1];
            if (sqDist(x, y, qx, qy) <= r2) result.push(ids[m]);
            // queue search in halves that intersect the query
            if (axis === 0 ? qx - r <= x : qy - r <= y) {
                stack.push(left);
                stack.push(m - 1);
                stack.push(1 - axis);
            }
            if (axis === 0 ? qx + r >= x : qy + r >= y) {
                stack.push(m + 1);
                stack.push(right);
                stack.push(1 - axis);
            }
        }
        return result;
    }
}
/**
 * @param {Uint16Array | Uint32Array} ids
 * @param {InstanceType<TypedArrayConstructor>} coords
 * @param {number} nodeSize
 * @param {number} left
 * @param {number} right
 * @param {number} axis
 */ function sort(ids, coords, nodeSize, left, right, axis) {
    if (right - left <= nodeSize) return;
    var m = left + right >> 1; // middle index
    // sort ids and coords around the middle index so that the halves lie
    // either left/right or top/bottom correspondingly (taking turns)
    select(ids, coords, m, left, right, axis);
    // recursively kd-sort first half and second half on the opposite axis
    sort(ids, coords, nodeSize, left, m - 1, 1 - axis);
    sort(ids, coords, nodeSize, m + 1, right, 1 - axis);
}
/**
 * Custom Floyd-Rivest selection algorithm: sort ids and coords so that
 * [left..k-1] items are smaller than k-th item (on either x or y axis)
 * @param {Uint16Array | Uint32Array} ids
 * @param {InstanceType<TypedArrayConstructor>} coords
 * @param {number} k
 * @param {number} left
 * @param {number} right
 * @param {number} axis
 */ function select(ids, coords, k, left, right, axis) {
    while(right > left){
        if (right - left > 600) {
            var n = right - left + 1;
            var m = k - left + 1;
            var z = Math.log(n);
            var s = 0.5 * Math.exp(2 * z / 3);
            var sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);
            var newLeft = Math.max(left, Math.floor(k - m * s / n + sd));
            var newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));
            select(ids, coords, k, newLeft, newRight, axis);
        }
        var t = coords[2 * k + axis];
        var i = left;
        var j = right;
        swapItem(ids, coords, left, k);
        if (coords[2 * right + axis] > t) swapItem(ids, coords, left, right);
        while(i < j){
            swapItem(ids, coords, i, j);
            i++;
            j--;
            while(coords[2 * i + axis] < t)i++;
            while(coords[2 * j + axis] > t)j--;
        }
        if (coords[2 * left + axis] === t) swapItem(ids, coords, left, j);
        else {
            j++;
            swapItem(ids, coords, j, right);
        }
        if (j <= k) left = j + 1;
        if (k <= j) right = j - 1;
    }
}
/**
 * @param {Uint16Array | Uint32Array} ids
 * @param {InstanceType<TypedArrayConstructor>} coords
 * @param {number} i
 * @param {number} j
 */ function swapItem(ids, coords, i, j) {
    swap(ids, i, j);
    swap(coords, 2 * i, 2 * j);
    swap(coords, 2 * i + 1, 2 * j + 1);
}
/**
 * @param {InstanceType<TypedArrayConstructor>} arr
 * @param {number} i
 * @param {number} j
 */ function swap(arr, i, j) {
    var tmp = arr[i];
    arr[i] = arr[j];
    arr[j] = tmp;
}
/**
 * @param {number} ax
 * @param {number} ay
 * @param {number} bx
 * @param {number} by
 */ function sqDist(ax, ay, bx, by) {
    var dx = ax - bx;
    var dy = ay - by;
    return dx * dx + dy * dy;
}
var defaultOptions$2 = {
    minZoom: 0,
    // min zoom to generate clusters on
    maxZoom: 16,
    // max zoom level to cluster the points on
    minPoints: 2,
    // minimum points to form a cluster
    radius: 40,
    // cluster radius in pixels
    extent: 512,
    // tile extent (radius is calculated relative to it)
    nodeSize: 64,
    // size of the KD-tree leaf node, affects performance
    log: false,
    // whether to log timing info
    // whether to generate numeric ids for input features (in vector tiles)
    generateId: false,
    // a reduce function for calculating custom cluster properties
    reduce: null,
    // (accumulated, props) => { accumulated.sum += props.sum; }
    // properties to use for individual points when running the reducer
    map: (props)=>props // props => ({sum: props.my_value})
};
var fround = Math.fround || ((tmp)=>(x)=>{
        tmp[0] = +x;
        return tmp[0];
    })(new Float32Array(1));
var OFFSET_ZOOM = 2;
var OFFSET_ID = 3;
var OFFSET_PARENT = 4;
var OFFSET_NUM = 5;
var OFFSET_PROP = 6;
class Supercluster {
    constructor(options){
        this.options = Object.assign(Object.create(defaultOptions$2), options);
        this.trees = new Array(this.options.maxZoom + 1);
        this.stride = this.options.reduce ? 7 : 6;
        this.clusterProps = [];
    }
    load(points) {
        var { log, minZoom, maxZoom } = this.options;
        if (log) console.time('total time');
        var timerId = "prepare ".concat(points.length, " points");
        if (log) console.time(timerId);
        this.points = points;
        // generate a cluster object for each point and index input points into a KD-tree
        var data = [];
        for(var i = 0; i < points.length; i++){
            var p = points[i];
            if (!p.geometry) continue;
            var [lng, lat] = p.geometry.coordinates;
            var x = fround(lngX(lng));
            var y = fround(latY(lat));
            // store internal point/cluster data in flat numeric arrays for performance
            data.push(x, y, // projected point coordinates
            Infinity, // the last zoom the point was processed at
            i, // index of the source feature in the original input array
            -1, // parent cluster id
            1 // number of points in a cluster
            );
            if (this.options.reduce) data.push(0); // noop
        }
        var tree = this.trees[maxZoom + 1] = this._createTree(data);
        if (log) console.timeEnd(timerId);
        // cluster points on max zoom, then cluster the results on previous zoom, etc.;
        // results in a cluster hierarchy across zoom levels
        for(var z = maxZoom; z >= minZoom; z--){
            var now = +Date.now();
            // create a new set of clusters for the zoom and index them with a KD-tree
            tree = this.trees[z] = this._createTree(this._cluster(tree, z));
            if (log) console.log('z%d: %d clusters in %dms', z, tree.numItems, +Date.now() - now);
        }
        if (log) console.timeEnd('total time');
        return this;
    }
    getClusters(bbox, zoom) {
        var minLng = ((bbox[0] + 180) % 360 + 360) % 360 - 180;
        var minLat = Math.max(-90, Math.min(90, bbox[1]));
        var maxLng = bbox[2] === 180 ? 180 : ((bbox[2] + 180) % 360 + 360) % 360 - 180;
        var maxLat = Math.max(-90, Math.min(90, bbox[3]));
        if (bbox[2] - bbox[0] >= 360) {
            minLng = -180;
            maxLng = 180;
        } else if (minLng > maxLng) {
            var easternHem = this.getClusters([
                minLng,
                minLat,
                180,
                maxLat
            ], zoom);
            var westernHem = this.getClusters([
                -180,
                minLat,
                maxLng,
                maxLat
            ], zoom);
            return easternHem.concat(westernHem);
        }
        var tree = this.trees[this._limitZoom(zoom)];
        var ids = tree.range(lngX(minLng), latY(maxLat), lngX(maxLng), latY(minLat));
        var data = tree.data;
        var clusters = [];
        for (var id of ids){
            var k = this.stride * id;
            clusters.push(data[k + OFFSET_NUM] > 1 ? getClusterJSON(data, k, this.clusterProps) : this.points[data[k + OFFSET_ID]]);
        }
        return clusters;
    }
    getChildren(clusterId) {
        var originId = this._getOriginId(clusterId);
        var originZoom = this._getOriginZoom(clusterId);
        var errorMsg = 'No cluster with the specified id.';
        var tree = this.trees[originZoom];
        if (!tree) throw new Error(errorMsg);
        var data = tree.data;
        if (originId * this.stride >= data.length) throw new Error(errorMsg);
        var r = this.options.radius / (this.options.extent * Math.pow(2, originZoom - 1));
        var x = data[originId * this.stride];
        var y = data[originId * this.stride + 1];
        var ids = tree.within(x, y, r);
        var children = [];
        for (var id of ids){
            var k = id * this.stride;
            if (data[k + OFFSET_PARENT] === clusterId) {
                children.push(data[k + OFFSET_NUM] > 1 ? getClusterJSON(data, k, this.clusterProps) : this.points[data[k + OFFSET_ID]]);
            }
        }
        if (children.length === 0) throw new Error(errorMsg);
        return children;
    }
    getLeaves(clusterId, limit, offset) {
        limit = limit || 10;
        offset = offset || 0;
        var leaves = [];
        this._appendLeaves(leaves, clusterId, limit, offset, 0);
        return leaves;
    }
    getTile(z, x, y) {
        var tree = this.trees[this._limitZoom(z)];
        var z2 = Math.pow(2, z);
        var { extent, radius } = this.options;
        var p = radius / extent;
        var top = (y - p) / z2;
        var bottom = (y + 1 + p) / z2;
        var tile = {
            features: []
        };
        this._addTileFeatures(tree.range((x - p) / z2, top, (x + 1 + p) / z2, bottom), tree.data, x, y, z2, tile);
        if (x === 0) {
            this._addTileFeatures(tree.range(1 - p / z2, top, 1, bottom), tree.data, z2, y, z2, tile);
        }
        if (x === z2 - 1) {
            this._addTileFeatures(tree.range(0, top, p / z2, bottom), tree.data, -1, y, z2, tile);
        }
        return tile.features.length ? tile : null;
    }
    getClusterExpansionZoom(clusterId) {
        var expansionZoom = this._getOriginZoom(clusterId) - 1;
        while(expansionZoom <= this.options.maxZoom){
            var children = this.getChildren(clusterId);
            expansionZoom++;
            if (children.length !== 1) break;
            clusterId = children[0].properties.cluster_id;
        }
        return expansionZoom;
    }
    _appendLeaves(result, clusterId, limit, offset, skipped) {
        var children = this.getChildren(clusterId);
        for (var child of children){
            var props = child.properties;
            if (props && props.cluster) {
                if (skipped + props.point_count <= offset) {
                    // skip the whole cluster
                    skipped += props.point_count;
                } else {
                    // enter the cluster
                    skipped = this._appendLeaves(result, props.cluster_id, limit, offset, skipped);
                // exit the cluster
                }
            } else if (skipped < offset) {
                // skip a single point
                skipped++;
            } else {
                // add a single point
                result.push(child);
            }
            if (result.length === limit) break;
        }
        return skipped;
    }
    _createTree(data) {
        var tree = new KDBush(data.length / this.stride | 0, this.options.nodeSize, Float32Array);
        for(var i = 0; i < data.length; i += this.stride)tree.add(data[i], data[i + 1]);
        tree.finish();
        tree.data = data;
        return tree;
    }
    _addTileFeatures(ids, data, x, y, z2, tile) {
        for (var i of ids){
            var k = i * this.stride;
            var isCluster = data[k + OFFSET_NUM] > 1;
            var tags = void 0, px = void 0, py = void 0;
            if (isCluster) {
                tags = getClusterProperties(data, k, this.clusterProps);
                px = data[k];
                py = data[k + 1];
            } else {
                var p = this.points[data[k + OFFSET_ID]];
                tags = p.properties;
                var [lng, lat] = p.geometry.coordinates;
                px = lngX(lng);
                py = latY(lat);
            }
            var f = {
                type: 1,
                geometry: [
                    [
                        Math.round(this.options.extent * (px * z2 - x)),
                        Math.round(this.options.extent * (py * z2 - y))
                    ]
                ],
                tags
            };
            // assign id
            var id = void 0;
            if (isCluster || this.options.generateId) {
                // optionally generate id for points
                id = data[k + OFFSET_ID];
            } else {
                // keep id if already assigned
                id = this.points[data[k + OFFSET_ID]].id;
            }
            if (id !== undefined) f.id = id;
            tile.features.push(f);
        }
    }
    _limitZoom(z) {
        return Math.max(this.options.minZoom, Math.min(Math.floor(+z), this.options.maxZoom + 1));
    }
    _cluster(tree, zoom) {
        var { radius, extent, reduce, minPoints } = this.options;
        var r = radius / (extent * Math.pow(2, zoom));
        var data = tree.data;
        var nextData = [];
        var stride = this.stride;
        // loop through each point
        for(var i = 0; i < data.length; i += stride){
            // if we've already visited the point at this zoom level, skip it
            if (data[i + OFFSET_ZOOM] <= zoom) continue;
            data[i + OFFSET_ZOOM] = zoom;
            // find all nearby points
            var x = data[i];
            var y = data[i + 1];
            var neighborIds = tree.within(data[i], data[i + 1], r);
            var numPointsOrigin = data[i + OFFSET_NUM];
            var numPoints = numPointsOrigin;
            // count the number of points in a potential cluster
            for (var neighborId of neighborIds){
                var k = neighborId * stride;
                // filter out neighbors that are already processed
                if (data[k + OFFSET_ZOOM] > zoom) numPoints += data[k + OFFSET_NUM];
            }
            // if there were neighbors to merge, and there are enough points to form a cluster
            if (numPoints > numPointsOrigin && numPoints >= minPoints) {
                var wx = x * numPointsOrigin;
                var wy = y * numPointsOrigin;
                var clusterProperties = void 0;
                var clusterPropIndex = -1;
                // encode both zoom and point index on which the cluster originated -- offset by total length of features
                var id = ((i / stride | 0) << 5) + (zoom + 1) + this.points.length;
                for (var _neighborId of neighborIds){
                    var _k = _neighborId * stride;
                    if (data[_k + OFFSET_ZOOM] <= zoom) continue;
                    data[_k + OFFSET_ZOOM] = zoom; // save the zoom (so it doesn't get processed twice)
                    var numPoints2 = data[_k + OFFSET_NUM];
                    wx += data[_k] * numPoints2; // accumulate coordinates for calculating weighted center
                    wy += data[_k + 1] * numPoints2;
                    data[_k + OFFSET_PARENT] = id;
                    if (reduce) {
                        if (!clusterProperties) {
                            clusterProperties = this._map(data, i, true);
                            clusterPropIndex = this.clusterProps.length;
                            this.clusterProps.push(clusterProperties);
                        }
                        reduce(clusterProperties, this._map(data, _k));
                    }
                }
                data[i + OFFSET_PARENT] = id;
                nextData.push(wx / numPoints, wy / numPoints, Infinity, id, -1, numPoints);
                if (reduce) nextData.push(clusterPropIndex);
            } else {
                // left points as unclustered
                for(var j = 0; j < stride; j++)nextData.push(data[i + j]);
                if (numPoints > 1) {
                    for (var _neighborId2 of neighborIds){
                        var _k2 = _neighborId2 * stride;
                        if (data[_k2 + OFFSET_ZOOM] <= zoom) continue;
                        data[_k2 + OFFSET_ZOOM] = zoom;
                        for(var _j = 0; _j < stride; _j++)nextData.push(data[_k2 + _j]);
                    }
                }
            }
        }
        return nextData;
    }
    // get index of the point from which the cluster originated
    _getOriginId(clusterId) {
        return clusterId - this.points.length >> 5;
    }
    // get zoom of the point from which the cluster originated
    _getOriginZoom(clusterId) {
        return (clusterId - this.points.length) % 32;
    }
    _map(data, i, clone) {
        if (data[i + OFFSET_NUM] > 1) {
            var props = this.clusterProps[data[i + OFFSET_PROP]];
            return clone ? Object.assign({}, props) : props;
        }
        var original = this.points[data[i + OFFSET_ID]].properties;
        var result = this.options.map(original);
        return clone && result === original ? Object.assign({}, result) : result;
    }
}
function getClusterJSON(data, i, clusterProps) {
    return {
        type: 'Feature',
        id: data[i + OFFSET_ID],
        properties: getClusterProperties(data, i, clusterProps),
        geometry: {
            type: 'Point',
            coordinates: [
                xLng(data[i]),
                yLat(data[i + 1])
            ]
        }
    };
}
function getClusterProperties(data, i, clusterProps) {
    var count = data[i + OFFSET_NUM];
    var abbrev = count >= 10000 ? "".concat(Math.round(count / 1000), "k") : count >= 1000 ? "".concat(Math.round(count / 100) / 10, "k") : count;
    var propIndex = data[i + OFFSET_PROP];
    var properties = propIndex === -1 ? {} : Object.assign({}, clusterProps[propIndex]);
    return Object.assign(properties, {
        cluster: true,
        cluster_id: data[i + OFFSET_ID],
        point_count: count,
        point_count_abbreviated: abbrev
    });
}
// longitude/latitude to spherical mercator in [0..1] range
function lngX(lng) {
    return lng / 360 + 0.5;
}
function latY(lat) {
    var sin = Math.sin(lat * Math.PI / 180);
    var y = 0.5 - 0.25 * Math.log((1 + sin) / (1 - sin)) / Math.PI;
    return y < 0 ? 0 : y > 1 ? 1 : y;
}
// spherical mercator to longitude/latitude
function xLng(x) {
    return (x - 0.5) * 360;
}
function yLat(y) {
    var y2 = (180 - y * 360) * Math.PI / 180;
    return 360 * Math.atan(Math.exp(y2)) / Math.PI - 90;
}
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */ function __rest(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
}
/**
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * util class that creates a common set of convenience functions to wrap
 * shared behavior of Advanced Markers and Markers.
 */ class MarkerUtils {
    static isAdvancedMarkerAvailable(map) {
        return google.maps.marker && map.getMapCapabilities().isAdvancedMarkersAvailable === true;
    }
    static isAdvancedMarker(marker) {
        return google.maps.marker && marker instanceof google.maps.marker.AdvancedMarkerElement;
    }
    static setMap(marker, map) {
        if (this.isAdvancedMarker(marker)) {
            marker.map = map;
        } else {
            marker.setMap(map);
        }
    }
    static getPosition(marker) {
        // SuperClusterAlgorithm.calculate expects a LatLng instance so we fake it for Adv Markers
        if (this.isAdvancedMarker(marker)) {
            if (marker.position) {
                if (marker.position instanceof google.maps.LatLng) {
                    return marker.position;
                }
                // since we can't cast to LatLngLiteral for reasons =(
                if (marker.position.lat && marker.position.lng) {
                    return new google.maps.LatLng(marker.position.lat, marker.position.lng);
                }
            }
            return new google.maps.LatLng(null);
        }
        return marker.getPosition();
    }
    static getVisible(marker) {
        if (this.isAdvancedMarker(marker)) {
            /**
       * Always return true for Advanced Markers because the clusterer
       * uses getVisible as a way to count legacy markers not as an actual
       * indicator of visibility for some reason. Even when markers are hidden
       * Marker.getVisible returns `true` and this is used to set the marker count
       * on the cluster. See the behavior of Cluster.count
       */ return true;
        }
        return marker.getVisible();
    }
}
/**
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ class Cluster {
    constructor(_ref){
        var { markers, position } = _ref;
        this.markers = markers;
        if (position) {
            if (position instanceof google.maps.LatLng) {
                this._position = position;
            } else {
                this._position = new google.maps.LatLng(position);
            }
        }
    }
    get bounds() {
        if (this.markers.length === 0 && !this._position) {
            return;
        }
        var bounds = new google.maps.LatLngBounds(this._position, this._position);
        for (var marker of this.markers){
            bounds.extend(MarkerUtils.getPosition(marker));
        }
        return bounds;
    }
    get position() {
        return this._position || this.bounds.getCenter();
    }
    /**
   * Get the count of **visible** markers.
   */ get count() {
        return this.markers.filter((m)=>MarkerUtils.getVisible(m)).length;
    }
    /**
   * Add a marker to the cluster.
   */ push(marker) {
        this.markers.push(marker);
    }
    /**
   * Cleanup references and remove marker from map.
   */ delete() {
        if (this.marker) {
            MarkerUtils.setMap(this.marker, null);
            this.marker = undefined;
        }
        this.markers.length = 0;
    }
}
/**
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Returns the markers visible in a padded map viewport
 *
 * @param map
 * @param mapCanvasProjection
 * @param markers The list of marker to filter
 * @param viewportPaddingPixels The padding in pixel
 * @returns The list of markers in the padded viewport
 */ var filterMarkersToPaddedViewport = (map, mapCanvasProjection, markers, viewportPaddingPixels)=>{
    var extendedMapBounds = extendBoundsToPaddedViewport(map.getBounds(), mapCanvasProjection, viewportPaddingPixels);
    return markers.filter((marker)=>extendedMapBounds.contains(MarkerUtils.getPosition(marker)));
};
/**
 * Extends a bounds by a number of pixels in each direction
 */ var extendBoundsToPaddedViewport = (bounds, projection, numPixels)=>{
    var { northEast, southWest } = latLngBoundsToPixelBounds(bounds, projection);
    var extendedPixelBounds = extendPixelBounds({
        northEast,
        southWest
    }, numPixels);
    return pixelBoundsToLatLngBounds(extendedPixelBounds, projection);
};
/**
 * Gets the extended bounds as a bbox [westLng, southLat, eastLng, northLat]
 */ var getPaddedViewport = (bounds, projection, pixels)=>{
    var extended = extendBoundsToPaddedViewport(bounds, projection, pixels);
    var ne = extended.getNorthEast();
    var sw = extended.getSouthWest();
    return [
        sw.lng(),
        sw.lat(),
        ne.lng(),
        ne.lat()
    ];
};
/**
 * Returns the distance between 2 positions.
 *
 * @hidden
 */ var distanceBetweenPoints = (p1, p2)=>{
    var R = 6371; // Radius of the Earth in km
    var dLat = (p2.lat - p1.lat) * Math.PI / 180;
    var dLon = (p2.lng - p1.lng) * Math.PI / 180;
    var sinDLat = Math.sin(dLat / 2);
    var sinDLon = Math.sin(dLon / 2);
    var a = sinDLat * sinDLat + Math.cos(p1.lat * Math.PI / 180) * Math.cos(p2.lat * Math.PI / 180) * sinDLon * sinDLon;
    var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
};
/**
 * Converts a LatLng bound to pixels.
 *
 * @hidden
 */ var latLngBoundsToPixelBounds = (bounds, projection)=>{
    return {
        northEast: projection.fromLatLngToDivPixel(bounds.getNorthEast()),
        southWest: projection.fromLatLngToDivPixel(bounds.getSouthWest())
    };
};
/**
 * Extends a pixel bounds by numPixels in all directions.
 *
 * @hidden
 */ var extendPixelBounds = (_ref2, numPixels)=>{
    var { northEast, southWest } = _ref2;
    northEast.x += numPixels;
    northEast.y -= numPixels;
    southWest.x -= numPixels;
    southWest.y += numPixels;
    return {
        northEast,
        southWest
    };
};
/**
 * @hidden
 */ var pixelBoundsToLatLngBounds = (_ref3, projection)=>{
    var { northEast, southWest } = _ref3;
    var sw = projection.fromDivPixelToLatLng(southWest);
    var ne = projection.fromDivPixelToLatLng(northEast);
    return new google.maps.LatLngBounds(sw, ne);
};
/**
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * @hidden
 */ class AbstractAlgorithm {
    constructor(_ref4){
        var { maxZoom = 16 } = _ref4;
        this.maxZoom = maxZoom;
    }
    /**
   * Helper function to bypass clustering based upon some map state such as
   * zoom, number of markers, etc.
   *
   * ```typescript
   *  cluster({markers, map}: AlgorithmInput): Cluster[] {
   *    if (shouldBypassClustering(map)) {
   *      return this.noop({markers})
   *    }
   * }
   * ```
   */ noop(_ref5) {
        var { markers } = _ref5;
        return noop$1(markers);
    }
}
/**
 * Abstract viewport algorithm proves a class to filter markers by a padded
 * viewport. This is a common optimization.
 *
 * @hidden
 */ class AbstractViewportAlgorithm extends AbstractAlgorithm {
    constructor(_a){
        var { viewportPadding = 60 } = _a, options = __rest(_a, [
            "viewportPadding"
        ]);
        super(options);
        this.viewportPadding = 60;
        this.viewportPadding = viewportPadding;
    }
    calculate(_ref6) {
        var { markers, map, mapCanvasProjection } = _ref6;
        if (map.getZoom() >= this.maxZoom) {
            return {
                clusters: this.noop({
                    markers
                }),
                changed: false
            };
        }
        return {
            clusters: this.cluster({
                markers: filterMarkersToPaddedViewport(map, mapCanvasProjection, markers, this.viewportPadding),
                map,
                mapCanvasProjection
            })
        };
    }
}
/**
 * @hidden
 */ var noop$1 = (markers)=>{
    var clusters = markers.map((marker)=>new Cluster({
            position: MarkerUtils.getPosition(marker),
            markers: [
                marker
            ]
        }));
    return clusters;
};
/**
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * The default Grid algorithm historically used in Google Maps marker
 * clustering.
 *
 * The Grid algorithm does not implement caching and markers may flash as the
 * viewport changes. Instead use {@link SuperClusterAlgorithm}.
 */ class GridAlgorithm extends AbstractViewportAlgorithm {
    constructor(_a){
        var { maxDistance = 40000, gridSize = 40 } = _a, options = __rest(_a, [
            "maxDistance",
            "gridSize"
        ]);
        super(options);
        this.clusters = [];
        this.state = {
            zoom: -1
        };
        this.maxDistance = maxDistance;
        this.gridSize = gridSize;
    }
    calculate(_ref7) {
        var { markers, map, mapCanvasProjection } = _ref7;
        var state = {
            zoom: map.getZoom()
        };
        var changed = false;
        if (this.state.zoom >= this.maxZoom && state.zoom >= this.maxZoom) ;
        else {
            changed = !equal(this.state, state);
        }
        this.state = state;
        if (map.getZoom() >= this.maxZoom) {
            return {
                clusters: this.noop({
                    markers
                }),
                changed
            };
        }
        return {
            clusters: this.cluster({
                markers: filterMarkersToPaddedViewport(map, mapCanvasProjection, markers, this.viewportPadding),
                map,
                mapCanvasProjection
            })
        };
    }
    cluster(_ref8) {
        var { markers, map, mapCanvasProjection } = _ref8;
        this.clusters = [];
        markers.forEach((marker)=>{
            this.addToClosestCluster(marker, map, mapCanvasProjection);
        });
        return this.clusters;
    }
    addToClosestCluster(marker, map, projection) {
        var maxDistance = this.maxDistance; // Some large number
        var cluster = null;
        for(var i = 0; i < this.clusters.length; i++){
            var candidate = this.clusters[i];
            var distance = distanceBetweenPoints(candidate.bounds.getCenter().toJSON(), MarkerUtils.getPosition(marker).toJSON());
            if (distance < maxDistance) {
                maxDistance = distance;
                cluster = candidate;
            }
        }
        if (cluster && extendBoundsToPaddedViewport(cluster.bounds, projection, this.gridSize).contains(MarkerUtils.getPosition(marker))) {
            cluster.push(marker);
        } else {
            var _cluster = new Cluster({
                markers: [
                    marker
                ]
            });
            this.clusters.push(_cluster);
        }
    }
}
/**
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Noop algorithm does not generate any clusters or filter markers by the an extended viewport.
 */ class NoopAlgorithm extends AbstractAlgorithm {
    constructor(_a){
        var options = __rest(_a, []);
        super(options);
    }
    calculate(_ref9) {
        var { markers, map, mapCanvasProjection } = _ref9;
        return {
            clusters: this.cluster({
                markers,
                map,
                mapCanvasProjection
            }),
            changed: false
        };
    }
    cluster(input) {
        return this.noop(input);
    }
}
/**
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * A very fast JavaScript algorithm for geospatial point clustering using KD trees.
 *
 * @see https://www.npmjs.com/package/supercluster for more information on options.
 */ class SuperClusterAlgorithm extends AbstractAlgorithm {
    constructor(_a){
        var { maxZoom, radius = 60 } = _a, options = __rest(_a, [
            "maxZoom",
            "radius"
        ]);
        super({
            maxZoom
        });
        this.state = {
            zoom: -1
        };
        this.superCluster = new Supercluster(Object.assign({
            maxZoom: this.maxZoom,
            radius
        }, options));
    }
    calculate(input) {
        var changed = false;
        var state = {
            zoom: input.map.getZoom()
        };
        if (!equal(input.markers, this.markers)) {
            changed = true;
            // TODO use proxy to avoid copy?
            this.markers = [
                ...input.markers
            ];
            var points = this.markers.map((marker)=>{
                var position = MarkerUtils.getPosition(marker);
                var coordinates = [
                    position.lng(),
                    position.lat()
                ];
                return {
                    type: "Feature",
                    geometry: {
                        type: "Point",
                        coordinates
                    },
                    properties: {
                        marker
                    }
                };
            });
            this.superCluster.load(points);
        }
        if (!changed) {
            if (this.state.zoom <= this.maxZoom || state.zoom <= this.maxZoom) {
                changed = !equal(this.state, state);
            }
        }
        this.state = state;
        if (changed) {
            this.clusters = this.cluster(input);
        }
        return {
            clusters: this.clusters,
            changed
        };
    }
    cluster(_ref10) {
        var { map } = _ref10;
        return this.superCluster.getClusters([
            -180,
            -90,
            180,
            90
        ], Math.round(map.getZoom())).map((feature)=>this.transformCluster(feature));
    }
    transformCluster(_ref11) {
        var { geometry: { coordinates: [lng, lat] }, properties } = _ref11;
        if (properties.cluster) {
            return new Cluster({
                markers: this.superCluster.getLeaves(properties.cluster_id, Infinity).map((leaf)=>leaf.properties.marker),
                position: {
                    lat,
                    lng
                }
            });
        }
        var marker = properties.marker;
        return new Cluster({
            markers: [
                marker
            ],
            position: MarkerUtils.getPosition(marker)
        });
    }
}
/**
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * A very fast JavaScript algorithm for geospatial point clustering using KD trees.
 *
 * @see https://www.npmjs.com/package/supercluster for more information on options.
 */ class SuperClusterViewportAlgorithm extends AbstractViewportAlgorithm {
    constructor(_a){
        var { maxZoom, radius = 60, viewportPadding = 60 } = _a, options = __rest(_a, [
            "maxZoom",
            "radius",
            "viewportPadding"
        ]);
        super({
            maxZoom,
            viewportPadding
        });
        this.superCluster = new Supercluster(Object.assign({
            maxZoom: this.maxZoom,
            radius
        }, options));
        this.state = {
            zoom: -1,
            view: [
                0,
                0,
                0,
                0
            ]
        };
    }
    calculate(input) {
        var state = {
            zoom: Math.round(input.map.getZoom()),
            view: getPaddedViewport(input.map.getBounds(), input.mapCanvasProjection, this.viewportPadding)
        };
        var changed = !equal(this.state, state);
        if (!equal(input.markers, this.markers)) {
            changed = true;
            // TODO use proxy to avoid copy?
            this.markers = [
                ...input.markers
            ];
            var points = this.markers.map((marker)=>{
                var position = MarkerUtils.getPosition(marker);
                var coordinates = [
                    position.lng(),
                    position.lat()
                ];
                return {
                    type: "Feature",
                    geometry: {
                        type: "Point",
                        coordinates
                    },
                    properties: {
                        marker
                    }
                };
            });
            this.superCluster.load(points);
        }
        if (changed) {
            this.clusters = this.cluster(input);
            this.state = state;
        }
        return {
            clusters: this.clusters,
            changed
        };
    }
    cluster(_ref12) {
        var { map, mapCanvasProjection } = _ref12;
        /* recalculate new state because we can't use the cached version. */ var state = {
            zoom: Math.round(map.getZoom()),
            view: getPaddedViewport(map.getBounds(), mapCanvasProjection, this.viewportPadding)
        };
        return this.superCluster.getClusters(state.view, state.zoom).map((feature)=>this.transformCluster(feature));
    }
    transformCluster(_ref13) {
        var { geometry: { coordinates: [lng, lat] }, properties } = _ref13;
        if (properties.cluster) {
            return new Cluster({
                markers: this.superCluster.getLeaves(properties.cluster_id, Infinity).map((leaf)=>leaf.properties.marker),
                position: {
                    lat,
                    lng
                }
            });
        }
        var marker = properties.marker;
        return new Cluster({
            markers: [
                marker
            ],
            position: MarkerUtils.getPosition(marker)
        });
    }
}
/**
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Provides statistics on all clusters in the current render cycle for use in {@link Renderer.render}.
 */ class ClusterStats {
    constructor(markers, clusters){
        this.markers = {
            sum: markers.length
        };
        var clusterMarkerCounts = clusters.map((a)=>a.count);
        var clusterMarkerSum = clusterMarkerCounts.reduce((a, b)=>a + b, 0);
        this.clusters = {
            count: clusters.length,
            markers: {
                mean: clusterMarkerSum / clusters.length,
                sum: clusterMarkerSum,
                min: Math.min(...clusterMarkerCounts),
                max: Math.max(...clusterMarkerCounts)
            }
        };
    }
}
class DefaultRenderer {
    /**
   * The default render function for the library used by {@link MarkerClusterer}.
   *
   * Currently set to use the following:
   *
   * ```typescript
   * // change color if this cluster has more markers than the mean cluster
   * const color =
   *   count > Math.max(10, stats.clusters.markers.mean)
   *     ? "#ff0000"
   *     : "#0000ff";
   *
   * // create svg url with fill color
   * const svg = window.btoa(`
   * <svg fill="${color}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 240">
   *   <circle cx="120" cy="120" opacity=".6" r="70" />
   *   <circle cx="120" cy="120" opacity=".3" r="90" />
   *   <circle cx="120" cy="120" opacity=".2" r="110" />
   *   <circle cx="120" cy="120" opacity=".1" r="130" />
   * </svg>`);
   *
   * // create marker using svg icon
   * return new google.maps.Marker({
   *   position,
   *   icon: {
   *     url: `data:image/svg+xml;base64,${svg}`,
   *     scaledSize: new google.maps.Size(45, 45),
   *   },
   *   label: {
   *     text: String(count),
   *     color: "rgba(255,255,255,0.9)",
   *     fontSize: "12px",
   *   },
   *   // adjust zIndex to be above other markers
   *   zIndex: 1000 + count,
   * });
   * ```
   */ render(_ref14, stats, map) {
        var { count, position } = _ref14;
        // change color if this cluster has more markers than the mean cluster
        var color = count > Math.max(10, stats.clusters.markers.mean) ? "#ff0000" : "#0000ff";
        // create svg literal with fill color
        var svg = "<svg fill=\"".concat(color, "\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 240 240\" width=\"50\" height=\"50\">\n<circle cx=\"120\" cy=\"120\" opacity=\".6\" r=\"70\" />\n<circle cx=\"120\" cy=\"120\" opacity=\".3\" r=\"90\" />\n<circle cx=\"120\" cy=\"120\" opacity=\".2\" r=\"110\" />\n<text x=\"50%\" y=\"50%\" style=\"fill:#fff\" text-anchor=\"middle\" font-size=\"50\" dominant-baseline=\"middle\" font-family=\"roboto,arial,sans-serif\">").concat(count, "</text>\n</svg>");
        var title = "Cluster of ".concat(count, " markers"), // adjust zIndex to be above other markers
        zIndex = Number(google.maps.Marker.MAX_ZINDEX) + count;
        if (MarkerUtils.isAdvancedMarkerAvailable(map)) {
            // create cluster SVG element
            var parser = new DOMParser();
            var svgEl = parser.parseFromString(svg, "image/svg+xml").documentElement;
            svgEl.setAttribute("transform", "translate(0 25)");
            var _clusterOptions = {
                map,
                position,
                zIndex,
                title,
                content: svgEl
            };
            return new google.maps.marker.AdvancedMarkerElement(_clusterOptions);
        }
        var clusterOptions = {
            position,
            zIndex,
            title,
            icon: {
                url: "data:image/svg+xml;base64,".concat(btoa(svg)),
                anchor: new google.maps.Point(25, 25)
            }
        };
        return new google.maps.Marker(clusterOptions);
    }
}
/**
 * Copyright 2019 Google LLC. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Extends an object's prototype by another's.
 *
 * @param type1 The Type to be extended.
 * @param type2 The Type to extend with.
 * @ignore
 */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
function extend(type1, type2) {
    /* istanbul ignore next */ // eslint-disable-next-line prefer-const
    for(var property in type2.prototype){
        type1.prototype[property] = type2.prototype[property];
    }
}
/**
 * @ignore
 */ class OverlayViewSafe {
    constructor(){
        // MarkerClusterer implements google.maps.OverlayView interface. We use the
        // extend function to extend MarkerClusterer with google.maps.OverlayView
        // because it might not always be available when the code is defined so we
        // look for it at the last possible moment. If it doesn't exist now then
        // there is no point going ahead :)
        extend(OverlayViewSafe, google.maps.OverlayView);
    }
}
/**
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ var MarkerClustererEvents;
(function(MarkerClustererEvents) {
    MarkerClustererEvents["CLUSTERING_BEGIN"] = "clusteringbegin";
    MarkerClustererEvents["CLUSTERING_END"] = "clusteringend";
    MarkerClustererEvents["CLUSTER_CLICK"] = "click";
})(MarkerClustererEvents || (MarkerClustererEvents = {}));
var defaultOnClusterClickHandler = (_, cluster, map)=>{
    map.fitBounds(cluster.bounds);
};
/**
 * MarkerClusterer creates and manages per-zoom-level clusters for large amounts
 * of markers. See {@link MarkerClustererOptions} for more details.
 *
 */ class MarkerClusterer extends OverlayViewSafe {
    constructor(_ref15){
        var { map, markers = [], algorithmOptions = {}, algorithm = new SuperClusterAlgorithm(algorithmOptions), renderer = new DefaultRenderer(), onClusterClick = defaultOnClusterClickHandler } = _ref15;
        super();
        this.markers = [
            ...markers
        ];
        this.clusters = [];
        this.algorithm = algorithm;
        this.renderer = renderer;
        this.onClusterClick = onClusterClick;
        if (map) {
            this.setMap(map);
        }
    }
    addMarker(marker, noDraw) {
        if (this.markers.includes(marker)) {
            return;
        }
        this.markers.push(marker);
        if (!noDraw) {
            this.render();
        }
    }
    addMarkers(markers, noDraw) {
        markers.forEach((marker)=>{
            this.addMarker(marker, true);
        });
        if (!noDraw) {
            this.render();
        }
    }
    removeMarker(marker, noDraw) {
        var index = this.markers.indexOf(marker);
        if (index === -1) {
            // Marker is not in our list of markers, so do nothing:
            return false;
        }
        MarkerUtils.setMap(marker, null);
        this.markers.splice(index, 1); // Remove the marker from the list of managed markers
        if (!noDraw) {
            this.render();
        }
        return true;
    }
    removeMarkers(markers, noDraw) {
        var removed = false;
        markers.forEach((marker)=>{
            removed = this.removeMarker(marker, true) || removed;
        });
        if (removed && !noDraw) {
            this.render();
        }
        return removed;
    }
    clearMarkers(noDraw) {
        this.markers.length = 0;
        if (!noDraw) {
            this.render();
        }
    }
    /**
   * Recalculates and draws all the marker clusters.
   */ render() {
        var map = this.getMap();
        if (map instanceof google.maps.Map && map.getProjection()) {
            google.maps.event.trigger(this, MarkerClustererEvents.CLUSTERING_BEGIN, this);
            var { clusters, changed } = this.algorithm.calculate({
                markers: this.markers,
                map,
                mapCanvasProjection: this.getProjection()
            });
            // Allow algorithms to return flag on whether the clusters/markers have changed.
            if (changed || changed == undefined) {
                // Accumulate the markers of the clusters composed of a single marker.
                // Those clusters directly use the marker.
                // Clusters with more than one markers use a group marker generated by a renderer.
                var singleMarker = new Set();
                for (var cluster of clusters){
                    if (cluster.markers.length == 1) {
                        singleMarker.add(cluster.markers[0]);
                    }
                }
                var groupMarkers = [];
                // Iterate the clusters that are currently rendered.
                for (var _cluster2 of this.clusters){
                    if (_cluster2.marker == null) {
                        continue;
                    }
                    if (_cluster2.markers.length == 1) {
                        if (!singleMarker.has(_cluster2.marker)) {
                            // The marker:
                            // - was previously rendered because it is from a cluster with 1 marker,
                            // - should no more be rendered as it is not in singleMarker.
                            MarkerUtils.setMap(_cluster2.marker, null);
                        }
                    } else {
                        // Delay the removal of old group markers to avoid flickering.
                        groupMarkers.push(_cluster2.marker);
                    }
                }
                this.clusters = clusters;
                this.renderClusters();
                // Delayed removal of the markers of the former groups.
                requestAnimationFrame(()=>groupMarkers.forEach((marker)=>MarkerUtils.setMap(marker, null)));
            }
            google.maps.event.trigger(this, MarkerClustererEvents.CLUSTERING_END, this);
        }
    }
    onAdd() {
        this.idleListener = this.getMap().addListener("idle", this.render.bind(this));
        this.render();
    }
    onRemove() {
        google.maps.event.removeListener(this.idleListener);
        this.reset();
    }
    reset() {
        this.markers.forEach((marker)=>MarkerUtils.setMap(marker, null));
        this.clusters.forEach((cluster)=>cluster.delete());
        this.clusters = [];
    }
    renderClusters() {
        // Generate stats to pass to renderers.
        var stats = new ClusterStats(this.markers, this.clusters);
        var map = this.getMap();
        this.clusters.forEach((cluster)=>{
            if (cluster.markers.length === 1) {
                cluster.marker = cluster.markers[0];
            } else {
                // Generate the marker to represent the group.
                cluster.marker = this.renderer.render(cluster, stats, map);
                // Make sure all individual markers are removed from the map.
                cluster.markers.forEach((marker)=>MarkerUtils.setMap(marker, null));
                if (this.onClusterClick) {
                    cluster.marker.addListener("click", /* istanbul ignore next */ (event)=>{
                        google.maps.event.trigger(this, MarkerClustererEvents.CLUSTER_CLICK, cluster);
                        this.onClusterClick(event, cluster, map);
                    });
                }
            }
            MarkerUtils.setMap(cluster.marker, map);
        });
    }
}
var index_esm = /*#__PURE__*/ Object.freeze({
    __proto__: null,
    AbstractAlgorithm: AbstractAlgorithm,
    AbstractViewportAlgorithm: AbstractViewportAlgorithm,
    Cluster: Cluster,
    ClusterStats: ClusterStats,
    DefaultRenderer: DefaultRenderer,
    GridAlgorithm: GridAlgorithm,
    MarkerClusterer: MarkerClusterer,
    get MarkerClustererEvents () {
        return MarkerClustererEvents;
    },
    MarkerUtils: MarkerUtils,
    NoopAlgorithm: NoopAlgorithm,
    SuperClusterAlgorithm: SuperClusterAlgorithm,
    SuperClusterViewportAlgorithm: SuperClusterViewportAlgorithm,
    defaultOnClusterClickHandler: defaultOnClusterClickHandler,
    distanceBetweenPoints: distanceBetweenPoints,
    extendBoundsToPaddedViewport: extendBoundsToPaddedViewport,
    extendPixelBounds: extendPixelBounds,
    filterMarkersToPaddedViewport: filterMarkersToPaddedViewport,
    getPaddedViewport: getPaddedViewport,
    noop: noop$1,
    pixelBoundsToLatLngBounds: pixelBoundsToLatLngBounds
});
function ownKeys$a(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$a(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$a(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$a(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
function useGoogleMarkerClusterer(options) {
    var map = useGoogleMap();
    var [markerClusterer, setMarkerClusterer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useGoogleMarkerClusterer.useEffect": ()=>{
            if (map && markerClusterer === null) {
                var markerCluster = new MarkerClusterer(_objectSpread$a(_objectSpread$a({}, options), {}, {
                    map
                }));
                setMarkerClusterer(markerCluster);
            }
        }
    }["useGoogleMarkerClusterer.useEffect"], [
        map
    ]);
    return markerClusterer;
}
/** Wrapper around [@googlemaps/markerclusterer](https://github.com/googlemaps/js-markerclusterer)
 *
 * Accepts {@link  MarkerClustererOptionsSubset} which is a subset of  {@link MarkerClustererOptions}
 */ function GoogleMarkerClusterer(_ref) {
    var { children, options } = _ref;
    var markerClusterer = useGoogleMarkerClusterer(options);
    return markerClusterer !== null ? children(markerClusterer) : null;
}
var GoogleMarkerClusterer$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(GoogleMarkerClusterer);
var eventMap$c = {
    onCloseClick: 'closeclick',
    onContentChanged: 'content_changed',
    onDomReady: 'domready',
    onPositionChanged: 'position_changed',
    onZindexChanged: 'zindex_changed'
};
var updaterMap$c = {
    options (instance, options) {
        instance.setOptions(options);
    },
    position (instance, position) {
        instance.setPosition(position);
    },
    zIndex (instance, zIndex) {
        instance.setZIndex(zIndex);
    }
};
function InfoWindowFunctional(_ref) {
    var { children, anchor, options, position, zIndex, onCloseClick, onDomReady, onContentChanged, onPositionChanged, onZindexChanged, onLoad, onUnmount } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [closeclickListener, setCloseClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [domreadyclickListener, setDomReadyClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [contentchangedclickListener, setContentChangedClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [positionchangedclickListener, setPositionChangedClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [zindexchangedclickListener, setZindexChangedClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var containerElementRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoWindowFunctional.useEffect": ()=>{
            if (instance !== null) {
                instance.close();
                if (anchor) {
                    instance.open(map, anchor);
                } else if (instance.getPosition()) {
                    instance.open(map);
                }
            }
        }
    }["InfoWindowFunctional.useEffect"], [
        map,
        instance,
        anchor
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoWindowFunctional.useEffect": ()=>{
            if (options && instance !== null) {
                instance.setOptions(options);
            }
        }
    }["InfoWindowFunctional.useEffect"], [
        instance,
        options
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoWindowFunctional.useEffect": ()=>{
            if (position && instance !== null) {
                instance.setPosition(position);
            }
        }
    }["InfoWindowFunctional.useEffect"], [
        position
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoWindowFunctional.useEffect": ()=>{
            if (typeof zIndex === 'number' && instance !== null) {
                instance.setZIndex(zIndex);
            }
        }
    }["InfoWindowFunctional.useEffect"], [
        zIndex
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoWindowFunctional.useEffect": ()=>{
            if (instance && onCloseClick) {
                if (closeclickListener !== null) {
                    google.maps.event.removeListener(closeclickListener);
                }
                setCloseClickListener(google.maps.event.addListener(instance, 'closeclick', onCloseClick));
            }
        }
    }["InfoWindowFunctional.useEffect"], [
        onCloseClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoWindowFunctional.useEffect": ()=>{
            if (instance && onDomReady) {
                if (domreadyclickListener !== null) {
                    google.maps.event.removeListener(domreadyclickListener);
                }
                setDomReadyClickListener(google.maps.event.addListener(instance, 'domready', onDomReady));
            }
        }
    }["InfoWindowFunctional.useEffect"], [
        onDomReady
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoWindowFunctional.useEffect": ()=>{
            if (instance && onContentChanged) {
                if (contentchangedclickListener !== null) {
                    google.maps.event.removeListener(contentchangedclickListener);
                }
                setContentChangedClickListener(google.maps.event.addListener(instance, 'content_changed', onContentChanged));
            }
        }
    }["InfoWindowFunctional.useEffect"], [
        onContentChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoWindowFunctional.useEffect": ()=>{
            if (instance && onPositionChanged) {
                if (positionchangedclickListener !== null) {
                    google.maps.event.removeListener(positionchangedclickListener);
                }
                setPositionChangedClickListener(google.maps.event.addListener(instance, 'position_changed', onPositionChanged));
            }
        }
    }["InfoWindowFunctional.useEffect"], [
        onPositionChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoWindowFunctional.useEffect": ()=>{
            if (instance && onZindexChanged) {
                if (zindexchangedclickListener !== null) {
                    google.maps.event.removeListener(zindexchangedclickListener);
                }
                setZindexChangedClickListener(google.maps.event.addListener(instance, 'zindex_changed', onZindexChanged));
            }
        }
    }["InfoWindowFunctional.useEffect"], [
        onZindexChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InfoWindowFunctional.useEffect": ()=>{
            var infoWindow = new google.maps.InfoWindow(options);
            setInstance(infoWindow);
            containerElementRef.current = document.createElement('div');
            if (onCloseClick) {
                setCloseClickListener(google.maps.event.addListener(infoWindow, 'closeclick', onCloseClick));
            }
            if (onDomReady) {
                setDomReadyClickListener(google.maps.event.addListener(infoWindow, 'domready', onDomReady));
            }
            if (onContentChanged) {
                setContentChangedClickListener(google.maps.event.addListener(infoWindow, 'content_changed', onContentChanged));
            }
            if (onPositionChanged) {
                setPositionChangedClickListener(google.maps.event.addListener(infoWindow, 'position_changed', onPositionChanged));
            }
            if (onZindexChanged) {
                setZindexChangedClickListener(google.maps.event.addListener(infoWindow, 'zindex_changed', onZindexChanged));
            }
            infoWindow.setContent(containerElementRef.current);
            if (position) {
                infoWindow.setPosition(position);
            }
            if (zIndex) {
                infoWindow.setZIndex(zIndex);
            }
            if (anchor) {
                infoWindow.open(map, anchor);
            } else if (infoWindow.getPosition()) {
                infoWindow.open(map);
            } else {
                invariant(false, "You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>.");
            }
            if (onLoad) {
                onLoad(infoWindow);
            }
            return ({
                "InfoWindowFunctional.useEffect": ()=>{
                    if (closeclickListener) {
                        google.maps.event.removeListener(closeclickListener);
                    }
                    if (contentchangedclickListener) {
                        google.maps.event.removeListener(contentchangedclickListener);
                    }
                    if (domreadyclickListener) {
                        google.maps.event.removeListener(domreadyclickListener);
                    }
                    if (positionchangedclickListener) {
                        google.maps.event.removeListener(positionchangedclickListener);
                    }
                    if (zindexchangedclickListener) {
                        google.maps.event.removeListener(zindexchangedclickListener);
                    }
                    if (onUnmount) {
                        onUnmount(infoWindow);
                    }
                    infoWindow.close();
                }
            })["InfoWindowFunctional.useEffect"];
        }
    }["InfoWindowFunctional.useEffect"], []);
    return containerElementRef.current ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPortal"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].only(children), containerElementRef.current) : null;
}
var InfoWindowF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(InfoWindowFunctional);
class InfoWindow extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "containerElement", null);
        _defineProperty(this, "state", {
            infoWindow: null
        });
        _defineProperty(this, "open", (infoWindow, anchor)=>{
            if (anchor) {
                infoWindow.open(this.context, anchor);
            } else if (infoWindow.getPosition()) {
                infoWindow.open(this.context);
            } else {
                invariant(false, "You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>.");
            }
        });
        _defineProperty(this, "setInfoWindowCallback", ()=>{
            if (this.state.infoWindow !== null && this.containerElement !== null) {
                this.state.infoWindow.setContent(this.containerElement);
                this.open(this.state.infoWindow, this.props.anchor);
                if (this.props.onLoad) {
                    this.props.onLoad(this.state.infoWindow);
                }
            }
        });
    }
    componentDidMount() {
        var infoWindow = new google.maps.InfoWindow(this.props.options);
        this.containerElement = document.createElement('div');
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$c,
            eventMap: eventMap$c,
            prevProps: {},
            nextProps: this.props,
            instance: infoWindow
        });
        this.setState(()=>{
            return {
                infoWindow
            };
        }, this.setInfoWindowCallback);
    }
    componentDidUpdate(prevProps) {
        if (this.state.infoWindow !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$c,
                eventMap: eventMap$c,
                prevProps,
                nextProps: this.props,
                instance: this.state.infoWindow
            });
        }
    }
    componentWillUnmount() {
        if (this.state.infoWindow !== null) {
            unregisterEvents(this.registeredEvents);
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.infoWindow);
            }
            this.state.infoWindow.close();
        }
    }
    render() {
        return this.containerElement ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPortal"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].only(this.props.children), this.containerElement) : null;
    }
}
_defineProperty(InfoWindow, "contextType", MapContext);
function ownKeys$9(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$9(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$9(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$9(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
var eventMap$b = {
    onClick: 'click',
    onDblClick: 'dblclick',
    onDrag: 'drag',
    onDragEnd: 'dragend',
    onDragStart: 'dragstart',
    onMouseDown: 'mousedown',
    onMouseMove: 'mousemove',
    onMouseOut: 'mouseout',
    onMouseOver: 'mouseover',
    onMouseUp: 'mouseup',
    onRightClick: 'rightclick'
};
var updaterMap$b = {
    draggable (instance, draggable) {
        instance.setDraggable(draggable);
    },
    editable (instance, editable) {
        instance.setEditable(editable);
    },
    map (instance, map) {
        instance.setMap(map);
    },
    options (instance, options) {
        instance.setOptions(options);
    },
    path (instance, path) {
        instance.setPath(path);
    },
    visible (instance, visible) {
        instance.setVisible(visible);
    }
};
var defaultOptions$1 = {};
function PolylineFunctional(_ref) {
    var { options, draggable, editable, visible, path, onDblClick, onDragEnd, onDragStart, onMouseDown, onMouseMove, onMouseOut, onMouseOver, onMouseUp, onRightClick, onClick, onDrag, onLoad, onUnmount } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dblclickListener, setDblclickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragendListener, setDragendListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragstartListener, setDragstartListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mousedownListener, setMousedownListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mousemoveListener, setMousemoveListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoutListener, setMouseoutListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoverListener, setMouseoverListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseupListener, setMouseupListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [rightclickListener, setRightclickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [clickListener, setClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragListener, setDragListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (instance !== null) {
                instance.setMap(map);
            }
        }
    }["PolylineFunctional.useEffect"], [
        map
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (typeof options !== 'undefined' && instance !== null) {
                instance.setOptions(options);
            }
        }
    }["PolylineFunctional.useEffect"], [
        instance,
        options
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (typeof draggable !== 'undefined' && instance !== null) {
                instance.setDraggable(draggable);
            }
        }
    }["PolylineFunctional.useEffect"], [
        instance,
        draggable
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (typeof editable !== 'undefined' && instance !== null) {
                instance.setEditable(editable);
            }
        }
    }["PolylineFunctional.useEffect"], [
        instance,
        editable
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (typeof visible !== 'undefined' && instance !== null) {
                instance.setVisible(visible);
            }
        }
    }["PolylineFunctional.useEffect"], [
        instance,
        visible
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (typeof path !== 'undefined' && instance !== null) {
                instance.setPath(path);
            }
        }
    }["PolylineFunctional.useEffect"], [
        instance,
        path
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (instance && onDblClick) {
                if (dblclickListener !== null) {
                    google.maps.event.removeListener(dblclickListener);
                }
                setDblclickListener(google.maps.event.addListener(instance, 'dblclick', onDblClick));
            }
        }
    }["PolylineFunctional.useEffect"], [
        onDblClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (instance && onDragEnd) {
                if (dragendListener !== null) {
                    google.maps.event.removeListener(dragendListener);
                }
                setDragendListener(google.maps.event.addListener(instance, 'dragend', onDragEnd));
            }
        }
    }["PolylineFunctional.useEffect"], [
        onDragEnd
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (instance && onDragStart) {
                if (dragstartListener !== null) {
                    google.maps.event.removeListener(dragstartListener);
                }
                setDragstartListener(google.maps.event.addListener(instance, 'dragstart', onDragStart));
            }
        }
    }["PolylineFunctional.useEffect"], [
        onDragStart
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (instance && onMouseDown) {
                if (mousedownListener !== null) {
                    google.maps.event.removeListener(mousedownListener);
                }
                setMousedownListener(google.maps.event.addListener(instance, 'mousedown', onMouseDown));
            }
        }
    }["PolylineFunctional.useEffect"], [
        onMouseDown
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (instance && onMouseMove) {
                if (mousemoveListener !== null) {
                    google.maps.event.removeListener(mousemoveListener);
                }
                setMousemoveListener(google.maps.event.addListener(instance, 'mousemove', onMouseMove));
            }
        }
    }["PolylineFunctional.useEffect"], [
        onMouseMove
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (instance && onMouseOut) {
                if (mouseoutListener !== null) {
                    google.maps.event.removeListener(mouseoutListener);
                }
                setMouseoutListener(google.maps.event.addListener(instance, 'mouseout', onMouseOut));
            }
        }
    }["PolylineFunctional.useEffect"], [
        onMouseOut
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (instance && onMouseOver) {
                if (mouseoverListener !== null) {
                    google.maps.event.removeListener(mouseoverListener);
                }
                setMouseoverListener(google.maps.event.addListener(instance, 'mouseover', onMouseOver));
            }
        }
    }["PolylineFunctional.useEffect"], [
        onMouseOver
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (instance && onMouseUp) {
                if (mouseupListener !== null) {
                    google.maps.event.removeListener(mouseupListener);
                }
                setMouseupListener(google.maps.event.addListener(instance, 'mouseup', onMouseUp));
            }
        }
    }["PolylineFunctional.useEffect"], [
        onMouseUp
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (instance && onRightClick) {
                if (rightclickListener !== null) {
                    google.maps.event.removeListener(rightclickListener);
                }
                setRightclickListener(google.maps.event.addListener(instance, 'rightclick', onRightClick));
            }
        }
    }["PolylineFunctional.useEffect"], [
        onRightClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (instance && onClick) {
                if (clickListener !== null) {
                    google.maps.event.removeListener(clickListener);
                }
                setClickListener(google.maps.event.addListener(instance, 'click', onClick));
            }
        }
    }["PolylineFunctional.useEffect"], [
        onClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            if (instance && onDrag) {
                if (dragListener !== null) {
                    google.maps.event.removeListener(dragListener);
                }
                setDragListener(google.maps.event.addListener(instance, 'drag', onDrag));
            }
        }
    }["PolylineFunctional.useEffect"], [
        onDrag
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolylineFunctional.useEffect": ()=>{
            var polyline = new google.maps.Polyline(_objectSpread$9(_objectSpread$9({}, options || defaultOptions$1), {}, {
                map
            }));
            if (path) {
                polyline.setPath(path);
            }
            if (typeof visible !== 'undefined') {
                polyline.setVisible(visible);
            }
            if (typeof editable !== 'undefined') {
                polyline.setEditable(editable);
            }
            if (typeof draggable !== 'undefined') {
                polyline.setDraggable(draggable);
            }
            if (onDblClick) {
                setDblclickListener(google.maps.event.addListener(polyline, 'dblclick', onDblClick));
            }
            if (onDragEnd) {
                setDragendListener(google.maps.event.addListener(polyline, 'dragend', onDragEnd));
            }
            if (onDragStart) {
                setDragstartListener(google.maps.event.addListener(polyline, 'dragstart', onDragStart));
            }
            if (onMouseDown) {
                setMousedownListener(google.maps.event.addListener(polyline, 'mousedown', onMouseDown));
            }
            if (onMouseMove) {
                setMousemoveListener(google.maps.event.addListener(polyline, 'mousemove', onMouseMove));
            }
            if (onMouseOut) {
                setMouseoutListener(google.maps.event.addListener(polyline, 'mouseout', onMouseOut));
            }
            if (onMouseOver) {
                setMouseoverListener(google.maps.event.addListener(polyline, 'mouseover', onMouseOver));
            }
            if (onMouseUp) {
                setMouseupListener(google.maps.event.addListener(polyline, 'mouseup', onMouseUp));
            }
            if (onRightClick) {
                setRightclickListener(google.maps.event.addListener(polyline, 'rightclick', onRightClick));
            }
            if (onClick) {
                setClickListener(google.maps.event.addListener(polyline, 'click', onClick));
            }
            if (onDrag) {
                setDragListener(google.maps.event.addListener(polyline, 'drag', onDrag));
            }
            setInstance(polyline);
            if (onLoad) {
                onLoad(polyline);
            }
            return ({
                "PolylineFunctional.useEffect": ()=>{
                    if (dblclickListener !== null) {
                        google.maps.event.removeListener(dblclickListener);
                    }
                    if (dragendListener !== null) {
                        google.maps.event.removeListener(dragendListener);
                    }
                    if (dragstartListener !== null) {
                        google.maps.event.removeListener(dragstartListener);
                    }
                    if (mousedownListener !== null) {
                        google.maps.event.removeListener(mousedownListener);
                    }
                    if (mousemoveListener !== null) {
                        google.maps.event.removeListener(mousemoveListener);
                    }
                    if (mouseoutListener !== null) {
                        google.maps.event.removeListener(mouseoutListener);
                    }
                    if (mouseoverListener !== null) {
                        google.maps.event.removeListener(mouseoverListener);
                    }
                    if (mouseupListener !== null) {
                        google.maps.event.removeListener(mouseupListener);
                    }
                    if (rightclickListener !== null) {
                        google.maps.event.removeListener(rightclickListener);
                    }
                    if (clickListener !== null) {
                        google.maps.event.removeListener(clickListener);
                    }
                    if (onUnmount) {
                        onUnmount(polyline);
                    }
                    polyline.setMap(null);
                }
            })["PolylineFunctional.useEffect"];
        }
    }["PolylineFunctional.useEffect"], []);
    return null;
}
var PolylineF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(PolylineFunctional);
class Polyline extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "state", {
            polyline: null
        });
        _defineProperty(this, "setPolylineCallback", ()=>{
            if (this.state.polyline !== null && this.props.onLoad) {
                this.props.onLoad(this.state.polyline);
            }
        });
    }
    componentDidMount() {
        var polyline = new google.maps.Polyline(_objectSpread$9(_objectSpread$9({}, this.props.options), {}, {
            map: this.context
        }));
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$b,
            eventMap: eventMap$b,
            prevProps: {},
            nextProps: this.props,
            instance: polyline
        });
        this.setState(function setPolyline() {
            return {
                polyline
            };
        }, this.setPolylineCallback);
    }
    componentDidUpdate(prevProps) {
        if (this.state.polyline !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$b,
                eventMap: eventMap$b,
                prevProps,
                nextProps: this.props,
                instance: this.state.polyline
            });
        }
    }
    componentWillUnmount() {
        if (this.state.polyline === null) {
            return;
        }
        if (this.props.onUnmount) {
            this.props.onUnmount(this.state.polyline);
        }
        unregisterEvents(this.registeredEvents);
        this.state.polyline.setMap(null);
    }
    render() {
        return null;
    }
}
_defineProperty(Polyline, "contextType", MapContext);
function ownKeys$8(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$8(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$8(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$8(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
var eventMap$a = {
    onClick: 'click',
    onDblClick: 'dblclick',
    onDrag: 'drag',
    onDragEnd: 'dragend',
    onDragStart: 'dragstart',
    onMouseDown: 'mousedown',
    onMouseMove: 'mousemove',
    onMouseOut: 'mouseout',
    onMouseOver: 'mouseover',
    onMouseUp: 'mouseup',
    onRightClick: 'rightclick'
};
var updaterMap$a = {
    draggable (instance, draggable) {
        instance.setDraggable(draggable);
    },
    editable (instance, editable) {
        instance.setEditable(editable);
    },
    map (instance, map) {
        instance.setMap(map);
    },
    options (instance, options) {
        instance.setOptions(options);
    },
    path (instance, path) {
        instance.setPath(path);
    },
    paths (instance, paths) {
        instance.setPaths(paths);
    },
    visible (instance, visible) {
        instance.setVisible(visible);
    }
};
function PolygonFunctional(_ref) {
    var { options, draggable, editable, visible, path, paths, onDblClick, onDragEnd, onDragStart, onMouseDown, onMouseMove, onMouseOut, onMouseOver, onMouseUp, onRightClick, onClick, onDrag, onLoad, onUnmount, onEdit } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dblclickListener, setDblclickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragendListener, setDragendListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragstartListener, setDragstartListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mousedownListener, setMousedownListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mousemoveListener, setMousemoveListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoutListener, setMouseoutListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoverListener, setMouseoverListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseupListener, setMouseupListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [rightclickListener, setRightclickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [clickListener, setClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragListener, setDragListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (instance !== null) {
                instance.setMap(map);
            }
        }
    }["PolygonFunctional.useEffect"], [
        map
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (typeof options !== 'undefined' && instance !== null) {
                instance.setOptions(options);
            }
        }
    }["PolygonFunctional.useEffect"], [
        instance,
        options
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (typeof draggable !== 'undefined' && instance !== null) {
                instance.setDraggable(draggable);
            }
        }
    }["PolygonFunctional.useEffect"], [
        instance,
        draggable
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (typeof editable !== 'undefined' && instance !== null) {
                instance.setEditable(editable);
            }
        }
    }["PolygonFunctional.useEffect"], [
        instance,
        editable
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (typeof visible !== 'undefined' && instance !== null) {
                instance.setVisible(visible);
            }
        }
    }["PolygonFunctional.useEffect"], [
        instance,
        visible
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (typeof path !== 'undefined' && instance !== null) {
                instance.setPath(path);
            }
        }
    }["PolygonFunctional.useEffect"], [
        instance,
        path
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (typeof paths !== 'undefined' && instance !== null) {
                instance.setPaths(paths);
            }
        }
    }["PolygonFunctional.useEffect"], [
        instance,
        paths
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (instance && typeof onDblClick === 'function') {
                if (dblclickListener !== null) {
                    google.maps.event.removeListener(dblclickListener);
                }
                setDblclickListener(google.maps.event.addListener(instance, 'dblclick', onDblClick));
            }
        }
    }["PolygonFunctional.useEffect"], [
        onDblClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (!instance) {
                return;
            }
            google.maps.event.addListener(instance.getPath(), 'insert_at', {
                "PolygonFunctional.useEffect": ()=>{
                    onEdit === null || onEdit === void 0 || onEdit(instance);
                }
            }["PolygonFunctional.useEffect"]);
            google.maps.event.addListener(instance.getPath(), 'set_at', {
                "PolygonFunctional.useEffect": ()=>{
                    onEdit === null || onEdit === void 0 || onEdit(instance);
                }
            }["PolygonFunctional.useEffect"]);
            google.maps.event.addListener(instance.getPath(), 'remove_at', {
                "PolygonFunctional.useEffect": ()=>{
                    onEdit === null || onEdit === void 0 || onEdit(instance);
                }
            }["PolygonFunctional.useEffect"]);
        }
    }["PolygonFunctional.useEffect"], [
        instance,
        onEdit
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (instance && typeof onDragEnd === 'function') {
                if (dragendListener !== null) {
                    google.maps.event.removeListener(dragendListener);
                }
                setDragendListener(google.maps.event.addListener(instance, 'dragend', onDragEnd));
            }
        }
    }["PolygonFunctional.useEffect"], [
        onDragEnd
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (instance && typeof onDragStart === 'function') {
                if (dragstartListener !== null) {
                    google.maps.event.removeListener(dragstartListener);
                }
                setDragstartListener(google.maps.event.addListener(instance, 'dragstart', onDragStart));
            }
        }
    }["PolygonFunctional.useEffect"], [
        onDragStart
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (instance && typeof onMouseDown === 'function') {
                if (mousedownListener !== null) {
                    google.maps.event.removeListener(mousedownListener);
                }
                setMousedownListener(google.maps.event.addListener(instance, 'mousedown', onMouseDown));
            }
        }
    }["PolygonFunctional.useEffect"], [
        onMouseDown
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (instance && typeof onMouseMove === 'function') {
                if (mousemoveListener !== null) {
                    google.maps.event.removeListener(mousemoveListener);
                }
                setMousemoveListener(google.maps.event.addListener(instance, 'mousemove', onMouseMove));
            }
        }
    }["PolygonFunctional.useEffect"], [
        onMouseMove
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (instance && typeof onMouseOut === 'function') {
                if (mouseoutListener !== null) {
                    google.maps.event.removeListener(mouseoutListener);
                }
                setMouseoutListener(google.maps.event.addListener(instance, 'mouseout', onMouseOut));
            }
        }
    }["PolygonFunctional.useEffect"], [
        onMouseOut
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (instance && typeof onMouseOver === 'function') {
                if (mouseoverListener !== null) {
                    google.maps.event.removeListener(mouseoverListener);
                }
                setMouseoverListener(google.maps.event.addListener(instance, 'mouseover', onMouseOver));
            }
        }
    }["PolygonFunctional.useEffect"], [
        onMouseOver
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (instance && typeof onMouseUp === 'function') {
                if (mouseupListener !== null) {
                    google.maps.event.removeListener(mouseupListener);
                }
                setMouseupListener(google.maps.event.addListener(instance, 'mouseup', onMouseUp));
            }
        }
    }["PolygonFunctional.useEffect"], [
        onMouseUp
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (instance && typeof onRightClick === 'function') {
                if (rightclickListener !== null) {
                    google.maps.event.removeListener(rightclickListener);
                }
                setRightclickListener(google.maps.event.addListener(instance, 'rightclick', onRightClick));
            }
        }
    }["PolygonFunctional.useEffect"], [
        onRightClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (instance && typeof onClick === 'function') {
                if (clickListener !== null) {
                    google.maps.event.removeListener(clickListener);
                }
                setClickListener(google.maps.event.addListener(instance, 'click', onClick));
            }
        }
    }["PolygonFunctional.useEffect"], [
        onClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            if (instance && typeof onDrag === 'function') {
                if (dragListener !== null) {
                    google.maps.event.removeListener(dragListener);
                }
                setDragListener(google.maps.event.addListener(instance, 'drag', onDrag));
            }
        }
    }["PolygonFunctional.useEffect"], [
        onDrag
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PolygonFunctional.useEffect": ()=>{
            var polygon = new google.maps.Polygon(_objectSpread$8(_objectSpread$8({}, options), {}, {
                map
            }));
            if (path) {
                polygon.setPath(path);
            }
            if (paths) {
                polygon.setPaths(paths);
            }
            if (typeof visible !== 'undefined') {
                polygon.setVisible(visible);
            }
            if (typeof editable !== 'undefined') {
                polygon.setEditable(editable);
            }
            if (typeof draggable !== 'undefined') {
                polygon.setDraggable(draggable);
            }
            if (onDblClick) {
                setDblclickListener(google.maps.event.addListener(polygon, 'dblclick', onDblClick));
            }
            if (onDragEnd) {
                setDragendListener(google.maps.event.addListener(polygon, 'dragend', onDragEnd));
            }
            if (onDragStart) {
                setDragstartListener(google.maps.event.addListener(polygon, 'dragstart', onDragStart));
            }
            if (onMouseDown) {
                setMousedownListener(google.maps.event.addListener(polygon, 'mousedown', onMouseDown));
            }
            if (onMouseMove) {
                setMousemoveListener(google.maps.event.addListener(polygon, 'mousemove', onMouseMove));
            }
            if (onMouseOut) {
                setMouseoutListener(google.maps.event.addListener(polygon, 'mouseout', onMouseOut));
            }
            if (onMouseOver) {
                setMouseoverListener(google.maps.event.addListener(polygon, 'mouseover', onMouseOver));
            }
            if (onMouseUp) {
                setMouseupListener(google.maps.event.addListener(polygon, 'mouseup', onMouseUp));
            }
            if (onRightClick) {
                setRightclickListener(google.maps.event.addListener(polygon, 'rightclick', onRightClick));
            }
            if (onClick) {
                setClickListener(google.maps.event.addListener(polygon, 'click', onClick));
            }
            if (onDrag) {
                setDragListener(google.maps.event.addListener(polygon, 'drag', onDrag));
            }
            setInstance(polygon);
            if (onLoad) {
                onLoad(polygon);
            }
            return ({
                "PolygonFunctional.useEffect": ()=>{
                    if (dblclickListener !== null) {
                        google.maps.event.removeListener(dblclickListener);
                    }
                    if (dragendListener !== null) {
                        google.maps.event.removeListener(dragendListener);
                    }
                    if (dragstartListener !== null) {
                        google.maps.event.removeListener(dragstartListener);
                    }
                    if (mousedownListener !== null) {
                        google.maps.event.removeListener(mousedownListener);
                    }
                    if (mousemoveListener !== null) {
                        google.maps.event.removeListener(mousemoveListener);
                    }
                    if (mouseoutListener !== null) {
                        google.maps.event.removeListener(mouseoutListener);
                    }
                    if (mouseoverListener !== null) {
                        google.maps.event.removeListener(mouseoverListener);
                    }
                    if (mouseupListener !== null) {
                        google.maps.event.removeListener(mouseupListener);
                    }
                    if (rightclickListener !== null) {
                        google.maps.event.removeListener(rightclickListener);
                    }
                    if (clickListener !== null) {
                        google.maps.event.removeListener(clickListener);
                    }
                    if (onUnmount) {
                        onUnmount(polygon);
                    }
                    polygon.setMap(null);
                }
            })["PolygonFunctional.useEffect"];
        }
    }["PolygonFunctional.useEffect"], []);
    return null;
}
var PolygonF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(PolygonFunctional);
class Polygon extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
    }
    componentDidMount() {
        var polygonOptions = this.props.options || {};
        this.polygon = new google.maps.Polygon(polygonOptions);
        this.polygon.setMap(this.context);
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$a,
            eventMap: eventMap$a,
            prevProps: {},
            nextProps: this.props,
            instance: this.polygon
        });
        if (this.props.onLoad) {
            this.props.onLoad(this.polygon);
        }
    }
    componentDidUpdate(prevProps) {
        if (this.polygon) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$a,
                eventMap: eventMap$a,
                prevProps,
                nextProps: this.props,
                instance: this.polygon
            });
        }
    }
    componentWillUnmount() {
        if (this.polygon) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.polygon);
            }
            unregisterEvents(this.registeredEvents);
            if (this.polygon) {
                this.polygon.setMap(null);
            }
        }
    }
    render() {
        return null;
    }
}
_defineProperty(Polygon, "contextType", MapContext);
function ownKeys$7(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$7(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$7(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$7(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
var eventMap$9 = {
    onBoundsChanged: 'bounds_changed',
    onClick: 'click',
    onDblClick: 'dblclick',
    onDrag: 'drag',
    onDragEnd: 'dragend',
    onDragStart: 'dragstart',
    onMouseDown: 'mousedown',
    onMouseMove: 'mousemove',
    onMouseOut: 'mouseout',
    onMouseOver: 'mouseover',
    onMouseUp: 'mouseup',
    onRightClick: 'rightclick'
};
var updaterMap$9 = {
    bounds (instance, bounds) {
        instance.setBounds(bounds);
    },
    draggable (instance, draggable) {
        instance.setDraggable(draggable);
    },
    editable (instance, editable) {
        instance.setEditable(editable);
    },
    map (instance, map) {
        instance.setMap(map);
    },
    options (instance, options) {
        instance.setOptions(options);
    },
    visible (instance, visible) {
        instance.setVisible(visible);
    }
};
function RectangleFunctional(_ref) {
    var { options, bounds, draggable, editable, visible, onDblClick, onDragEnd, onDragStart, onMouseDown, onMouseMove, onMouseOut, onMouseOver, onMouseUp, onRightClick, onClick, onDrag, onBoundsChanged, onLoad, onUnmount } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dblclickListener, setDblclickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragendListener, setDragendListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragstartListener, setDragstartListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mousedownListener, setMousedownListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mousemoveListener, setMousemoveListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoutListener, setMouseoutListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoverListener, setMouseoverListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseupListener, setMouseupListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [rightClickListener, setRightClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [clickListener, setClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragListener, setDragListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [boundsChangedListener, setBoundsChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (instance !== null) {
                instance.setMap(map);
            }
        }
    }["RectangleFunctional.useEffect"], [
        map
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (typeof options !== 'undefined' && instance !== null) {
                instance.setOptions(options);
            }
        }
    }["RectangleFunctional.useEffect"], [
        instance,
        options
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (typeof draggable !== 'undefined' && instance !== null) {
                instance.setDraggable(draggable);
            }
        }
    }["RectangleFunctional.useEffect"], [
        instance,
        draggable
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (typeof editable !== 'undefined' && instance !== null) {
                instance.setEditable(editable);
            }
        }
    }["RectangleFunctional.useEffect"], [
        instance,
        editable
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (typeof visible !== 'undefined' && instance !== null) {
                instance.setVisible(visible);
            }
        }
    }["RectangleFunctional.useEffect"], [
        instance,
        visible
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (typeof bounds !== 'undefined' && instance !== null) {
                instance.setBounds(bounds);
            }
        }
    }["RectangleFunctional.useEffect"], [
        instance,
        bounds
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (instance && onDblClick) {
                if (dblclickListener !== null) {
                    google.maps.event.removeListener(dblclickListener);
                }
                setDblclickListener(google.maps.event.addListener(instance, 'dblclick', onDblClick));
            }
        }
    }["RectangleFunctional.useEffect"], [
        onDblClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (instance && onDragEnd) {
                if (dragendListener !== null) {
                    google.maps.event.removeListener(dragendListener);
                }
                setDragendListener(google.maps.event.addListener(instance, 'dragend', onDragEnd));
            }
        }
    }["RectangleFunctional.useEffect"], [
        onDragEnd
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (instance && onDragStart) {
                if (dragstartListener !== null) {
                    google.maps.event.removeListener(dragstartListener);
                }
                setDragstartListener(google.maps.event.addListener(instance, 'dragstart', onDragStart));
            }
        }
    }["RectangleFunctional.useEffect"], [
        onDragStart
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (instance && onMouseDown) {
                if (mousedownListener !== null) {
                    google.maps.event.removeListener(mousedownListener);
                }
                setMousedownListener(google.maps.event.addListener(instance, 'mousedown', onMouseDown));
            }
        }
    }["RectangleFunctional.useEffect"], [
        onMouseDown
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (instance && onMouseMove) {
                if (mousemoveListener !== null) {
                    google.maps.event.removeListener(mousemoveListener);
                }
                setMousemoveListener(google.maps.event.addListener(instance, 'mousemove', onMouseMove));
            }
        }
    }["RectangleFunctional.useEffect"], [
        onMouseMove
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (instance && onMouseOut) {
                if (mouseoutListener !== null) {
                    google.maps.event.removeListener(mouseoutListener);
                }
                setMouseoutListener(google.maps.event.addListener(instance, 'mouseout', onMouseOut));
            }
        }
    }["RectangleFunctional.useEffect"], [
        onMouseOut
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (instance && onMouseOver) {
                if (mouseoverListener !== null) {
                    google.maps.event.removeListener(mouseoverListener);
                }
                setMouseoverListener(google.maps.event.addListener(instance, 'mouseover', onMouseOver));
            }
        }
    }["RectangleFunctional.useEffect"], [
        onMouseOver
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (instance && onMouseUp) {
                if (mouseupListener !== null) {
                    google.maps.event.removeListener(mouseupListener);
                }
                setMouseupListener(google.maps.event.addListener(instance, 'mouseup', onMouseUp));
            }
        }
    }["RectangleFunctional.useEffect"], [
        onMouseUp
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (instance && onRightClick) {
                if (rightClickListener !== null) {
                    google.maps.event.removeListener(rightClickListener);
                }
                setRightClickListener(google.maps.event.addListener(instance, 'rightclick', onRightClick));
            }
        }
    }["RectangleFunctional.useEffect"], [
        onRightClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (instance && onClick) {
                if (clickListener !== null) {
                    google.maps.event.removeListener(clickListener);
                }
                setClickListener(google.maps.event.addListener(instance, 'click', onClick));
            }
        }
    }["RectangleFunctional.useEffect"], [
        onClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (instance && onDrag) {
                if (dragListener !== null) {
                    google.maps.event.removeListener(dragListener);
                }
                setDragListener(google.maps.event.addListener(instance, 'drag', onDrag));
            }
        }
    }["RectangleFunctional.useEffect"], [
        onDrag
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            if (instance && onBoundsChanged) {
                if (boundsChangedListener !== null) {
                    google.maps.event.removeListener(boundsChangedListener);
                }
                setBoundsChangedListener(google.maps.event.addListener(instance, 'bounds_changed', onBoundsChanged));
            }
        }
    }["RectangleFunctional.useEffect"], [
        onBoundsChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RectangleFunctional.useEffect": ()=>{
            var rectangle = new google.maps.Rectangle(_objectSpread$7(_objectSpread$7({}, options), {}, {
                map
            }));
            if (typeof visible !== 'undefined') {
                rectangle.setVisible(visible);
            }
            if (typeof editable !== 'undefined') {
                rectangle.setEditable(editable);
            }
            if (typeof draggable !== 'undefined') {
                rectangle.setDraggable(draggable);
            }
            if (typeof bounds !== 'undefined') {
                rectangle.setBounds(bounds);
            }
            if (onDblClick) {
                setDblclickListener(google.maps.event.addListener(rectangle, 'dblclick', onDblClick));
            }
            if (onDragEnd) {
                setDragendListener(google.maps.event.addListener(rectangle, 'dragend', onDragEnd));
            }
            if (onDragStart) {
                setDragstartListener(google.maps.event.addListener(rectangle, 'dragstart', onDragStart));
            }
            if (onMouseDown) {
                setMousedownListener(google.maps.event.addListener(rectangle, 'mousedown', onMouseDown));
            }
            if (onMouseMove) {
                setMousemoveListener(google.maps.event.addListener(rectangle, 'mousemove', onMouseMove));
            }
            if (onMouseOut) {
                setMouseoutListener(google.maps.event.addListener(rectangle, 'mouseout', onMouseOut));
            }
            if (onMouseOver) {
                setMouseoverListener(google.maps.event.addListener(rectangle, 'mouseover', onMouseOver));
            }
            if (onMouseUp) {
                setMouseupListener(google.maps.event.addListener(rectangle, 'mouseup', onMouseUp));
            }
            if (onRightClick) {
                setRightClickListener(google.maps.event.addListener(rectangle, 'rightclick', onRightClick));
            }
            if (onClick) {
                setClickListener(google.maps.event.addListener(rectangle, 'click', onClick));
            }
            if (onDrag) {
                setDragListener(google.maps.event.addListener(rectangle, 'drag', onDrag));
            }
            if (onBoundsChanged) {
                setBoundsChangedListener(google.maps.event.addListener(rectangle, 'bounds_changed', onBoundsChanged));
            }
            setInstance(rectangle);
            if (onLoad) {
                onLoad(rectangle);
            }
            return ({
                "RectangleFunctional.useEffect": ()=>{
                    if (dblclickListener !== null) {
                        google.maps.event.removeListener(dblclickListener);
                    }
                    if (dragendListener !== null) {
                        google.maps.event.removeListener(dragendListener);
                    }
                    if (dragstartListener !== null) {
                        google.maps.event.removeListener(dragstartListener);
                    }
                    if (mousedownListener !== null) {
                        google.maps.event.removeListener(mousedownListener);
                    }
                    if (mousemoveListener !== null) {
                        google.maps.event.removeListener(mousemoveListener);
                    }
                    if (mouseoutListener !== null) {
                        google.maps.event.removeListener(mouseoutListener);
                    }
                    if (mouseoverListener !== null) {
                        google.maps.event.removeListener(mouseoverListener);
                    }
                    if (mouseupListener !== null) {
                        google.maps.event.removeListener(mouseupListener);
                    }
                    if (rightClickListener !== null) {
                        google.maps.event.removeListener(rightClickListener);
                    }
                    if (clickListener !== null) {
                        google.maps.event.removeListener(clickListener);
                    }
                    if (dragListener !== null) {
                        google.maps.event.removeListener(dragListener);
                    }
                    if (boundsChangedListener !== null) {
                        google.maps.event.removeListener(boundsChangedListener);
                    }
                    if (onUnmount) {
                        onUnmount(rectangle);
                    }
                    rectangle.setMap(null);
                }
            })["RectangleFunctional.useEffect"];
        }
    }["RectangleFunctional.useEffect"], []);
    return null;
}
var RectangleF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(RectangleFunctional);
class Rectangle extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "state", {
            rectangle: null
        });
        _defineProperty(this, "setRectangleCallback", ()=>{
            if (this.state.rectangle !== null && this.props.onLoad) {
                this.props.onLoad(this.state.rectangle);
            }
        });
    }
    componentDidMount() {
        var rectangle = new google.maps.Rectangle(_objectSpread$7(_objectSpread$7({}, this.props.options), {}, {
            map: this.context
        }));
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$9,
            eventMap: eventMap$9,
            prevProps: {},
            nextProps: this.props,
            instance: rectangle
        });
        this.setState(function setRectangle() {
            return {
                rectangle
            };
        }, this.setRectangleCallback);
    }
    componentDidUpdate(prevProps) {
        if (this.state.rectangle !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$9,
                eventMap: eventMap$9,
                prevProps,
                nextProps: this.props,
                instance: this.state.rectangle
            });
        }
    }
    componentWillUnmount() {
        if (this.state.rectangle !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.rectangle);
            }
            unregisterEvents(this.registeredEvents);
            this.state.rectangle.setMap(null);
        }
    }
    render() {
        return null;
    }
}
_defineProperty(Rectangle, "contextType", MapContext);
function ownKeys$6(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$6(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$6(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$6(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
var eventMap$8 = {
    onCenterChanged: 'center_changed',
    onRadiusChanged: 'radius_changed',
    onClick: 'click',
    onDblClick: 'dblclick',
    onDrag: 'drag',
    onDragEnd: 'dragend',
    onDragStart: 'dragstart',
    onMouseDown: 'mousedown',
    onMouseMove: 'mousemove',
    onMouseOut: 'mouseout',
    onMouseOver: 'mouseover',
    onMouseUp: 'mouseup',
    onRightClick: 'rightclick'
};
var updaterMap$8 = {
    center (instance, center) {
        instance.setCenter(center);
    },
    draggable (instance, draggable) {
        instance.setDraggable(draggable);
    },
    editable (instance, editable) {
        instance.setEditable(editable);
    },
    map (instance, map) {
        instance.setMap(map);
    },
    options (instance, options) {
        instance.setOptions(options);
    },
    radius (instance, radius) {
        instance.setRadius(radius);
    },
    visible (instance, visible) {
        instance.setVisible(visible);
    }
};
var defaultOptions = {};
function CircleFunctional(_ref) {
    var { options, center, radius, draggable, editable, visible, onDblClick, onDragEnd, onDragStart, onMouseDown, onMouseMove, onMouseOut, onMouseOver, onMouseUp, onRightClick, onClick, onDrag, onCenterChanged, onRadiusChanged, onLoad, onUnmount } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dblclickListener, setDblclickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragendListener, setDragendListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragstartListener, setDragstartListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mousedownListener, setMousedownListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mousemoveListener, setMousemoveListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoutListener, setMouseoutListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoverListener, setMouseoverListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseupListener, setMouseupListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [rightclickListener, setRightclickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [clickListener, setClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dragListener, setDragListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [centerChangedListener, setCenterChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [radiusChangedListener, setRadiusChangedListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance !== null) {
                instance.setMap(map);
            }
        }
    }["CircleFunctional.useEffect"], [
        map
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (typeof options !== 'undefined' && instance !== null) {
                instance.setOptions(options);
            }
        }
    }["CircleFunctional.useEffect"], [
        instance,
        options
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (typeof draggable !== 'undefined' && instance !== null) {
                instance.setDraggable(draggable);
            }
        }
    }["CircleFunctional.useEffect"], [
        instance,
        draggable
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (typeof editable !== 'undefined' && instance !== null) {
                instance.setEditable(editable);
            }
        }
    }["CircleFunctional.useEffect"], [
        instance,
        editable
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (typeof visible !== 'undefined' && instance !== null) {
                instance.setVisible(visible);
            }
        }
    }["CircleFunctional.useEffect"], [
        instance,
        visible
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (typeof radius === 'number' && instance !== null) {
                instance.setRadius(radius);
            }
        }
    }["CircleFunctional.useEffect"], [
        instance,
        radius
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (typeof center !== 'undefined' && instance !== null) {
                instance.setCenter(center);
            }
        }
    }["CircleFunctional.useEffect"], [
        instance,
        center
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance && onDblClick) {
                if (dblclickListener !== null) {
                    google.maps.event.removeListener(dblclickListener);
                }
                setDblclickListener(google.maps.event.addListener(instance, 'dblclick', onDblClick));
            }
        }
    }["CircleFunctional.useEffect"], [
        onDblClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance && onDragEnd) {
                if (dragendListener !== null) {
                    google.maps.event.removeListener(dragendListener);
                }
                setDragendListener(google.maps.event.addListener(instance, 'dragend', onDragEnd));
            }
        }
    }["CircleFunctional.useEffect"], [
        onDragEnd
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance && onDragStart) {
                if (dragstartListener !== null) {
                    google.maps.event.removeListener(dragstartListener);
                }
                setDragstartListener(google.maps.event.addListener(instance, 'dragstart', onDragStart));
            }
        }
    }["CircleFunctional.useEffect"], [
        onDragStart
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance && onMouseDown) {
                if (mousedownListener !== null) {
                    google.maps.event.removeListener(mousedownListener);
                }
                setMousedownListener(google.maps.event.addListener(instance, 'mousedown', onMouseDown));
            }
        }
    }["CircleFunctional.useEffect"], [
        onMouseDown
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance && onMouseMove) {
                if (mousemoveListener !== null) {
                    google.maps.event.removeListener(mousemoveListener);
                }
                setMousemoveListener(google.maps.event.addListener(instance, 'mousemove', onMouseMove));
            }
        }
    }["CircleFunctional.useEffect"], [
        onMouseMove
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance && onMouseOut) {
                if (mouseoutListener !== null) {
                    google.maps.event.removeListener(mouseoutListener);
                }
                setMouseoutListener(google.maps.event.addListener(instance, 'mouseout', onMouseOut));
            }
        }
    }["CircleFunctional.useEffect"], [
        onMouseOut
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance && onMouseOver) {
                if (mouseoverListener !== null) {
                    google.maps.event.removeListener(mouseoverListener);
                }
                setMouseoverListener(google.maps.event.addListener(instance, 'mouseover', onMouseOver));
            }
        }
    }["CircleFunctional.useEffect"], [
        onMouseOver
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance && onMouseUp) {
                if (mouseupListener !== null) {
                    google.maps.event.removeListener(mouseupListener);
                }
                setMouseupListener(google.maps.event.addListener(instance, 'mouseup', onMouseUp));
            }
        }
    }["CircleFunctional.useEffect"], [
        onMouseUp
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance && onRightClick) {
                if (rightclickListener !== null) {
                    google.maps.event.removeListener(rightclickListener);
                }
                setRightclickListener(google.maps.event.addListener(instance, 'rightclick', onRightClick));
            }
        }
    }["CircleFunctional.useEffect"], [
        onRightClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance && onClick) {
                if (clickListener !== null) {
                    google.maps.event.removeListener(clickListener);
                }
                setClickListener(google.maps.event.addListener(instance, 'click', onClick));
            }
        }
    }["CircleFunctional.useEffect"], [
        onClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance && onDrag) {
                if (dragListener !== null) {
                    google.maps.event.removeListener(dragListener);
                }
                setDragListener(google.maps.event.addListener(instance, 'drag', onDrag));
            }
        }
    }["CircleFunctional.useEffect"], [
        onDrag
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance && onCenterChanged) {
                if (centerChangedListener !== null) {
                    google.maps.event.removeListener(centerChangedListener);
                }
                setCenterChangedListener(google.maps.event.addListener(instance, 'center_changed', onCenterChanged));
            }
        }
    }["CircleFunctional.useEffect"], [
        onClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            if (instance && onRadiusChanged) {
                if (radiusChangedListener !== null) {
                    google.maps.event.removeListener(radiusChangedListener);
                }
                setRadiusChangedListener(google.maps.event.addListener(instance, 'radius_changed', onRadiusChanged));
            }
        }
    }["CircleFunctional.useEffect"], [
        onRadiusChanged
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CircleFunctional.useEffect": ()=>{
            var circle = new google.maps.Circle(_objectSpread$6(_objectSpread$6({}, options || defaultOptions), {}, {
                map
            }));
            if (typeof radius === 'number') {
                circle.setRadius(radius);
            }
            if (typeof center !== 'undefined') {
                circle.setCenter(center);
            }
            if (typeof radius === 'number') {
                circle.setRadius(radius);
            }
            if (typeof visible !== 'undefined') {
                circle.setVisible(visible);
            }
            if (typeof editable !== 'undefined') {
                circle.setEditable(editable);
            }
            if (typeof draggable !== 'undefined') {
                circle.setDraggable(draggable);
            }
            if (onDblClick) {
                setDblclickListener(google.maps.event.addListener(circle, 'dblclick', onDblClick));
            }
            if (onDragEnd) {
                setDragendListener(google.maps.event.addListener(circle, 'dragend', onDragEnd));
            }
            if (onDragStart) {
                setDragstartListener(google.maps.event.addListener(circle, 'dragstart', onDragStart));
            }
            if (onMouseDown) {
                setMousedownListener(google.maps.event.addListener(circle, 'mousedown', onMouseDown));
            }
            if (onMouseMove) {
                setMousemoveListener(google.maps.event.addListener(circle, 'mousemove', onMouseMove));
            }
            if (onMouseOut) {
                setMouseoutListener(google.maps.event.addListener(circle, 'mouseout', onMouseOut));
            }
            if (onMouseOver) {
                setMouseoverListener(google.maps.event.addListener(circle, 'mouseover', onMouseOver));
            }
            if (onMouseUp) {
                setMouseupListener(google.maps.event.addListener(circle, 'mouseup', onMouseUp));
            }
            if (onRightClick) {
                setRightclickListener(google.maps.event.addListener(circle, 'rightclick', onRightClick));
            }
            if (onClick) {
                setClickListener(google.maps.event.addListener(circle, 'click', onClick));
            }
            if (onDrag) {
                setDragListener(google.maps.event.addListener(circle, 'drag', onDrag));
            }
            if (onCenterChanged) {
                setCenterChangedListener(google.maps.event.addListener(circle, 'center_changed', onCenterChanged));
            }
            if (onRadiusChanged) {
                setRadiusChangedListener(google.maps.event.addListener(circle, 'radius_changed', onRadiusChanged));
            }
            setInstance(circle);
            if (onLoad) {
                onLoad(circle);
            }
            return ({
                "CircleFunctional.useEffect": ()=>{
                    if (dblclickListener !== null) {
                        google.maps.event.removeListener(dblclickListener);
                    }
                    if (dragendListener !== null) {
                        google.maps.event.removeListener(dragendListener);
                    }
                    if (dragstartListener !== null) {
                        google.maps.event.removeListener(dragstartListener);
                    }
                    if (mousedownListener !== null) {
                        google.maps.event.removeListener(mousedownListener);
                    }
                    if (mousemoveListener !== null) {
                        google.maps.event.removeListener(mousemoveListener);
                    }
                    if (mouseoutListener !== null) {
                        google.maps.event.removeListener(mouseoutListener);
                    }
                    if (mouseoverListener !== null) {
                        google.maps.event.removeListener(mouseoverListener);
                    }
                    if (mouseupListener !== null) {
                        google.maps.event.removeListener(mouseupListener);
                    }
                    if (rightclickListener !== null) {
                        google.maps.event.removeListener(rightclickListener);
                    }
                    if (clickListener !== null) {
                        google.maps.event.removeListener(clickListener);
                    }
                    if (centerChangedListener !== null) {
                        google.maps.event.removeListener(centerChangedListener);
                    }
                    if (radiusChangedListener !== null) {
                        google.maps.event.removeListener(radiusChangedListener);
                    }
                    if (onUnmount) {
                        onUnmount(circle);
                    }
                    circle.setMap(null);
                }
            })["CircleFunctional.useEffect"];
        }
    }["CircleFunctional.useEffect"], []);
    return null;
}
var CircleF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(CircleFunctional);
class Circle extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "state", {
            circle: null
        });
        _defineProperty(this, "setCircleCallback", ()=>{
            if (this.state.circle !== null && this.props.onLoad) {
                this.props.onLoad(this.state.circle);
            }
        });
    }
    componentDidMount() {
        var circle = new google.maps.Circle(_objectSpread$6(_objectSpread$6({}, this.props.options), {}, {
            map: this.context
        }));
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$8,
            eventMap: eventMap$8,
            prevProps: {},
            nextProps: this.props,
            instance: circle
        });
        this.setState(function setCircle() {
            return {
                circle
            };
        }, this.setCircleCallback);
    }
    componentDidUpdate(prevProps) {
        if (this.state.circle !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$8,
                eventMap: eventMap$8,
                prevProps,
                nextProps: this.props,
                instance: this.state.circle
            });
        }
    }
    componentWillUnmount() {
        if (this.state.circle !== null) {
            var _this$state$circle;
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.circle);
            }
            unregisterEvents(this.registeredEvents);
            (_this$state$circle = this.state.circle) === null || _this$state$circle === void 0 || _this$state$circle.setMap(null);
        }
    }
    render() {
        return null;
    }
}
_defineProperty(Circle, "contextType", MapContext);
function ownKeys$5(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$5(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$5(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$5(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
var eventMap$7 = {
    onClick: 'click',
    onDblClick: 'dblclick',
    onMouseDown: 'mousedown',
    onMouseOut: 'mouseout',
    onMouseOver: 'mouseover',
    onMouseUp: 'mouseup',
    onRightClick: 'rightclick',
    onAddFeature: 'addfeature',
    onRemoveFeature: 'removefeature',
    onRemoveProperty: 'removeproperty',
    onSetGeometry: 'setgeometry',
    onSetProperty: 'setproperty'
};
var updaterMap$7 = {
    add (instance, feature) {
        instance.add(feature);
    },
    addgeojson (instance, geojson, options) {
        instance.addGeoJson(geojson, options);
    },
    contains (instance, feature) {
        instance.contains(feature);
    },
    foreach (instance, callback) {
        instance.forEach(callback);
    },
    loadgeojson (instance, url, options, callback) {
        instance.loadGeoJson(url, options, callback);
    },
    overridestyle (instance, feature, style) {
        instance.overrideStyle(feature, style);
    },
    remove (instance, feature) {
        instance.remove(feature);
    },
    revertstyle (instance, feature) {
        instance.revertStyle(feature);
    },
    controlposition (instance, controlPosition) {
        instance.setControlPosition(controlPosition);
    },
    controls (instance, controls) {
        instance.setControls(controls);
    },
    drawingmode (instance, mode) {
        instance.setDrawingMode(mode);
    },
    map (instance, map) {
        instance.setMap(map);
    },
    style (instance, style) {
        instance.setStyle(style);
    },
    togeojson (instance, callback) {
        instance.toGeoJson(callback);
    }
};
function DataFunctional(_ref) {
    var { options, onClick, onDblClick, onMouseDown, onMouseMove, onMouseOut, onMouseOver, onMouseUp, onRightClick, onAddFeature, onRemoveFeature, onRemoveProperty, onSetGeometry, onSetProperty, onLoad, onUnmount } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [dblclickListener, setDblclickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mousedownListener, setMousedownListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mousemoveListener, setMousemoveListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoutListener, setMouseoutListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseoverListener, setMouseoverListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [mouseupListener, setMouseupListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [rightclickListener, setRightclickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [clickListener, setClickListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [addFeatureListener, setAddFeatureListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [removeFeatureListener, setRemoveFeatureListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [removePropertyListener, setRemovePropertyListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [setGeometryListener, setSetGeometryListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    var [setPropertyListener, setSetPropertyListener] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance !== null) {
                instance.setMap(map);
            }
        }
    }["DataFunctional.useEffect"], [
        map
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance && onDblClick) {
                if (dblclickListener !== null) {
                    google.maps.event.removeListener(dblclickListener);
                }
                setDblclickListener(google.maps.event.addListener(instance, 'dblclick', onDblClick));
            }
        }
    }["DataFunctional.useEffect"], [
        onDblClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance && onMouseDown) {
                if (mousedownListener !== null) {
                    google.maps.event.removeListener(mousedownListener);
                }
                setMousedownListener(google.maps.event.addListener(instance, 'mousedown', onMouseDown));
            }
        }
    }["DataFunctional.useEffect"], [
        onMouseDown
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance && onMouseMove) {
                if (mousemoveListener !== null) {
                    google.maps.event.removeListener(mousemoveListener);
                }
                setMousemoveListener(google.maps.event.addListener(instance, 'mousemove', onMouseMove));
            }
        }
    }["DataFunctional.useEffect"], [
        onMouseMove
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance && onMouseOut) {
                if (mouseoutListener !== null) {
                    google.maps.event.removeListener(mouseoutListener);
                }
                setMouseoutListener(google.maps.event.addListener(instance, 'mouseout', onMouseOut));
            }
        }
    }["DataFunctional.useEffect"], [
        onMouseOut
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance && onMouseOver) {
                if (mouseoverListener !== null) {
                    google.maps.event.removeListener(mouseoverListener);
                }
                setMouseoverListener(google.maps.event.addListener(instance, 'mouseover', onMouseOver));
            }
        }
    }["DataFunctional.useEffect"], [
        onMouseOver
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance && onMouseUp) {
                if (mouseupListener !== null) {
                    google.maps.event.removeListener(mouseupListener);
                }
                setMouseupListener(google.maps.event.addListener(instance, 'mouseup', onMouseUp));
            }
        }
    }["DataFunctional.useEffect"], [
        onMouseUp
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance && onRightClick) {
                if (rightclickListener !== null) {
                    google.maps.event.removeListener(rightclickListener);
                }
                setRightclickListener(google.maps.event.addListener(instance, 'rightclick', onRightClick));
            }
        }
    }["DataFunctional.useEffect"], [
        onRightClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance && onClick) {
                if (clickListener !== null) {
                    google.maps.event.removeListener(clickListener);
                }
                setClickListener(google.maps.event.addListener(instance, 'click', onClick));
            }
        }
    }["DataFunctional.useEffect"], [
        onClick
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance && onAddFeature) {
                if (addFeatureListener !== null) {
                    google.maps.event.removeListener(addFeatureListener);
                }
                setAddFeatureListener(google.maps.event.addListener(instance, 'addfeature', onAddFeature));
            }
        }
    }["DataFunctional.useEffect"], [
        onAddFeature
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance && onRemoveFeature) {
                if (removeFeatureListener !== null) {
                    google.maps.event.removeListener(removeFeatureListener);
                }
                setRemoveFeatureListener(google.maps.event.addListener(instance, 'removefeature', onRemoveFeature));
            }
        }
    }["DataFunctional.useEffect"], [
        onRemoveFeature
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance && onRemoveProperty) {
                if (removePropertyListener !== null) {
                    google.maps.event.removeListener(removePropertyListener);
                }
                setRemovePropertyListener(google.maps.event.addListener(instance, 'removeproperty', onRemoveProperty));
            }
        }
    }["DataFunctional.useEffect"], [
        onRemoveProperty
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance && onSetGeometry) {
                if (setGeometryListener !== null) {
                    google.maps.event.removeListener(setGeometryListener);
                }
                setSetGeometryListener(google.maps.event.addListener(instance, 'setgeometry', onSetGeometry));
            }
        }
    }["DataFunctional.useEffect"], [
        onSetGeometry
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (instance && onSetProperty) {
                if (setPropertyListener !== null) {
                    google.maps.event.removeListener(setPropertyListener);
                }
                setSetPropertyListener(google.maps.event.addListener(instance, 'setproperty', onSetProperty));
            }
        }
    }["DataFunctional.useEffect"], [
        onSetProperty
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataFunctional.useEffect": ()=>{
            if (map !== null) {
                var data = new google.maps.Data(_objectSpread$5(_objectSpread$5({}, options), {}, {
                    map
                }));
                if (onDblClick) {
                    setDblclickListener(google.maps.event.addListener(data, 'dblclick', onDblClick));
                }
                if (onMouseDown) {
                    setMousedownListener(google.maps.event.addListener(data, 'mousedown', onMouseDown));
                }
                if (onMouseMove) {
                    setMousemoveListener(google.maps.event.addListener(data, 'mousemove', onMouseMove));
                }
                if (onMouseOut) {
                    setMouseoutListener(google.maps.event.addListener(data, 'mouseout', onMouseOut));
                }
                if (onMouseOver) {
                    setMouseoverListener(google.maps.event.addListener(data, 'mouseover', onMouseOver));
                }
                if (onMouseUp) {
                    setMouseupListener(google.maps.event.addListener(data, 'mouseup', onMouseUp));
                }
                if (onRightClick) {
                    setRightclickListener(google.maps.event.addListener(data, 'rightclick', onRightClick));
                }
                if (onClick) {
                    setClickListener(google.maps.event.addListener(data, 'click', onClick));
                }
                if (onAddFeature) {
                    setAddFeatureListener(google.maps.event.addListener(data, 'addfeature', onAddFeature));
                }
                if (onRemoveFeature) {
                    setRemoveFeatureListener(google.maps.event.addListener(data, 'removefeature', onRemoveFeature));
                }
                if (onRemoveProperty) {
                    setRemovePropertyListener(google.maps.event.addListener(data, 'removeproperty', onRemoveProperty));
                }
                if (onSetGeometry) {
                    setSetGeometryListener(google.maps.event.addListener(data, 'setgeometry', onSetGeometry));
                }
                if (onSetProperty) {
                    setSetPropertyListener(google.maps.event.addListener(data, 'setproperty', onSetProperty));
                }
                setInstance(data);
                if (onLoad) {
                    onLoad(data);
                }
            }
            return ({
                "DataFunctional.useEffect": ()=>{
                    if (instance) {
                        if (dblclickListener !== null) {
                            google.maps.event.removeListener(dblclickListener);
                        }
                        if (mousedownListener !== null) {
                            google.maps.event.removeListener(mousedownListener);
                        }
                        if (mousemoveListener !== null) {
                            google.maps.event.removeListener(mousemoveListener);
                        }
                        if (mouseoutListener !== null) {
                            google.maps.event.removeListener(mouseoutListener);
                        }
                        if (mouseoverListener !== null) {
                            google.maps.event.removeListener(mouseoverListener);
                        }
                        if (mouseupListener !== null) {
                            google.maps.event.removeListener(mouseupListener);
                        }
                        if (rightclickListener !== null) {
                            google.maps.event.removeListener(rightclickListener);
                        }
                        if (clickListener !== null) {
                            google.maps.event.removeListener(clickListener);
                        }
                        if (addFeatureListener !== null) {
                            google.maps.event.removeListener(addFeatureListener);
                        }
                        if (removeFeatureListener !== null) {
                            google.maps.event.removeListener(removeFeatureListener);
                        }
                        if (removePropertyListener !== null) {
                            google.maps.event.removeListener(removePropertyListener);
                        }
                        if (setGeometryListener !== null) {
                            google.maps.event.removeListener(setGeometryListener);
                        }
                        if (setPropertyListener !== null) {
                            google.maps.event.removeListener(setPropertyListener);
                        }
                        if (onUnmount) {
                            onUnmount(instance);
                        }
                        instance.setMap(null);
                    }
                }
            })["DataFunctional.useEffect"];
        }
    }["DataFunctional.useEffect"], []);
    return null;
}
var DataF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(DataFunctional);
class Data extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "state", {
            data: null
        });
        _defineProperty(this, "setDataCallback", ()=>{
            if (this.state.data !== null && this.props.onLoad) {
                this.props.onLoad(this.state.data);
            }
        });
    }
    componentDidMount() {
        if (this.context !== null) {
            var data = new google.maps.Data(_objectSpread$5(_objectSpread$5({}, this.props.options), {}, {
                map: this.context
            }));
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$7,
                eventMap: eventMap$7,
                prevProps: {},
                nextProps: this.props,
                instance: data
            });
            this.setState(()=>{
                return {
                    data
                };
            }, this.setDataCallback);
        }
    }
    componentDidUpdate(prevProps) {
        if (this.state.data !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$7,
                eventMap: eventMap$7,
                prevProps,
                nextProps: this.props,
                instance: this.state.data
            });
        }
    }
    componentWillUnmount() {
        if (this.state.data !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.data);
            }
            unregisterEvents(this.registeredEvents);
            if (this.state.data) {
                this.state.data.setMap(null);
            }
        }
    }
    render() {
        return null;
    }
}
_defineProperty(Data, "contextType", MapContext);
function ownKeys$4(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$4(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$4(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$4(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
var eventMap$6 = {
    onClick: 'click',
    onDefaultViewportChanged: 'defaultviewport_changed',
    onStatusChanged: 'status_changed'
};
var updaterMap$6 = {
    options (instance, options) {
        instance.setOptions(options);
    },
    url (instance, url) {
        instance.setUrl(url);
    },
    zIndex (instance, zIndex) {
        instance.setZIndex(zIndex);
    }
};
class KmlLayer extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "state", {
            kmlLayer: null
        });
        _defineProperty(this, "setKmlLayerCallback", ()=>{
            if (this.state.kmlLayer !== null && this.props.onLoad) {
                this.props.onLoad(this.state.kmlLayer);
            }
        });
    }
    componentDidMount() {
        var kmlLayer = new google.maps.KmlLayer(_objectSpread$4(_objectSpread$4({}, this.props.options), {}, {
            map: this.context
        }));
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$6,
            eventMap: eventMap$6,
            prevProps: {},
            nextProps: this.props,
            instance: kmlLayer
        });
        this.setState(function setLmlLayer() {
            return {
                kmlLayer
            };
        }, this.setKmlLayerCallback);
    }
    componentDidUpdate(prevProps) {
        if (this.state.kmlLayer !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$6,
                eventMap: eventMap$6,
                prevProps,
                nextProps: this.props,
                instance: this.state.kmlLayer
            });
        }
    }
    componentWillUnmount() {
        if (this.state.kmlLayer !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.kmlLayer);
            }
            unregisterEvents(this.registeredEvents);
            this.state.kmlLayer.setMap(null);
        }
    }
    render() {
        return null;
    }
}
_defineProperty(KmlLayer, "contextType", MapContext);
function getOffsetOverride(containerElement, getPixelPositionOffset) {
    return typeof getPixelPositionOffset === 'function' ? getPixelPositionOffset(containerElement.offsetWidth, containerElement.offsetHeight) : {
        x: 0,
        y: 0
    };
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function createLatLng(inst, Type) {
    return new Type(inst.lat, inst.lng);
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function createLatLngBounds(inst, Type) {
    return new Type(new google.maps.LatLng(inst.ne.lat, inst.ne.lng), new google.maps.LatLng(inst.sw.lat, inst.sw.lng));
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function ensureOfType(inst, // eslint-disable-next-line @typescript-eslint/no-explicit-any
type, // eslint-disable-next-line @typescript-eslint/no-explicit-any
factory) {
    return inst instanceof type ? inst : factory(inst, type);
}
function ensureOfTypeBounds(inst, // eslint-disable-next-line @typescript-eslint/no-explicit-any
type, // eslint-disable-next-line @typescript-eslint/no-explicit-any
factory) {
    return inst instanceof type ? inst : factory(inst, type);
}
function getLayoutStylesByBounds(mapCanvasProjection, offset, bounds) {
    var ne = mapCanvasProjection && mapCanvasProjection.fromLatLngToDivPixel(bounds.getNorthEast());
    var sw = mapCanvasProjection && mapCanvasProjection.fromLatLngToDivPixel(bounds.getSouthWest());
    if (ne && sw) {
        return {
            left: "".concat(sw.x + offset.x, "px"),
            top: "".concat(ne.y + offset.y, "px"),
            width: "".concat(ne.x - sw.x - offset.x, "px"),
            height: "".concat(sw.y - ne.y - offset.y, "px")
        };
    }
    return {
        left: '-9999px',
        top: '-9999px'
    };
}
function getLayoutStylesByPosition(mapCanvasProjection, offset, position) {
    var point = mapCanvasProjection && mapCanvasProjection.fromLatLngToDivPixel(position);
    if (point) {
        var { x, y } = point;
        return {
            left: "".concat(x + offset.x, "px"),
            top: "".concat(y + offset.y, "px")
        };
    }
    return {
        left: '-9999px',
        top: '-9999px'
    };
}
function getLayoutStyles(mapCanvasProjection, offset, bounds, position) {
    return bounds !== undefined ? getLayoutStylesByBounds(mapCanvasProjection, offset, ensureOfTypeBounds(bounds, google.maps.LatLngBounds, createLatLngBounds)) : getLayoutStylesByPosition(mapCanvasProjection, offset, ensureOfType(position, google.maps.LatLng, createLatLng));
}
function arePositionsEqual(currentPosition, previousPosition) {
    return currentPosition.left === previousPosition.left && currentPosition.top === previousPosition.top && currentPosition.width === previousPosition.height && currentPosition.height === previousPosition.height;
}
function ownKeys$3(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$3(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$3(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$3(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
function createOverlay(container, pane, position, bounds, getPixelPositionOffset) {
    class Overlay extends google.maps.OverlayView {
        constructor(container, pane, position, bounds){
            super();
            this.container = container;
            this.pane = pane;
            this.position = position;
            this.bounds = bounds;
        }
        onAdd() {
            var _this$getPanes;
            var pane = (_this$getPanes = this.getPanes()) === null || _this$getPanes === void 0 ? void 0 : _this$getPanes[this.pane];
            pane === null || pane === void 0 || pane.appendChild(this.container);
        }
        draw() {
            var projection = this.getProjection();
            var offset = _objectSpread$3({}, this.container ? getOffsetOverride(this.container, getPixelPositionOffset) : {
                x: 0,
                y: 0
            });
            var layoutStyles = getLayoutStyles(projection, offset, this.bounds, this.position);
            for (var [key, value] of Object.entries(layoutStyles)){
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                this.container.style[key] = value;
            }
        }
        onRemove() {
            if (this.container.parentNode !== null) {
                this.container.parentNode.removeChild(this.container);
            }
        }
    }
    return new Overlay(container, pane, position, bounds);
}
function ownKeys$2(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$2(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$2(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$2(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
function convertToLatLngString(latLngLike) {
    if (!latLngLike) {
        return '';
    }
    var latLng = latLngLike instanceof google.maps.LatLng ? latLngLike : new google.maps.LatLng(latLngLike.lat, latLngLike.lng);
    return latLng + '';
}
function convertToLatLngBoundsString(latLngBoundsLike) {
    if (!latLngBoundsLike) {
        return '';
    }
    var latLngBounds = latLngBoundsLike instanceof google.maps.LatLngBounds ? latLngBoundsLike : new google.maps.LatLngBounds(new google.maps.LatLng(latLngBoundsLike.south, latLngBoundsLike.east), new google.maps.LatLng(latLngBoundsLike.north, latLngBoundsLike.west));
    return latLngBounds + '';
}
var FLOAT_PANE = "floatPane";
var MAP_PANE = "mapPane";
var MARKER_LAYER = "markerLayer";
var OVERLAY_LAYER = "overlayLayer";
var OVERLAY_MOUSE_TARGET = "overlayMouseTarget";
function OverlayViewFunctional(_ref) {
    var { position, bounds, mapPaneName, zIndex, onLoad, onUnmount, getPixelPositionOffset, children } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var container = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "OverlayViewFunctional.useMemo[container]": ()=>{
            var div = document.createElement('div');
            div.style.position = 'absolute';
            return div;
        }
    }["OverlayViewFunctional.useMemo[container]"], []);
    var overlay = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "OverlayViewFunctional.useMemo[overlay]": ()=>{
            return createOverlay(container, mapPaneName, position, bounds, getPixelPositionOffset);
        }
    }["OverlayViewFunctional.useMemo[overlay]"], [
        container,
        mapPaneName,
        position,
        bounds
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "OverlayViewFunctional.useEffect": ()=>{
            onLoad === null || onLoad === void 0 || onLoad(overlay);
            overlay === null || overlay === void 0 || overlay.setMap(map);
            return ({
                "OverlayViewFunctional.useEffect": ()=>{
                    onUnmount === null || onUnmount === void 0 || onUnmount(overlay);
                    overlay === null || overlay === void 0 || overlay.setMap(null);
                }
            })["OverlayViewFunctional.useEffect"];
        }
    }["OverlayViewFunctional.useEffect"], [
        map,
        overlay
    ]);
    // to move the container to the foreground and background
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "OverlayViewFunctional.useEffect": ()=>{
            container.style.zIndex = "".concat(zIndex);
        }
    }["OverlayViewFunctional.useEffect"], [
        zIndex,
        container
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPortal"])(children, container);
}
var OverlayViewF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(OverlayViewFunctional);
class OverlayView extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(props){
        super(props);
        _defineProperty(this, "state", {
            paneEl: null,
            containerStyle: {
                // set initial position
                position: 'absolute'
            }
        });
        _defineProperty(this, "updatePane", ()=>{
            var mapPaneName = this.props.mapPaneName;
            // https://developers.google.com/maps/documentation/javascript/3.exp/reference#MapPanes
            var mapPanes = this.overlayView.getPanes();
            invariant(!!mapPaneName, "OverlayView requires props.mapPaneName but got %s", mapPaneName);
            if (mapPanes) {
                this.setState({
                    paneEl: mapPanes[mapPaneName]
                });
            } else {
                this.setState({
                    paneEl: null
                });
            }
        });
        _defineProperty(this, "onAdd", ()=>{
            var _this$props$onLoad, _this$props;
            this.updatePane();
            (_this$props$onLoad = (_this$props = this.props).onLoad) === null || _this$props$onLoad === void 0 || _this$props$onLoad.call(_this$props, this.overlayView);
        });
        _defineProperty(this, "onPositionElement", ()=>{
            var mapCanvasProjection = this.overlayView.getProjection();
            var offset = _objectSpread$2({
                x: 0,
                y: 0
            }, this.containerRef.current ? getOffsetOverride(this.containerRef.current, this.props.getPixelPositionOffset) : {});
            var layoutStyles = getLayoutStyles(mapCanvasProjection, offset, this.props.bounds, this.props.position);
            if (!arePositionsEqual(layoutStyles, {
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                left: this.state.containerStyle.left,
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                top: this.state.containerStyle.top,
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                width: this.state.containerStyle.width,
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                height: this.state.containerStyle.height
            })) {
                var _layoutStyles$top, _layoutStyles$left, _layoutStyles$width, _layoutStyles$height;
                this.setState({
                    containerStyle: {
                        top: (_layoutStyles$top = layoutStyles.top) !== null && _layoutStyles$top !== void 0 ? _layoutStyles$top : 0,
                        left: (_layoutStyles$left = layoutStyles.left) !== null && _layoutStyles$left !== void 0 ? _layoutStyles$left : 0,
                        width: (_layoutStyles$width = layoutStyles.width) !== null && _layoutStyles$width !== void 0 ? _layoutStyles$width : 0,
                        height: (_layoutStyles$height = layoutStyles.height) !== null && _layoutStyles$height !== void 0 ? _layoutStyles$height : 0,
                        position: 'absolute'
                    }
                });
            }
        });
        _defineProperty(this, "draw", ()=>{
            this.onPositionElement();
        });
        _defineProperty(this, "onRemove", ()=>{
            var _this$props$onUnmount, _this$props2;
            this.setState(()=>({
                    paneEl: null
                }));
            (_this$props$onUnmount = (_this$props2 = this.props).onUnmount) === null || _this$props$onUnmount === void 0 || _this$props$onUnmount.call(_this$props2, this.overlayView);
        });
        this.containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createRef"])();
        // You must implement three methods: onAdd(), draw(), and onRemove().
        var overlayView = new google.maps.OverlayView();
        overlayView.onAdd = this.onAdd;
        overlayView.draw = this.draw;
        overlayView.onRemove = this.onRemove;
        this.overlayView = overlayView;
    }
    componentDidMount() {
        this.overlayView.setMap(this.context);
    }
    componentDidUpdate(prevProps) {
        var prevPositionString = convertToLatLngString(prevProps.position);
        var positionString = convertToLatLngString(this.props.position);
        var prevBoundsString = convertToLatLngBoundsString(prevProps.bounds);
        var boundsString = convertToLatLngBoundsString(this.props.bounds);
        if (prevPositionString !== positionString || prevBoundsString !== boundsString) {
            this.overlayView.draw();
        }
        if (prevProps.mapPaneName !== this.props.mapPaneName) {
            this.updatePane();
        }
    }
    componentWillUnmount() {
        this.overlayView.setMap(null);
    }
    render() {
        var paneEl = this.state.paneEl;
        if (paneEl) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPortal"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                ref: this.containerRef,
                style: this.state.containerStyle,
                children: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].only(this.props.children)
            }), paneEl);
        } else {
            return null;
        }
    }
}
_defineProperty(OverlayView, "FLOAT_PANE", "floatPane");
_defineProperty(OverlayView, "MAP_PANE", "mapPane");
_defineProperty(OverlayView, "MARKER_LAYER", "markerLayer");
_defineProperty(OverlayView, "OVERLAY_LAYER", "overlayLayer");
_defineProperty(OverlayView, "OVERLAY_MOUSE_TARGET", "overlayMouseTarget");
_defineProperty(OverlayView, "contextType", MapContext);
function noop() {
    return;
}
function ownKeys$1(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread$1(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys$1(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
var eventMap$5 = {
    onDblClick: 'dblclick',
    onClick: 'click'
};
var updaterMap$5 = {
    opacity (instance, opacity) {
        instance.setOpacity(opacity);
    }
};
function GroundOverlayFunctional(_ref) {
    var { url, bounds, options, visible } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var imageBounds = new google.maps.LatLngBounds(new google.maps.LatLng(bounds.south, bounds.west), new google.maps.LatLng(bounds.north, bounds.east));
    var groundOverlay = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "GroundOverlayFunctional.useMemo[groundOverlay]": ()=>{
            return new google.maps.GroundOverlay(url, imageBounds, options);
        }
    }["GroundOverlayFunctional.useMemo[groundOverlay]"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GroundOverlayFunctional.useEffect": ()=>{
            if (groundOverlay !== null) {
                groundOverlay.setMap(map);
            }
        }
    }["GroundOverlayFunctional.useEffect"], [
        map
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GroundOverlayFunctional.useEffect": ()=>{
            if (typeof url !== 'undefined' && groundOverlay !== null) {
                groundOverlay.set('url', url);
                groundOverlay.setMap(map);
            }
        }
    }["GroundOverlayFunctional.useEffect"], [
        groundOverlay,
        url
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GroundOverlayFunctional.useEffect": ()=>{
            if (typeof visible !== 'undefined' && groundOverlay !== null) {
                groundOverlay.setOpacity(visible ? 1 : 0);
            }
        }
    }["GroundOverlayFunctional.useEffect"], [
        groundOverlay,
        visible
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GroundOverlayFunctional.useEffect": ()=>{
            var newBounds = new google.maps.LatLngBounds(new google.maps.LatLng(bounds.south, bounds.west), new google.maps.LatLng(bounds.north, bounds.east));
            if (typeof bounds !== 'undefined' && groundOverlay !== null) {
                groundOverlay.set('bounds', newBounds);
                groundOverlay.setMap(map);
            }
        }
    }["GroundOverlayFunctional.useEffect"], [
        groundOverlay,
        bounds
    ]);
    return null;
}
var GroundOverlayF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(GroundOverlayFunctional);
class GroundOverlay extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "state", {
            groundOverlay: null
        });
        _defineProperty(this, "setGroundOverlayCallback", ()=>{
            if (this.state.groundOverlay !== null && this.props.onLoad) {
                this.props.onLoad(this.state.groundOverlay);
            }
        });
    }
    componentDidMount() {
        invariant(!!this.props.url || !!this.props.bounds, "For GroundOverlay, url and bounds are passed in to constructor and are immutable after instantiated. This is the behavior of Google Maps JavaScript API v3 ( See https://developers.google.com/maps/documentation/javascript/reference#GroundOverlay) Hence, use the corresponding two props provided by `react-google-maps-api`, url and bounds. In some cases, you'll need the GroundOverlay component to reflect the changes of url and bounds. You can leverage the React's key property to remount the component. Typically, just `key={url}` would serve your need. See https://github.com/tomchentw/react-google-maps/issues/655");
        var groundOverlay = new google.maps.GroundOverlay(this.props.url, this.props.bounds, _objectSpread$1(_objectSpread$1({}, this.props.options), {}, {
            map: this.context
        }));
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$5,
            eventMap: eventMap$5,
            prevProps: {},
            nextProps: this.props,
            instance: groundOverlay
        });
        this.setState(function setGroundOverlay() {
            return {
                groundOverlay
            };
        }, this.setGroundOverlayCallback);
    }
    componentDidUpdate(prevProps) {
        if (this.state.groundOverlay !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$5,
                eventMap: eventMap$5,
                prevProps,
                nextProps: this.props,
                instance: this.state.groundOverlay
            });
        }
    }
    componentWillUnmount() {
        if (this.state.groundOverlay) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.groundOverlay);
            }
            this.state.groundOverlay.setMap(null);
        }
    }
    render() {
        return null;
    }
}
_defineProperty(GroundOverlay, "defaultProps", {
    onLoad: noop
});
_defineProperty(GroundOverlay, "contextType", MapContext);
function ownKeys(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
var eventMap$4 = {};
var updaterMap$4 = {
    data (instance, data) {
        instance.setData(data);
    },
    map (instance, map) {
        instance.setMap(map);
    },
    options (instance, options) {
        instance.setOptions(options);
    }
};
function HeatmapLayerFunctional(_ref) {
    var { data, onLoad, onUnmount, options } = _ref;
    var map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(MapContext);
    var [instance, setInstance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HeatmapLayerFunctional.useEffect": ()=>{
            if (!google.maps.visualization) {
                invariant(!!google.maps.visualization, 'Did you include prop libraries={["visualization"]} in useJsApiScript? %s', google.maps.visualization);
            }
        }
    }["HeatmapLayerFunctional.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HeatmapLayerFunctional.useEffect": ()=>{
            invariant(!!data, 'data property is required in HeatmapLayer %s', data);
        }
    }["HeatmapLayerFunctional.useEffect"], [
        data
    ]);
    // Order does matter
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HeatmapLayerFunctional.useEffect": ()=>{
            if (instance !== null) {
                instance.setMap(map);
            }
        }
    }["HeatmapLayerFunctional.useEffect"], [
        map
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HeatmapLayerFunctional.useEffect": ()=>{
            if (options && instance !== null) {
                instance.setOptions(options);
            }
        }
    }["HeatmapLayerFunctional.useEffect"], [
        instance,
        options
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HeatmapLayerFunctional.useEffect": ()=>{
            var heatmapLayer = new google.maps.visualization.HeatmapLayer(_objectSpread(_objectSpread({}, options), {}, {
                data,
                map
            }));
            setInstance(heatmapLayer);
            if (onLoad) {
                onLoad(heatmapLayer);
            }
            return ({
                "HeatmapLayerFunctional.useEffect": ()=>{
                    if (instance !== null) {
                        if (onUnmount) {
                            onUnmount(instance);
                        }
                        instance.setMap(null);
                    }
                }
            })["HeatmapLayerFunctional.useEffect"];
        }
    }["HeatmapLayerFunctional.useEffect"], []);
    return null;
}
var HeatmapLayerF = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(HeatmapLayerFunctional);
class HeatmapLayer extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "state", {
            heatmapLayer: null
        });
        _defineProperty(this, "setHeatmapLayerCallback", ()=>{
            if (this.state.heatmapLayer !== null && this.props.onLoad) {
                this.props.onLoad(this.state.heatmapLayer);
            }
        });
    }
    componentDidMount() {
        invariant(!!google.maps.visualization, 'Did you include prop libraries={["visualization"]} to <LoadScript />? %s', google.maps.visualization);
        invariant(!!this.props.data, 'data property is required in HeatmapLayer %s', this.props.data);
        var heatmapLayer = new google.maps.visualization.HeatmapLayer(_objectSpread(_objectSpread({}, this.props.options), {}, {
            data: this.props.data,
            map: this.context
        }));
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$4,
            eventMap: eventMap$4,
            prevProps: {},
            nextProps: this.props,
            instance: heatmapLayer
        });
        this.setState(function setHeatmapLayer() {
            return {
                heatmapLayer
            };
        }, this.setHeatmapLayerCallback);
    }
    componentDidUpdate(prevProps) {
        unregisterEvents(this.registeredEvents);
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$4,
            eventMap: eventMap$4,
            prevProps,
            nextProps: this.props,
            instance: this.state.heatmapLayer
        });
    }
    componentWillUnmount() {
        if (this.state.heatmapLayer !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.heatmapLayer);
            }
            unregisterEvents(this.registeredEvents);
            this.state.heatmapLayer.setMap(null);
        }
    }
    render() {
        return null;
    }
}
_defineProperty(HeatmapLayer, "contextType", MapContext);
var eventMap$3 = {
    onCloseClick: 'closeclick',
    onPanoChanged: 'pano_changed',
    onPositionChanged: 'position_changed',
    onPovChanged: 'pov_changed',
    onResize: 'resize',
    onStatusChanged: 'status_changed',
    onVisibleChanged: 'visible_changed',
    onZoomChanged: 'zoom_changed'
};
var updaterMap$3 = {
    register (instance, provider, options) {
        instance.registerPanoProvider(provider, options);
    },
    links (instance, links) {
        instance.setLinks(links);
    },
    motionTracking (instance, motionTracking) {
        instance.setMotionTracking(motionTracking);
    },
    options (instance, options) {
        instance.setOptions(options);
    },
    pano (instance, pano) {
        instance.setPano(pano);
    },
    position (instance, position) {
        instance.setPosition(position);
    },
    pov (instance, pov) {
        instance.setPov(pov);
    },
    visible (instance, visible) {
        instance.setVisible(visible);
    },
    zoom (instance, zoom) {
        instance.setZoom(zoom);
    }
};
class StreetViewPanorama extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "state", {
            streetViewPanorama: null
        });
        _defineProperty(this, "setStreetViewPanoramaCallback", ()=>{
            if (this.state.streetViewPanorama !== null && this.props.onLoad) {
                this.props.onLoad(this.state.streetViewPanorama);
            }
        });
    }
    componentDidMount() {
        var _this$context$getStre, _this$context;
        var streetViewPanorama = (_this$context$getStre = (_this$context = this.context) === null || _this$context === void 0 ? void 0 : _this$context.getStreetView()) !== null && _this$context$getStre !== void 0 ? _this$context$getStre : null;
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$3,
            eventMap: eventMap$3,
            prevProps: {},
            nextProps: this.props,
            instance: streetViewPanorama
        });
        this.setState(()=>{
            return {
                streetViewPanorama
            };
        }, this.setStreetViewPanoramaCallback);
    }
    componentDidUpdate(prevProps) {
        if (this.state.streetViewPanorama !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$3,
                eventMap: eventMap$3,
                prevProps,
                nextProps: this.props,
                instance: this.state.streetViewPanorama
            });
        }
    }
    componentWillUnmount() {
        if (this.state.streetViewPanorama !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.streetViewPanorama);
            }
            unregisterEvents(this.registeredEvents);
            this.state.streetViewPanorama.setVisible(false);
        }
    }
    render() {
        return null;
    }
}
_defineProperty(StreetViewPanorama, "contextType", MapContext);
class StreetViewService extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "state", {
            streetViewService: null
        });
        _defineProperty(this, "setStreetViewServiceCallback", ()=>{
            if (this.state.streetViewService !== null && this.props.onLoad) {
                this.props.onLoad(this.state.streetViewService);
            }
        });
    }
    componentDidMount() {
        var streetViewService = new google.maps.StreetViewService();
        this.setState(function setStreetViewService() {
            return {
                streetViewService
            };
        }, this.setStreetViewServiceCallback);
    }
    componentWillUnmount() {
        if (this.state.streetViewService !== null && this.props.onUnmount) {
            this.props.onUnmount(this.state.streetViewService);
        }
    }
    render() {
        return null;
    }
}
_defineProperty(StreetViewService, "contextType", MapContext);
class DirectionsService extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "state", {
            directionsService: null
        });
        _defineProperty(this, "setDirectionsServiceCallback", ()=>{
            if (this.state.directionsService !== null && this.props.onLoad) {
                this.props.onLoad(this.state.directionsService);
            }
        });
    }
    componentDidMount() {
        invariant(!!this.props.options, 'DirectionsService expected options object as parameter, but got %s', this.props.options);
        var directionsService = new google.maps.DirectionsService();
        this.setState(function setDirectionsService() {
            return {
                directionsService
            };
        }, this.setDirectionsServiceCallback);
    }
    componentDidUpdate() {
        if (this.state.directionsService !== null) {
            this.state.directionsService.route(this.props.options, this.props.callback);
        }
    }
    componentWillUnmount() {
        if (this.state.directionsService !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.directionsService);
            }
        }
    }
    render() {
        return null;
    }
}
var eventMap$2 = {
    onDirectionsChanged: 'directions_changed'
};
var updaterMap$2 = {
    directions (instance, directions) {
        instance.setDirections(directions);
    },
    map (instance, map) {
        instance.setMap(map);
    },
    options (instance, options) {
        instance.setOptions(options);
    },
    panel (instance, panel) {
        instance.setPanel(panel);
    },
    routeIndex (instance, routeIndex) {
        instance.setRouteIndex(routeIndex);
    }
};
class DirectionsRenderer extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "state", {
            directionsRenderer: null
        });
        _defineProperty(this, "setDirectionsRendererCallback", ()=>{
            if (this.state.directionsRenderer !== null) {
                this.state.directionsRenderer.setMap(this.context);
                if (this.props.onLoad) {
                    this.props.onLoad(this.state.directionsRenderer);
                }
            }
        });
    }
    componentDidMount() {
        var directionsRenderer = new google.maps.DirectionsRenderer(this.props.options);
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap: updaterMap$2,
            eventMap: eventMap$2,
            prevProps: {},
            nextProps: this.props,
            instance: directionsRenderer
        });
        this.setState(function setDirectionsRenderer() {
            return {
                directionsRenderer
            };
        }, this.setDirectionsRendererCallback);
    }
    componentDidUpdate(prevProps) {
        if (this.state.directionsRenderer !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$2,
                eventMap: eventMap$2,
                prevProps,
                nextProps: this.props,
                instance: this.state.directionsRenderer
            });
        }
    }
    componentWillUnmount() {
        if (this.state.directionsRenderer !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.directionsRenderer);
            }
            unregisterEvents(this.registeredEvents);
            if (this.state.directionsRenderer) {
                this.state.directionsRenderer.setMap(null);
            }
        }
    }
    render() {
        return null;
    }
}
_defineProperty(DirectionsRenderer, "contextType", MapContext);
class DistanceMatrixService extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "state", {
            distanceMatrixService: null
        });
        _defineProperty(this, "setDistanceMatrixServiceCallback", ()=>{
            if (this.state.distanceMatrixService !== null && this.props.onLoad) {
                this.props.onLoad(this.state.distanceMatrixService);
            }
        });
    }
    componentDidMount() {
        invariant(!!this.props.options, 'DistanceMatrixService expected options object as parameter, but go %s', this.props.options);
        var distanceMatrixService = new google.maps.DistanceMatrixService();
        this.setState(function setDistanceMatrixService() {
            return {
                distanceMatrixService
            };
        }, this.setDistanceMatrixServiceCallback);
    }
    componentDidUpdate() {
        if (this.state.distanceMatrixService !== null) {
            this.state.distanceMatrixService.getDistanceMatrix(this.props.options, this.props.callback);
        }
    }
    componentWillUnmount() {
        if (this.state.distanceMatrixService !== null && this.props.onUnmount) {
            this.props.onUnmount(this.state.distanceMatrixService);
        }
    }
    render() {
        return null;
    }
}
var eventMap$1 = {
    onPlacesChanged: 'places_changed'
};
var updaterMap$1 = {
    bounds (instance, bounds) {
        instance.setBounds(bounds);
    }
};
class StandaloneSearchBox extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "containerElement", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createRef"])());
        _defineProperty(this, "state", {
            searchBox: null
        });
        _defineProperty(this, "setSearchBoxCallback", ()=>{
            if (this.state.searchBox !== null && this.props.onLoad) {
                this.props.onLoad(this.state.searchBox);
            }
        });
    }
    componentDidMount() {
        invariant(!!google.maps.places, 'You need to provide libraries={["places"]} prop to <LoadScript /> component %s', google.maps.places);
        if (this.containerElement !== null && this.containerElement.current !== null) {
            var input = this.containerElement.current.querySelector('input');
            if (input !== null) {
                var searchBox = new google.maps.places.SearchBox(input, this.props.options);
                this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                    updaterMap: updaterMap$1,
                    eventMap: eventMap$1,
                    prevProps: {},
                    nextProps: this.props,
                    instance: searchBox
                });
                this.setState(function setSearchBox() {
                    return {
                        searchBox
                    };
                }, this.setSearchBoxCallback);
            }
        }
    }
    componentDidUpdate(prevProps) {
        if (this.state.searchBox !== null) {
            unregisterEvents(this.registeredEvents);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap: updaterMap$1,
                eventMap: eventMap$1,
                prevProps,
                nextProps: this.props,
                instance: this.state.searchBox
            });
        }
    }
    componentWillUnmount() {
        if (this.state.searchBox !== null) {
            if (this.props.onUnmount) {
                this.props.onUnmount(this.state.searchBox);
            }
            unregisterEvents(this.registeredEvents);
        }
    }
    render() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
            ref: this.containerElement,
            children: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].only(this.props.children)
        });
    }
}
_defineProperty(StandaloneSearchBox, "contextType", MapContext);
var eventMap = {
    onPlaceChanged: 'place_changed'
};
var updaterMap = {
    bounds (instance, bounds) {
        instance.setBounds(bounds);
    },
    restrictions (instance, restrictions) {
        instance.setComponentRestrictions(restrictions);
    },
    fields (instance, fields) {
        instance.setFields(fields);
    },
    options (instance, options) {
        instance.setOptions(options);
    },
    types (instance, types) {
        instance.setTypes(types);
    }
};
class Autocomplete extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PureComponent"] {
    constructor(){
        super(...arguments);
        _defineProperty(this, "registeredEvents", []);
        _defineProperty(this, "containerElement", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createRef"])());
        _defineProperty(this, "state", {
            autocomplete: null
        });
        _defineProperty(this, "setAutocompleteCallback", ()=>{
            if (this.state.autocomplete !== null && this.props.onLoad) {
                this.props.onLoad(this.state.autocomplete);
            }
        });
    }
    componentDidMount() {
        var _this$containerElemen;
        invariant(!!google.maps.places, 'You need to provide libraries={["places"]} prop to <LoadScript /> component %s', google.maps.places);
        // TODO: why current could be equal null?
        var input = (_this$containerElemen = this.containerElement.current) === null || _this$containerElemen === void 0 ? void 0 : _this$containerElemen.querySelector('input');
        if (input) {
            var autocomplete = new google.maps.places.Autocomplete(input, this.props.options);
            this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
                updaterMap,
                eventMap,
                prevProps: {},
                nextProps: this.props,
                instance: autocomplete
            });
            this.setState(()=>{
                return {
                    autocomplete
                };
            }, this.setAutocompleteCallback);
        }
    }
    componentDidUpdate(prevProps) {
        unregisterEvents(this.registeredEvents);
        this.registeredEvents = applyUpdatersToPropsAndRegisterEvents({
            updaterMap,
            eventMap,
            prevProps,
            nextProps: this.props,
            instance: this.state.autocomplete
        });
    }
    componentWillUnmount() {
        if (this.state.autocomplete !== null) {
            unregisterEvents(this.registeredEvents);
        }
    }
    render() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
            ref: this.containerElement,
            className: this.props.className,
            children: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].only(this.props.children)
        });
    }
}
_defineProperty(Autocomplete, "defaultProps", {
    className: ''
});
_defineProperty(Autocomplete, "contextType", MapContext);
;
 //# sourceMappingURL=esm.js.map
}}),
}]);

//# sourceMappingURL=node_modules_9e4ccd89._.js.map