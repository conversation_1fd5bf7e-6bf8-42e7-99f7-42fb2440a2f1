[{"name": "hot-reloader", "duration": 93, "timestamp": 1074509797591, "id": 3, "tags": {"version": "15.3.5"}, "startTime": 1751906509811, "traceId": "f210df389f5bc450"}, {"name": "setup-dev-bundler", "duration": 414802, "timestamp": 1074509676137, "id": 2, "parentId": 1, "tags": {}, "startTime": 1751906509690, "traceId": "f210df389f5bc450"}, {"name": "run-instrumentation-hook", "duration": 11, "timestamp": 1074510116322, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751906510130, "traceId": "f210df389f5bc450"}, {"name": "start-dev-server", "duration": 718579, "timestamp": 1074509403865, "id": 1, "tags": {"cpus": "14", "platform": "darwin", "memory.freeMem": "85344256", "memory.totalMem": "38654705664", "memory.heapSizeLimit": "19377684480", "memory.rss": "297779200", "memory.heapTotal": "97402880", "memory.heapUsed": "75632032"}, "startTime": 1751906509418, "traceId": "f210df389f5bc450"}, {"name": "compile-path", "duration": 1382823, "timestamp": 1074533897320, "id": 7, "tags": {"trigger": "/"}, "startTime": 1751906533911, "traceId": "f210df389f5bc450"}, {"name": "ensure-page", "duration": 1383483, "timestamp": 1074533896964, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1751906533911, "traceId": "f210df389f5bc450"}]