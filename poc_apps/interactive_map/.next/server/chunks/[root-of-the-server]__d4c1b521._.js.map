{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/app/api/logs/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { VehicleLog, EventType, AlertLevel, ApiResponse, PaginatedResponse, TimeFilter } from '@/types';\n\n// Riyadh locations for realistic data\nconst riyadhLocations = [\n  { lat: 24.7136, lng: 46.6753, address: 'King Fahd Road, Riyadh' },\n  { lat: 24.7200, lng: 46.6800, address: 'Olaya District, Riyadh' },\n  { lat: 24.7280, lng: 46.6850, address: 'Al Malaz, Riyadh' },\n  { lat: 24.7350, lng: 46.6900, address: 'King Abdulaziz Road, Riyadh' },\n  { lat: 24.7420, lng: 46.6950, address: 'Diplomatic Quarter, Riyadh' },\n  { lat: 24.7480, lng: 46.7000, address: 'Al Muhammadiyah, Riyadh' },\n  { lat: 24.7540, lng: 46.7050, address: 'Al Naseem, Riyadh' },\n  { lat: 24.7600, lng: 46.7100, address: 'King Khalid Airport Road, Riyadh' },\n  { lat: 24.7660, lng: 46.7150, address: 'Al Rawdah, Riyadh' },\n  { lat: 24.7720, lng: 46.7200, address: 'Northern Ring Road, Riyadh' },\n  { lat: 24.6877, lng: 46.7219, address: 'King Khalid International Airport, Riyadh' },\n  { lat: 24.6333, lng: 46.7167, address: 'Al Diriyah, Riyadh' },\n  { lat: 24.7744, lng: 46.7388, address: 'King Saud University, Riyadh' },\n  { lat: 24.6408, lng: 46.7728, address: 'Riyadh Gallery Mall, Riyadh' },\n  { lat: 24.7311, lng: 46.6753, address: 'Kingdom Centre, Riyadh' }\n];\n\n// Generate mock vehicle logs\nfunction generateMockLogs(): VehicleLog[] {\n  const logs: VehicleLog[] = [];\n  const vehicleIds = ['VH-001', 'VH-002', 'VH-003', 'VH-004', 'VH-005'];\n  const eventTypes: EventType[] = [\n    'position_update', 'alert_triggered', 'route_deviation', 'route_started',\n    'route_completed', 'waypoint_reached', 'emergency_stop', 'speed_violation',\n    'maintenance_due', 'vehicle_stopped', 'vehicle_resumed'\n  ];\n  \n  const statusOptions = ['on-route', 'off-route', 'stopped'] as const;\n  const alertLevels: AlertLevel[] = ['low', 'medium', 'high', 'critical'];\n\n  const eventMessages = {\n    position_update: 'Vehicle position updated',\n    alert_triggered: 'Alert triggered for vehicle',\n    route_deviation: 'Vehicle deviated from planned route',\n    route_started: 'Vehicle started route journey',\n    route_completed: 'Vehicle completed route successfully',\n    waypoint_reached: 'Vehicle reached designated waypoint',\n    emergency_stop: 'Emergency stop activated',\n    speed_violation: 'Speed limit violation detected',\n    maintenance_due: 'Vehicle maintenance is due',\n    vehicle_stopped: 'Vehicle stopped at location',\n    vehicle_resumed: 'Vehicle resumed journey'\n  };\n\n  // Generate logs for the past 7 days\n  const now = new Date();\n  for (let i = 0; i < 500; i++) {\n    const daysAgo = Math.floor(Math.random() * 7);\n    const hoursAgo = Math.floor(Math.random() * 24);\n    const minutesAgo = Math.floor(Math.random() * 60);\n    \n    const timestamp = new Date(now);\n    timestamp.setDate(timestamp.getDate() - daysAgo);\n    timestamp.setHours(timestamp.getHours() - hoursAgo);\n    timestamp.setMinutes(timestamp.getMinutes() - minutesAgo);\n\n    const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];\n    const vehicleId = vehicleIds[Math.floor(Math.random() * vehicleIds.length)];\n    const location = riyadhLocations[Math.floor(Math.random() * riyadhLocations.length)];\n    const status = statusOptions[Math.floor(Math.random() * statusOptions.length)];\n    \n    // Only add alert level for certain event types\n    const needsAlertLevel = ['alert_triggered', 'route_deviation', 'emergency_stop', 'speed_violation', 'maintenance_due'];\n    const alertLevel = needsAlertLevel.includes(eventType) \n      ? alertLevels[Math.floor(Math.random() * alertLevels.length)]\n      : undefined;\n\n    logs.push({\n      id: `log-${i + 1}`,\n      timestamp,\n      vehicleId,\n      eventType,\n      location: { lat: location.lat, lng: location.lng },\n      address: location.address,\n      status,\n      alertLevel,\n      message: eventMessages[eventType],\n      metadata: {\n        speed: eventType === 'speed_violation' ? Math.floor(Math.random() * 50) + 80 : Math.floor(Math.random() * 60) + 20,\n        fuel: Math.floor(Math.random() * 100),\n        driver: `Driver-${Math.floor(Math.random() * 10) + 1}`\n      }\n    });\n  }\n\n  return logs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());\n}\n\n// Filter logs based on parameters\nfunction filterLogs(logs: VehicleLog[], filters: {\n  timeFilter: TimeFilter;\n  vehicleId?: string;\n  eventType?: EventType;\n  status?: string;\n  alertLevel?: AlertLevel;\n  searchQuery?: string;\n}): VehicleLog[] {\n  let filtered = [...logs];\n\n  // Time filter\n  if (filters.timeFilter !== 'all') {\n    const now = new Date();\n    const cutoffTime = new Date(now);\n\n    switch (filters.timeFilter) {\n      case '24h':\n        cutoffTime.setHours(cutoffTime.getHours() - 24);\n        break;\n      case '7d':\n        cutoffTime.setDate(cutoffTime.getDate() - 7);\n        break;\n      case '30d':\n        cutoffTime.setDate(cutoffTime.getDate() - 30);\n        break;\n    }\n\n    filtered = filtered.filter(log => log.timestamp >= cutoffTime);\n  }\n\n  // Vehicle ID filter\n  if (filters.vehicleId) {\n    filtered = filtered.filter(log => log.vehicleId === filters.vehicleId);\n  }\n\n  // Event type filter\n  if (filters.eventType) {\n    filtered = filtered.filter(log => log.eventType === filters.eventType);\n  }\n\n  // Status filter\n  if (filters.status) {\n    filtered = filtered.filter(log => log.status === filters.status);\n  }\n\n  // Alert level filter\n  if (filters.alertLevel) {\n    filtered = filtered.filter(log => log.alertLevel === filters.alertLevel);\n  }\n\n  // Search query filter\n  if (filters.searchQuery) {\n    const query = filters.searchQuery.toLowerCase();\n    filtered = filtered.filter(log => \n      log.vehicleId.toLowerCase().includes(query) ||\n      log.eventType.toLowerCase().includes(query) ||\n      log.message.toLowerCase().includes(query) ||\n      log.address?.toLowerCase().includes(query)\n    );\n  }\n\n  return filtered;\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    \n    // Parse query parameters\n    const timeFilter = (searchParams.get('timeFilter') as TimeFilter) || '7d';\n    const vehicleId = searchParams.get('vehicleId') || undefined;\n    const eventType = searchParams.get('eventType') as EventType || undefined;\n    const status = searchParams.get('status') || undefined;\n    const alertLevel = searchParams.get('alertLevel') as AlertLevel || undefined;\n    const searchQuery = searchParams.get('search') || undefined;\n    const page = parseInt(searchParams.get('page') || '1');\n    const pageSize = parseInt(searchParams.get('pageSize') || '20');\n\n    // Generate and filter logs\n    const allLogs = generateMockLogs();\n    const filteredLogs = filterLogs(allLogs, {\n      timeFilter,\n      vehicleId,\n      eventType,\n      status,\n      alertLevel,\n      searchQuery\n    });\n\n    // Pagination\n    const total = filteredLogs.length;\n    const totalPages = Math.ceil(total / pageSize);\n    const startIndex = (page - 1) * pageSize;\n    const endIndex = startIndex + pageSize;\n    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);\n\n    const response: ApiResponse<PaginatedResponse<VehicleLog>> = {\n      success: true,\n      data: {\n        items: paginatedLogs,\n        total,\n        page,\n        pageSize,\n        totalPages\n      }\n    };\n\n    return NextResponse.json(response);\n  } catch (error) {\n    const response: ApiResponse<null> = {\n      success: false,\n      data: null,\n      error: 'Failed to fetch logs'\n    };\n    return NextResponse.json(response, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGA,sCAAsC;AACtC,MAAM,kBAAkB;IACtB;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAAyB;IAChE;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAAyB;IAChE;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAAmB;IAC1D;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAA8B;IACrE;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAA6B;IACpE;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAA0B;IACjE;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAAoB;IAC3D;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAAmC;IAC1E;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAAoB;IAC3D;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAA6B;IACpE;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAA4C;IACnF;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAAqB;IAC5D;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAA+B;IACtE;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAA8B;IACrE;QAAE,KAAK;QAAS,KAAK;QAAS,SAAS;IAAyB;CACjE;AAED,6BAA6B;AAC7B,SAAS;IACP,MAAM,OAAqB,EAAE;IAC7B,MAAM,aAAa;QAAC;QAAU;QAAU;QAAU;QAAU;KAAS;IACrE,MAAM,aAA0B;QAC9B;QAAmB;QAAmB;QAAmB;QACzD;QAAmB;QAAoB;QAAkB;QACzD;QAAmB;QAAmB;KACvC;IAED,MAAM,gBAAgB;QAAC;QAAY;QAAa;KAAU;IAC1D,MAAM,cAA4B;QAAC;QAAO;QAAU;QAAQ;KAAW;IAEvE,MAAM,gBAAgB;QACpB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,oCAAoC;IACpC,MAAM,MAAM,IAAI;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAC5C,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAE9C,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QACxC,UAAU,QAAQ,CAAC,UAAU,QAAQ,KAAK;QAC1C,UAAU,UAAU,CAAC,UAAU,UAAU,KAAK;QAE9C,MAAM,YAAY,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;QAC3E,MAAM,YAAY,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;QAC3E,MAAM,WAAW,eAAe,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,gBAAgB,MAAM,EAAE;QACpF,MAAM,SAAS,aAAa,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,cAAc,MAAM,EAAE;QAE9E,+CAA+C;QAC/C,MAAM,kBAAkB;YAAC;YAAmB;YAAmB;YAAkB;YAAmB;SAAkB;QACtH,MAAM,aAAa,gBAAgB,QAAQ,CAAC,aACxC,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE,GAC3D;QAEJ,KAAK,IAAI,CAAC;YACR,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG;YAClB;YACA;YACA;YACA,UAAU;gBAAE,KAAK,SAAS,GAAG;gBAAE,KAAK,SAAS,GAAG;YAAC;YACjD,SAAS,SAAS,OAAO;YACzB;YACA;YACA,SAAS,aAAa,CAAC,UAAU;YACjC,UAAU;gBACR,OAAO,cAAc,oBAAoB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAChH,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBACjC,QAAQ,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG;YACxD;QACF;IACF;IAEA,OAAO,KAAK,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;AACxE;AAEA,kCAAkC;AAClC,SAAS,WAAW,IAAkB,EAAE,OAOvC;IACC,IAAI,WAAW;WAAI;KAAK;IAExB,cAAc;IACd,IAAI,QAAQ,UAAU,KAAK,OAAO;QAChC,MAAM,MAAM,IAAI;QAChB,MAAM,aAAa,IAAI,KAAK;QAE5B,OAAQ,QAAQ,UAAU;YACxB,KAAK;gBACH,WAAW,QAAQ,CAAC,WAAW,QAAQ,KAAK;gBAC5C;YACF,KAAK;gBACH,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;gBAC1C;YACF,KAAK;gBACH,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;gBAC1C;QACJ;QAEA,WAAW,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS,IAAI;IACrD;IAEA,oBAAoB;IACpB,IAAI,QAAQ,SAAS,EAAE;QACrB,WAAW,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS,KAAK,QAAQ,SAAS;IACvE;IAEA,oBAAoB;IACpB,IAAI,QAAQ,SAAS,EAAE;QACrB,WAAW,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS,KAAK,QAAQ,SAAS;IACvE;IAEA,gBAAgB;IAChB,IAAI,QAAQ,MAAM,EAAE;QAClB,WAAW,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,QAAQ,MAAM;IACjE;IAEA,qBAAqB;IACrB,IAAI,QAAQ,UAAU,EAAE;QACtB,WAAW,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,KAAK,QAAQ,UAAU;IACzE;IAEA,sBAAsB;IACtB,IAAI,QAAQ,WAAW,EAAE;QACvB,MAAM,QAAQ,QAAQ,WAAW,CAAC,WAAW;QAC7C,WAAW,SAAS,MAAM,CAAC,CAAA,MACzB,IAAI,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,IAAI,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,UACnC,IAAI,OAAO,EAAE,cAAc,SAAS;IAExC;IAEA,OAAO;AACT;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,yBAAyB;QACzB,MAAM,aAAa,AAAC,aAAa,GAAG,CAAC,iBAAgC;QACrE,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QACnD,MAAM,YAAY,aAAa,GAAG,CAAC,gBAA6B;QAChE,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,aAAa,aAAa,GAAG,CAAC,iBAA+B;QACnE,MAAM,cAAc,aAAa,GAAG,CAAC,aAAa;QAClD,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,WAAW,SAAS,aAAa,GAAG,CAAC,eAAe;QAE1D,2BAA2B;QAC3B,MAAM,UAAU;QAChB,MAAM,eAAe,WAAW,SAAS;YACvC;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,aAAa;QACb,MAAM,QAAQ,aAAa,MAAM;QACjC,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;QACrC,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,gBAAgB,aAAa,KAAK,CAAC,YAAY;QAErD,MAAM,WAAuD;YAC3D,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP;gBACA;gBACA;gBACA;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,MAAM,WAA8B;YAClC,SAAS;YACT,MAAM;YACN,OAAO;QACT;QACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD;AACF", "debugId": null}}]}