{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/app/api/alerts/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { Alert, AlertStats, ApiResponse, TimeFilter } from '@/types';\n\n// Mock alert data generator\nfunction generateMockAlerts(): Alert[] {\n  const alerts: Alert[] = [];\n  const vehicleIds = ['VH-001', 'VH-002', 'VH-003', 'VH-004', 'VH-005'];\n  const alertTypes: Alert['type'][] = ['warning', 'error', 'info'];\n  const messages = {\n    warning: [\n      'Vehicle is outside allowed route',\n      'Speed limit exceeded',\n      'Maintenance due soon',\n      'Low fuel warning',\n      'Route deviation detected'\n    ],\n    error: [\n      'Emergency stop activated',\n      'Critical system failure',\n      'GPS signal lost',\n      'Engine malfunction',\n      'Unauthorized access attempt'\n    ],\n    info: [\n      'Route completed successfully',\n      'Waypoint reached',\n      'Vehicle started journey',\n      'Regular maintenance completed',\n      'Driver shift change'\n    ]\n  };\n\n  // Generate alerts for the past 7 days\n  const now = new Date();\n  for (let i = 0; i < 150; i++) {\n    const daysAgo = Math.floor(Math.random() * 7);\n    const hoursAgo = Math.floor(Math.random() * 24);\n    const minutesAgo = Math.floor(Math.random() * 60);\n    \n    const timestamp = new Date(now);\n    timestamp.setDate(timestamp.getDate() - daysAgo);\n    timestamp.setHours(timestamp.getHours() - hoursAgo);\n    timestamp.setMinutes(timestamp.getMinutes() - minutesAgo);\n\n    const type = alertTypes[Math.floor(Math.random() * alertTypes.length)];\n    const vehicleId = vehicleIds[Math.floor(Math.random() * vehicleIds.length)];\n    const messageList = messages[type];\n    const message = messageList[Math.floor(Math.random() * messageList.length)];\n    \n    // 70% chance of being resolved for older alerts\n    const isActive = daysAgo === 0 ? Math.random() > 0.3 : Math.random() > 0.7;\n\n    alerts.push({\n      id: `alert-${i + 1}`,\n      type,\n      message,\n      timestamp,\n      vehicleId,\n      isActive\n    });\n  }\n\n  return alerts.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());\n}\n\n// Filter alerts based on time filter\nfunction filterAlertsByTime(alerts: Alert[], timeFilter: TimeFilter): Alert[] {\n  if (timeFilter === 'all') return alerts;\n\n  const now = new Date();\n  const cutoffTime = new Date(now);\n\n  switch (timeFilter) {\n    case '24h':\n      cutoffTime.setHours(cutoffTime.getHours() - 24);\n      break;\n    case '7d':\n      cutoffTime.setDate(cutoffTime.getDate() - 7);\n      break;\n    case '30d':\n      cutoffTime.setDate(cutoffTime.getDate() - 30);\n      break;\n  }\n\n  return alerts.filter(alert => alert.timestamp >= cutoffTime);\n}\n\n// Calculate alert statistics\nfunction calculateAlertStats(alerts: Alert[]): AlertStats {\n  const stats: AlertStats = {\n    total: alerts.length,\n    byType: { warning: 0, error: 0, info: 0 },\n    byStatus: { active: 0, resolved: 0 },\n    byLevel: { low: 0, medium: 0, high: 0, critical: 0 }\n  };\n\n  alerts.forEach(alert => {\n    // Count by type\n    stats.byType[alert.type]++;\n    \n    // Count by status\n    if (alert.isActive) {\n      stats.byStatus.active++;\n    } else {\n      stats.byStatus.resolved++;\n    }\n    \n    // Count by level (map type to level)\n    if (alert.type === 'info') {\n      stats.byLevel.low++;\n    } else if (alert.type === 'warning') {\n      stats.byLevel.medium++;\n    } else if (alert.type === 'error') {\n      stats.byLevel.high++;\n    }\n  });\n\n  return stats;\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const timeFilter = (searchParams.get('timeFilter') as TimeFilter) || '7d';\n    const statsOnly = searchParams.get('statsOnly') === 'true';\n\n    // Generate mock data\n    const allAlerts = generateMockAlerts();\n    const filteredAlerts = filterAlertsByTime(allAlerts, timeFilter);\n\n    if (statsOnly) {\n      const stats = calculateAlertStats(filteredAlerts);\n      const response: ApiResponse<AlertStats> = {\n        success: true,\n        data: stats\n      };\n      return NextResponse.json(response);\n    }\n\n    const response: ApiResponse<Alert[]> = {\n      success: true,\n      data: filteredAlerts\n    };\n\n    return NextResponse.json(response);\n  } catch (error) {\n    const response: ApiResponse<null> = {\n      success: false,\n      data: null,\n      error: 'Failed to fetch alerts'\n    };\n    return NextResponse.json(response, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGA,4BAA4B;AAC5B,SAAS;IACP,MAAM,SAAkB,EAAE;IAC1B,MAAM,aAAa;QAAC;QAAU;QAAU;QAAU;QAAU;KAAS;IACrE,MAAM,aAA8B;QAAC;QAAW;QAAS;KAAO;IAChE,MAAM,WAAW;QACf,SAAS;YACP;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;YACL;YACA;YACA;YACA;YACA;SACD;QACD,MAAM;YACJ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,sCAAsC;IACtC,MAAM,MAAM,IAAI;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAC5C,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAE9C,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QACxC,UAAU,QAAQ,CAAC,UAAU,QAAQ,KAAK;QAC1C,UAAU,UAAU,CAAC,UAAU,UAAU,KAAK;QAE9C,MAAM,OAAO,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;QACtE,MAAM,YAAY,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;QAC3E,MAAM,cAAc,QAAQ,CAAC,KAAK;QAClC,MAAM,UAAU,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;QAE3E,gDAAgD;QAChD,MAAM,WAAW,YAAY,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;QAEvE,OAAO,IAAI,CAAC;YACV,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG;YACpB;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;AAC1E;AAEA,qCAAqC;AACrC,SAAS,mBAAmB,MAAe,EAAE,UAAsB;IACjE,IAAI,eAAe,OAAO,OAAO;IAEjC,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAE5B,OAAQ;QACN,KAAK;YACH,WAAW,QAAQ,CAAC,WAAW,QAAQ,KAAK;YAC5C;QACF,KAAK;YACH,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;YAC1C;QACF,KAAK;YACH,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;YAC1C;IACJ;IAEA,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,IAAI;AACnD;AAEA,6BAA6B;AAC7B,SAAS,oBAAoB,MAAe;IAC1C,MAAM,QAAoB;QACxB,OAAO,OAAO,MAAM;QACpB,QAAQ;YAAE,SAAS;YAAG,OAAO;YAAG,MAAM;QAAE;QACxC,UAAU;YAAE,QAAQ;YAAG,UAAU;QAAE;QACnC,SAAS;YAAE,KAAK;YAAG,QAAQ;YAAG,MAAM;YAAG,UAAU;QAAE;IACrD;IAEA,OAAO,OAAO,CAAC,CAAA;QACb,gBAAgB;QAChB,MAAM,MAAM,CAAC,MAAM,IAAI,CAAC;QAExB,kBAAkB;QAClB,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,MAAM;QACvB,OAAO;YACL,MAAM,QAAQ,CAAC,QAAQ;QACzB;QAEA,qCAAqC;QACrC,IAAI,MAAM,IAAI,KAAK,QAAQ;YACzB,MAAM,OAAO,CAAC,GAAG;QACnB,OAAO,IAAI,MAAM,IAAI,KAAK,WAAW;YACnC,MAAM,OAAO,CAAC,MAAM;QACtB,OAAO,IAAI,MAAM,IAAI,KAAK,SAAS;YACjC,MAAM,OAAO,CAAC,IAAI;QACpB;IACF;IAEA,OAAO;AACT;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,aAAa,AAAC,aAAa,GAAG,CAAC,iBAAgC;QACrE,MAAM,YAAY,aAAa,GAAG,CAAC,iBAAiB;QAEpD,qBAAqB;QACrB,MAAM,YAAY;QAClB,MAAM,iBAAiB,mBAAmB,WAAW;QAErD,IAAI,WAAW;YACb,MAAM,QAAQ,oBAAoB;YAClC,MAAM,WAAoC;gBACxC,SAAS;gBACT,MAAM;YACR;YACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,MAAM,WAAiC;YACrC,SAAS;YACT,MAAM;QACR;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,MAAM,WAA8B;YAClC,SAAS;YACT,MAAM;YACN,OAAO;QACT;QACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD;AACF", "debugId": null}}]}