module.exports = {

"[project]/.next-internal/server/app/api/analytics/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/analytics/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
// Generate alert trends data for line chart
function generateAlertTrendsData(timeFilter) {
    const data = [];
    const days = timeFilter === '24h' ? 1 : timeFilter === '7d' ? 7 : timeFilter === '30d' ? 30 : 7;
    for(let i = days - 1; i >= 0; i--){
        const date = new Date();
        date.setDate(date.getDate() - i);
        // Generate realistic alert counts with some variation
        const baseWarning = Math.floor(Math.random() * 15) + 5;
        const baseError = Math.floor(Math.random() * 8) + 2;
        const baseInfo = Math.floor(Math.random() * 20) + 10;
        // Add some patterns - more alerts during weekdays
        const isWeekend = date.getDay() === 0 || date.getDay() === 6;
        const multiplier = isWeekend ? 0.6 : 1.2;
        const warning = Math.floor(baseWarning * multiplier);
        const error = Math.floor(baseError * multiplier);
        const info = Math.floor(baseInfo * multiplier);
        data.push({
            date: date.toISOString().split('T')[0],
            warning,
            error,
            info,
            total: warning + error + info
        });
    }
    return data;
}
// Generate vehicle status distribution data for pie chart
function generateVehicleStatusData() {
    const totalVehicles = 5;
    const onRoute = Math.floor(Math.random() * 3) + 2; // 2-4 vehicles
    const offRoute = Math.floor(Math.random() * 2) + 1; // 1-2 vehicles
    const stopped = totalVehicles - onRoute - offRoute;
    return [
        {
            status: 'on-route',
            count: onRoute,
            percentage: onRoute / totalVehicles * 100,
            color: '#10b981'
        },
        {
            status: 'off-route',
            count: offRoute,
            percentage: offRoute / totalVehicles * 100,
            color: '#ef4444'
        },
        {
            status: 'stopped',
            count: stopped,
            percentage: stopped / totalVehicles * 100,
            color: '#f59e0b'
        }
    ];
}
// Generate event frequency data for bar chart
function generateEventFrequencyData(timeFilter) {
    const eventTypes = [
        {
            type: 'position_update',
            label: 'Position Updates',
            color: '#3b82f6'
        },
        {
            type: 'route_started',
            label: 'Route Started',
            color: '#10b981'
        },
        {
            type: 'route_completed',
            label: 'Route Completed',
            color: '#059669'
        },
        {
            type: 'waypoint_reached',
            label: 'Waypoint Reached',
            color: '#06b6d4'
        },
        {
            type: 'alert_triggered',
            label: 'Alert Triggered',
            color: '#f59e0b'
        },
        {
            type: 'route_deviation',
            label: 'Route Deviation',
            color: '#ef4444'
        },
        {
            type: 'speed_violation',
            label: 'Speed Violation',
            color: '#dc2626'
        },
        {
            type: 'emergency_stop',
            label: 'Emergency Stop',
            color: '#991b1b'
        },
        {
            type: 'maintenance_due',
            label: 'Maintenance Due',
            color: '#7c3aed'
        },
        {
            type: 'vehicle_stopped',
            label: 'Vehicle Stopped',
            color: '#6b7280'
        },
        {
            type: 'vehicle_resumed',
            label: 'Vehicle Resumed',
            color: '#16a34a'
        }
    ];
    // Adjust counts based on time filter
    const multiplier = timeFilter === '24h' ? 0.1 : timeFilter === '7d' ? 1 : timeFilter === '30d' ? 4 : 1;
    return eventTypes.map((event)=>{
        let baseCount = 0;
        // Different base counts for different event types
        switch(event.type){
            case 'position_update':
                baseCount = Math.floor((Math.random() * 100 + 200) * multiplier);
                break;
            case 'route_started':
            case 'route_completed':
                baseCount = Math.floor((Math.random() * 20 + 30) * multiplier);
                break;
            case 'waypoint_reached':
                baseCount = Math.floor((Math.random() * 50 + 80) * multiplier);
                break;
            case 'alert_triggered':
                baseCount = Math.floor((Math.random() * 15 + 25) * multiplier);
                break;
            case 'route_deviation':
            case 'speed_violation':
                baseCount = Math.floor((Math.random() * 8 + 12) * multiplier);
                break;
            case 'emergency_stop':
                baseCount = Math.floor((Math.random() * 3 + 2) * multiplier);
                break;
            case 'maintenance_due':
                baseCount = Math.floor((Math.random() * 5 + 5) * multiplier);
                break;
            case 'vehicle_stopped':
            case 'vehicle_resumed':
                baseCount = Math.floor((Math.random() * 25 + 35) * multiplier);
                break;
        }
        return {
            eventType: event.type,
            count: Math.max(1, baseCount),
            label: event.label,
            color: event.color
        };
    });
}
// Generate fleet performance metrics
function generateFleetPerformanceMetrics(timeFilter) {
    const multiplier = timeFilter === '24h' ? 0.1 : timeFilter === '7d' ? 1 : timeFilter === '30d' ? 4 : 1;
    return {
        averageRouteCompletionTime: Math.floor(Math.random() * 30 + 45),
        totalDistanceTraveled: Math.floor((Math.random() * 500 + 1000) * multiplier),
        fuelEfficiency: Math.round((Math.random() * 2 + 8) * 10) / 10,
        onTimePerformance: Math.floor(Math.random() * 15 + 85),
        totalTrips: Math.floor((Math.random() * 20 + 50) * multiplier),
        averageSpeed: Math.floor(Math.random() * 20 + 45),
        maintenanceAlerts: Math.floor((Math.random() * 5 + 2) * multiplier),
        emergencyStops: Math.floor((Math.random() * 3 + 1) * multiplier)
    };
}
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const timeFilter = searchParams.get('timeFilter') || '7d';
        const dataType = searchParams.get('type') || 'all';
        let responseData = {};
        switch(dataType){
            case 'trends':
                responseData = generateAlertTrendsData(timeFilter);
                break;
            case 'status':
                responseData = generateVehicleStatusData();
                break;
            case 'events':
                responseData = generateEventFrequencyData(timeFilter);
                break;
            case 'metrics':
                responseData = generateFleetPerformanceMetrics(timeFilter);
                break;
            case 'all':
            default:
                responseData = {
                    alertTrends: generateAlertTrendsData(timeFilter),
                    vehicleStatus: generateVehicleStatusData(),
                    eventFrequency: generateEventFrequencyData(timeFilter),
                    fleetMetrics: generateFleetPerformanceMetrics(timeFilter)
                };
                break;
        }
        const response = {
            success: true,
            data: responseData,
            timeFilter,
            generatedAt: new Date()
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
    } catch (error) {
        const response = {
            success: false,
            data: null,
            error: 'Failed to fetch analytics data',
            timeFilter: '7d',
            generatedAt: new Date()
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__aa30a4ea._.js.map