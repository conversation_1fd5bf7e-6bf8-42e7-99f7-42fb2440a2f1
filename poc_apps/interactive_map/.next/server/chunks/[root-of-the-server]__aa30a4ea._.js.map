{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/app/api/analytics/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { \n  AlertTrendData, \n  VehicleStatusData, \n  EventFrequencyData, \n  FleetPerformanceMetrics,\n  AnalyticsApiResponse,\n  TimeFilter,\n  EventType \n} from '@/types';\n\n// Generate alert trends data for line chart\nfunction generateAlertTrendsData(timeFilter: TimeFilter): AlertTrendData[] {\n  const data: AlertTrendData[] = [];\n  const days = timeFilter === '24h' ? 1 : timeFilter === '7d' ? 7 : timeFilter === '30d' ? 30 : 7;\n  \n  for (let i = days - 1; i >= 0; i--) {\n    const date = new Date();\n    date.setDate(date.getDate() - i);\n    \n    // Generate realistic alert counts with some variation\n    const baseWarning = Math.floor(Math.random() * 15) + 5;\n    const baseError = Math.floor(Math.random() * 8) + 2;\n    const baseInfo = Math.floor(Math.random() * 20) + 10;\n    \n    // Add some patterns - more alerts during weekdays\n    const isWeekend = date.getDay() === 0 || date.getDay() === 6;\n    const multiplier = isWeekend ? 0.6 : 1.2;\n    \n    const warning = Math.floor(baseWarning * multiplier);\n    const error = Math.floor(baseError * multiplier);\n    const info = Math.floor(baseInfo * multiplier);\n    \n    data.push({\n      date: date.toISOString().split('T')[0],\n      warning,\n      error,\n      info,\n      total: warning + error + info\n    });\n  }\n  \n  return data;\n}\n\n// Generate vehicle status distribution data for pie chart\nfunction generateVehicleStatusData(): VehicleStatusData[] {\n  const totalVehicles = 5;\n  const onRoute = Math.floor(Math.random() * 3) + 2; // 2-4 vehicles\n  const offRoute = Math.floor(Math.random() * 2) + 1; // 1-2 vehicles\n  const stopped = totalVehicles - onRoute - offRoute;\n  \n  return [\n    {\n      status: 'on-route',\n      count: onRoute,\n      percentage: (onRoute / totalVehicles) * 100,\n      color: '#10b981'\n    },\n    {\n      status: 'off-route',\n      count: offRoute,\n      percentage: (offRoute / totalVehicles) * 100,\n      color: '#ef4444'\n    },\n    {\n      status: 'stopped',\n      count: stopped,\n      percentage: (stopped / totalVehicles) * 100,\n      color: '#f59e0b'\n    }\n  ];\n}\n\n// Generate event frequency data for bar chart\nfunction generateEventFrequencyData(timeFilter: TimeFilter): EventFrequencyData[] {\n  const eventTypes: { type: EventType; label: string; color: string }[] = [\n    { type: 'position_update', label: 'Position Updates', color: '#3b82f6' },\n    { type: 'route_started', label: 'Route Started', color: '#10b981' },\n    { type: 'route_completed', label: 'Route Completed', color: '#059669' },\n    { type: 'waypoint_reached', label: 'Waypoint Reached', color: '#06b6d4' },\n    { type: 'alert_triggered', label: 'Alert Triggered', color: '#f59e0b' },\n    { type: 'route_deviation', label: 'Route Deviation', color: '#ef4444' },\n    { type: 'speed_violation', label: 'Speed Violation', color: '#dc2626' },\n    { type: 'emergency_stop', label: 'Emergency Stop', color: '#991b1b' },\n    { type: 'maintenance_due', label: 'Maintenance Due', color: '#7c3aed' },\n    { type: 'vehicle_stopped', label: 'Vehicle Stopped', color: '#6b7280' },\n    { type: 'vehicle_resumed', label: 'Vehicle Resumed', color: '#16a34a' }\n  ];\n  \n  // Adjust counts based on time filter\n  const multiplier = timeFilter === '24h' ? 0.1 : timeFilter === '7d' ? 1 : timeFilter === '30d' ? 4 : 1;\n  \n  return eventTypes.map(event => {\n    let baseCount = 0;\n    \n    // Different base counts for different event types\n    switch (event.type) {\n      case 'position_update':\n        baseCount = Math.floor((Math.random() * 100 + 200) * multiplier);\n        break;\n      case 'route_started':\n      case 'route_completed':\n        baseCount = Math.floor((Math.random() * 20 + 30) * multiplier);\n        break;\n      case 'waypoint_reached':\n        baseCount = Math.floor((Math.random() * 50 + 80) * multiplier);\n        break;\n      case 'alert_triggered':\n        baseCount = Math.floor((Math.random() * 15 + 25) * multiplier);\n        break;\n      case 'route_deviation':\n      case 'speed_violation':\n        baseCount = Math.floor((Math.random() * 8 + 12) * multiplier);\n        break;\n      case 'emergency_stop':\n        baseCount = Math.floor((Math.random() * 3 + 2) * multiplier);\n        break;\n      case 'maintenance_due':\n        baseCount = Math.floor((Math.random() * 5 + 5) * multiplier);\n        break;\n      case 'vehicle_stopped':\n      case 'vehicle_resumed':\n        baseCount = Math.floor((Math.random() * 25 + 35) * multiplier);\n        break;\n    }\n    \n    return {\n      eventType: event.type,\n      count: Math.max(1, baseCount),\n      label: event.label,\n      color: event.color\n    };\n  });\n}\n\n// Generate fleet performance metrics\nfunction generateFleetPerformanceMetrics(timeFilter: TimeFilter): FleetPerformanceMetrics {\n  const multiplier = timeFilter === '24h' ? 0.1 : timeFilter === '7d' ? 1 : timeFilter === '30d' ? 4 : 1;\n  \n  return {\n    averageRouteCompletionTime: Math.floor(Math.random() * 30 + 45), // 45-75 minutes\n    totalDistanceTraveled: Math.floor((Math.random() * 500 + 1000) * multiplier), // km\n    fuelEfficiency: Math.round((Math.random() * 2 + 8) * 10) / 10, // 8-10 km/l\n    onTimePerformance: Math.floor(Math.random() * 15 + 85), // 85-100%\n    totalTrips: Math.floor((Math.random() * 20 + 50) * multiplier),\n    averageSpeed: Math.floor(Math.random() * 20 + 45), // 45-65 km/h\n    maintenanceAlerts: Math.floor((Math.random() * 5 + 2) * multiplier),\n    emergencyStops: Math.floor((Math.random() * 3 + 1) * multiplier)\n  };\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const timeFilter = (searchParams.get('timeFilter') as TimeFilter) || '7d';\n    const dataType = searchParams.get('type') || 'all';\n\n    let responseData: any = {};\n\n    switch (dataType) {\n      case 'trends':\n        responseData = generateAlertTrendsData(timeFilter);\n        break;\n      case 'status':\n        responseData = generateVehicleStatusData();\n        break;\n      case 'events':\n        responseData = generateEventFrequencyData(timeFilter);\n        break;\n      case 'metrics':\n        responseData = generateFleetPerformanceMetrics(timeFilter);\n        break;\n      case 'all':\n      default:\n        responseData = {\n          alertTrends: generateAlertTrendsData(timeFilter),\n          vehicleStatus: generateVehicleStatusData(),\n          eventFrequency: generateEventFrequencyData(timeFilter),\n          fleetMetrics: generateFleetPerformanceMetrics(timeFilter)\n        };\n        break;\n    }\n\n    const response: AnalyticsApiResponse<typeof responseData> = {\n      success: true,\n      data: responseData,\n      timeFilter,\n      generatedAt: new Date()\n    };\n\n    return NextResponse.json(response);\n  } catch (error) {\n    const response: AnalyticsApiResponse<null> = {\n      success: false,\n      data: null,\n      error: 'Failed to fetch analytics data',\n      timeFilter: '7d',\n      generatedAt: new Date()\n    };\n    return NextResponse.json(response, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAWA,4CAA4C;AAC5C,SAAS,wBAAwB,UAAsB;IACrD,MAAM,OAAyB,EAAE;IACjC,MAAM,OAAO,eAAe,QAAQ,IAAI,eAAe,OAAO,IAAI,eAAe,QAAQ,KAAK;IAE9F,IAAK,IAAI,IAAI,OAAO,GAAG,KAAK,GAAG,IAAK;QAClC,MAAM,OAAO,IAAI;QACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;QAE9B,sDAAsD;QACtD,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;QACrD,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;QAClD,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;QAElD,kDAAkD;QAClD,MAAM,YAAY,KAAK,MAAM,OAAO,KAAK,KAAK,MAAM,OAAO;QAC3D,MAAM,aAAa,YAAY,MAAM;QAErC,MAAM,UAAU,KAAK,KAAK,CAAC,cAAc;QACzC,MAAM,QAAQ,KAAK,KAAK,CAAC,YAAY;QACrC,MAAM,OAAO,KAAK,KAAK,CAAC,WAAW;QAEnC,KAAK,IAAI,CAAC;YACR,MAAM,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC;YACA;YACA;YACA,OAAO,UAAU,QAAQ;QAC3B;IACF;IAEA,OAAO;AACT;AAEA,0DAA0D;AAC1D,SAAS;IACP,MAAM,gBAAgB;IACtB,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,GAAG,eAAe;IAClE,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,GAAG,eAAe;IACnE,MAAM,UAAU,gBAAgB,UAAU;IAE1C,OAAO;QACL;YACE,QAAQ;YACR,OAAO;YACP,YAAY,AAAC,UAAU,gBAAiB;YACxC,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,YAAY,AAAC,WAAW,gBAAiB;YACzC,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,YAAY,AAAC,UAAU,gBAAiB;YACxC,OAAO;QACT;KACD;AACH;AAEA,8CAA8C;AAC9C,SAAS,2BAA2B,UAAsB;IACxD,MAAM,aAAkE;QACtE;YAAE,MAAM;YAAmB,OAAO;YAAoB,OAAO;QAAU;QACvE;YAAE,MAAM;YAAiB,OAAO;YAAiB,OAAO;QAAU;QAClE;YAAE,MAAM;YAAmB,OAAO;YAAmB,OAAO;QAAU;QACtE;YAAE,MAAM;YAAoB,OAAO;YAAoB,OAAO;QAAU;QACxE;YAAE,MAAM;YAAmB,OAAO;YAAmB,OAAO;QAAU;QACtE;YAAE,MAAM;YAAmB,OAAO;YAAmB,OAAO;QAAU;QACtE;YAAE,MAAM;YAAmB,OAAO;YAAmB,OAAO;QAAU;QACtE;YAAE,MAAM;YAAkB,OAAO;YAAkB,OAAO;QAAU;QACpE;YAAE,MAAM;YAAmB,OAAO;YAAmB,OAAO;QAAU;QACtE;YAAE,MAAM;YAAmB,OAAO;YAAmB,OAAO;QAAU;QACtE;YAAE,MAAM;YAAmB,OAAO;YAAmB,OAAO;QAAU;KACvE;IAED,qCAAqC;IACrC,MAAM,aAAa,eAAe,QAAQ,MAAM,eAAe,OAAO,IAAI,eAAe,QAAQ,IAAI;IAErG,OAAO,WAAW,GAAG,CAAC,CAAA;QACpB,IAAI,YAAY;QAEhB,kDAAkD;QAClD,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,YAAY,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,IAAI;gBACrD;YACF,KAAK;YACL,KAAK;gBACH,YAAY,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,EAAE,IAAI;gBACnD;YACF,KAAK;gBACH,YAAY,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,EAAE,IAAI;gBACnD;YACF,KAAK;gBACH,YAAY,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,EAAE,IAAI;gBACnD;YACF,KAAK;YACL,KAAK;gBACH,YAAY,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,IAAI,EAAE,IAAI;gBAClD;YACF,KAAK;gBACH,YAAY,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI;gBACjD;YACF,KAAK;gBACH,YAAY,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI;gBACjD;YACF,KAAK;YACL,KAAK;gBACH,YAAY,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,EAAE,IAAI;gBACnD;QACJ;QAEA,OAAO;YACL,WAAW,MAAM,IAAI;YACrB,OAAO,KAAK,GAAG,CAAC,GAAG;YACnB,OAAO,MAAM,KAAK;YAClB,OAAO,MAAM,KAAK;QACpB;IACF;AACF;AAEA,qCAAqC;AACrC,SAAS,gCAAgC,UAAsB;IAC7D,MAAM,aAAa,eAAe,QAAQ,MAAM,eAAe,OAAO,IAAI,eAAe,QAAQ,IAAI;IAErG,OAAO;QACL,4BAA4B,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;QAC5D,uBAAuB,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,MAAM,IAAI,IAAI;QACjE,gBAAgB,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,MAAM;QAC3D,mBAAmB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;QACnD,YAAY,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,KAAK,EAAE,IAAI;QACnD,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;QAC9C,mBAAmB,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI;QACxD,gBAAgB,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI;IACvD;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,aAAa,AAAC,aAAa,GAAG,CAAC,iBAAgC;QACrE,MAAM,WAAW,aAAa,GAAG,CAAC,WAAW;QAE7C,IAAI,eAAoB,CAAC;QAEzB,OAAQ;YACN,KAAK;gBACH,eAAe,wBAAwB;gBACvC;YACF,KAAK;gBACH,eAAe;gBACf;YACF,KAAK;gBACH,eAAe,2BAA2B;gBAC1C;YACF,KAAK;gBACH,eAAe,gCAAgC;gBAC/C;YACF,KAAK;YACL;gBACE,eAAe;oBACb,aAAa,wBAAwB;oBACrC,eAAe;oBACf,gBAAgB,2BAA2B;oBAC3C,cAAc,gCAAgC;gBAChD;gBACA;QACJ;QAEA,MAAM,WAAsD;YAC1D,SAAS;YACT,MAAM;YACN;YACA,aAAa,IAAI;QACnB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,MAAM,WAAuC;YAC3C,SAAS;YACT,MAAM;YACN,OAAO;YACP,YAAY;YACZ,aAAa,IAAI;QACnB;QACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD;AACF", "debugId": null}}]}