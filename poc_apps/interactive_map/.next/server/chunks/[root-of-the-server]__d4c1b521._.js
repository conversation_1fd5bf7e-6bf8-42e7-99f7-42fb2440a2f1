module.exports = {

"[project]/.next-internal/server/app/api/logs/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/logs/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
// Riyadh locations for realistic data
const riyadhLocations = [
    {
        lat: 24.7136,
        lng: 46.6753,
        address: 'King Fahd Road, Riyadh'
    },
    {
        lat: 24.7200,
        lng: 46.6800,
        address: 'Olaya District, Riyadh'
    },
    {
        lat: 24.7280,
        lng: 46.6850,
        address: 'Al Malaz, Riyadh'
    },
    {
        lat: 24.7350,
        lng: 46.6900,
        address: 'King Abdulaziz Road, Riyadh'
    },
    {
        lat: 24.7420,
        lng: 46.6950,
        address: 'Diplomatic Quarter, Riyadh'
    },
    {
        lat: 24.7480,
        lng: 46.7000,
        address: 'Al Muhammadiyah, Riyadh'
    },
    {
        lat: 24.7540,
        lng: 46.7050,
        address: 'Al Naseem, Riyadh'
    },
    {
        lat: 24.7600,
        lng: 46.7100,
        address: 'King Khalid Airport Road, Riyadh'
    },
    {
        lat: 24.7660,
        lng: 46.7150,
        address: 'Al Rawdah, Riyadh'
    },
    {
        lat: 24.7720,
        lng: 46.7200,
        address: 'Northern Ring Road, Riyadh'
    },
    {
        lat: 24.6877,
        lng: 46.7219,
        address: 'King Khalid International Airport, Riyadh'
    },
    {
        lat: 24.6333,
        lng: 46.7167,
        address: 'Al Diriyah, Riyadh'
    },
    {
        lat: 24.7744,
        lng: 46.7388,
        address: 'King Saud University, Riyadh'
    },
    {
        lat: 24.6408,
        lng: 46.7728,
        address: 'Riyadh Gallery Mall, Riyadh'
    },
    {
        lat: 24.7311,
        lng: 46.6753,
        address: 'Kingdom Centre, Riyadh'
    }
];
// Generate mock vehicle logs
function generateMockLogs() {
    const logs = [];
    const vehicleIds = [
        'VH-001',
        'VH-002',
        'VH-003',
        'VH-004',
        'VH-005'
    ];
    const eventTypes = [
        'position_update',
        'alert_triggered',
        'route_deviation',
        'route_started',
        'route_completed',
        'waypoint_reached',
        'emergency_stop',
        'speed_violation',
        'maintenance_due',
        'vehicle_stopped',
        'vehicle_resumed'
    ];
    const statusOptions = [
        'on-route',
        'off-route',
        'stopped'
    ];
    const alertLevels = [
        'low',
        'medium',
        'high',
        'critical'
    ];
    const eventMessages = {
        position_update: 'Vehicle position updated',
        alert_triggered: 'Alert triggered for vehicle',
        route_deviation: 'Vehicle deviated from planned route',
        route_started: 'Vehicle started route journey',
        route_completed: 'Vehicle completed route successfully',
        waypoint_reached: 'Vehicle reached designated waypoint',
        emergency_stop: 'Emergency stop activated',
        speed_violation: 'Speed limit violation detected',
        maintenance_due: 'Vehicle maintenance is due',
        vehicle_stopped: 'Vehicle stopped at location',
        vehicle_resumed: 'Vehicle resumed journey'
    };
    // Generate logs for the past 7 days
    const now = new Date();
    for(let i = 0; i < 500; i++){
        const daysAgo = Math.floor(Math.random() * 7);
        const hoursAgo = Math.floor(Math.random() * 24);
        const minutesAgo = Math.floor(Math.random() * 60);
        const timestamp = new Date(now);
        timestamp.setDate(timestamp.getDate() - daysAgo);
        timestamp.setHours(timestamp.getHours() - hoursAgo);
        timestamp.setMinutes(timestamp.getMinutes() - minutesAgo);
        const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
        const vehicleId = vehicleIds[Math.floor(Math.random() * vehicleIds.length)];
        const location = riyadhLocations[Math.floor(Math.random() * riyadhLocations.length)];
        const status = statusOptions[Math.floor(Math.random() * statusOptions.length)];
        // Only add alert level for certain event types
        const needsAlertLevel = [
            'alert_triggered',
            'route_deviation',
            'emergency_stop',
            'speed_violation',
            'maintenance_due'
        ];
        const alertLevel = needsAlertLevel.includes(eventType) ? alertLevels[Math.floor(Math.random() * alertLevels.length)] : undefined;
        logs.push({
            id: `log-${i + 1}`,
            timestamp,
            vehicleId,
            eventType,
            location: {
                lat: location.lat,
                lng: location.lng
            },
            address: location.address,
            status,
            alertLevel,
            message: eventMessages[eventType],
            metadata: {
                speed: eventType === 'speed_violation' ? Math.floor(Math.random() * 50) + 80 : Math.floor(Math.random() * 60) + 20,
                fuel: Math.floor(Math.random() * 100),
                driver: `Driver-${Math.floor(Math.random() * 10) + 1}`
            }
        });
    }
    return logs.sort((a, b)=>b.timestamp.getTime() - a.timestamp.getTime());
}
// Filter logs based on parameters
function filterLogs(logs, filters) {
    let filtered = [
        ...logs
    ];
    // Time filter
    if (filters.timeFilter !== 'all') {
        const now = new Date();
        const cutoffTime = new Date(now);
        switch(filters.timeFilter){
            case '24h':
                cutoffTime.setHours(cutoffTime.getHours() - 24);
                break;
            case '7d':
                cutoffTime.setDate(cutoffTime.getDate() - 7);
                break;
            case '30d':
                cutoffTime.setDate(cutoffTime.getDate() - 30);
                break;
        }
        filtered = filtered.filter((log)=>log.timestamp >= cutoffTime);
    }
    // Vehicle ID filter
    if (filters.vehicleId) {
        filtered = filtered.filter((log)=>log.vehicleId === filters.vehicleId);
    }
    // Event type filter
    if (filters.eventType) {
        filtered = filtered.filter((log)=>log.eventType === filters.eventType);
    }
    // Status filter
    if (filters.status) {
        filtered = filtered.filter((log)=>log.status === filters.status);
    }
    // Alert level filter
    if (filters.alertLevel) {
        filtered = filtered.filter((log)=>log.alertLevel === filters.alertLevel);
    }
    // Search query filter
    if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase();
        filtered = filtered.filter((log)=>log.vehicleId.toLowerCase().includes(query) || log.eventType.toLowerCase().includes(query) || log.message.toLowerCase().includes(query) || log.address?.toLowerCase().includes(query));
    }
    return filtered;
}
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        // Parse query parameters
        const timeFilter = searchParams.get('timeFilter') || '7d';
        const vehicleId = searchParams.get('vehicleId') || undefined;
        const eventType = searchParams.get('eventType') || undefined;
        const status = searchParams.get('status') || undefined;
        const alertLevel = searchParams.get('alertLevel') || undefined;
        const searchQuery = searchParams.get('search') || undefined;
        const page = parseInt(searchParams.get('page') || '1');
        const pageSize = parseInt(searchParams.get('pageSize') || '20');
        // Generate and filter logs
        const allLogs = generateMockLogs();
        const filteredLogs = filterLogs(allLogs, {
            timeFilter,
            vehicleId,
            eventType,
            status,
            alertLevel,
            searchQuery
        });
        // Pagination
        const total = filteredLogs.length;
        const totalPages = Math.ceil(total / pageSize);
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedLogs = filteredLogs.slice(startIndex, endIndex);
        const response = {
            success: true,
            data: {
                items: paginatedLogs,
                total,
                page,
                pageSize,
                totalPages
            }
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
    } catch (error) {
        const response = {
            success: false,
            data: null,
            error: 'Failed to fetch logs'
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__d4c1b521._.js.map