{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/TrackingMap.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useRef, useState, useEffect } from 'react';\nimport { GoogleMap, LoadScript, <PERSON>yline, Marker } from '@react-google-maps/api';\nimport { TrackingMapProps, MapConfig, Coordinate } from '@/types';\n\n// Default map configuration\nconst defaultMapConfig: MapConfig = {\n  center: { lat: 24.7136, lng: 46.6753 }, // Riyadh, Saudi Arabia\n  zoom: 12,\n  disableDefaultUI: false,\n  zoomControl: true,\n  streetViewControl: false,\n  fullscreenControl: true,\n};\n\n// Google Maps libraries to load\nconst libraries: ('geometry' | 'places' | 'drawing' | 'visualization')[] = ['geometry'];\n\n// Map container style\nconst mapContainerStyle = {\n  width: '100%',\n  height: '100%',\n  minHeight: '500px',\n};\n\nconst TrackingMap: React.FC<TrackingMapProps> = ({\n  route,\n  vehicle,\n  onVehicleUpdate,\n  onAlert,\n  mapConfig = {},\n  geofenceConfig = {},\n}) => {\n  const mapRef = useRef<google.maps.Map | null>(null);\n  const [isMapLoaded, setIsMapLoaded] = useState(false);\n  const [loadError, setLoadError] = useState<string | null>(null);\n  const [isMounted, setIsMounted] = useState(false);\n\n  // Merge default config with provided config\n  const finalMapConfig = { ...defaultMapConfig, ...mapConfig };\n\n  // Get API key from environment\n  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;\n\n  // Ensure component only renders on client side\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  // Handle map load\n  const onMapLoad = useCallback((map: google.maps.Map) => {\n    mapRef.current = map;\n    setIsMapLoaded(true);\n\n    // Fit map to show the entire route\n    if (route.waypoints.length > 0 && window.google) {\n      const bounds = new window.google.maps.LatLngBounds();\n      route.waypoints.forEach(point => {\n        bounds.extend(new window.google.maps.LatLng(point.lat, point.lng));\n      });\n      map.fitBounds(bounds);\n    }\n  }, [route.waypoints]);\n\n  // Handle map unmount\n  const onMapUnmount = useCallback(() => {\n    mapRef.current = null;\n    setIsMapLoaded(false);\n  }, []);\n\n  // Handle load error\n  const onLoadError = useCallback((error: Error) => {\n    console.error('Google Maps load error:', error);\n    setLoadError(error.message);\n  }, []);\n\n  // Calculate route polyline options\n  const routeOptions = {\n    path: route.waypoints,\n    geodesic: true,\n    strokeColor: route.color || '#2563eb',\n    strokeOpacity: 1.0,\n    strokeWeight: route.strokeWeight || 4,\n  };\n\n  // Custom vehicle icon path (truck/car shape)\n  const vehicleIconPath = \"M12 2C13.1 2 14 2.9 14 4V6H18C19.1 6 20 6.9 20 8V14H18.5C18.5 15.9 16.9 17.5 15 17.5S11.5 15.9 11.5 14H8.5C8.5 15.9 6.9 17.5 5 17.5S1.5 15.9 1.5 14H0V8C0 6.9 0.9 6 2 6H6V4C6 2.9 6.9 2 8 2H12M8 4V6H12V4H8M5 12.5C6.4 12.5 7.5 13.6 7.5 15S6.4 17.5 5 17.5 2.5 16.4 2.5 15 3.6 12.5 5 12.5M15 12.5C16.4 12.5 17.5 13.6 17.5 15S16.4 17.5 15 17.5 12.5 16.4 12.5 15 13.6 12.5 15 12.5Z\";\n\n  // Vehicle marker options\n  const vehicleIcon = isMounted && isMapLoaded && typeof window !== 'undefined' && window.google ? {\n    path: vehicleIconPath,\n    scale: 1.5,\n    fillColor: vehicle.isOnRoute ? '#10b981' : '#ef4444',\n    fillOpacity: 1,\n    strokeColor: '#ffffff',\n    strokeWeight: 2,\n    rotation: vehicle.position.heading || 0,\n    anchor: new window.google.maps.Point(10, 10), // Center the icon\n  } : undefined;\n\n  // Show loading state during hydration\n  if (!isMounted) {\n    return (\n      <div className=\"flex items-center justify-center h-full bg-gray-100 rounded-lg\">\n        <div className=\"text-center p-6\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">Initializing map...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state\n  if (!apiKey) {\n    return (\n      <div className=\"flex items-center justify-center h-full bg-gray-100 rounded-lg\">\n        <div className=\"text-center p-6\">\n          <div className=\"text-red-500 text-xl mb-2\">⚠️</div>\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n            Google Maps API Key Missing\n          </h3>\n          <p className=\"text-gray-600 text-sm\">\n            Please add your Google Maps API key to the .env.local file\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  if (loadError) {\n    return (\n      <div className=\"flex items-center justify-center h-full bg-gray-100 rounded-lg\">\n        <div className=\"text-center p-6\">\n          <div className=\"text-red-500 text-xl mb-2\">❌</div>\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n            Map Load Error\n          </h3>\n          <p className=\"text-gray-600 text-sm\">{loadError}</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"w-full h-full relative\">\n      <LoadScript\n        googleMapsApiKey={apiKey}\n        libraries={libraries}\n        onError={onLoadError}\n        loadingElement={\n          <div className=\"flex items-center justify-center h-full bg-gray-100 rounded-lg\">\n            <div className=\"text-center p-6\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n              <p className=\"text-gray-600\">Loading Google Maps...</p>\n            </div>\n          </div>\n        }\n      >\n        <GoogleMap\n          mapContainerStyle={mapContainerStyle}\n          center={finalMapConfig.center}\n          zoom={finalMapConfig.zoom}\n          onLoad={onMapLoad}\n          onUnmount={onMapUnmount}\n          options={{\n            disableDefaultUI: finalMapConfig.disableDefaultUI,\n            zoomControl: finalMapConfig.zoomControl,\n            streetViewControl: finalMapConfig.streetViewControl,\n            fullscreenControl: finalMapConfig.fullscreenControl,\n            mapTypeControl: false,\n            scaleControl: true,\n          }}\n        >\n          {/* Route polyline */}\n          {route.waypoints.length > 1 && (\n            <Polyline options={routeOptions} />\n          )}\n\n          {/* Vehicle marker */}\n          {vehicleIcon && (\n            <Marker\n              position={vehicle.position}\n              icon={vehicleIcon}\n              title={`Vehicle ${vehicle.id} - ${vehicle.status}`}\n            />\n          )}\n\n          {/* Route waypoint markers */}\n          {isMounted && isMapLoaded && typeof window !== 'undefined' && window.google && route.waypoints.map((waypoint, index) => (\n            <Marker\n              key={`waypoint-${index}`}\n              position={waypoint}\n              icon={{\n                path: window.google.maps.SymbolPath.CIRCLE,\n                scale: 4,\n                fillColor: index === 0 ? '#10b981' : index === route.waypoints.length - 1 ? '#ef4444' : '#6b7280',\n                fillOpacity: 1,\n                strokeColor: '#ffffff',\n                strokeWeight: 2,\n              }}\n              title={\n                index === 0\n                  ? 'Start Point'\n                  : index === route.waypoints.length - 1\n                    ? 'End Point'\n                    : `Waypoint ${index + 1}`\n              }\n            />\n          ))}\n        </GoogleMap>\n      </LoadScript>\n\n      {/* Map overlay with vehicle status */}\n      <div className=\"absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 z-10\">\n        <div className=\"flex items-center space-x-2\">\n          <div \n            className={`w-3 h-3 rounded-full ${\n              vehicle.isOnRoute ? 'bg-green-500' : 'bg-red-500'\n            }`}\n          />\n          <span className=\"text-sm font-medium\">\n            {vehicle.isOnRoute ? 'On Route' : 'Off Route'}\n          </span>\n        </div>\n        <div className=\"text-xs text-gray-500 mt-1\">\n          Distance from route: {Math.round(vehicle.distanceFromRoute)}m\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TrackingMap;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAMA,4BAA4B;AAC5B,MAAM,mBAA8B;IAClC,QAAQ;QAAE,KAAK;QAAS,KAAK;IAAQ;IACrC,MAAM;IACN,kBAAkB;IAClB,aAAa;IACb,mBAAmB;IACnB,mBAAmB;AACrB;AAEA,gCAAgC;AAChC,MAAM,YAAqE;IAAC;CAAW;AAEvF,sBAAsB;AACtB,MAAM,oBAAoB;IACxB,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAEA,MAAM,cAA0C,CAAC,EAC/C,KAAK,EACL,OAAO,EACP,eAAe,EACf,OAAO,EACP,YAAY,CAAC,CAAC,EACd,iBAAiB,CAAC,CAAC,EACpB;IACC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,4CAA4C;IAC5C,MAAM,iBAAiB;QAAE,GAAG,gBAAgB;QAAE,GAAG,SAAS;IAAC;IAE3D,+BAA+B;IAC/B,MAAM;IAEN,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,kBAAkB;IAClB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,OAAO,OAAO,GAAG;QACjB,eAAe;QAEf,mCAAmC;QACnC,IAAI,MAAM,SAAS,CAAC,MAAM,GAAG,KAAK,OAAO,MAAM,EAAE;YAC/C,MAAM,SAAS,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY;YAClD,MAAM,SAAS,CAAC,OAAO,CAAC,CAAA;gBACtB,OAAO,MAAM,CAAC,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,MAAM,GAAG;YAClE;YACA,IAAI,SAAS,CAAC;QAChB;IACF,GAAG;QAAC,MAAM,SAAS;KAAC;IAEpB,qBAAqB;IACrB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,OAAO,OAAO,GAAG;QACjB,eAAe;IACjB,GAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,QAAQ,KAAK,CAAC,2BAA2B;QACzC,aAAa,MAAM,OAAO;IAC5B,GAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,eAAe;QACnB,MAAM,MAAM,SAAS;QACrB,UAAU;QACV,aAAa,MAAM,KAAK,IAAI;QAC5B,eAAe;QACf,cAAc,MAAM,YAAY,IAAI;IACtC;IAEA,6CAA6C;IAC7C,MAAM,kBAAkB;IAExB,yBAAyB;IACzB,MAAM,cAAc,6EAShB;IAEJ,sCAAsC;IACtC,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,mBAAmB;IACnB,uCAAa;;IAcb;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAA4B;;;;;;kCAC3C,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;;;;;;IAI9C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+JAAA,CAAA,aAAU;gBACT,kBAAkB;gBAClB,WAAW;gBACX,SAAS;gBACT,8BACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;0BAKnC,cAAA,8OAAC,+JAAA,CAAA,YAAS;oBACR,mBAAmB;oBACnB,QAAQ,eAAe,MAAM;oBAC7B,MAAM,eAAe,IAAI;oBACzB,QAAQ;oBACR,WAAW;oBACX,SAAS;wBACP,kBAAkB,eAAe,gBAAgB;wBACjD,aAAa,eAAe,WAAW;wBACvC,mBAAmB,eAAe,iBAAiB;wBACnD,mBAAmB,eAAe,iBAAiB;wBACnD,gBAAgB;wBAChB,cAAc;oBAChB;;wBAGC,MAAM,SAAS,CAAC,MAAM,GAAG,mBACxB,8OAAC,+JAAA,CAAA,WAAQ;4BAAC,SAAS;;;;;;wBAIpB,6BACC,8OAAC,+JAAA,CAAA,SAAM;4BACL,UAAU,QAAQ,QAAQ;4BAC1B,MAAM;4BACN,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,QAAQ,MAAM,EAAE;;;;;;wBAKrD,aAAa,eAAe,gBAAkB,eAAe,OAAO,MAAM,IAAI,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC5G,8OAAC,+JAAA,CAAA,SAAM;gCAEL,UAAU;gCACV,MAAM;oCACJ,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM;oCAC1C,OAAO;oCACP,WAAW,UAAU,IAAI,YAAY,UAAU,MAAM,SAAS,CAAC,MAAM,GAAG,IAAI,YAAY;oCACxF,aAAa;oCACb,aAAa;oCACb,cAAc;gCAChB;gCACA,OACE,UAAU,IACN,gBACA,UAAU,MAAM,SAAS,CAAC,MAAM,GAAG,IACjC,cACA,CAAC,SAAS,EAAE,QAAQ,GAAG;+BAf1B,CAAC,SAAS,EAAE,OAAO;;;;;;;;;;;;;;;;0BAuBhC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAW,CAAC,qBAAqB,EAC/B,QAAQ,SAAS,GAAG,iBAAiB,cACrC;;;;;;0CAEJ,8OAAC;gCAAK,WAAU;0CACb,QAAQ,SAAS,GAAG,aAAa;;;;;;;;;;;;kCAGtC,8OAAC;wBAAI,WAAU;;4BAA6B;4BACpB,KAAK,KAAK,CAAC,QAAQ,iBAAiB;4BAAE;;;;;;;;;;;;;;;;;;;AAKtE;uCAEe", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/VehicleTracker.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useCallback } from 'react';\nimport { VehicleTrackerProps, VehiclePosition, Coordinate } from '@/types';\n\nconst VehicleTracker: React.FC<VehicleTrackerProps> = ({\n  route,\n  onPositionUpdate,\n  onStatusChange,\n  isActive,\n  speed = 1,\n}) => {\n  const currentWaypointIndex = useRef(0);\n  const progress = useRef(0); // Progress between current and next waypoint (0-1)\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n  const isOffRoute = useRef(false);\n  const offRouteCounter = useRef(0);\n  const totalUpdateCounter = useRef(0);\n  const offRouteTestTriggered = useRef(false);\n\n\n\n  // Calculate bearing between two coordinates\n  const calculateBearing = useCallback((coord1: Coordinate, coord2: Coordinate): number => {\n    const dLng = (coord2.lng - coord1.lng) * Math.PI / 180;\n    const lat1 = coord1.lat * Math.PI / 180;\n    const lat2 = coord2.lat * Math.PI / 180;\n    \n    const y = Math.sin(dLng) * Math.cos(lat2);\n    const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLng);\n    \n    const bearing = Math.atan2(y, x) * 180 / Math.PI;\n    return (bearing + 360) % 360;\n  }, []);\n\n  // Interpolate between two coordinates\n  const interpolatePosition = useCallback((\n    start: Coordinate,\n    end: Coordinate,\n    progress: number\n  ): Coordinate => {\n    // Calculate base position along the route\n    let lat = start.lat + (end.lat - start.lat) * progress;\n    let lng = start.lng + (end.lng - start.lng) * progress;\n\n    // Increment counters\n    totalUpdateCounter.current++;\n    offRouteCounter.current++;\n\n    // Controlled off-route testing: trigger early after 5 updates (10 seconds), then stay off-route for 8 updates\n    if (!offRouteTestTriggered.current && totalUpdateCounter.current > 5 && totalUpdateCounter.current < 16) {\n      if (totalUpdateCounter.current === 6) {\n        // Start off-route test\n        isOffRoute.current = true;\n        offRouteTestTriggered.current = true;\n        offRouteCounter.current = 0;\n        console.log('🚨 Starting off-route test scenario');\n      }\n\n      if (isOffRoute.current) {\n        // Apply much larger offset while off-route to exceed 120m tolerance\n        const offsetDistance = 0.005; // Roughly 500 meters - guaranteed to exceed 120m threshold\n        lat += offsetDistance; // Move north consistently\n        lng += offsetDistance * 0.8; // Move east as well\n        console.log(`🔴 Vehicle off-route: ${Math.round(totalUpdateCounter.current - 5)} updates`);\n      }\n    } else if (isOffRoute.current && offRouteCounter.current > 8) {\n      // Return to route after being off-route for 8 updates\n      isOffRoute.current = false;\n      offRouteCounter.current = 0;\n      console.log('✅ Vehicle returning to route');\n    }\n\n    return { lat, lng };\n  }, []);\n\n  // Update vehicle position\n  const updatePosition = useCallback(() => {\n    if (!route.waypoints || route.waypoints.length < 2) return;\n\n    const currentWaypoint = route.waypoints[currentWaypointIndex.current];\n    const nextWaypointIndex = currentWaypointIndex.current + 1;\n\n    // Check if we've reached the end of the route\n    if (nextWaypointIndex >= route.waypoints.length) {\n      // Restart from the beginning\n      currentWaypointIndex.current = 0;\n      progress.current = 0;\n      onStatusChange('on-route');\n      return;\n    }\n\n    const nextWaypoint = route.waypoints[nextWaypointIndex];\n\n    // Calculate new position\n    const newPosition = interpolatePosition(currentWaypoint, nextWaypoint, progress.current);\n    \n    // Calculate bearing for vehicle orientation\n    const bearing = calculateBearing(currentWaypoint, nextWaypoint);\n\n    // Create vehicle position object\n    const vehiclePosition: VehiclePosition = {\n      lat: newPosition.lat,\n      lng: newPosition.lng,\n      timestamp: new Date(),\n      speed: 50 + Math.random() * 20, // Random speed between 50-70 km/h\n      heading: bearing,\n    };\n\n    // Update progress\n    progress.current += 0.02 * speed; // Adjust speed multiplier\n\n    // Check if we've reached the next waypoint\n    if (progress.current >= 1) {\n      currentWaypointIndex.current = nextWaypointIndex;\n      progress.current = 0;\n    }\n\n    // Update status based on whether vehicle is off-route\n    // Note: The actual route status will be determined by the distance calculation in the parent component\n    const status = isOffRoute.current ? 'off-route' : 'on-route';\n    onStatusChange(status);\n\n    // Notify parent component\n    onPositionUpdate(vehiclePosition);\n  }, [route.waypoints, speed, onPositionUpdate, onStatusChange, interpolatePosition, calculateBearing]);\n\n  // Start/stop simulation\n  useEffect(() => {\n    if (isActive && route.waypoints.length >= 2) {\n      // Start simulation\n      intervalRef.current = setInterval(updatePosition, 2000); // Update every 2 seconds\n    } else {\n      // Stop simulation\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n        intervalRef.current = null;\n      }\n    }\n\n    // Cleanup on unmount\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [isActive, route.waypoints.length, updatePosition]);\n\n  // Reset simulation when route changes\n  useEffect(() => {\n    currentWaypointIndex.current = 0;\n    progress.current = 0;\n    isOffRoute.current = false;\n    offRouteCounter.current = 0;\n    totalUpdateCounter.current = 0;\n    offRouteTestTriggered.current = false;\n  }, [route]);\n\n  // This component doesn't render anything visible\n  return null;\n};\n\nexport default VehicleTracker;\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAKA,MAAM,iBAAgD,CAAC,EACrD,KAAK,EACL,gBAAgB,EAChB,cAAc,EACd,QAAQ,EACR,QAAQ,CAAC,EACV;IACC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACpC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI,mDAAmD;IAC/E,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAClD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAClC,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAIrC,4CAA4C;IAC5C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAoB;QACxD,MAAM,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG,IAAI,KAAK,EAAE,GAAG;QACnD,MAAM,OAAO,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG;QACpC,MAAM,OAAO,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG;QAEpC,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC;QACpC,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC;QAEvF,MAAM,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,KAAK,EAAE;QAChD,OAAO,CAAC,UAAU,GAAG,IAAI;IAC3B,GAAG,EAAE;IAEL,sCAAsC;IACtC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CACtC,OACA,KACA;QAEA,0CAA0C;QAC1C,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI;QAC9C,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI;QAE9C,qBAAqB;QACrB,mBAAmB,OAAO;QAC1B,gBAAgB,OAAO;QAEvB,8GAA8G;QAC9G,IAAI,CAAC,sBAAsB,OAAO,IAAI,mBAAmB,OAAO,GAAG,KAAK,mBAAmB,OAAO,GAAG,IAAI;YACvG,IAAI,mBAAmB,OAAO,KAAK,GAAG;gBACpC,uBAAuB;gBACvB,WAAW,OAAO,GAAG;gBACrB,sBAAsB,OAAO,GAAG;gBAChC,gBAAgB,OAAO,GAAG;gBAC1B,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI,WAAW,OAAO,EAAE;gBACtB,oEAAoE;gBACpE,MAAM,iBAAiB,OAAO,2DAA2D;gBACzF,OAAO,gBAAgB,0BAA0B;gBACjD,OAAO,iBAAiB,KAAK,oBAAoB;gBACjD,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,KAAK,KAAK,CAAC,mBAAmB,OAAO,GAAG,GAAG,QAAQ,CAAC;YAC3F;QACF,OAAO,IAAI,WAAW,OAAO,IAAI,gBAAgB,OAAO,GAAG,GAAG;YAC5D,sDAAsD;YACtD,WAAW,OAAO,GAAG;YACrB,gBAAgB,OAAO,GAAG;YAC1B,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO;YAAE;YAAK;QAAI;IACpB,GAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,MAAM,GAAG,GAAG;QAEpD,MAAM,kBAAkB,MAAM,SAAS,CAAC,qBAAqB,OAAO,CAAC;QACrE,MAAM,oBAAoB,qBAAqB,OAAO,GAAG;QAEzD,8CAA8C;QAC9C,IAAI,qBAAqB,MAAM,SAAS,CAAC,MAAM,EAAE;YAC/C,6BAA6B;YAC7B,qBAAqB,OAAO,GAAG;YAC/B,SAAS,OAAO,GAAG;YACnB,eAAe;YACf;QACF;QAEA,MAAM,eAAe,MAAM,SAAS,CAAC,kBAAkB;QAEvD,yBAAyB;QACzB,MAAM,cAAc,oBAAoB,iBAAiB,cAAc,SAAS,OAAO;QAEvF,4CAA4C;QAC5C,MAAM,UAAU,iBAAiB,iBAAiB;QAElD,iCAAiC;QACjC,MAAM,kBAAmC;YACvC,KAAK,YAAY,GAAG;YACpB,KAAK,YAAY,GAAG;YACpB,WAAW,IAAI;YACf,OAAO,KAAK,KAAK,MAAM,KAAK;YAC5B,SAAS;QACX;QAEA,kBAAkB;QAClB,SAAS,OAAO,IAAI,OAAO,OAAO,0BAA0B;QAE5D,2CAA2C;QAC3C,IAAI,SAAS,OAAO,IAAI,GAAG;YACzB,qBAAqB,OAAO,GAAG;YAC/B,SAAS,OAAO,GAAG;QACrB;QAEA,sDAAsD;QACtD,uGAAuG;QACvG,MAAM,SAAS,WAAW,OAAO,GAAG,cAAc;QAClD,eAAe;QAEf,0BAA0B;QAC1B,iBAAiB;IACnB,GAAG;QAAC,MAAM,SAAS;QAAE;QAAO;QAAkB;QAAgB;QAAqB;KAAiB;IAEpG,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,MAAM,SAAS,CAAC,MAAM,IAAI,GAAG;YAC3C,mBAAmB;YACnB,YAAY,OAAO,GAAG,YAAY,gBAAgB,OAAO,yBAAyB;QACpF,OAAO;YACL,kBAAkB;YAClB,IAAI,YAAY,OAAO,EAAE;gBACvB,cAAc,YAAY,OAAO;gBACjC,YAAY,OAAO,GAAG;YACxB;QACF;QAEA,qBAAqB;QACrB,OAAO;YACL,IAAI,YAAY,OAAO,EAAE;gBACvB,cAAc,YAAY,OAAO;YACnC;QACF;IACF,GAAG;QAAC;QAAU,MAAM,SAAS,CAAC,MAAM;QAAE;KAAe;IAErD,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB,OAAO,GAAG;QAC/B,SAAS,OAAO,GAAG;QACnB,WAAW,OAAO,GAAG;QACrB,gBAAgB,OAAO,GAAG;QAC1B,mBAAmB,OAAO,GAAG;QAC7B,sBAAsB,OAAO,GAAG;IAClC,GAAG;QAAC;KAAM;IAEV,iDAAiD;IACjD,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/AlertBanner.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { AlertBannerProps, Alert } from '@/types';\n\nconst AlertBanner: React.FC<AlertBannerProps> = ({\n  alerts,\n  onDismiss,\n  maxVisible = 3,\n}) => {\n  // Filter active alerts and limit to maxVisible\n  const activeAlerts = alerts\n    .filter(alert => alert.isActive)\n    .slice(0, maxVisible);\n\n  if (activeAlerts.length === 0) {\n    return null;\n  }\n\n  // Get alert styling based on type\n  const getAlertStyles = (type: Alert['type']) => {\n    switch (type) {\n      case 'error':\n        return {\n          container: 'bg-red-50 border-red-200 text-red-800',\n          icon: '🚨',\n          iconBg: 'bg-red-100',\n        };\n      case 'warning':\n        return {\n          container: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n          icon: '⚠️',\n          iconBg: 'bg-yellow-100',\n        };\n      case 'info':\n        return {\n          container: 'bg-blue-50 border-blue-200 text-blue-800',\n          icon: 'ℹ️',\n          iconBg: 'bg-blue-100',\n        };\n      default:\n        return {\n          container: 'bg-gray-50 border-gray-200 text-gray-800',\n          icon: '📢',\n          iconBg: 'bg-gray-100',\n        };\n    }\n  };\n\n  // Format timestamp\n  const formatTime = (timestamp: Date) => {\n    return timestamp.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit',\n    });\n  };\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-md\">\n      {activeAlerts.map((alert) => {\n        const styles = getAlertStyles(alert.type);\n        \n        return (\n          <div\n            key={alert.id}\n            className={`\n              ${styles.container}\n              border rounded-lg shadow-lg p-4 \n              transform transition-all duration-300 ease-in-out\n              animate-slide-in-right\n            `}\n            role=\"alert\"\n          >\n            <div className=\"flex items-start space-x-3\">\n              {/* Alert Icon */}\n              <div className={`\n                ${styles.iconBg} \n                rounded-full p-1 flex-shrink-0 mt-0.5\n              `}>\n                <span className=\"text-sm\">{styles.icon}</span>\n              </div>\n\n              {/* Alert Content */}\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium leading-5\">\n                      {alert.message}\n                    </p>\n                    <div className=\"mt-1 flex items-center space-x-2 text-xs opacity-75\">\n                      <span>Vehicle: {alert.vehicleId}</span>\n                      <span>•</span>\n                      <span>{formatTime(alert.timestamp)}</span>\n                    </div>\n                  </div>\n\n                  {/* Dismiss Button */}\n                  <button\n                    onClick={() => onDismiss(alert.id)}\n                    className=\"\n                      ml-2 flex-shrink-0 rounded-md p-1.5 \n                      hover:bg-black hover:bg-opacity-10 \n                      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent\n                      transition-colors duration-200\n                    \"\n                    aria-label=\"Dismiss alert\"\n                  >\n                    <svg\n                      className=\"h-4 w-4\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M6 18L18 6M6 6l12 12\"\n                      />\n                    </svg>\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Progress bar for auto-dismiss (optional) */}\n            {alert.type === 'info' && (\n              <div className=\"mt-3 w-full bg-black bg-opacity-10 rounded-full h-1\">\n                <div \n                  className=\"bg-current h-1 rounded-full transition-all duration-1000 ease-linear\"\n                  style={{ width: '100%' }}\n                />\n              </div>\n            )}\n          </div>\n        );\n      })}\n\n      {/* Alert counter if there are more alerts */}\n      {alerts.filter(a => a.isActive).length > maxVisible && (\n        <div className=\"text-center\">\n          <div className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n            +{alerts.filter(a => a.isActive).length - maxVisible} more alerts\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AlertBanner;\n"], "names": [], "mappings": ";;;;AAAA;;AAKA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,SAAS,EACT,aAAa,CAAC,EACf;IACC,+CAA+C;IAC/C,MAAM,eAAe,OAClB,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,EAC9B,KAAK,CAAC,GAAG;IAEZ,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,QAAQ;gBACV;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,QAAQ;gBACV;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,QAAQ;gBACV;YACF;gBACE,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,QAAQ;gBACV;QACJ;IACF;IAEA,mBAAmB;IACnB,MAAM,aAAa,CAAC;QAClB,OAAO,UAAU,kBAAkB,CAAC,SAAS;YAC3C,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,aAAa,GAAG,CAAC,CAAC;gBACjB,MAAM,SAAS,eAAe,MAAM,IAAI;gBAExC,qBACE,8OAAC;oBAEC,WAAW,CAAC;cACV,EAAE,OAAO,SAAS,CAAC;;;;YAIrB,CAAC;oBACD,MAAK;;sCAEL,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAW,CAAC;gBACf,EAAE,OAAO,MAAM,CAAC;;cAElB,CAAC;8CACC,cAAA,8OAAC;wCAAK,WAAU;kDAAW,OAAO,IAAI;;;;;;;;;;;8CAIxC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,MAAM,OAAO;;;;;;kEAEhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAK;oEAAU,MAAM,SAAS;;;;;;;0EAC/B,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAM,WAAW,MAAM,SAAS;;;;;;;;;;;;;;;;;;0DAKrC,8OAAC;gDACC,SAAS,IAAM,UAAU,MAAM,EAAE;gDACjC,WAAU;gDAMV,cAAW;0DAEX,cAAA,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,QAAO;8DAEP,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASb,MAAM,IAAI,KAAK,wBACd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAO;;;;;;;;;;;;mBAlExB,MAAM,EAAE;;;;;YAwEnB;YAGC,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM,GAAG,4BACvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBAAgG;wBAC3G,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM,GAAG;wBAAW;;;;;;;;;;;;;;;;;;AAMjE;uCAEe", "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/ClientTimestamp.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\n\ninterface ClientTimestampProps {\n  timestamp: Date;\n  className?: string;\n}\n\nconst ClientTimestamp: React.FC<ClientTimestampProps> = ({ timestamp, className }) => {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return <span className={className}>Loading...</span>;\n  }\n\n  return (\n    <span className={className}>\n      {timestamp.toLocaleTimeString()}\n    </span>\n  );\n};\n\nexport default ClientTimestamp;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASA,MAAM,kBAAkD,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE;IAC/E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;YAAK,WAAW;sBAAW;;;;;;IACrC;IAEA,qBACE,8OAAC;QAAK,WAAW;kBACd,UAAU,kBAAkB;;;;;;AAGnC;uCAEe", "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\n\nconst Navigation: React.FC = () => {\n  const pathname = usePathname();\n\n  const navItems = [\n    {\n      href: '/',\n      label: 'Live Tracking',\n      icon: '🗺️',\n      description: 'Real-time vehicle tracking'\n    },\n    {\n      href: '/dashboard/monitoring',\n      label: 'Monitoring',\n      icon: '📊',\n      description: 'Analytics and logs'\n    },\n    {\n      href: '/dashboard/analytics',\n      label: 'Analytics',\n      icon: '📈',\n      description: 'Data visualization and insights'\n    }\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo/Brand */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <h1 className=\"text-xl font-bold text-gray-900\">\n                🚛 Vehicle Tracking System\n              </h1>\n            </div>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navItems.map((item) => {\n                const isActive = pathname === item.href;\n                return (\n                  <Link\n                    key={item.href}\n                    href={item.href}\n                    className={`\n                      px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\n                      ${isActive\n                        ? 'bg-blue-100 text-blue-700 border border-blue-200'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                      }\n                    `}\n                  >\n                    <span className=\"mr-2\">{item.icon}</span>\n                    {item.label}\n                  </Link>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"bg-gray-100 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n              aria-expanded=\"false\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              <svg\n                className=\"block h-6 w-6\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n                aria-hidden=\"true\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth=\"2\"\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3\">\n            {navItems.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`\n                    block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200\n                    ${isActive\n                      ? 'bg-blue-100 text-blue-700 border border-blue-200'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                    }\n                  `}\n                >\n                  <div className=\"flex items-center\">\n                    <span className=\"mr-3\">{item.icon}</span>\n                    <div>\n                      <div>{item.label}</div>\n                      <div className=\"text-xs text-gray-500\">{item.description}</div>\n                    </div>\n                  </div>\n                </Link>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,aAAuB;IAC3B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;;;;;;sCAOpD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC;oCACb,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC;;sBAEV,EAAE,WACE,qDACA,sDACH;oBACH,CAAC;;0DAED,8OAAC;gDAAK,WAAU;0DAAQ,KAAK,IAAI;;;;;;4CAChC,KAAK,KAAK;;uCAXN,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;sCAKJ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,iBAAc;;kDAEd,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCACC,WAAU;wCACV,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAY;4CACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC;;oBAEV,EAAE,WACE,qDACA,sDACH;kBACH,CAAC;0CAED,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ,KAAK,IAAI;;;;;;sDACjC,8OAAC;;8DACC,8OAAC;8DAAK,KAAK,KAAK;;;;;;8DAChB,8OAAC;oDAAI,WAAU;8DAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;+BAdvD,KAAK,IAAI;;;;;wBAmBpB;;;;;;;;;;;;;;;;;;;;;;AAMZ;uCAEe", "debugId": null}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback, useEffect } from 'react';\nimport TrackingMap from '@/components/TrackingMap';\nimport VehicleTracker from '@/components/VehicleTracker';\nimport AlertBanner from '@/components/AlertBanner';\nimport ClientTimestamp from '@/components/ClientTimestamp';\nimport Navigation from '@/components/Navigation';\nimport { Route, VehicleState, VehiclePosition, Alert, Coordinate } from '@/types';\n\n// Sample route data (Riyadh City Route)\nconst sampleRoute: Route = {\n  id: 'route-1',\n  name: 'Riyadh City Route',\n  waypoints: [\n    { lat: 24.7136, lng: 46.6753 }, // King Fahd Road (Start)\n    { lat: 24.7200, lng: 46.6800 }, // Olaya District\n    { lat: 24.7280, lng: 46.6850 }, // Al Malaz\n    { lat: 24.7350, lng: 46.6900 }, // King Abdulaziz Road\n    { lat: 24.7420, lng: 46.6950 }, // Diplomatic Quarter\n    { lat: 24.7480, lng: 46.7000 }, // Al Muhammadiya<PERSON>\n    { lat: 24.7540, lng: 46.7050 }, // Al Naseem\n    { lat: 24.7600, lng: 46.7100 }, // King Khalid Airport Road\n    { lat: 24.7660, lng: 46.7150 }, // Al Rawdah\n    { lat: 24.7720, lng: 46.7200 }, // Northern Ring Road (End)\n  ],\n  color: '#2563eb',\n  strokeWeight: 4,\n};\n\nexport default function VehicleTrackingDashboard() {\n  const [vehicle, setVehicle] = useState<VehicleState>({\n    id: 'vehicle-001',\n    position: {\n      lat: sampleRoute.waypoints[0].lat,\n      lng: sampleRoute.waypoints[0].lng,\n      timestamp: new Date(),\n    },\n    isOnRoute: true,\n    currentWaypointIndex: 0,\n    distanceFromRoute: 0,\n    status: 'on-route',\n  });\n\n  const [alerts, setAlerts] = useState<Alert[]>([]);\n  const [isSimulationActive, setIsSimulationActive] = useState(true);\n\n  // Function to log events to terminal\n  const logEventToTerminal = useCallback(async (eventType: string, vehicleId: string, position: VehiclePosition, distance: number, message: string) => {\n    try {\n      await fetch('/api/vehicle-events', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          eventType,\n          vehicleId,\n          position,\n          distance,\n          timestamp: new Date().toISOString(),\n          message\n        })\n      });\n    } catch (error) {\n      console.error('Failed to log event to terminal:', error);\n    }\n  }, []);\n\n  // Calculate distance from route using proper point-to-line segment distance\n  const calculateDistanceFromRoute = useCallback((position: VehiclePosition): number => {\n    let minDistance = Infinity;\n\n    // Helper function to calculate distance between two coordinates using Haversine formula\n    const haversineDistance = (coord1: Coordinate, coord2: Coordinate): number => {\n      const R = 6371000; // Earth's radius in meters\n      const dLat = (coord2.lat - coord1.lat) * Math.PI / 180;\n      const dLng = (coord2.lng - coord1.lng) * Math.PI / 180;\n      const a =\n        Math.sin(dLat/2) * Math.sin(dLat/2) +\n        Math.cos(coord1.lat * Math.PI / 180) * Math.cos(coord2.lat * Math.PI / 180) *\n        Math.sin(dLng/2) * Math.sin(dLng/2);\n      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n      return R * c;\n    };\n\n    // Helper function to calculate distance from point to line segment\n    const pointToLineDistance = (point: Coordinate, lineStart: Coordinate, lineEnd: Coordinate): number => {\n      const A = point.lat - lineStart.lat;\n      const B = point.lng - lineStart.lng;\n      const C = lineEnd.lat - lineStart.lat;\n      const D = lineEnd.lng - lineStart.lng;\n\n      const dot = A * C + B * D;\n      const lenSq = C * C + D * D;\n\n      if (lenSq === 0) {\n        // Line segment is actually a point\n        return haversineDistance(point, lineStart);\n      }\n\n      const param = dot / lenSq;\n\n      let closestPoint: Coordinate;\n      if (param < 0) {\n        closestPoint = lineStart;\n      } else if (param > 1) {\n        closestPoint = lineEnd;\n      } else {\n        closestPoint = {\n          lat: lineStart.lat + param * C,\n          lng: lineStart.lng + param * D\n        };\n      }\n\n      return haversineDistance(point, closestPoint);\n    };\n\n    // Calculate minimum distance to any route segment\n    for (let i = 0; i < sampleRoute.waypoints.length - 1; i++) {\n      const start = sampleRoute.waypoints[i];\n      const end = sampleRoute.waypoints[i + 1];\n\n      const distance = pointToLineDistance(position, start, end);\n      minDistance = Math.min(minDistance, distance);\n    }\n\n    return minDistance;\n  }, []);\n\n  // Handle vehicle position updates\n  const handleVehicleUpdate = useCallback((newVehicle: VehicleState) => {\n    setVehicle(newVehicle);\n  }, []);\n\n  // Handle position updates from tracker\n  const handlePositionUpdate = useCallback((position: VehiclePosition) => {\n    const distanceFromRoute = calculateDistanceFromRoute(position);\n    const isOnRoute = distanceFromRoute <= 100; // 100 meter tolerance for off-route detection\n\n    // Enhanced debug logging\n    console.log(`🚗 Vehicle position: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`);\n    console.log(`📏 Distance from route: ${Math.round(distanceFromRoute)}m, Status: ${isOnRoute ? '✅ On Route' : '🔴 Off Route'}`);\n\n    if (!isOnRoute) {\n      console.log(`⚠️ ALERT: Vehicle is ${Math.round(distanceFromRoute)}m from route (tolerance: 100m)`);\n    }\n\n    // Log position updates to terminal (every update)\n    logEventToTerminal('position-update', vehicle.id, position, distanceFromRoute,\n      `Vehicle position update: ${isOnRoute ? 'On Route' : 'Off Route'}`);\n\n\n    setVehicle(prev => {\n      const wasOnRoute = prev.isOnRoute;\n      const newVehicle = {\n        ...prev,\n        position,\n        distanceFromRoute,\n        isOnRoute,\n      };\n\n      // Create alert only when vehicle goes from on-route to off-route\n      if (wasOnRoute && !isOnRoute) {\n        console.log(`🚨 CREATING ALERT: Vehicle went off-route! Distance: ${Math.round(distanceFromRoute)}m`);\n\n        // Log to terminal\n        logEventToTerminal('off-route-start', prev.id, position, distanceFromRoute,\n          `Vehicle went off-route! Distance: ${Math.round(distanceFromRoute)}m`);\n\n        const newAlert: Alert = {\n          id: `alert-${Date.now()}`,\n          type: 'warning',\n          message: `Vehicle is outside allowed route (${Math.round(distanceFromRoute)}m from route)`,\n          timestamp: new Date(),\n          vehicleId: prev.id,\n          isActive: true,\n        };\n\n        setAlerts(prevAlerts => {\n          console.log(`📢 Alert added to list. Total alerts: ${prevAlerts.length + 1}`);\n          return [...prevAlerts, newAlert];\n        });\n      }\n\n      // Log when vehicle returns to route\n      if (!wasOnRoute && isOnRoute) {\n        logEventToTerminal('back-on-route', prev.id, position, distanceFromRoute,\n          `Vehicle returned to route! Distance: ${Math.round(distanceFromRoute)}m`);\n      }\n\n      // Log continued off-route status\n      if (!wasOnRoute && !isOnRoute) {\n        logEventToTerminal('off-route-continue', prev.id, position, distanceFromRoute,\n          `Vehicle still off-route: ${Math.round(distanceFromRoute)}m`);\n      }\n\n      return newVehicle;\n    });\n  }, [calculateDistanceFromRoute, logEventToTerminal, vehicle.id]);\n\n  // Handle alerts based on vehicle position (simplified)\n  const handleStatusChange = useCallback(() => {\n    // This function is called by VehicleTracker but we determine status based on distance only\n    // Alerts are now handled in handlePositionUpdate when distance changes\n  }, []);\n\n  // Handle alert creation\n  const handleAlert = useCallback((alert: Alert) => {\n    setAlerts(prev => [...prev, alert]);\n  }, []);\n\n  // Handle alert dismissal\n  const handleAlertDismiss = useCallback((alertId: string) => {\n    setAlerts(prev =>\n      prev.map(alert =>\n        alert.id === alertId\n          ? { ...alert, isActive: false }\n          : alert\n      )\n    );\n  }, []);\n\n  // Auto-dismiss info alerts after 5 seconds\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setAlerts(prev =>\n        prev.map(alert =>\n          alert.type === 'info' && alert.isActive\n            ? { ...alert, isActive: false }\n            : alert\n        )\n      );\n    }, 5000);\n\n    return () => clearTimeout(timer);\n  }, [alerts]);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation */}\n      <Navigation />\n\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                Live Vehicle Tracking\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => {\n                  const newState = !isSimulationActive;\n                  setIsSimulationActive(newState);\n\n                  // Log simulation state change to terminal\n                  logEventToTerminal(\n                    newState ? 'simulation-start' : 'simulation-stop',\n                    vehicle.id,\n                    vehicle.position,\n                    vehicle.distanceFromRoute,\n                    newState ? 'Vehicle simulation started' : 'Vehicle simulation stopped'\n                  );\n                }}\n                className={`\n                  px-4 py-2 rounded-md text-sm font-medium transition-colors\n                  ${isSimulationActive\n                    ? 'bg-red-600 text-white hover:bg-red-700'\n                    : 'bg-green-600 text-white hover:bg-green-700'\n                  }\n                `}\n              >\n                {isSimulationActive ? 'Stop Simulation' : 'Start Simulation'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n          {/* Status Panel */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                Vehicle Status\n              </h2>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Vehicle ID</label>\n                  <p className=\"text-lg font-mono\">{vehicle.id}</p>\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Status</label>\n                  <div className=\"flex items-center space-x-2\">\n                    <div\n                      className={`w-3 h-3 rounded-full ${\n                        vehicle.isOnRoute ? 'bg-green-500' : 'bg-red-500'\n                      }`}\n                    />\n                    <span className={`font-medium ${\n                      vehicle.isOnRoute ? 'text-green-700' : 'text-red-700'\n                    }`}>\n                      {vehicle.isOnRoute ? 'On Route' : 'Off Route'}\n                    </span>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Distance from Route</label>\n                  <p className=\"text-lg\">{Math.round(vehicle.distanceFromRoute)}m</p>\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Last Update</label>\n                  <ClientTimestamp\n                    timestamp={vehicle.position.timestamp}\n                    className=\"text-sm text-gray-600\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Speed</label>\n                  <p className=\"text-lg\">\n                    {vehicle.position.speed ? `${Math.round(vehicle.position.speed)} km/h` : 'N/A'}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Map */}\n          <div className=\"lg:col-span-3\">\n            <div className=\"bg-white rounded-lg shadow overflow-hidden\" style={{ height: '600px' }}>\n              <TrackingMap\n                route={sampleRoute}\n                vehicle={vehicle}\n                onVehicleUpdate={handleVehicleUpdate}\n                onAlert={handleAlert}\n              />\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Vehicle Tracker (invisible component) */}\n      <VehicleTracker\n        route={sampleRoute}\n        onPositionUpdate={handlePositionUpdate}\n        onStatusChange={handleStatusChange}\n        isActive={isSimulationActive}\n        speed={1}\n      />\n\n      {/* Alert Banner */}\n      <AlertBanner\n        alerts={alerts}\n        onDismiss={handleAlertDismiss}\n        maxVisible={3}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAUA,wCAAwC;AACxC,MAAM,cAAqB;IACzB,IAAI;IACJ,MAAM;IACN,WAAW;QACT;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;KAC9B;IACD,OAAO;IACP,cAAc;AAChB;AAEe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACnD,IAAI;QACJ,UAAU;YACR,KAAK,YAAY,SAAS,CAAC,EAAE,CAAC,GAAG;YACjC,KAAK,YAAY,SAAS,CAAC,EAAE,CAAC,GAAG;YACjC,WAAW,IAAI;QACjB;QACA,WAAW;QACX,sBAAsB;QACtB,mBAAmB;QACnB,QAAQ;IACV;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,qCAAqC;IACrC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAmB,WAAmB,UAA2B,UAAkB;QAC/H,IAAI;YACF,MAAM,MAAM,uBAAuB;gBACjC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;oBACA;oBACA,WAAW,IAAI,OAAO,WAAW;oBACjC;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF,GAAG,EAAE;IAEL,4EAA4E;IAC5E,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9C,IAAI,cAAc;QAElB,wFAAwF;QACxF,MAAM,oBAAoB,CAAC,QAAoB;YAC7C,MAAM,IAAI,SAAS,2BAA2B;YAC9C,MAAM,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG,IAAI,KAAK,EAAE,GAAG;YACnD,MAAM,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG,IAAI,KAAK,EAAE,GAAG;YACnD,MAAM,IACJ,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK,KACjC,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,OACvE,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK;YACnC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAE;YACnD,OAAO,IAAI;QACb;QAEA,mEAAmE;QACnE,MAAM,sBAAsB,CAAC,OAAmB,WAAuB;YACrE,MAAM,IAAI,MAAM,GAAG,GAAG,UAAU,GAAG;YACnC,MAAM,IAAI,MAAM,GAAG,GAAG,UAAU,GAAG;YACnC,MAAM,IAAI,QAAQ,GAAG,GAAG,UAAU,GAAG;YACrC,MAAM,IAAI,QAAQ,GAAG,GAAG,UAAU,GAAG;YAErC,MAAM,MAAM,IAAI,IAAI,IAAI;YACxB,MAAM,QAAQ,IAAI,IAAI,IAAI;YAE1B,IAAI,UAAU,GAAG;gBACf,mCAAmC;gBACnC,OAAO,kBAAkB,OAAO;YAClC;YAEA,MAAM,QAAQ,MAAM;YAEpB,IAAI;YACJ,IAAI,QAAQ,GAAG;gBACb,eAAe;YACjB,OAAO,IAAI,QAAQ,GAAG;gBACpB,eAAe;YACjB,OAAO;gBACL,eAAe;oBACb,KAAK,UAAU,GAAG,GAAG,QAAQ;oBAC7B,KAAK,UAAU,GAAG,GAAG,QAAQ;gBAC/B;YACF;YAEA,OAAO,kBAAkB,OAAO;QAClC;QAEA,kDAAkD;QAClD,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,SAAS,CAAC,MAAM,GAAG,GAAG,IAAK;YACzD,MAAM,QAAQ,YAAY,SAAS,CAAC,EAAE;YACtC,MAAM,MAAM,YAAY,SAAS,CAAC,IAAI,EAAE;YAExC,MAAM,WAAW,oBAAoB,UAAU,OAAO;YACtD,cAAc,KAAK,GAAG,CAAC,aAAa;QACtC;QAEA,OAAO;IACT,GAAG,EAAE;IAEL,kCAAkC;IAClC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,WAAW;IACb,GAAG,EAAE;IAEL,uCAAuC;IACvC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,MAAM,oBAAoB,2BAA2B;QACrD,MAAM,YAAY,qBAAqB,KAAK,8CAA8C;QAE1F,yBAAyB;QACzB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,SAAS,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,GAAG,CAAC,OAAO,CAAC,IAAI;QACzF,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,KAAK,KAAK,CAAC,mBAAmB,WAAW,EAAE,YAAY,eAAe,gBAAgB;QAE7H,IAAI,CAAC,WAAW;YACd,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,KAAK,KAAK,CAAC,mBAAmB,8BAA8B,CAAC;QACnG;QAEA,kDAAkD;QAClD,mBAAmB,mBAAmB,QAAQ,EAAE,EAAE,UAAU,mBAC1D,CAAC,yBAAyB,EAAE,YAAY,aAAa,aAAa;QAGpE,WAAW,CAAA;YACT,MAAM,aAAa,KAAK,SAAS;YACjC,MAAM,aAAa;gBACjB,GAAG,IAAI;gBACP;gBACA;gBACA;YACF;YAEA,iEAAiE;YACjE,IAAI,cAAc,CAAC,WAAW;gBAC5B,QAAQ,GAAG,CAAC,CAAC,qDAAqD,EAAE,KAAK,KAAK,CAAC,mBAAmB,CAAC,CAAC;gBAEpG,kBAAkB;gBAClB,mBAAmB,mBAAmB,KAAK,EAAE,EAAE,UAAU,mBACvD,CAAC,kCAAkC,EAAE,KAAK,KAAK,CAAC,mBAAmB,CAAC,CAAC;gBAEvE,MAAM,WAAkB;oBACtB,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;oBACzB,MAAM;oBACN,SAAS,CAAC,kCAAkC,EAAE,KAAK,KAAK,CAAC,mBAAmB,aAAa,CAAC;oBAC1F,WAAW,IAAI;oBACf,WAAW,KAAK,EAAE;oBAClB,UAAU;gBACZ;gBAEA,UAAU,CAAA;oBACR,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,WAAW,MAAM,GAAG,GAAG;oBAC5E,OAAO;2BAAI;wBAAY;qBAAS;gBAClC;YACF;YAEA,oCAAoC;YACpC,IAAI,CAAC,cAAc,WAAW;gBAC5B,mBAAmB,iBAAiB,KAAK,EAAE,EAAE,UAAU,mBACrD,CAAC,qCAAqC,EAAE,KAAK,KAAK,CAAC,mBAAmB,CAAC,CAAC;YAC5E;YAEA,iCAAiC;YACjC,IAAI,CAAC,cAAc,CAAC,WAAW;gBAC7B,mBAAmB,sBAAsB,KAAK,EAAE,EAAE,UAAU,mBAC1D,CAAC,yBAAyB,EAAE,KAAK,KAAK,CAAC,mBAAmB,CAAC,CAAC;YAChE;YAEA,OAAO;QACT;IACF,GAAG;QAAC;QAA4B;QAAoB,QAAQ,EAAE;KAAC;IAE/D,uDAAuD;IACvD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;IACrC,2FAA2F;IAC3F,uEAAuE;IACzE,GAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAM;IACpC,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,UAAU,CAAA,OACR,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,UACT;oBAAE,GAAG,KAAK;oBAAE,UAAU;gBAAM,IAC5B;IAGV,GAAG,EAAE;IAEL,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,UAAU,CAAA,OACR,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,IAAI,KAAK,UAAU,MAAM,QAAQ,GACnC;wBAAE,GAAG,KAAK;wBAAE,UAAU;oBAAM,IAC5B;QAGV,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,UAAU;;;;;0BAGX,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;0CAInD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;wCACP,MAAM,WAAW,CAAC;wCAClB,sBAAsB;wCAEtB,0CAA0C;wCAC1C,mBACE,WAAW,qBAAqB,mBAChC,QAAQ,EAAE,EACV,QAAQ,QAAQ,EAChB,QAAQ,iBAAiB,EACzB,WAAW,+BAA+B;oCAE9C;oCACA,WAAW,CAAC;;kBAEV,EAAE,qBACE,2CACA,6CACH;gBACH,CAAC;8CAEA,qBAAqB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpD,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAIzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAqB,QAAQ,EAAE;;;;;;;;;;;;0DAG9C,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,WAAW,CAAC,qBAAqB,EAC/B,QAAQ,SAAS,GAAG,iBAAiB,cACrC;;;;;;0EAEJ,8OAAC;gEAAK,WAAW,CAAC,YAAY,EAC5B,QAAQ,SAAS,GAAG,mBAAmB,gBACvC;0EACC,QAAQ,SAAS,GAAG,aAAa;;;;;;;;;;;;;;;;;;0DAKxC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;;4DAAW,KAAK,KAAK,CAAC,QAAQ,iBAAiB;4DAAE;;;;;;;;;;;;;0DAGhE,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC,qIAAA,CAAA,UAAe;wDACd,WAAW,QAAQ,QAAQ,CAAC,SAAS;wDACrC,WAAU;;;;;;;;;;;;0DAId,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEACV,QAAQ,QAAQ,CAAC,KAAK,GAAG,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQnF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAA6C,OAAO;oCAAE,QAAQ;gCAAQ;0CACnF,cAAA,8OAAC,iIAAA,CAAA,UAAW;oCACV,OAAO;oCACP,SAAS;oCACT,iBAAiB;oCACjB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnB,8OAAC,oIAAA,CAAA,UAAc;gBACb,OAAO;gBACP,kBAAkB;gBAClB,gBAAgB;gBAChB,UAAU;gBACV,OAAO;;;;;;0BAIT,8OAAC,iIAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,WAAW;gBACX,YAAY;;;;;;;;;;;;AAIpB", "debugId": null}}]}