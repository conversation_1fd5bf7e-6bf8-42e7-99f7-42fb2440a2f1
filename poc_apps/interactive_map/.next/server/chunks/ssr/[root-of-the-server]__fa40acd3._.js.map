{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/TrackingMap.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useRef, useState } from 'react';\nimport { GoogleMap, LoadScript, <PERSON>yline, Marker } from '@react-google-maps/api';\nimport { TrackingMapProps, MapConfig, Coordinate } from '@/types';\n\n// Default map configuration\nconst defaultMapConfig: MapConfig = {\n  center: { lat: 37.7749, lng: -122.4194 }, // San Francisco\n  zoom: 13,\n  disableDefaultUI: false,\n  zoomControl: true,\n  streetViewControl: false,\n  fullscreenControl: true,\n};\n\n// Google Maps libraries to load\nconst libraries: ('geometry' | 'places' | 'drawing' | 'visualization')[] = ['geometry'];\n\n// Map container style\nconst mapContainerStyle = {\n  width: '100%',\n  height: '100%',\n  minHeight: '500px',\n};\n\nconst TrackingMap: React.FC<TrackingMapProps> = ({\n  route,\n  vehicle,\n  onVehicleUpdate,\n  onAlert,\n  mapConfig = {},\n  geofenceConfig = {},\n}) => {\n  const mapRef = useRef<google.maps.Map | null>(null);\n  const [isMapLoaded, setIsMapLoaded] = useState(false);\n  const [loadError, setLoadError] = useState<string | null>(null);\n\n  // Merge default config with provided config\n  const finalMapConfig = { ...defaultMapConfig, ...mapConfig };\n\n  // Get API key from environment\n  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;\n\n  // Handle map load\n  const onMapLoad = useCallback((map: google.maps.Map) => {\n    mapRef.current = map;\n    setIsMapLoaded(true);\n    \n    // Fit map to show the entire route\n    if (route.waypoints.length > 0) {\n      const bounds = new google.maps.LatLngBounds();\n      route.waypoints.forEach(point => {\n        bounds.extend(new google.maps.LatLng(point.lat, point.lng));\n      });\n      map.fitBounds(bounds);\n    }\n  }, [route.waypoints]);\n\n  // Handle map unmount\n  const onMapUnmount = useCallback(() => {\n    mapRef.current = null;\n    setIsMapLoaded(false);\n  }, []);\n\n  // Handle load error\n  const onLoadError = useCallback((error: Error) => {\n    console.error('Google Maps load error:', error);\n    setLoadError(error.message);\n  }, []);\n\n  // Calculate route polyline options\n  const routeOptions = {\n    path: route.waypoints,\n    geodesic: true,\n    strokeColor: route.color || '#2563eb',\n    strokeOpacity: 1.0,\n    strokeWeight: route.strokeWeight || 4,\n  };\n\n  // Vehicle marker options\n  const vehicleIcon = isMapLoaded && typeof google !== 'undefined' ? {\n    path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,\n    scale: 6,\n    fillColor: vehicle.isOnRoute ? '#10b981' : '#ef4444',\n    fillOpacity: 1,\n    strokeColor: '#ffffff',\n    strokeWeight: 2,\n    rotation: vehicle.position.heading || 0,\n  } : undefined;\n\n  // Show error state\n  if (!apiKey) {\n    return (\n      <div className=\"flex items-center justify-center h-full bg-gray-100 rounded-lg\">\n        <div className=\"text-center p-6\">\n          <div className=\"text-red-500 text-xl mb-2\">⚠️</div>\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n            Google Maps API Key Missing\n          </h3>\n          <p className=\"text-gray-600 text-sm\">\n            Please add your Google Maps API key to the .env.local file\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  if (loadError) {\n    return (\n      <div className=\"flex items-center justify-center h-full bg-gray-100 rounded-lg\">\n        <div className=\"text-center p-6\">\n          <div className=\"text-red-500 text-xl mb-2\">❌</div>\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n            Map Load Error\n          </h3>\n          <p className=\"text-gray-600 text-sm\">{loadError}</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"w-full h-full relative\">\n      <LoadScript\n        googleMapsApiKey={apiKey}\n        libraries={libraries}\n        onError={onLoadError}\n        loadingElement={\n          <div className=\"flex items-center justify-center h-full bg-gray-100 rounded-lg\">\n            <div className=\"text-center p-6\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n              <p className=\"text-gray-600\">Loading Google Maps...</p>\n            </div>\n          </div>\n        }\n      >\n        <GoogleMap\n          mapContainerStyle={mapContainerStyle}\n          center={finalMapConfig.center}\n          zoom={finalMapConfig.zoom}\n          onLoad={onMapLoad}\n          onUnmount={onMapUnmount}\n          options={{\n            disableDefaultUI: finalMapConfig.disableDefaultUI,\n            zoomControl: finalMapConfig.zoomControl,\n            streetViewControl: finalMapConfig.streetViewControl,\n            fullscreenControl: finalMapConfig.fullscreenControl,\n            mapTypeControl: false,\n            scaleControl: true,\n          }}\n        >\n          {/* Route polyline */}\n          {route.waypoints.length > 1 && (\n            <Polyline options={routeOptions} />\n          )}\n\n          {/* Vehicle marker */}\n          {vehicleIcon && (\n            <Marker\n              position={vehicle.position}\n              icon={vehicleIcon}\n              title={`Vehicle ${vehicle.id} - ${vehicle.status}`}\n            />\n          )}\n\n          {/* Route waypoint markers */}\n          {isMapLoaded && typeof google !== 'undefined' && route.waypoints.map((waypoint, index) => (\n            <Marker\n              key={`waypoint-${index}`}\n              position={waypoint}\n              icon={{\n                path: google.maps.SymbolPath.CIRCLE,\n                scale: 4,\n                fillColor: index === 0 ? '#10b981' : index === route.waypoints.length - 1 ? '#ef4444' : '#6b7280',\n                fillOpacity: 1,\n                strokeColor: '#ffffff',\n                strokeWeight: 2,\n              }}\n              title={\n                index === 0\n                  ? 'Start Point'\n                  : index === route.waypoints.length - 1\n                    ? 'End Point'\n                    : `Waypoint ${index + 1}`\n              }\n            />\n          ))}\n        </GoogleMap>\n      </LoadScript>\n\n      {/* Map overlay with vehicle status */}\n      <div className=\"absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 z-10\">\n        <div className=\"flex items-center space-x-2\">\n          <div \n            className={`w-3 h-3 rounded-full ${\n              vehicle.isOnRoute ? 'bg-green-500' : 'bg-red-500'\n            }`}\n          />\n          <span className=\"text-sm font-medium\">\n            {vehicle.isOnRoute ? 'On Route' : 'Off Route'}\n          </span>\n        </div>\n        <div className=\"text-xs text-gray-500 mt-1\">\n          Distance from route: {Math.round(vehicle.distanceFromRoute)}m\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TrackingMap;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAMA,4BAA4B;AAC5B,MAAM,mBAA8B;IAClC,QAAQ;QAAE,KAAK;QAAS,KAAK,CAAC;IAAS;IACvC,MAAM;IACN,kBAAkB;IAClB,aAAa;IACb,mBAAmB;IACnB,mBAAmB;AACrB;AAEA,gCAAgC;AAChC,MAAM,YAAqE;IAAC;CAAW;AAEvF,sBAAsB;AACtB,MAAM,oBAAoB;IACxB,OAAO;IACP,QAAQ;IACR,WAAW;AACb;AAEA,MAAM,cAA0C,CAAC,EAC/C,KAAK,EACL,OAAO,EACP,eAAe,EACf,OAAO,EACP,YAAY,CAAC,CAAC,EACd,iBAAiB,CAAC,CAAC,EACpB;IACC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,4CAA4C;IAC5C,MAAM,iBAAiB;QAAE,GAAG,gBAAgB;QAAE,GAAG,SAAS;IAAC;IAE3D,+BAA+B;IAC/B,MAAM;IAEN,kBAAkB;IAClB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,OAAO,OAAO,GAAG;QACjB,eAAe;QAEf,mCAAmC;QACnC,IAAI,MAAM,SAAS,CAAC,MAAM,GAAG,GAAG;YAC9B,MAAM,SAAS,IAAI,OAAO,IAAI,CAAC,YAAY;YAC3C,MAAM,SAAS,CAAC,OAAO,CAAC,CAAA;gBACtB,OAAO,MAAM,CAAC,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,MAAM,GAAG;YAC3D;YACA,IAAI,SAAS,CAAC;QAChB;IACF,GAAG;QAAC,MAAM,SAAS;KAAC;IAEpB,qBAAqB;IACrB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,OAAO,OAAO,GAAG;QACjB,eAAe;IACjB,GAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,QAAQ,KAAK,CAAC,2BAA2B;QACzC,aAAa,MAAM,OAAO;IAC5B,GAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,eAAe;QACnB,MAAM,MAAM,SAAS;QACrB,UAAU;QACV,aAAa,MAAM,KAAK,IAAI;QAC5B,eAAe;QACf,cAAc,MAAM,YAAY,IAAI;IACtC;IAEA,yBAAyB;IACzB,MAAM,cAAc,eAAe,OAAO,WAAW,cAAc;QACjE,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB;QACjD,OAAO;QACP,WAAW,QAAQ,SAAS,GAAG,YAAY;QAC3C,aAAa;QACb,aAAa;QACb,cAAc;QACd,UAAU,QAAQ,QAAQ,CAAC,OAAO,IAAI;IACxC,IAAI;IAEJ,mBAAmB;IACnB,uCAAa;;IAcb;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAA4B;;;;;;kCAC3C,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAAyB;;;;;;;;;;;;;;;;;IAI9C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+JAAA,CAAA,aAAU;gBACT,kBAAkB;gBAClB,WAAW;gBACX,SAAS;gBACT,8BACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;0BAKnC,cAAA,8OAAC,+JAAA,CAAA,YAAS;oBACR,mBAAmB;oBACnB,QAAQ,eAAe,MAAM;oBAC7B,MAAM,eAAe,IAAI;oBACzB,QAAQ;oBACR,WAAW;oBACX,SAAS;wBACP,kBAAkB,eAAe,gBAAgB;wBACjD,aAAa,eAAe,WAAW;wBACvC,mBAAmB,eAAe,iBAAiB;wBACnD,mBAAmB,eAAe,iBAAiB;wBACnD,gBAAgB;wBAChB,cAAc;oBAChB;;wBAGC,MAAM,SAAS,CAAC,MAAM,GAAG,mBACxB,8OAAC,+JAAA,CAAA,WAAQ;4BAAC,SAAS;;;;;;wBAIpB,6BACC,8OAAC,+JAAA,CAAA,SAAM;4BACL,UAAU,QAAQ,QAAQ;4BAC1B,MAAM;4BACN,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,QAAQ,MAAM,EAAE;;;;;;wBAKrD,eAAe,OAAO,WAAW,eAAe,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC9E,8OAAC,+JAAA,CAAA,SAAM;gCAEL,UAAU;gCACV,MAAM;oCACJ,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM;oCACnC,OAAO;oCACP,WAAW,UAAU,IAAI,YAAY,UAAU,MAAM,SAAS,CAAC,MAAM,GAAG,IAAI,YAAY;oCACxF,aAAa;oCACb,aAAa;oCACb,cAAc;gCAChB;gCACA,OACE,UAAU,IACN,gBACA,UAAU,MAAM,SAAS,CAAC,MAAM,GAAG,IACjC,cACA,CAAC,SAAS,EAAE,QAAQ,GAAG;+BAf1B,CAAC,SAAS,EAAE,OAAO;;;;;;;;;;;;;;;;0BAuBhC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAW,CAAC,qBAAqB,EAC/B,QAAQ,SAAS,GAAG,iBAAiB,cACrC;;;;;;0CAEJ,8OAAC;gCAAK,WAAU;0CACb,QAAQ,SAAS,GAAG,aAAa;;;;;;;;;;;;kCAGtC,8OAAC;wBAAI,WAAU;;4BAA6B;4BACpB,KAAK,KAAK,CAAC,QAAQ,iBAAiB;4BAAE;;;;;;;;;;;;;;;;;;;AAKtE;uCAEe", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/VehicleTracker.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useCallback } from 'react';\nimport { VehicleTrackerProps, VehiclePosition, Coordinate } from '@/types';\n\nconst VehicleTracker: React.FC<VehicleTrackerProps> = ({\n  route,\n  onPositionUpdate,\n  onStatusChange,\n  isActive,\n  speed = 1,\n}) => {\n  const currentWaypointIndex = useRef(0);\n  const progress = useRef(0); // Progress between current and next waypoint (0-1)\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n  const isOffRoute = useRef(false);\n  const offRouteCounter = useRef(0);\n\n  // Calculate distance between two coordinates using Haversine formula\n  const calculateDistance = useCallback((coord1: Coordinate, coord2: Coordinate): number => {\n    const R = 6371000; // Earth's radius in meters\n    const dLat = (coord2.lat - coord1.lat) * Math.PI / 180;\n    const dLng = (coord2.lng - coord1.lng) * Math.PI / 180;\n    const a = \n      Math.sin(dLat/2) * Math.sin(dLat/2) +\n      Math.cos(coord1.lat * Math.PI / 180) * Math.cos(coord2.lat * Math.PI / 180) * \n      Math.sin(dLng/2) * Math.sin(dLng/2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n    return R * c;\n  }, []);\n\n  // Calculate bearing between two coordinates\n  const calculateBearing = useCallback((coord1: Coordinate, coord2: Coordinate): number => {\n    const dLng = (coord2.lng - coord1.lng) * Math.PI / 180;\n    const lat1 = coord1.lat * Math.PI / 180;\n    const lat2 = coord2.lat * Math.PI / 180;\n    \n    const y = Math.sin(dLng) * Math.cos(lat2);\n    const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLng);\n    \n    const bearing = Math.atan2(y, x) * 180 / Math.PI;\n    return (bearing + 360) % 360;\n  }, []);\n\n  // Interpolate between two coordinates\n  const interpolatePosition = useCallback((\n    start: Coordinate, \n    end: Coordinate, \n    progress: number\n  ): Coordinate => {\n    // Add some randomness for off-route simulation\n    let lat = start.lat + (end.lat - start.lat) * progress;\n    let lng = start.lng + (end.lng - start.lng) * progress;\n\n    // Simulate going off-route occasionally (every 20-30 updates)\n    if (offRouteCounter.current > 20 && Math.random() < 0.1) {\n      const offsetDistance = 0.001; // Roughly 100 meters\n      lat += (Math.random() - 0.5) * offsetDistance;\n      lng += (Math.random() - 0.5) * offsetDistance;\n      isOffRoute.current = true;\n      offRouteCounter.current = 0;\n    } else if (isOffRoute.current && offRouteCounter.current > 5) {\n      // Return to route after being off-route for a while\n      isOffRoute.current = false;\n    }\n\n    offRouteCounter.current++;\n\n    return { lat, lng };\n  }, []);\n\n  // Update vehicle position\n  const updatePosition = useCallback(() => {\n    if (!route.waypoints || route.waypoints.length < 2) return;\n\n    const currentWaypoint = route.waypoints[currentWaypointIndex.current];\n    const nextWaypointIndex = currentWaypointIndex.current + 1;\n\n    // Check if we've reached the end of the route\n    if (nextWaypointIndex >= route.waypoints.length) {\n      // Restart from the beginning\n      currentWaypointIndex.current = 0;\n      progress.current = 0;\n      onStatusChange('on-route');\n      return;\n    }\n\n    const nextWaypoint = route.waypoints[nextWaypointIndex];\n\n    // Calculate new position\n    const newPosition = interpolatePosition(currentWaypoint, nextWaypoint, progress.current);\n    \n    // Calculate bearing for vehicle orientation\n    const bearing = calculateBearing(currentWaypoint, nextWaypoint);\n\n    // Create vehicle position object\n    const vehiclePosition: VehiclePosition = {\n      lat: newPosition.lat,\n      lng: newPosition.lng,\n      timestamp: new Date(),\n      speed: 50 + Math.random() * 20, // Random speed between 50-70 km/h\n      heading: bearing,\n    };\n\n    // Update progress\n    progress.current += 0.02 * speed; // Adjust speed multiplier\n\n    // Check if we've reached the next waypoint\n    if (progress.current >= 1) {\n      currentWaypointIndex.current = nextWaypointIndex;\n      progress.current = 0;\n    }\n\n    // Update status based on whether vehicle is off-route\n    const status = isOffRoute.current ? 'off-route' : 'on-route';\n    onStatusChange(status);\n\n    // Notify parent component\n    onPositionUpdate(vehiclePosition);\n  }, [route.waypoints, speed, onPositionUpdate, onStatusChange, interpolatePosition, calculateBearing]);\n\n  // Start/stop simulation\n  useEffect(() => {\n    if (isActive && route.waypoints.length >= 2) {\n      // Start simulation\n      intervalRef.current = setInterval(updatePosition, 2000); // Update every 2 seconds\n    } else {\n      // Stop simulation\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n        intervalRef.current = null;\n      }\n    }\n\n    // Cleanup on unmount\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [isActive, route.waypoints.length, updatePosition]);\n\n  // Reset simulation when route changes\n  useEffect(() => {\n    currentWaypointIndex.current = 0;\n    progress.current = 0;\n    isOffRoute.current = false;\n    offRouteCounter.current = 0;\n  }, [route]);\n\n  // This component doesn't render anything visible\n  return null;\n};\n\nexport default VehicleTracker;\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAKA,MAAM,iBAAgD,CAAC,EACrD,KAAK,EACL,gBAAgB,EAChB,cAAc,EACd,QAAQ,EACR,QAAQ,CAAC,EACV;IACC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACpC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI,mDAAmD;IAC/E,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAClD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,qEAAqE;IACrE,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAoB;QACzD,MAAM,IAAI,SAAS,2BAA2B;QAC9C,MAAM,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG,IAAI,KAAK,EAAE,GAAG;QACnD,MAAM,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG,IAAI,KAAK,EAAE,GAAG;QACnD,MAAM,IACJ,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK,KACjC,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,OAAO,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,OACvE,KAAK,GAAG,CAAC,OAAK,KAAK,KAAK,GAAG,CAAC,OAAK;QACnC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAE;QACnD,OAAO,IAAI;IACb,GAAG,EAAE;IAEL,4CAA4C;IAC5C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAoB;QACxD,MAAM,OAAO,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG,IAAI,KAAK,EAAE,GAAG;QACnD,MAAM,OAAO,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG;QACpC,MAAM,OAAO,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG;QAEpC,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC;QACpC,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC;QAEvF,MAAM,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,KAAK,EAAE;QAChD,OAAO,CAAC,UAAU,GAAG,IAAI;IAC3B,GAAG,EAAE;IAEL,sCAAsC;IACtC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CACtC,OACA,KACA;QAEA,+CAA+C;QAC/C,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI;QAC9C,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI;QAE9C,8DAA8D;QAC9D,IAAI,gBAAgB,OAAO,GAAG,MAAM,KAAK,MAAM,KAAK,KAAK;YACvD,MAAM,iBAAiB,OAAO,qBAAqB;YACnD,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAC/B,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAC/B,WAAW,OAAO,GAAG;YACrB,gBAAgB,OAAO,GAAG;QAC5B,OAAO,IAAI,WAAW,OAAO,IAAI,gBAAgB,OAAO,GAAG,GAAG;YAC5D,oDAAoD;YACpD,WAAW,OAAO,GAAG;QACvB;QAEA,gBAAgB,OAAO;QAEvB,OAAO;YAAE;YAAK;QAAI;IACpB,GAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,MAAM,GAAG,GAAG;QAEpD,MAAM,kBAAkB,MAAM,SAAS,CAAC,qBAAqB,OAAO,CAAC;QACrE,MAAM,oBAAoB,qBAAqB,OAAO,GAAG;QAEzD,8CAA8C;QAC9C,IAAI,qBAAqB,MAAM,SAAS,CAAC,MAAM,EAAE;YAC/C,6BAA6B;YAC7B,qBAAqB,OAAO,GAAG;YAC/B,SAAS,OAAO,GAAG;YACnB,eAAe;YACf;QACF;QAEA,MAAM,eAAe,MAAM,SAAS,CAAC,kBAAkB;QAEvD,yBAAyB;QACzB,MAAM,cAAc,oBAAoB,iBAAiB,cAAc,SAAS,OAAO;QAEvF,4CAA4C;QAC5C,MAAM,UAAU,iBAAiB,iBAAiB;QAElD,iCAAiC;QACjC,MAAM,kBAAmC;YACvC,KAAK,YAAY,GAAG;YACpB,KAAK,YAAY,GAAG;YACpB,WAAW,IAAI;YACf,OAAO,KAAK,KAAK,MAAM,KAAK;YAC5B,SAAS;QACX;QAEA,kBAAkB;QAClB,SAAS,OAAO,IAAI,OAAO,OAAO,0BAA0B;QAE5D,2CAA2C;QAC3C,IAAI,SAAS,OAAO,IAAI,GAAG;YACzB,qBAAqB,OAAO,GAAG;YAC/B,SAAS,OAAO,GAAG;QACrB;QAEA,sDAAsD;QACtD,MAAM,SAAS,WAAW,OAAO,GAAG,cAAc;QAClD,eAAe;QAEf,0BAA0B;QAC1B,iBAAiB;IACnB,GAAG;QAAC,MAAM,SAAS;QAAE;QAAO;QAAkB;QAAgB;QAAqB;KAAiB;IAEpG,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,MAAM,SAAS,CAAC,MAAM,IAAI,GAAG;YAC3C,mBAAmB;YACnB,YAAY,OAAO,GAAG,YAAY,gBAAgB,OAAO,yBAAyB;QACpF,OAAO;YACL,kBAAkB;YAClB,IAAI,YAAY,OAAO,EAAE;gBACvB,cAAc,YAAY,OAAO;gBACjC,YAAY,OAAO,GAAG;YACxB;QACF;QAEA,qBAAqB;QACrB,OAAO;YACL,IAAI,YAAY,OAAO,EAAE;gBACvB,cAAc,YAAY,OAAO;YACnC;QACF;IACF,GAAG;QAAC;QAAU,MAAM,SAAS,CAAC,MAAM;QAAE;KAAe;IAErD,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB,OAAO,GAAG;QAC/B,SAAS,OAAO,GAAG;QACnB,WAAW,OAAO,GAAG;QACrB,gBAAgB,OAAO,GAAG;IAC5B,GAAG;QAAC;KAAM;IAEV,iDAAiD;IACjD,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/AlertBanner.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { AlertBannerProps, Alert } from '@/types';\n\nconst AlertBanner: React.FC<AlertBannerProps> = ({\n  alerts,\n  onDismiss,\n  maxVisible = 3,\n}) => {\n  // Filter active alerts and limit to maxVisible\n  const activeAlerts = alerts\n    .filter(alert => alert.isActive)\n    .slice(0, maxVisible);\n\n  if (activeAlerts.length === 0) {\n    return null;\n  }\n\n  // Get alert styling based on type\n  const getAlertStyles = (type: Alert['type']) => {\n    switch (type) {\n      case 'error':\n        return {\n          container: 'bg-red-50 border-red-200 text-red-800',\n          icon: '🚨',\n          iconBg: 'bg-red-100',\n        };\n      case 'warning':\n        return {\n          container: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n          icon: '⚠️',\n          iconBg: 'bg-yellow-100',\n        };\n      case 'info':\n        return {\n          container: 'bg-blue-50 border-blue-200 text-blue-800',\n          icon: 'ℹ️',\n          iconBg: 'bg-blue-100',\n        };\n      default:\n        return {\n          container: 'bg-gray-50 border-gray-200 text-gray-800',\n          icon: '📢',\n          iconBg: 'bg-gray-100',\n        };\n    }\n  };\n\n  // Format timestamp\n  const formatTime = (timestamp: Date) => {\n    return timestamp.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit',\n    });\n  };\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-md\">\n      {activeAlerts.map((alert) => {\n        const styles = getAlertStyles(alert.type);\n        \n        return (\n          <div\n            key={alert.id}\n            className={`\n              ${styles.container}\n              border rounded-lg shadow-lg p-4 \n              transform transition-all duration-300 ease-in-out\n              animate-slide-in-right\n            `}\n            role=\"alert\"\n          >\n            <div className=\"flex items-start space-x-3\">\n              {/* Alert Icon */}\n              <div className={`\n                ${styles.iconBg} \n                rounded-full p-1 flex-shrink-0 mt-0.5\n              `}>\n                <span className=\"text-sm\">{styles.icon}</span>\n              </div>\n\n              {/* Alert Content */}\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium leading-5\">\n                      {alert.message}\n                    </p>\n                    <div className=\"mt-1 flex items-center space-x-2 text-xs opacity-75\">\n                      <span>Vehicle: {alert.vehicleId}</span>\n                      <span>•</span>\n                      <span>{formatTime(alert.timestamp)}</span>\n                    </div>\n                  </div>\n\n                  {/* Dismiss Button */}\n                  <button\n                    onClick={() => onDismiss(alert.id)}\n                    className=\"\n                      ml-2 flex-shrink-0 rounded-md p-1.5 \n                      hover:bg-black hover:bg-opacity-10 \n                      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent\n                      transition-colors duration-200\n                    \"\n                    aria-label=\"Dismiss alert\"\n                  >\n                    <svg\n                      className=\"h-4 w-4\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M6 18L18 6M6 6l12 12\"\n                      />\n                    </svg>\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Progress bar for auto-dismiss (optional) */}\n            {alert.type === 'info' && (\n              <div className=\"mt-3 w-full bg-black bg-opacity-10 rounded-full h-1\">\n                <div \n                  className=\"bg-current h-1 rounded-full transition-all duration-1000 ease-linear\"\n                  style={{ width: '100%' }}\n                />\n              </div>\n            )}\n          </div>\n        );\n      })}\n\n      {/* Alert counter if there are more alerts */}\n      {alerts.filter(a => a.isActive).length > maxVisible && (\n        <div className=\"text-center\">\n          <div className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n            +{alerts.filter(a => a.isActive).length - maxVisible} more alerts\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AlertBanner;\n"], "names": [], "mappings": ";;;;AAAA;;AAKA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,SAAS,EACT,aAAa,CAAC,EACf;IACC,+CAA+C;IAC/C,MAAM,eAAe,OAClB,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,EAC9B,KAAK,CAAC,GAAG;IAEZ,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,QAAQ;gBACV;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,QAAQ;gBACV;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,QAAQ;gBACV;YACF;gBACE,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,QAAQ;gBACV;QACJ;IACF;IAEA,mBAAmB;IACnB,MAAM,aAAa,CAAC;QAClB,OAAO,UAAU,kBAAkB,CAAC,SAAS;YAC3C,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,aAAa,GAAG,CAAC,CAAC;gBACjB,MAAM,SAAS,eAAe,MAAM,IAAI;gBAExC,qBACE,8OAAC;oBAEC,WAAW,CAAC;cACV,EAAE,OAAO,SAAS,CAAC;;;;YAIrB,CAAC;oBACD,MAAK;;sCAEL,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAW,CAAC;gBACf,EAAE,OAAO,MAAM,CAAC;;cAElB,CAAC;8CACC,cAAA,8OAAC;wCAAK,WAAU;kDAAW,OAAO,IAAI;;;;;;;;;;;8CAIxC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,MAAM,OAAO;;;;;;kEAEhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAK;oEAAU,MAAM,SAAS;;;;;;;0EAC/B,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAM,WAAW,MAAM,SAAS;;;;;;;;;;;;;;;;;;0DAKrC,8OAAC;gDACC,SAAS,IAAM,UAAU,MAAM,EAAE;gDACjC,WAAU;gDAMV,cAAW;0DAEX,cAAA,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,QAAO;8DAEP,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASb,MAAM,IAAI,KAAK,wBACd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAO;;;;;;;;;;;;mBAlExB,MAAM,EAAE;;;;;YAwEnB;YAGC,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM,GAAG,4BACvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBAAgG;wBAC3G,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM,GAAG;wBAAW;;;;;;;;;;;;;;;;;;AAMjE;uCAEe", "debugId": null}}, {"offset": {"line": 686, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback, useEffect } from 'react';\nimport TrackingMap from '@/components/TrackingMap';\nimport VehicleTracker from '@/components/VehicleTracker';\nimport AlertBanner from '@/components/AlertBanner';\nimport ClientTimestamp from '@/components/ClientTimestamp';\nimport { Route, VehicleState, VehiclePosition, Alert } from '@/types';\n\n// Sample route data (Riyadh City Route)\nconst sampleRoute: Route = {\n  id: 'route-1',\n  name: 'Riyadh City Route',\n  waypoints: [\n    { lat: 24.7136, lng: 46.6753 }, // King Fahd Road (Start)\n    { lat: 24.7200, lng: 46.6800 }, // Olaya District\n    { lat: 24.7280, lng: 46.6850 }, // Al Malaz\n    { lat: 24.7350, lng: 46.6900 }, // King Abdulaziz Road\n    { lat: 24.7420, lng: 46.6950 }, // Diplomatic Quarter\n    { lat: 24.7480, lng: 46.7000 }, // Al <PERSON>\n    { lat: 24.7540, lng: 46.7050 }, // Al <PERSON>\n    { lat: 24.7600, lng: 46.7100 }, // King Khalid Airport Road\n    { lat: 24.7660, lng: 46.7150 }, // Al Rawdah\n    { lat: 24.7720, lng: 46.7200 }, // Northern Ring Road (End)\n  ],\n  color: '#2563eb',\n  strokeWeight: 4,\n};\n\nexport default function VehicleTrackingDashboard() {\n  const [vehicle, setVehicle] = useState<VehicleState>({\n    id: 'vehicle-001',\n    position: {\n      lat: sampleRoute.waypoints[0].lat,\n      lng: sampleRoute.waypoints[0].lng,\n      timestamp: new Date(),\n    },\n    isOnRoute: true,\n    currentWaypointIndex: 0,\n    distanceFromRoute: 0,\n    status: 'on-route',\n  });\n\n  const [alerts, setAlerts] = useState<Alert[]>([]);\n  const [isSimulationActive, setIsSimulationActive] = useState(true);\n\n  // Calculate distance from route using Google Maps geometry (simplified version)\n  const calculateDistanceFromRoute = useCallback((position: VehiclePosition): number => {\n    // Simplified distance calculation - in a real app, use Google Maps geometry library\n    let minDistance = Infinity;\n\n    for (let i = 0; i < sampleRoute.waypoints.length - 1; i++) {\n      const start = sampleRoute.waypoints[i];\n      const end = sampleRoute.waypoints[i + 1];\n\n      // Calculate distance from point to line segment (simplified)\n      const distance = Math.sqrt(\n        Math.pow((position.lat - start.lat) * 111000, 2) +\n        Math.pow((position.lng - start.lng) * 111000 * Math.cos(start.lat * Math.PI / 180), 2)\n      );\n\n      minDistance = Math.min(minDistance, distance);\n    }\n\n    return minDistance;\n  }, []);\n\n  // Handle vehicle position updates\n  const handleVehicleUpdate = useCallback((newVehicle: VehicleState) => {\n    setVehicle(newVehicle);\n  }, []);\n\n  // Handle position updates from tracker\n  const handlePositionUpdate = useCallback((position: VehiclePosition) => {\n    const distanceFromRoute = calculateDistanceFromRoute(position);\n    const isOnRoute = distanceFromRoute <= 100; // 100 meter tolerance\n\n    setVehicle(prev => ({\n      ...prev,\n      position,\n      distanceFromRoute,\n      isOnRoute,\n    }));\n  }, [calculateDistanceFromRoute]);\n\n  // Handle status changes from tracker\n  const handleStatusChange = useCallback((status: VehicleState['status']) => {\n    setVehicle(prev => ({ ...prev, status }));\n\n    // Create alert if vehicle goes off-route\n    if (status === 'off-route') {\n      const newAlert: Alert = {\n        id: `alert-${Date.now()}`,\n        type: 'warning',\n        message: 'Vehicle is outside allowed route',\n        timestamp: new Date(),\n        vehicleId: vehicle.id,\n        isActive: true,\n      };\n\n      setAlerts(prev => [...prev, newAlert]);\n    }\n  }, [vehicle.id]);\n\n  // Handle alert creation\n  const handleAlert = useCallback((alert: Alert) => {\n    setAlerts(prev => [...prev, alert]);\n  }, []);\n\n  // Handle alert dismissal\n  const handleAlertDismiss = useCallback((alertId: string) => {\n    setAlerts(prev =>\n      prev.map(alert =>\n        alert.id === alertId\n          ? { ...alert, isActive: false }\n          : alert\n      )\n    );\n  }, []);\n\n  // Auto-dismiss info alerts after 5 seconds\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setAlerts(prev =>\n        prev.map(alert =>\n          alert.type === 'info' && alert.isActive\n            ? { ...alert, isActive: false }\n            : alert\n        )\n      );\n    }, 5000);\n\n    return () => clearTimeout(timer);\n  }, [alerts]);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                Vehicle Tracking Dashboard\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => setIsSimulationActive(!isSimulationActive)}\n                className={`\n                  px-4 py-2 rounded-md text-sm font-medium transition-colors\n                  ${isSimulationActive\n                    ? 'bg-red-600 text-white hover:bg-red-700'\n                    : 'bg-green-600 text-white hover:bg-green-700'\n                  }\n                `}\n              >\n                {isSimulationActive ? 'Stop Simulation' : 'Start Simulation'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n          {/* Status Panel */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                Vehicle Status\n              </h2>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Vehicle ID</label>\n                  <p className=\"text-lg font-mono\">{vehicle.id}</p>\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Status</label>\n                  <div className=\"flex items-center space-x-2\">\n                    <div\n                      className={`w-3 h-3 rounded-full ${\n                        vehicle.isOnRoute ? 'bg-green-500' : 'bg-red-500'\n                      }`}\n                    />\n                    <span className={`font-medium ${\n                      vehicle.isOnRoute ? 'text-green-700' : 'text-red-700'\n                    }`}>\n                      {vehicle.isOnRoute ? 'On Route' : 'Off Route'}\n                    </span>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Distance from Route</label>\n                  <p className=\"text-lg\">{Math.round(vehicle.distanceFromRoute)}m</p>\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Last Update</label>\n                  <p className=\"text-sm text-gray-600\">\n                    {vehicle.position.timestamp.toLocaleTimeString()}\n                  </p>\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Speed</label>\n                  <p className=\"text-lg\">\n                    {vehicle.position.speed ? `${Math.round(vehicle.position.speed)} km/h` : 'N/A'}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Map */}\n          <div className=\"lg:col-span-3\">\n            <div className=\"bg-white rounded-lg shadow overflow-hidden\" style={{ height: '600px' }}>\n              <TrackingMap\n                route={sampleRoute}\n                vehicle={vehicle}\n                onVehicleUpdate={handleVehicleUpdate}\n                onAlert={handleAlert}\n              />\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Vehicle Tracker (invisible component) */}\n      <VehicleTracker\n        route={sampleRoute}\n        onPositionUpdate={handlePositionUpdate}\n        onStatusChange={handleStatusChange}\n        isActive={isSimulationActive}\n        speed={1}\n      />\n\n      {/* Alert Banner */}\n      <AlertBanner\n        alerts={alerts}\n        onDismiss={handleAlertDismiss}\n        maxVisible={3}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AASA,wCAAwC;AACxC,MAAM,cAAqB;IACzB,IAAI;IACJ,MAAM;IACN,WAAW;QACT;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;QAC7B;YAAE,KAAK;YAAS,KAAK;QAAQ;KAC9B;IACD,OAAO;IACP,cAAc;AAChB;AAEe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACnD,IAAI;QACJ,UAAU;YACR,KAAK,YAAY,SAAS,CAAC,EAAE,CAAC,GAAG;YACjC,KAAK,YAAY,SAAS,CAAC,EAAE,CAAC,GAAG;YACjC,WAAW,IAAI;QACjB;QACA,WAAW;QACX,sBAAsB;QACtB,mBAAmB;QACnB,QAAQ;IACV;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,gFAAgF;IAChF,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9C,oFAAoF;QACpF,IAAI,cAAc;QAElB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,SAAS,CAAC,MAAM,GAAG,GAAG,IAAK;YACzD,MAAM,QAAQ,YAAY,SAAS,CAAC,EAAE;YACtC,MAAM,MAAM,YAAY,SAAS,CAAC,IAAI,EAAE;YAExC,6DAA6D;YAC7D,MAAM,WAAW,KAAK,IAAI,CACxB,KAAK,GAAG,CAAC,CAAC,SAAS,GAAG,GAAG,MAAM,GAAG,IAAI,QAAQ,KAC9C,KAAK,GAAG,CAAC,CAAC,SAAS,GAAG,GAAG,MAAM,GAAG,IAAI,SAAS,KAAK,GAAG,CAAC,MAAM,GAAG,GAAG,KAAK,EAAE,GAAG,MAAM;YAGtF,cAAc,KAAK,GAAG,CAAC,aAAa;QACtC;QAEA,OAAO;IACT,GAAG,EAAE;IAEL,kCAAkC;IAClC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,WAAW;IACb,GAAG,EAAE;IAEL,uCAAuC;IACvC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,MAAM,oBAAoB,2BAA2B;QACrD,MAAM,YAAY,qBAAqB,KAAK,sBAAsB;QAElE,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP;gBACA;gBACA;YACF,CAAC;IACH,GAAG;QAAC;KAA2B;IAE/B,qCAAqC;IACrC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAO,CAAC;QAEvC,yCAAyC;QACzC,IAAI,WAAW,aAAa;YAC1B,MAAM,WAAkB;gBACtB,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;gBACzB,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;gBACf,WAAW,QAAQ,EAAE;gBACrB,UAAU;YACZ;YAEA,UAAU,CAAA,OAAQ;uBAAI;oBAAM;iBAAS;QACvC;IACF,GAAG;QAAC,QAAQ,EAAE;KAAC;IAEf,wBAAwB;IACxB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAM;IACpC,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,UAAU,CAAA,OACR,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,UACT;oBAAE,GAAG,KAAK;oBAAE,UAAU;gBAAM,IAC5B;IAGV,GAAG,EAAE;IAEL,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,UAAU,CAAA,OACR,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,IAAI,KAAK,UAAU,MAAM,QAAQ,GACnC;wBAAE,GAAG,KAAK;wBAAE,UAAU;oBAAM,IAC5B;QAGV,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;0CAInD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS,IAAM,sBAAsB,CAAC;oCACtC,WAAW,CAAC;;kBAEV,EAAE,qBACE,2CACA,6CACH;gBACH,CAAC;8CAEA,qBAAqB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpD,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAIzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAqB,QAAQ,EAAE;;;;;;;;;;;;0DAG9C,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,WAAW,CAAC,qBAAqB,EAC/B,QAAQ,SAAS,GAAG,iBAAiB,cACrC;;;;;;0EAEJ,8OAAC;gEAAK,WAAW,CAAC,YAAY,EAC5B,QAAQ,SAAS,GAAG,mBAAmB,gBACvC;0EACC,QAAQ,SAAS,GAAG,aAAa;;;;;;;;;;;;;;;;;;0DAKxC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;;4DAAW,KAAK,KAAK,CAAC,QAAQ,iBAAiB;4DAAE;;;;;;;;;;;;;0DAGhE,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEACV,QAAQ,QAAQ,CAAC,SAAS,CAAC,kBAAkB;;;;;;;;;;;;0DAIlD,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEACV,QAAQ,QAAQ,CAAC,KAAK,GAAG,GAAG,KAAK,KAAK,CAAC,QAAQ,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQnF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAA6C,OAAO;oCAAE,QAAQ;gCAAQ;0CACnF,cAAA,8OAAC,iIAAA,CAAA,UAAW;oCACV,OAAO;oCACP,SAAS;oCACT,iBAAiB;oCACjB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnB,8OAAC,oIAAA,CAAA,UAAc;gBACb,OAAO;gBACP,kBAAkB;gBAClB,gBAAgB;gBAChB,UAAU;gBACV,OAAO;;;;;;0BAIT,8OAAC,iIAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,WAAW;gBACX,YAAY;;;;;;;;;;;;AAIpB", "debugId": null}}]}