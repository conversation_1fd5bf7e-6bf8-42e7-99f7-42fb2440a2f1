{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\n\nconst Navigation: React.FC = () => {\n  const pathname = usePathname();\n\n  const navItems = [\n    {\n      href: '/',\n      label: 'Live Tracking',\n      icon: '🗺️',\n      description: 'Real-time vehicle tracking'\n    },\n    {\n      href: '/dashboard/monitoring',\n      label: 'Monitoring',\n      icon: '📊',\n      description: 'Analytics and logs'\n    },\n    {\n      href: '/dashboard/analytics',\n      label: 'Analytics',\n      icon: '📈',\n      description: 'Data visualization and insights'\n    }\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo/Brand */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <h1 className=\"text-xl font-bold text-gray-900\">\n                🚛 Vehicle Tracking System\n              </h1>\n            </div>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navItems.map((item) => {\n                const isActive = pathname === item.href;\n                return (\n                  <Link\n                    key={item.href}\n                    href={item.href}\n                    className={`\n                      px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\n                      ${isActive\n                        ? 'bg-blue-100 text-blue-700 border border-blue-200'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                      }\n                    `}\n                  >\n                    <span className=\"mr-2\">{item.icon}</span>\n                    {item.label}\n                  </Link>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"bg-gray-100 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n              aria-expanded=\"false\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              <svg\n                className=\"block h-6 w-6\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n                aria-hidden=\"true\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth=\"2\"\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3\">\n            {navItems.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`\n                    block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200\n                    ${isActive\n                      ? 'bg-blue-100 text-blue-700 border border-blue-200'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                    }\n                  `}\n                >\n                  <div className=\"flex items-center\">\n                    <span className=\"mr-3\">{item.icon}</span>\n                    <div>\n                      <div>{item.label}</div>\n                      <div className=\"text-xs text-gray-500\">{item.description}</div>\n                    </div>\n                  </div>\n                </Link>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,aAAuB;IAC3B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;;;;;;sCAOpD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC;oCACb,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC;;sBAEV,EAAE,WACE,qDACA,sDACH;oBACH,CAAC;;0DAED,8OAAC;gDAAK,WAAU;0DAAQ,KAAK,IAAI;;;;;;4CAChC,KAAK,KAAK;;uCAXN,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;sCAKJ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,iBAAc;;kDAEd,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCACC,WAAU;wCACV,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAY;4CACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC;;oBAEV,EAAE,WACE,qDACA,sDACH;kBACH,CAAC;0CAED,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAQ,KAAK,IAAI;;;;;;sDACjC,8OAAC;;8DACC,8OAAC;8DAAK,KAAK,KAAK;;;;;;8DAChB,8OAAC;oDAAI,WAAU;8DAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;+BAdvD,KAAK,IAAI;;;;;wBAmBpB;;;;;;;;;;;;;;;;;;;;;;AAMZ;uCAEe", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/charts/AlertTrendsChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  LineChart,\n  Line,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer\n} from 'recharts';\nimport { AlertTrendsChartProps } from '@/types';\n\nconst AlertTrendsChart: React.FC<AlertTrendsChartProps> = ({\n  data,\n  loading = false,\n  error\n}) => {\n  // Custom tooltip component\n  const CustomTooltip = ({ active, payload, label }: any) => {\n    if (active && payload && payload.length) {\n      return (\n        <div className=\"bg-white p-3 border border-gray-200 rounded-lg shadow-lg\">\n          <p className=\"font-medium text-gray-900\">{`Date: ${label}`}</p>\n          {payload.map((entry: any, index: number) => (\n            <p key={index} style={{ color: entry.color }} className=\"text-sm\">\n              {`${entry.name}: ${entry.value}`}\n            </p>\n          ))}\n          <p className=\"text-sm text-gray-600 border-t pt-1 mt-1\">\n            Total: {payload.reduce((sum: number, entry: any) => sum + entry.value, 0)}\n          </p>\n        </div>\n      );\n    }\n    return null;\n  };\n\n  // Loading state\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-6 bg-gray-200 rounded mb-4 w-1/3\"></div>\n          <div className=\"h-64 bg-gray-200 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Alert Trends</h3>\n        <div className=\"text-center text-red-600\">\n          <p>Error loading chart data: {error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Empty data state\n  if (!data || data.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Alert Trends</h3>\n        <div className=\"text-center text-gray-500 py-8\">\n          <p>No alert data available for the selected time period</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Alert Trends Over Time</h3>\n        <div className=\"flex items-center space-x-4 text-sm\">\n          <div className=\"flex items-center\">\n            <div className=\"w-3 h-3 bg-yellow-500 rounded-full mr-2\"></div>\n            <span className=\"text-gray-600\">Warnings</span>\n          </div>\n          <div className=\"flex items-center\">\n            <div className=\"w-3 h-3 bg-red-500 rounded-full mr-2\"></div>\n            <span className=\"text-gray-600\">Errors</span>\n          </div>\n          <div className=\"flex items-center\">\n            <div className=\"w-3 h-3 bg-blue-500 rounded-full mr-2\"></div>\n            <span className=\"text-gray-600\">Info</span>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"h-80\">\n        <ResponsiveContainer width=\"100%\" height=\"100%\">\n          <LineChart\n            data={data}\n            margin={{\n              top: 5,\n              right: 30,\n              left: 20,\n              bottom: 5,\n            }}\n          >\n            <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n            <XAxis \n              dataKey=\"date\" \n              stroke=\"#6b7280\"\n              fontSize={12}\n              tickFormatter={(value) => {\n                const date = new Date(value);\n                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });\n              }}\n            />\n            <YAxis \n              stroke=\"#6b7280\"\n              fontSize={12}\n            />\n            <Tooltip content={<CustomTooltip />} />\n            <Legend />\n            <Line\n              type=\"monotone\"\n              dataKey=\"warning\"\n              stroke=\"#f59e0b\"\n              strokeWidth={2}\n              dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}\n              activeDot={{ r: 6, stroke: '#f59e0b', strokeWidth: 2 }}\n              name=\"Warnings\"\n            />\n            <Line\n              type=\"monotone\"\n              dataKey=\"error\"\n              stroke=\"#ef4444\"\n              strokeWidth={2}\n              dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}\n              activeDot={{ r: 6, stroke: '#ef4444', strokeWidth: 2 }}\n              name=\"Errors\"\n            />\n            <Line\n              type=\"monotone\"\n              dataKey=\"info\"\n              stroke=\"#3b82f6\"\n              strokeWidth={2}\n              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}\n              activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}\n              name=\"Info\"\n            />\n          </LineChart>\n        </ResponsiveContainer>\n      </div>\n\n      {/* Summary statistics */}\n      <div className=\"mt-4 pt-4 border-t border-gray-200\">\n        <div className=\"grid grid-cols-3 gap-4 text-center\">\n          <div>\n            <p className=\"text-2xl font-bold text-yellow-600\">\n              {data.reduce((sum, item) => sum + item.warning, 0)}\n            </p>\n            <p className=\"text-sm text-gray-600\">Total Warnings</p>\n          </div>\n          <div>\n            <p className=\"text-2xl font-bold text-red-600\">\n              {data.reduce((sum, item) => sum + item.error, 0)}\n            </p>\n            <p className=\"text-sm text-gray-600\">Total Errors</p>\n          </div>\n          <div>\n            <p className=\"text-2xl font-bold text-blue-600\">\n              {data.reduce((sum, item) => sum + item.info, 0)}\n            </p>\n            <p className=\"text-sm text-gray-600\">Total Info</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AlertTrendsChart;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAeA,MAAM,mBAAoD,CAAC,EACzD,IAAI,EACJ,UAAU,KAAK,EACf,KAAK,EACN;IACC,2BAA2B;IAC3B,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;QACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAA6B,CAAC,MAAM,EAAE,OAAO;;;;;;oBACzD,QAAQ,GAAG,CAAC,CAAC,OAAY,sBACxB,8OAAC;4BAAc,OAAO;gCAAE,OAAO,MAAM,KAAK;4BAAC;4BAAG,WAAU;sCACrD,GAAG,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,KAAK,EAAE;2BAD1B;;;;;kCAIV,8OAAC;wBAAE,WAAU;;4BAA2C;4BAC9C,QAAQ,MAAM,CAAC,CAAC,KAAa,QAAe,MAAM,MAAM,KAAK,EAAE;;;;;;;;;;;;;QAI/E;QACA,OAAO;IACT;IAEA,gBAAgB;IAChB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;4BAAE;4BAA2B;;;;;;;;;;;;;;;;;;IAItC;IAEA,mBAAmB;IACnB,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;0BAKtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;wBACR,MAAM;wBACN,QAAQ;4BACN,KAAK;4BACL,OAAO;4BACP,MAAM;4BACN,QAAQ;wBACV;;0CAEA,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,QAAO;;;;;;0CAC5C,8OAAC,qJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,QAAO;gCACP,UAAU;gCACV,eAAe,CAAC;oCACd,MAAM,OAAO,IAAI,KAAK;oCACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;wCAAE,OAAO;wCAAS,KAAK;oCAAU;gCAC3E;;;;;;0CAEF,8OAAC,qJAAA,CAAA,QAAK;gCACJ,QAAO;gCACP,UAAU;;;;;;0CAEZ,8OAAC,uJAAA,CAAA,UAAO;gCAAC,uBAAS,8OAAC;;;;;;;;;;0CACnB,8OAAC,sJAAA,CAAA,SAAM;;;;;0CACP,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;oCAAE,MAAM;oCAAW,aAAa;oCAAG,GAAG;gCAAE;gCAC7C,WAAW;oCAAE,GAAG;oCAAG,QAAQ;oCAAW,aAAa;gCAAE;gCACrD,MAAK;;;;;;0CAEP,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;oCAAE,MAAM;oCAAW,aAAa;oCAAG,GAAG;gCAAE;gCAC7C,WAAW;oCAAE,GAAG;oCAAG,QAAQ;oCAAW,aAAa;gCAAE;gCACrD,MAAK;;;;;;0CAEP,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;gCACb,KAAK;oCAAE,MAAM;oCAAW,aAAa;oCAAG,GAAG;gCAAE;gCAC7C,WAAW;oCAAE,GAAG;oCAAG,QAAQ;oCAAW,aAAa;gCAAE;gCACrD,MAAK;;;;;;;;;;;;;;;;;;;;;;0BAOb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CACV,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,OAAO,EAAE;;;;;;8CAElD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CACV,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;;;;;;8CAEhD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CACV,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,IAAI,EAAE;;;;;;8CAE/C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD;uCAEe", "debugId": null}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/charts/VehicleStatusChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Pie,\n  Cell,\n  ResponsiveContainer,\n  <PERSON><PERSON><PERSON>,\n  Legend\n} from 'recharts';\nimport { VehicleStatusChartProps } from '@/types';\n\nconst VehicleStatusChart: React.FC<VehicleStatusChartProps> = ({\n  data,\n  loading = false,\n  error\n}) => {\n  // Custom tooltip component\n  const CustomTooltip = ({ active, payload }: any) => {\n    if (active && payload && payload.length) {\n      const data = payload[0].payload;\n      return (\n        <div className=\"bg-white p-3 border border-gray-200 rounded-lg shadow-lg\">\n          <p className=\"font-medium text-gray-900 capitalize\">{data.status.replace('-', ' ')}</p>\n          <p className=\"text-sm text-gray-600\">Count: {data.count}</p>\n          <p className=\"text-sm text-gray-600\">Percentage: {data.percentage.toFixed(1)}%</p>\n        </div>\n      );\n    }\n    return null;\n  };\n\n  // Custom label function\n  const renderLabel = (entry: any) => {\n    return `${entry.percentage.toFixed(1)}%`;\n  };\n\n  // Loading state\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-6 bg-gray-200 rounded mb-4 w-1/3\"></div>\n          <div className=\"h-64 bg-gray-200 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Vehicle Status Distribution</h3>\n        <div className=\"text-center text-red-600\">\n          <p>Error loading chart data: {error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Empty data state\n  if (!data || data.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Vehicle Status Distribution</h3>\n        <div className=\"text-center text-gray-500 py-8\">\n          <p>No vehicle status data available</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Vehicle Status Distribution</h3>\n        <div className=\"text-sm text-gray-600\">\n          Total Vehicles: {data.reduce((sum, item) => sum + item.count, 0)}\n        </div>\n      </div>\n\n      <div className=\"h-80\">\n        <ResponsiveContainer width=\"100%\" height=\"100%\">\n          <PieChart>\n            <Pie\n              data={data}\n              cx=\"50%\"\n              cy=\"50%\"\n              labelLine={false}\n              label={renderLabel}\n              outerRadius={100}\n              fill=\"#8884d8\"\n              dataKey=\"count\"\n              stroke=\"#ffffff\"\n              strokeWidth={2}\n            >\n              {data.map((entry, index) => (\n                <Cell key={`cell-${index}`} fill={entry.color} />\n              ))}\n            </Pie>\n            <Tooltip content={<CustomTooltip />} />\n            <Legend \n              verticalAlign=\"bottom\" \n              height={36}\n              formatter={(value, entry: any) => (\n                <span style={{ color: entry.color }} className=\"capitalize\">\n                  {value.replace('-', ' ')}\n                </span>\n              )}\n            />\n          </PieChart>\n        </ResponsiveContainer>\n      </div>\n\n      {/* Status breakdown */}\n      <div className=\"mt-4 pt-4 border-t border-gray-200\">\n        <div className=\"space-y-3\">\n          {data.map((item, index) => (\n            <div key={index} className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div \n                  className=\"w-4 h-4 rounded-full mr-3\"\n                  style={{ backgroundColor: item.color }}\n                ></div>\n                <span className=\"text-sm font-medium text-gray-900 capitalize\">\n                  {item.status.replace('-', ' ')}\n                </span>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"text-sm text-gray-600\">\n                  {item.count} vehicles\n                </span>\n                <span className=\"text-sm font-medium text-gray-900\">\n                  {item.percentage.toFixed(1)}%\n                </span>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Status indicators */}\n      <div className=\"mt-4 pt-4 border-t border-gray-200\">\n        <div className=\"grid grid-cols-3 gap-4 text-center\">\n          {data.map((item, index) => (\n            <div key={index} className=\"p-3 rounded-lg\" style={{ backgroundColor: `${item.color}15` }}>\n              <div className=\"flex items-center justify-center mb-2\">\n                <div \n                  className=\"w-3 h-3 rounded-full mr-2\"\n                  style={{ backgroundColor: item.color }}\n                ></div>\n                <span className=\"text-xs font-medium text-gray-700 uppercase tracking-wide\">\n                  {item.status.replace('-', ' ')}\n                </span>\n              </div>\n              <p className=\"text-2xl font-bold\" style={{ color: item.color }}>\n                {item.count}\n              </p>\n              <p className=\"text-xs text-gray-600\">\n                {item.percentage.toFixed(1)}% of fleet\n              </p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VehicleStatusChart;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAaA,MAAM,qBAAwD,CAAC,EAC7D,IAAI,EACJ,UAAU,KAAK,EACf,KAAK,EACN;IACC,2BAA2B;IAC3B,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAO;QAC7C,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;YAC/B,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAwC,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;kCAC9E,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAQ,KAAK,KAAK;;;;;;;kCACvD,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAa,KAAK,UAAU,CAAC,OAAO,CAAC;4BAAG;;;;;;;;;;;;;QAGnF;QACA,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAM,cAAc,CAAC;QACnB,OAAO,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1C;IAEA,gBAAgB;IAChB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;4BAAE;4BAA2B;;;;;;;;;;;;;;;;;;IAItC;IAEA,mBAAmB;IACnB,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;4BAAwB;4BACpB,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;;;;;;;;;;;;;0BAIlE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;0CACP,8OAAC,+IAAA,CAAA,MAAG;gCACF,MAAM;gCACN,IAAG;gCACH,IAAG;gCACH,WAAW;gCACX,OAAO;gCACP,aAAa;gCACb,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,aAAa;0CAEZ,KAAK,GAAG,CAAC,CAAC,OAAO,sBAChB,8OAAC,oJAAA,CAAA,OAAI;wCAAuB,MAAM,MAAM,KAAK;uCAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;0CAG9B,8OAAC,uJAAA,CAAA,UAAO;gCAAC,uBAAS,8OAAC;;;;;;;;;;0CACnB,8OAAC,sJAAA,CAAA,SAAM;gCACL,eAAc;gCACd,QAAQ;gCACR,WAAW,CAAC,OAAO,sBACjB,8OAAC;wCAAK,OAAO;4CAAE,OAAO,MAAM,KAAK;wCAAC;wCAAG,WAAU;kDAC5C,MAAM,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,KAAK,KAAK;4CAAC;;;;;;sDAEvC,8OAAC;4CAAK,WAAU;sDACb,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;8CAG9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;gDACb,KAAK,KAAK;gDAAC;;;;;;;sDAEd,8OAAC;4CAAK,WAAU;;gDACb,KAAK,UAAU,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;2BAfxB;;;;;;;;;;;;;;;0BAwBhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC;4BAAgB,WAAU;4BAAiB,OAAO;gCAAE,iBAAiB,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC;4BAAC;;8CACtF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,KAAK,KAAK;4CAAC;;;;;;sDAEvC,8OAAC;4CAAK,WAAU;sDACb,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;8CAG9B,8OAAC;oCAAE,WAAU;oCAAqB,OAAO;wCAAE,OAAO,KAAK,KAAK;oCAAC;8CAC1D,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAE,WAAU;;wCACV,KAAK,UAAU,CAAC,OAAO,CAAC;wCAAG;;;;;;;;2BAdtB;;;;;;;;;;;;;;;;;;;;;AAsBtB;uCAEe", "debugId": null}}, {"offset": {"line": 1236, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/charts/EventFrequencyChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  BarChart,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer\n} from 'recharts';\nimport { EventFrequencyChartProps } from '@/types';\n\nconst EventFrequencyChart: React.FC<EventFrequencyChartProps> = ({\n  data,\n  loading = false,\n  error\n}) => {\n  // Custom tooltip component\n  const CustomTooltip = ({ active, payload, label }: any) => {\n    if (active && payload && payload.length) {\n      const data = payload[0].payload;\n      return (\n        <div className=\"bg-white p-3 border border-gray-200 rounded-lg shadow-lg\">\n          <p className=\"font-medium text-gray-900\">{data.label}</p>\n          <p className=\"text-sm text-gray-600\">Count: {data.count}</p>\n          <p className=\"text-sm text-gray-600\">Event Type: {data.eventType}</p>\n        </div>\n      );\n    }\n    return null;\n  };\n\n  // Loading state\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-6 bg-gray-200 rounded mb-4 w-1/3\"></div>\n          <div className=\"h-64 bg-gray-200 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Event Frequency</h3>\n        <div className=\"text-center text-red-600\">\n          <p>Error loading chart data: {error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Empty data state\n  if (!data || data.length === 0) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Event Frequency</h3>\n        <div className=\"text-center text-gray-500 py-8\">\n          <p>No event data available for the selected time period</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Sort data by count for better visualization\n  const sortedData = [...data].sort((a, b) => b.count - a.count);\n\n  return (\n    <div className=\"bg-white rounded-lg shadow p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Event Frequency by Type</h3>\n        <div className=\"text-sm text-gray-600\">\n          Total Events: {data.reduce((sum, item) => sum + item.count, 0)}\n        </div>\n      </div>\n\n      <div className=\"h-80\">\n        <ResponsiveContainer width=\"100%\" height=\"100%\">\n          <BarChart\n            data={sortedData}\n            margin={{\n              top: 5,\n              right: 30,\n              left: 20,\n              bottom: 60,\n            }}\n          >\n            <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n            <XAxis \n              dataKey=\"label\"\n              stroke=\"#6b7280\"\n              fontSize={10}\n              angle={-45}\n              textAnchor=\"end\"\n              height={80}\n              interval={0}\n            />\n            <YAxis \n              stroke=\"#6b7280\"\n              fontSize={12}\n            />\n            <Tooltip content={<CustomTooltip />} />\n            <Bar \n              dataKey=\"count\" \n              fill={(entry: any) => entry.color}\n              radius={[4, 4, 0, 0]}\n            >\n              {sortedData.map((entry, index) => (\n                <Bar key={`bar-${index}`} fill={entry.color} />\n              ))}\n            </Bar>\n          </BarChart>\n        </ResponsiveContainer>\n      </div>\n\n      {/* Top events summary */}\n      <div className=\"mt-4 pt-4 border-t border-gray-200\">\n        <h4 className=\"text-sm font-medium text-gray-900 mb-3\">Top Event Types</h4>\n        <div className=\"space-y-2\">\n          {sortedData.slice(0, 5).map((item, index) => (\n            <div key={index} className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <div className=\"flex items-center justify-center w-6 h-6 rounded text-white text-xs font-bold mr-3\" style={{ backgroundColor: item.color }}>\n                  {index + 1}\n                </div>\n                <span className=\"text-sm text-gray-900\">{item.label}</span>\n              </div>\n              <span className=\"text-sm font-medium text-gray-900\">{item.count}</span>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Event categories */}\n      <div className=\"mt-4 pt-4 border-t border-gray-200\">\n        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n          {/* Operational Events */}\n          <div className=\"text-center\">\n            <p className=\"text-sm font-medium text-gray-900 mb-1\">Operational</p>\n            <p className=\"text-2xl font-bold text-blue-600\">\n              {data.filter(item => \n                ['position_update', 'route_started', 'route_completed', 'waypoint_reached'].includes(item.eventType)\n              ).reduce((sum, item) => sum + item.count, 0)}\n            </p>\n            <p className=\"text-xs text-gray-600\">Normal operations</p>\n          </div>\n\n          {/* Alert Events */}\n          <div className=\"text-center\">\n            <p className=\"text-sm font-medium text-gray-900 mb-1\">Alerts</p>\n            <p className=\"text-2xl font-bold text-yellow-600\">\n              {data.filter(item => \n                ['alert_triggered', 'route_deviation', 'speed_violation'].includes(item.eventType)\n              ).reduce((sum, item) => sum + item.count, 0)}\n            </p>\n            <p className=\"text-xs text-gray-600\">Warning events</p>\n          </div>\n\n          {/* Critical Events */}\n          <div className=\"text-center\">\n            <p className=\"text-sm font-medium text-gray-900 mb-1\">Critical</p>\n            <p className=\"text-2xl font-bold text-red-600\">\n              {data.filter(item => \n                ['emergency_stop'].includes(item.eventType)\n              ).reduce((sum, item) => sum + item.count, 0)}\n            </p>\n            <p className=\"text-xs text-gray-600\">Emergency events</p>\n          </div>\n\n          {/* Maintenance Events */}\n          <div className=\"text-center\">\n            <p className=\"text-sm font-medium text-gray-900 mb-1\">Maintenance</p>\n            <p className=\"text-2xl font-bold text-purple-600\">\n              {data.filter(item => \n                ['maintenance_due'].includes(item.eventType)\n              ).reduce((sum, item) => sum + item.count, 0)}\n            </p>\n            <p className=\"text-xs text-gray-600\">Service events</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EventFrequencyChart;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AAcA,MAAM,sBAA0D,CAAC,EAC/D,IAAI,EACJ,UAAU,KAAK,EACf,KAAK,EACN;IACC,2BAA2B;IAC3B,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;QACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;YAC/B,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAA6B,KAAK,KAAK;;;;;;kCACpD,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAQ,KAAK,KAAK;;;;;;;kCACvD,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAa,KAAK,SAAS;;;;;;;;;;;;;QAGtE;QACA,OAAO;IACT;IAEA,gBAAgB;IAChB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;4BAAE;4BAA2B;;;;;;;;;;;;;;;;;;IAItC;IAEA,mBAAmB;IACnB,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,8CAA8C;IAC9C,MAAM,aAAa;WAAI;KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;IAE7D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;4BAAwB;4BACtB,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;;;;;;;;;;;;;0BAIhE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;wBACP,MAAM;wBACN,QAAQ;4BACN,KAAK;4BACL,OAAO;4BACP,MAAM;4BACN,QAAQ;wBACV;;0CAEA,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,QAAO;;;;;;0CAC5C,8OAAC,qJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,QAAO;gCACP,UAAU;gCACV,OAAO,CAAC;gCACR,YAAW;gCACX,QAAQ;gCACR,UAAU;;;;;;0CAEZ,8OAAC,qJAAA,CAAA,QAAK;gCACJ,QAAO;gCACP,UAAU;;;;;;0CAEZ,8OAAC,uJAAA,CAAA,UAAO;gCAAC,uBAAS,8OAAC;;;;;;;;;;0CACnB,8OAAC,mJAAA,CAAA,MAAG;gCACF,SAAQ;gCACR,MAAM,CAAC,QAAe,MAAM,KAAK;gCACjC,QAAQ;oCAAC;oCAAG;oCAAG;oCAAG;iCAAE;0CAEnB,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,8OAAC,mJAAA,CAAA,MAAG;wCAAsB,MAAM,MAAM,KAAK;uCAAjC,CAAC,IAAI,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAI,WAAU;kCACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACjC,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAqF,OAAO;oDAAE,iBAAiB,KAAK,KAAK;gDAAC;0DACtI,QAAQ;;;;;;0DAEX,8OAAC;gDAAK,WAAU;0DAAyB,KAAK,KAAK;;;;;;;;;;;;kDAErD,8OAAC;wCAAK,WAAU;kDAAqC,KAAK,KAAK;;;;;;;+BAPvD;;;;;;;;;;;;;;;;0BAchB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAyC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CACV,KAAK,MAAM,CAAC,CAAA,OACX;4CAAC;4CAAmB;4CAAiB;4CAAmB;yCAAmB,CAAC,QAAQ,CAAC,KAAK,SAAS,GACnG,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;;;;;;8CAE5C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAyC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CACV,KAAK,MAAM,CAAC,CAAA,OACX;4CAAC;4CAAmB;4CAAmB;yCAAkB,CAAC,QAAQ,CAAC,KAAK,SAAS,GACjF,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;;;;;;8CAE5C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAyC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CACV,KAAK,MAAM,CAAC,CAAA,OACX;4CAAC;yCAAiB,CAAC,QAAQ,CAAC,KAAK,SAAS,GAC1C,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;;;;;;8CAE5C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAyC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CACV,KAAK,MAAM,CAAC,CAAA,OACX;4CAAC;yCAAkB,CAAC,QAAQ,CAAC,KAAK,SAAS,GAC3C,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;;;;;;8CAE5C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD;uCAEe", "debugId": null}}, {"offset": {"line": 1771, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/components/FleetMetrics.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { FleetMetricsProps } from '@/types';\n\nconst FleetMetrics: React.FC<FleetMetricsProps> = ({\n  metrics,\n  loading = false,\n  error\n}) => {\n  // Loading state\n  if (loading) {\n    return (\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {[...Array(8)].map((_, i) => (\n          <div key={i} className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"animate-pulse\">\n              <div className=\"h-4 bg-gray-200 rounded mb-2 w-3/4\"></div>\n              <div className=\"h-8 bg-gray-200 rounded mb-2\"></div>\n              <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <div className=\"text-center text-red-600\">\n          <p>Error loading fleet metrics: {error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Format time in hours and minutes\n  const formatTime = (minutes: number) => {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n  };\n\n  // Format large numbers\n  const formatNumber = (num: number) => {\n    if (num >= 1000) {\n      return `${(num / 1000).toFixed(1)}k`;\n    }\n    return num.toString();\n  };\n\n  const metricCards = [\n    {\n      title: 'Avg Route Time',\n      value: formatTime(metrics.averageRouteCompletionTime),\n      icon: '⏱️',\n      color: 'blue',\n      description: 'Average completion time',\n      trend: metrics.averageRouteCompletionTime < 60 ? 'good' : 'warning'\n    },\n    {\n      title: 'Total Distance',\n      value: `${formatNumber(metrics.totalDistanceTraveled)} km`,\n      icon: '🛣️',\n      color: 'green',\n      description: 'Distance traveled',\n      trend: 'neutral'\n    },\n    {\n      title: 'Fuel Efficiency',\n      value: `${metrics.fuelEfficiency} km/L`,\n      icon: '⛽',\n      color: 'yellow',\n      description: 'Average fuel efficiency',\n      trend: metrics.fuelEfficiency > 9 ? 'good' : 'warning'\n    },\n    {\n      title: 'On-Time Performance',\n      value: `${metrics.onTimePerformance}%`,\n      icon: '🎯',\n      color: 'purple',\n      description: 'Routes completed on time',\n      trend: metrics.onTimePerformance > 90 ? 'good' : metrics.onTimePerformance > 80 ? 'warning' : 'poor'\n    },\n    {\n      title: 'Total Trips',\n      value: formatNumber(metrics.totalTrips),\n      icon: '🚛',\n      color: 'indigo',\n      description: 'Completed trips',\n      trend: 'neutral'\n    },\n    {\n      title: 'Average Speed',\n      value: `${metrics.averageSpeed} km/h`,\n      icon: '🏃',\n      color: 'cyan',\n      description: 'Average vehicle speed',\n      trend: metrics.averageSpeed > 50 && metrics.averageSpeed < 70 ? 'good' : 'warning'\n    },\n    {\n      title: 'Maintenance Alerts',\n      value: metrics.maintenanceAlerts.toString(),\n      icon: '🔧',\n      color: 'orange',\n      description: 'Maintenance required',\n      trend: metrics.maintenanceAlerts < 3 ? 'good' : 'warning'\n    },\n    {\n      title: 'Emergency Stops',\n      value: metrics.emergencyStops.toString(),\n      icon: '🚨',\n      color: 'red',\n      description: 'Emergency incidents',\n      trend: metrics.emergencyStops === 0 ? 'good' : metrics.emergencyStops < 3 ? 'warning' : 'poor'\n    }\n  ];\n\n  const getColorClasses = (color: string) => {\n    const colors = {\n      blue: 'bg-blue-50 border-blue-200 text-blue-800',\n      green: 'bg-green-50 border-green-200 text-green-800',\n      yellow: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n      purple: 'bg-purple-50 border-purple-200 text-purple-800',\n      indigo: 'bg-indigo-50 border-indigo-200 text-indigo-800',\n      cyan: 'bg-cyan-50 border-cyan-200 text-cyan-800',\n      orange: 'bg-orange-50 border-orange-200 text-orange-800',\n      red: 'bg-red-50 border-red-200 text-red-800'\n    };\n    return colors[color as keyof typeof colors] || colors.blue;\n  };\n\n  const getTrendIcon = (trend: string) => {\n    switch (trend) {\n      case 'good':\n        return <span className=\"text-green-500\">↗️</span>;\n      case 'warning':\n        return <span className=\"text-yellow-500\">➡️</span>;\n      case 'poor':\n        return <span className=\"text-red-500\">↘️</span>;\n      default:\n        return <span className=\"text-gray-400\">➡️</span>;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">Fleet Performance Metrics</h2>\n        <div className=\"text-sm text-gray-600\">\n          Real-time fleet performance indicators\n        </div>\n      </div>\n\n      {/* Metrics Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {metricCards.map((metric, index) => (\n          <div\n            key={index}\n            className={`rounded-lg border p-6 ${getColorClasses(metric.color)}`}\n          >\n            <div className=\"flex items-center justify-between mb-2\">\n              <div className=\"text-2xl\">{metric.icon}</div>\n              {getTrendIcon(metric.trend)}\n            </div>\n            \n            <div className=\"mb-1\">\n              <h3 className=\"text-sm font-medium opacity-80\">{metric.title}</h3>\n            </div>\n            \n            <div className=\"mb-2\">\n              <p className=\"text-2xl font-bold\">{metric.value}</p>\n            </div>\n            \n            <div>\n              <p className=\"text-xs opacity-70\">{metric.description}</p>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Performance Summary */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Performance Summary</h3>\n        \n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Efficiency Score */}\n          <div className=\"text-center\">\n            <div className=\"mb-2\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full\">\n                <span className=\"text-2xl\">🏆</span>\n              </div>\n            </div>\n            <h4 className=\"text-lg font-semibold text-gray-900\">Efficiency Score</h4>\n            <p className=\"text-3xl font-bold text-green-600 mb-1\">\n              {Math.round((metrics.onTimePerformance + (metrics.fuelEfficiency * 10)) / 2)}%\n            </p>\n            <p className=\"text-sm text-gray-600\">Overall fleet efficiency</p>\n          </div>\n\n          {/* Safety Rating */}\n          <div className=\"text-center\">\n            <div className=\"mb-2\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full\">\n                <span className=\"text-2xl\">🛡️</span>\n              </div>\n            </div>\n            <h4 className=\"text-lg font-semibold text-gray-900\">Safety Rating</h4>\n            <p className=\"text-3xl font-bold text-blue-600 mb-1\">\n              {metrics.emergencyStops === 0 ? 'A+' : metrics.emergencyStops < 3 ? 'B+' : 'C'}\n            </p>\n            <p className=\"text-sm text-gray-600\">Based on incidents</p>\n          </div>\n\n          {/* Maintenance Health */}\n          <div className=\"text-center\">\n            <div className=\"mb-2\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full\">\n                <span className=\"text-2xl\">🔧</span>\n              </div>\n            </div>\n            <h4 className=\"text-lg font-semibold text-gray-900\">Maintenance Health</h4>\n            <p className=\"text-3xl font-bold text-orange-600 mb-1\">\n              {metrics.maintenanceAlerts < 3 ? 'Good' : metrics.maintenanceAlerts < 6 ? 'Fair' : 'Poor'}\n            </p>\n            <p className=\"text-sm text-gray-600\">Fleet maintenance status</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FleetMetrics;\n"], "names": [], "mappings": ";;;;AAAA;;AAKA,MAAM,eAA4C,CAAC,EACjD,OAAO,EACP,UAAU,KAAK,EACf,KAAK,EACN;IACC,gBAAgB;IAChB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oBAAY,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;mBAJT;;;;;;;;;;IAUlB;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;;wBAAE;wBAA8B;;;;;;;;;;;;;;;;;IAIzC;IAEA,mCAAmC;IACnC,MAAM,aAAa,CAAC;QAClB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,OAAO,UAAU;QACvB,OAAO,QAAQ,IAAI,GAAG,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;IACtD;IAEA,uBAAuB;IACvB,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,MAAM;YACf,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACtC;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,MAAM,cAAc;QAClB;YACE,OAAO;YACP,OAAO,WAAW,QAAQ,0BAA0B;YACpD,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO,QAAQ,0BAA0B,GAAG,KAAK,SAAS;QAC5D;QACA;YACE,OAAO;YACP,OAAO,GAAG,aAAa,QAAQ,qBAAqB,EAAE,GAAG,CAAC;YAC1D,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,GAAG,QAAQ,cAAc,CAAC,KAAK,CAAC;YACvC,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO,QAAQ,cAAc,GAAG,IAAI,SAAS;QAC/C;QACA;YACE,OAAO;YACP,OAAO,GAAG,QAAQ,iBAAiB,CAAC,CAAC,CAAC;YACtC,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO,QAAQ,iBAAiB,GAAG,KAAK,SAAS,QAAQ,iBAAiB,GAAG,KAAK,YAAY;QAChG;QACA;YACE,OAAO;YACP,OAAO,aAAa,QAAQ,UAAU;YACtC,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,GAAG,QAAQ,YAAY,CAAC,KAAK,CAAC;YACrC,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO,QAAQ,YAAY,GAAG,MAAM,QAAQ,YAAY,GAAG,KAAK,SAAS;QAC3E;QACA;YACE,OAAO;YACP,OAAO,QAAQ,iBAAiB,CAAC,QAAQ;YACzC,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO,QAAQ,iBAAiB,GAAG,IAAI,SAAS;QAClD;QACA;YACE,OAAO;YACP,OAAO,QAAQ,cAAc,CAAC,QAAQ;YACtC,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO,QAAQ,cAAc,KAAK,IAAI,SAAS,QAAQ,cAAc,GAAG,IAAI,YAAY;QAC1F;KACD;IAED,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS;YACb,MAAM;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,KAAK;QACP;QACA,OAAO,MAAM,CAAC,MAA6B,IAAI,OAAO,IAAI;IAC5D;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAiB;;;;;;YAC1C,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAkB;;;;;;YAC3C,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAe;;;;;;YACxC;gBACE,qBAAO,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;QAC3C;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;kCAAwB;;;;;;;;;;;;0BAMzC,8OAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;wBAEC,WAAW,CAAC,sBAAsB,EAAE,gBAAgB,OAAO,KAAK,GAAG;;0CAEnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAY,OAAO,IAAI;;;;;;oCACrC,aAAa,OAAO,KAAK;;;;;;;0CAG5B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkC,OAAO,KAAK;;;;;;;;;;;0CAG9D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAsB,OAAO,KAAK;;;;;;;;;;;0CAGjD,8OAAC;0CACC,cAAA,8OAAC;oCAAE,WAAU;8CAAsB,OAAO,WAAW;;;;;;;;;;;;uBAjBlD;;;;;;;;;;0BAwBX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAEzD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;;;;;;kDAG/B,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC;wCAAE,WAAU;;4CACV,KAAK,KAAK,CAAC,CAAC,QAAQ,iBAAiB,GAAI,QAAQ,cAAc,GAAG,EAAG,IAAI;4CAAG;;;;;;;kDAE/E,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;;;;;;kDAG/B,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC;wCAAE,WAAU;kDACV,QAAQ,cAAc,KAAK,IAAI,OAAO,QAAQ,cAAc,GAAG,IAAI,OAAO;;;;;;kDAE7E,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;;;;;;kDAG/B,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC;wCAAE,WAAU;kDACV,QAAQ,iBAAiB,GAAG,IAAI,SAAS,QAAQ,iBAAiB,GAAG,IAAI,SAAS;;;;;;kDAErF,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD;uCAEe", "debugId": null}}, {"offset": {"line": 2300, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/app/dashboard/analytics/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport Navigation from '@/components/Navigation';\nimport AlertTrendsChart from '@/components/charts/AlertTrendsChart';\nimport VehicleStatusChart from '@/components/charts/VehicleStatusChart';\nimport EventFrequencyChart from '@/components/charts/EventFrequencyChart';\nimport FleetMetrics from '@/components/FleetMetrics';\nimport { \n  TimeFilter, \n  AlertTrendData, \n  VehicleStatusData, \n  EventFrequencyData, \n  FleetPerformanceMetrics,\n  AnalyticsApiResponse \n} from '@/types';\n\nexport default function AnalyticsDashboard() {\n  const [timeFilter, setTimeFilter] = useState<TimeFilter>('7d');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  \n  // Chart data states\n  const [alertTrends, setAlertTrends] = useState<AlertTrendData[]>([]);\n  const [vehicleStatus, setVehicleStatus] = useState<VehicleStatusData[]>([]);\n  const [eventFrequency, setEventFrequency] = useState<EventFrequencyData[]>([]);\n  const [fleetMetrics, setFleetMetrics] = useState<FleetPerformanceMetrics | null>(null);\n\n  // Fetch analytics data\n  const fetchAnalyticsData = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch(`/api/analytics?timeFilter=${timeFilter}&type=all`);\n      const result: AnalyticsApiResponse<{\n        alertTrends: AlertTrendData[];\n        vehicleStatus: VehicleStatusData[];\n        eventFrequency: EventFrequencyData[];\n        fleetMetrics: FleetPerformanceMetrics;\n      }> = await response.json();\n\n      if (result.success) {\n        setAlertTrends(result.data.alertTrends);\n        setVehicleStatus(result.data.vehicleStatus);\n        setEventFrequency(result.data.eventFrequency);\n        setFleetMetrics(result.data.fleetMetrics);\n      } else {\n        setError(result.error || 'Failed to fetch analytics data');\n      }\n    } catch (err) {\n      setError('Network error occurred');\n    } finally {\n      setLoading(false);\n    }\n  }, [timeFilter]);\n\n  useEffect(() => {\n    fetchAnalyticsData();\n  }, [fetchAnalyticsData]);\n\n  // Time filter options\n  const timeFilterOptions: { value: TimeFilter; label: string }[] = [\n    { value: '24h', label: 'Last 24 Hours' },\n    { value: '7d', label: 'Last 7 Days' },\n    { value: '30d', label: 'Last 30 Days' },\n    { value: 'all', label: 'All Time' },\n  ];\n\n  // Export data functionality\n  const exportData = () => {\n    const data = {\n      timeFilter,\n      generatedAt: new Date().toISOString(),\n      alertTrends,\n      vehicleStatus,\n      eventFrequency,\n      fleetMetrics\n    };\n    \n    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `fleet-analytics-${timeFilter}-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation */}\n      <Navigation />\n\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                Analytics Dashboard\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              {/* Time Filter */}\n              <div className=\"flex items-center space-x-2\">\n                <label className=\"text-sm font-medium text-gray-700\">Time Period:</label>\n                <select\n                  value={timeFilter}\n                  onChange={(e) => setTimeFilter(e.target.value as TimeFilter)}\n                  className=\"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  {timeFilterOptions.map((option) => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Export Button */}\n              <button\n                onClick={exportData}\n                disabled={loading}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                📊 Export Data\n              </button>\n\n              {/* Refresh Button */}\n              <button\n                onClick={fetchAnalyticsData}\n                disabled={loading}\n                className=\"px-4 py-2 bg-gray-600 text-white rounded-md text-sm font-medium hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                🔄 Refresh\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Error State */}\n        {error && (\n          <div className=\"mb-6 bg-red-50 border border-red-200 rounded-lg p-4\">\n            <div className=\"flex items-center\">\n              <span className=\"text-red-500 mr-2\">❌</span>\n              <p className=\"text-red-800\">{error}</p>\n              <button\n                onClick={fetchAnalyticsData}\n                className=\"ml-auto text-red-600 hover:text-red-800 text-sm font-medium\"\n              >\n                Try Again\n              </button>\n            </div>\n          </div>\n        )}\n\n        <div className=\"space-y-8\">\n          {/* Fleet Performance Metrics */}\n          <FleetMetrics\n            metrics={fleetMetrics || {\n              averageRouteCompletionTime: 0,\n              totalDistanceTraveled: 0,\n              fuelEfficiency: 0,\n              onTimePerformance: 0,\n              totalTrips: 0,\n              averageSpeed: 0,\n              maintenanceAlerts: 0,\n              emergencyStops: 0\n            }}\n            loading={loading}\n            error={error}\n          />\n\n          {/* Charts Grid */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n            {/* Alert Trends Chart */}\n            <div className=\"lg:col-span-2\">\n              <AlertTrendsChart\n                data={alertTrends}\n                loading={loading}\n                error={error}\n              />\n            </div>\n\n            {/* Vehicle Status Chart */}\n            <VehicleStatusChart\n              data={vehicleStatus}\n              loading={loading}\n              error={error}\n            />\n\n            {/* Event Frequency Chart */}\n            <EventFrequencyChart\n              data={eventFrequency}\n              loading={loading}\n              error={error}\n            />\n          </div>\n\n          {/* Data Summary */}\n          {!loading && !error && (\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Data Summary</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 text-center\">\n                <div className=\"p-4 bg-blue-50 rounded-lg\">\n                  <p className=\"text-2xl font-bold text-blue-600\">\n                    {alertTrends.reduce((sum, item) => sum + item.total, 0)}\n                  </p>\n                  <p className=\"text-sm text-gray-600\">Total Alerts</p>\n                </div>\n                <div className=\"p-4 bg-green-50 rounded-lg\">\n                  <p className=\"text-2xl font-bold text-green-600\">\n                    {vehicleStatus.reduce((sum, item) => sum + item.count, 0)}\n                  </p>\n                  <p className=\"text-sm text-gray-600\">Active Vehicles</p>\n                </div>\n                <div className=\"p-4 bg-purple-50 rounded-lg\">\n                  <p className=\"text-2xl font-bold text-purple-600\">\n                    {eventFrequency.reduce((sum, item) => sum + item.count, 0)}\n                  </p>\n                  <p className=\"text-sm text-gray-600\">Total Events</p>\n                </div>\n                <div className=\"p-4 bg-orange-50 rounded-lg\">\n                  <p className=\"text-2xl font-bold text-orange-600\">\n                    {fleetMetrics?.onTimePerformance || 0}%\n                  </p>\n                  <p className=\"text-sm text-gray-600\">On-Time Performance</p>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAiBe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,oBAAoB;IACpB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC1E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IAC7E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC;IAEjF,uBAAuB;IACvB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,0BAA0B,EAAE,WAAW,SAAS,CAAC;YAC/E,MAAM,SAKD,MAAM,SAAS,IAAI;YAExB,IAAI,OAAO,OAAO,EAAE;gBAClB,eAAe,OAAO,IAAI,CAAC,WAAW;gBACtC,iBAAiB,OAAO,IAAI,CAAC,aAAa;gBAC1C,kBAAkB,OAAO,IAAI,CAAC,cAAc;gBAC5C,gBAAgB,OAAO,IAAI,CAAC,YAAY;YAC1C,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAW;IAEf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAmB;IAEvB,sBAAsB;IACtB,MAAM,oBAA4D;QAChE;YAAE,OAAO;YAAO,OAAO;QAAgB;QACvC;YAAE,OAAO;YAAM,OAAO;QAAc;QACpC;YAAE,OAAO;YAAO,OAAO;QAAe;QACtC;YAAE,OAAO;YAAO,OAAO;QAAW;KACnC;IAED,4BAA4B;IAC5B,MAAM,aAAa;QACjB,MAAM,OAAO;YACX;YACA,aAAa,IAAI,OAAO,WAAW;YACnC;YACA;YACA;YACA;QACF;QAEA,MAAM,OAAO,IAAI,KAAK;YAAC,KAAK,SAAS,CAAC,MAAM,MAAM;SAAG,EAAE;YAAE,MAAM;QAAmB;QAClF,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;QAC3F,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,UAAU;;;;;0BAGX,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;0CAInD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;0DAET,kBAAkB,GAAG,CAAC,CAAC,uBACtB,8OAAC;wDAA0B,OAAO,OAAO,KAAK;kEAC3C,OAAO,KAAK;uDADF,OAAO,KAAK;;;;;;;;;;;;;;;;kDAQ/B,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDACX;;;;;;kDAKD,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;;oBAEb,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAoB;;;;;;8CACpC,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;8CAC7B,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAOP,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,kIAAA,CAAA,UAAY;gCACX,SAAS,gBAAgB;oCACvB,4BAA4B;oCAC5B,uBAAuB;oCACvB,gBAAgB;oCAChB,mBAAmB;oCACnB,YAAY;oCACZ,cAAc;oCACd,mBAAmB;oCACnB,gBAAgB;gCAClB;gCACA,SAAS;gCACT,OAAO;;;;;;0CAIT,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gJAAA,CAAA,UAAgB;4CACf,MAAM;4CACN,SAAS;4CACT,OAAO;;;;;;;;;;;kDAKX,8OAAC,kJAAA,CAAA,UAAkB;wCACjB,MAAM;wCACN,SAAS;wCACT,OAAO;;;;;;kDAIT,8OAAC,mJAAA,CAAA,UAAmB;wCAClB,MAAM;wCACN,SAAS;wCACT,OAAO;;;;;;;;;;;;4BAKV,CAAC,WAAW,CAAC,uBACZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;;;;;;kEAEvD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;;;;;;kEAEzD,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,eAAe,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;;;;;;kEAE1D,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;4DACV,cAAc,qBAAqB;4DAAE;;;;;;;kEAExC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvD", "debugId": null}}]}