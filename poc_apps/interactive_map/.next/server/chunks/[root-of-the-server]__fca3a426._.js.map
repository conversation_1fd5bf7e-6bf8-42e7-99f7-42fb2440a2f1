{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Laplace/Projects/nextjs-dev/poc_apps/interactive_map/src/app/api/vehicle-events/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { eventType, vehicleId, position, distance, timestamp, message } = body;\n\n    // Get current time for terminal logging\n    const now = new Date();\n    const timeString = now.toLocaleTimeString('en-US', { \n      hour12: false, \n      hour: '2-digit', \n      minute: '2-digit', \n      second: '2-digit' \n    });\n\n    // Log different types of events with colored output\n    switch (eventType) {\n      case 'off-route-start':\n        console.log('\\n' + '='.repeat(80));\n        console.log(`🚨 [${timeString}] VEHICLE OFF-ROUTE ALERT!`);\n        console.log(`   Vehicle ID: ${vehicleId}`);\n        console.log(`   Position: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`);\n        console.log(`   Distance from route: ${Math.round(distance)}m`);\n        console.log(`   Message: ${message}`);\n        console.log('='.repeat(80) + '\\n');\n        break;\n\n      case 'off-route-continue':\n        console.log(`🔴 [${timeString}] Vehicle ${vehicleId} still off-route: ${Math.round(distance)}m`);\n        break;\n\n      case 'back-on-route':\n        console.log('\\n' + '-'.repeat(80));\n        console.log(`✅ [${timeString}] VEHICLE BACK ON ROUTE!`);\n        console.log(`   Vehicle ID: ${vehicleId}`);\n        console.log(`   Position: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`);\n        console.log(`   Distance from route: ${Math.round(distance)}m`);\n        console.log(`   Message: ${message}`);\n        console.log('-'.repeat(80) + '\\n');\n        break;\n\n      case 'simulation-start':\n        console.log('\\n' + '🚀 [' + timeString + '] Vehicle simulation started for ' + vehicleId);\n        break;\n\n      case 'simulation-stop':\n        console.log('\\n' + '⏹️  [' + timeString + '] Vehicle simulation stopped for ' + vehicleId);\n        break;\n\n      case 'position-update':\n        // Only log every 10th position update to avoid spam\n        if (Math.random() < 0.1) {\n          console.log(`📍 [${timeString}] Vehicle ${vehicleId}: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)} (${Math.round(distance)}m from route)`);\n        }\n        break;\n\n      default:\n        console.log(`📝 [${timeString}] Vehicle Event: ${eventType} - ${vehicleId} - ${message}`);\n    }\n\n    return NextResponse.json({ \n      success: true, \n      message: 'Event logged successfully',\n      timestamp: now.toISOString()\n    });\n\n  } catch (error) {\n    console.error('❌ Error logging vehicle event:', error);\n    return NextResponse.json(\n      { success: false, error: 'Failed to log event' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;QAEzE,wCAAwC;QACxC,MAAM,MAAM,IAAI;QAChB,MAAM,aAAa,IAAI,kBAAkB,CAAC,SAAS;YACjD,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QAEA,oDAAoD;QACpD,OAAQ;YACN,KAAK;gBACH,QAAQ,GAAG,CAAC,OAAO,IAAI,MAAM,CAAC;gBAC9B,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,WAAW,0BAA0B,CAAC;gBACzD,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;gBACzC,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,SAAS,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,GAAG,CAAC,OAAO,CAAC,IAAI;gBACjF,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC9D,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,SAAS;gBACpC,QAAQ,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM;gBAC7B;YAEF,KAAK;gBACH,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,WAAW,UAAU,EAAE,UAAU,kBAAkB,EAAE,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC/F;YAEF,KAAK;gBACH,QAAQ,GAAG,CAAC,OAAO,IAAI,MAAM,CAAC;gBAC9B,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,WAAW,wBAAwB,CAAC;gBACtD,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;gBACzC,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,SAAS,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,GAAG,CAAC,OAAO,CAAC,IAAI;gBACjF,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC9D,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,SAAS;gBACpC,QAAQ,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM;gBAC7B;YAEF,KAAK;gBACH,QAAQ,GAAG,CAAC,OAAO,SAAS,aAAa,sCAAsC;gBAC/E;YAEF,KAAK;gBACH,QAAQ,GAAG,CAAC,OAAO,UAAU,aAAa,sCAAsC;gBAChF;YAEF,KAAK;gBACH,oDAAoD;gBACpD,IAAI,KAAK,MAAM,KAAK,KAAK;oBACvB,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,WAAW,UAAU,EAAE,UAAU,EAAE,EAAE,SAAS,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,KAAK,KAAK,CAAC,UAAU,aAAa,CAAC;gBACrJ;gBACA;YAEF;gBACE,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,WAAW,iBAAiB,EAAE,UAAU,GAAG,EAAE,UAAU,GAAG,EAAE,SAAS;QAC5F;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,WAAW,IAAI,WAAW;QAC5B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAsB,GAC/C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}