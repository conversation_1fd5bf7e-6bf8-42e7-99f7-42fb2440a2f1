module.exports = {

"[project]/.next-internal/server/app/api/alerts/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/alerts/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
// Mock alert data generator
function generateMockAlerts() {
    const alerts = [];
    const vehicleIds = [
        'VH-001',
        'VH-002',
        'VH-003',
        'VH-004',
        'VH-005'
    ];
    const alertTypes = [
        'warning',
        'error',
        'info'
    ];
    const messages = {
        warning: [
            'Vehicle is outside allowed route',
            'Speed limit exceeded',
            'Maintenance due soon',
            'Low fuel warning',
            'Route deviation detected'
        ],
        error: [
            'Emergency stop activated',
            'Critical system failure',
            'GPS signal lost',
            'Engine malfunction',
            'Unauthorized access attempt'
        ],
        info: [
            'Route completed successfully',
            'Waypoint reached',
            'Vehicle started journey',
            'Regular maintenance completed',
            'Driver shift change'
        ]
    };
    // Generate alerts for the past 7 days
    const now = new Date();
    for(let i = 0; i < 150; i++){
        const daysAgo = Math.floor(Math.random() * 7);
        const hoursAgo = Math.floor(Math.random() * 24);
        const minutesAgo = Math.floor(Math.random() * 60);
        const timestamp = new Date(now);
        timestamp.setDate(timestamp.getDate() - daysAgo);
        timestamp.setHours(timestamp.getHours() - hoursAgo);
        timestamp.setMinutes(timestamp.getMinutes() - minutesAgo);
        const type = alertTypes[Math.floor(Math.random() * alertTypes.length)];
        const vehicleId = vehicleIds[Math.floor(Math.random() * vehicleIds.length)];
        const messageList = messages[type];
        const message = messageList[Math.floor(Math.random() * messageList.length)];
        // 70% chance of being resolved for older alerts
        const isActive = daysAgo === 0 ? Math.random() > 0.3 : Math.random() > 0.7;
        alerts.push({
            id: `alert-${i + 1}`,
            type,
            message,
            timestamp,
            vehicleId,
            isActive
        });
    }
    return alerts.sort((a, b)=>b.timestamp.getTime() - a.timestamp.getTime());
}
// Filter alerts based on time filter
function filterAlertsByTime(alerts, timeFilter) {
    if (timeFilter === 'all') return alerts;
    const now = new Date();
    const cutoffTime = new Date(now);
    switch(timeFilter){
        case '24h':
            cutoffTime.setHours(cutoffTime.getHours() - 24);
            break;
        case '7d':
            cutoffTime.setDate(cutoffTime.getDate() - 7);
            break;
        case '30d':
            cutoffTime.setDate(cutoffTime.getDate() - 30);
            break;
    }
    return alerts.filter((alert)=>alert.timestamp >= cutoffTime);
}
// Calculate alert statistics
function calculateAlertStats(alerts) {
    const stats = {
        total: alerts.length,
        byType: {
            warning: 0,
            error: 0,
            info: 0
        },
        byStatus: {
            active: 0,
            resolved: 0
        },
        byLevel: {
            low: 0,
            medium: 0,
            high: 0,
            critical: 0
        }
    };
    alerts.forEach((alert)=>{
        // Count by type
        stats.byType[alert.type]++;
        // Count by status
        if (alert.isActive) {
            stats.byStatus.active++;
        } else {
            stats.byStatus.resolved++;
        }
        // Count by level (map type to level)
        if (alert.type === 'info') {
            stats.byLevel.low++;
        } else if (alert.type === 'warning') {
            stats.byLevel.medium++;
        } else if (alert.type === 'error') {
            stats.byLevel.high++;
        }
    });
    return stats;
}
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const timeFilter = searchParams.get('timeFilter') || '7d';
        const statsOnly = searchParams.get('statsOnly') === 'true';
        // Generate mock data
        const allAlerts = generateMockAlerts();
        const filteredAlerts = filterAlertsByTime(allAlerts, timeFilter);
        if (statsOnly) {
            const stats = calculateAlertStats(filteredAlerts);
            const response = {
                success: true,
                data: stats
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
        }
        const response = {
            success: true,
            data: filteredAlerts
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
    } catch (error) {
        const response = {
            success: false,
            data: null,
            error: 'Failed to fetch alerts'
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e64503e7._.js.map