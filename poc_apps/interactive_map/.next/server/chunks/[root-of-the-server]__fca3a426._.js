module.exports = {

"[project]/.next-internal/server/app/api/vehicle-events/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/vehicle-events/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
async function POST(request) {
    try {
        const body = await request.json();
        const { eventType, vehicleId, position, distance, timestamp, message } = body;
        // Get current time for terminal logging
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        // Log different types of events with colored output
        switch(eventType){
            case 'off-route-start':
                console.log('\n' + '='.repeat(80));
                console.log(`🚨 [${timeString}] VEHICLE OFF-ROUTE ALERT!`);
                console.log(`   Vehicle ID: ${vehicleId}`);
                console.log(`   Position: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`);
                console.log(`   Distance from route: ${Math.round(distance)}m`);
                console.log(`   Message: ${message}`);
                console.log('='.repeat(80) + '\n');
                break;
            case 'off-route-continue':
                console.log(`🔴 [${timeString}] Vehicle ${vehicleId} still off-route: ${Math.round(distance)}m`);
                break;
            case 'back-on-route':
                console.log('\n' + '-'.repeat(80));
                console.log(`✅ [${timeString}] VEHICLE BACK ON ROUTE!`);
                console.log(`   Vehicle ID: ${vehicleId}`);
                console.log(`   Position: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`);
                console.log(`   Distance from route: ${Math.round(distance)}m`);
                console.log(`   Message: ${message}`);
                console.log('-'.repeat(80) + '\n');
                break;
            case 'simulation-start':
                console.log('\n' + '🚀 [' + timeString + '] Vehicle simulation started for ' + vehicleId);
                break;
            case 'simulation-stop':
                console.log('\n' + '⏹️  [' + timeString + '] Vehicle simulation stopped for ' + vehicleId);
                break;
            case 'position-update':
                // Only log every 10th position update to avoid spam
                if (Math.random() < 0.1) {
                    console.log(`📍 [${timeString}] Vehicle ${vehicleId}: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)} (${Math.round(distance)}m from route)`);
                }
                break;
            default:
                console.log(`📝 [${timeString}] Vehicle Event: ${eventType} - ${vehicleId} - ${message}`);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Event logged successfully',
            timestamp: now.toISOString()
        });
    } catch (error) {
        console.error('❌ Error logging vehicle event:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to log event'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__fca3a426._.js.map