# 🗝️ Google Maps API Key Setup Guide

## Step 1: Create or Access Google Cloud Platform Project

1. **Go to Google Cloud Console**
   👉 [https://console.cloud.google.com/](https://console.cloud.google.com/)
   Sign in with your email: `<EMAIL>`
2. **Create a New Project (or select existing):**

   * Click the project dropdown at the top
   * Click **"New Project"**
   * **Project Name**: `vehicle-tracking-dashboard-dev`
   * **Organization**: Leave as default (No organization)
   * Click **"Create"**
   * Wait for the project to be created and select it

---

## Step 2: Enable Required APIs

1. **Navigate to APIs & Services:**

   * Go to **"APIs & Services" → "Library"**
   * Or use: [API Library](https://console.cloud.google.com/apis/library)
2. **Enable Maps JavaScript API:**

   * Search for **"Maps JavaScript API"**
   * Click it → Click **"Enable"**
   * Wait 1–2 minutes for activation
3. **Enable Geometry API:**

   * Geometry is included with **Maps JavaScript API** (no separate action needed)

---

## Step 3: Create API Key

1. **Go to Credentials:**

   * Navigate to **"APIs & Services" → "Credentials"**
   * Or go directly: [Credentials Console](https://console.cloud.google.com/apis/credentials)
2. **Create a New API Key:**

   * Click **"+ CREATE CREDENTIALS" → "API key"**
   * A new API key will be generated
     🔒 **Copy and store this key securely**
     * example: AIzaSyDk9C1Ht7B8h33ptLRLhWu-JtStV0Gfzx

---

## Step 4: Configure API Key Restrictions

1. **Restrict the API Key:**

   * Click the newly created key to edit
2. **Set Name:**

   * Rename to: `Vehicle-Tracking-Dashboard-Dev-Key`
3. **Application Restrictions:**

   * Select: **HTTP referrers (web sites)**
   * Click **"Add an item"** and add your local dev URLs (e.g., `http://localhost:3003/*`)

     ```
     http://localhost:3000/*
     http://localhost:3001/*
     http://localhost:3002/*
     http://localhost:3003/*

     ```
4. **API Restrictions:**

   * Choose: **Restrict key**
   * Check only ✅ **Maps JavaScript API**
5. **Click Save**

---

## Step 5: Set Up Billing (If Required)

1. **Check Billing Status:**

   * Go to **"Billing"** in the sidebar
2. **Create a Billing Account (if none exists):**

   * Click **"Link a billing account"** and follow prompts
3. **Free Tier Info:**

   * \$200 free credit/month
   * **Maps JavaScript API**: \$7 per 1,000 requests after the free tier
   * Free usage: \~28,500 map loads/month

---

## Step 6: Update Your Next.js Project

1. **Navigate to your project**
2. **Update `.env.local` file:**

   ```env
   NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key_here
   ```

---

## Step 7: Test the Configuration

1. **Restart your dev server**
2. **Visit:** `http://localhost:3003`
3. **Open browser dev tools (F12)** and check for:

   * No `"For development purposes only"` watermark
   * No console API key errors
   * Map and vehicle tracking loads correctly

---

## 🔒 Security Best Practices

### Development Security

* ✅ API key is restricted to `localhost`
* ✅ Only required APIs are enabled
* ✅ Descriptive naming used for keys
* ✅ Use `NEXT_PUBLIC_` prefix in env vars for client-side access

### Important Notes

* ❌ Never commit API keys to Git/version control
* ✅ `.env.local` should already be in `.gitignore`
* 🔒 Use separate API keys for **production**
* 🧠 Monitor usage in Google Cloud Console

---

## 🚀 Production Considerations

* Create a **separate production API key**
* Update referrers to production domain (e.g., `https://yourdomain.com/*`)
* Consider using a **server-side proxy** for added security
* Set up **monitoring & alerts**

---

## 🚨 Troubleshooting Common Issues

### 🔹 *"This page can't load Google Maps correctly"*

* ✅ Check if APIs are enabled
* ✅ Ensure API key is valid in `.env.local`
* ✅ Ensure HTTP referrers include your local dev URL

### 🔹 *"RefererNotAllowedMapError"*

* ✅ Add `http://localhost:3003/*` and `http://127.0.0.1:3003/*` to referrers

### 🔹 Billing Errors

* ✅ Make sure billing is enabled
* ✅ Verify billing account is active and linked

---

## 📊 Monitoring Usage

1. Go to: **"APIs & Services" → "Dashboard"**
2. Track daily/weekly usage
3. Set up alerts to avoid hitting limits

**Free Tier Limit**
✅ 28,500 **map loads/month**

---

Let me know if you'd like a downloadable `.md` version of this!
