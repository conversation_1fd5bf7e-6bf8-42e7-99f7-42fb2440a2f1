# System Architecture Documentation

## 🏛️ High-Level Architecture

The Vehicle Tracking Dashboard follows a modern web application architecture with clear separation of concerns:

```mermaid
graph TB
    subgraph "Client Layer"
        UI[React Components]
        State[Client State Management]
        Router[Next.js App Router]
    end
    
    subgraph "API Layer"
        API[Next.js API Routes]
        Mock[Mock Data Generator]
        Validation[Data Validation]
    end
    
    subgraph "External Services"
        GMaps[Google Maps API]
        Geometry[Geometry Library]
    end
    
    UI --> State
    State --> Router
    Router --> API
    API --> Mock
    API --> Validation
    UI --> GMaps
    UI --> Geometry
```

## 🔄 Data Flow Architecture

### Live Tracking Data Flow

```mermaid
sequenceDiagram
    participant User
    participant VehicleTracker
    participant TrackingMap
    participant GeofenceEngine
    participant AlertSystem
    participant GoogleMaps
    
    User->>VehicleTracker: Start Simulation
    VehicleTracker->>VehicleTracker: Generate Position
    VehicleTracker->>TrackingMap: Update Position
    TrackingMap->>GoogleMaps: Render Vehicle Marker
    TrackingMap->>GeofenceEngine: Check Distance
    GeofenceEngine->>AlertSystem: Trigger Alert (if needed)
    AlertSystem->>User: Display Alert Banner
```

### Monitoring Dashboard Data Flow

```mermaid
sequenceDiagram
    participant User
    participant Dashboard
    participant API
    participant DataGenerator
    participant UI
    
    User->>Dashboard: Load Page
    Dashboard->>API: Fetch Alert Stats
    API->>DataGenerator: Generate Mock Data
    DataGenerator->>API: Return Data
    API->>Dashboard: JSON Response
    Dashboard->>UI: Render Components
    
    User->>Dashboard: Apply Filters
    Dashboard->>API: Fetch Filtered Logs
    API->>DataGenerator: Filter Data
    DataGenerator->>API: Return Filtered Data
    API->>Dashboard: Paginated Response
    Dashboard->>UI: Update Table
```

## 🧩 Component Architecture

### Component Hierarchy

```mermaid
graph TD
    App[App Layout]
    Nav[Navigation]
    LivePage[Live Tracking Page]
    MonitorPage[Monitoring Page]
    
    App --> Nav
    App --> LivePage
    App --> MonitorPage
    
    LivePage --> TrackingMap
    LivePage --> VehicleTracker
    LivePage --> AlertBanner
    
    MonitorPage --> AlertSummary
    MonitorPage --> LogsTable
    
    TrackingMap --> GoogleMap
    TrackingMap --> Markers
    TrackingMap --> Polylines
    
    AlertSummary --> StatsCards
    AlertSummary --> TimeFilter
    
    LogsTable --> SearchBar
    LogsTable --> FilterDropdowns
    LogsTable --> DataTable
    LogsTable --> Pagination
```

### Component Responsibilities

#### **TrackingMap Component**
- **Purpose**: Google Maps integration and vehicle visualization
- **Responsibilities**:
  - Render interactive map with Google Maps API
  - Display vehicle markers with real-time position updates
  - Show route polylines and waypoint markers
  - Handle map interactions and zoom controls
  - Manage loading states and error handling

#### **VehicleTracker Component**
- **Purpose**: Vehicle simulation engine
- **Responsibilities**:
  - Generate realistic vehicle movement along routes
  - Calculate position interpolation between waypoints
  - Simulate off-route scenarios for testing
  - Provide position updates to parent components
  - Handle simulation start/stop controls

#### **AlertSummary Component**
- **Purpose**: Alert statistics and analytics
- **Responsibilities**:
  - Fetch and display alert statistics from API
  - Provide time-based filtering options
  - Render statistics cards with visual indicators
  - Handle loading states and error scenarios
  - Update data based on filter changes

#### **LogsTable Component**
- **Purpose**: Vehicle logs data table
- **Responsibilities**:
  - Display paginated vehicle tracking logs
  - Provide search and filtering capabilities
  - Handle sorting by different columns
  - Manage pagination controls
  - Format data for display (timestamps, badges, etc.)

## 🗄️ Data Architecture

### Data Models Relationship

```mermaid
erDiagram
    Vehicle ||--o{ VehicleLog : generates
    Vehicle ||--o{ Alert : triggers
    Route ||--o{ Vehicle : follows
    Route ||--o{ Waypoint : contains
    
    Vehicle {
        string id
        VehiclePosition position
        boolean isOnRoute
        number currentWaypointIndex
        number distanceFromRoute
        string status
    }
    
    VehicleLog {
        string id
        Date timestamp
        string vehicleId
        EventType eventType
        Coordinate location
        string address
        string status
        AlertLevel alertLevel
        string message
    }
    
    Alert {
        string id
        string type
        string message
        Date timestamp
        string vehicleId
        boolean isActive
    }
    
    Route {
        string id
        string name
        Coordinate[] waypoints
        string color
        number strokeWeight
    }
```

### State Management

```mermaid
graph LR
    subgraph "Live Tracking State"
        VehicleState[Vehicle State]
        AlertState[Alert State]
        SimulationState[Simulation State]
    end
    
    subgraph "Monitoring State"
        FilterState[Filter State]
        PaginationState[Pagination State]
        SearchState[Search State]
    end
    
    subgraph "Shared State"
        LoadingState[Loading State]
        ErrorState[Error State]
        TimeFilter[Time Filter]
    end
    
    VehicleState --> AlertState
    FilterState --> PaginationState
    SearchState --> FilterState
```

## 🔌 API Architecture

### API Endpoints Structure

```mermaid
graph TD
    subgraph "API Routes"
        AlertsAPI[/api/alerts]
        LogsAPI[/api/logs]
    end
    
    subgraph "Alert Endpoints"
        AlertsAPI --> GetAlerts[GET: Fetch alerts]
        AlertsAPI --> GetStats[GET: Alert statistics]
    end
    
    subgraph "Logs Endpoints"
        LogsAPI --> GetLogs[GET: Fetch logs]
        LogsAPI --> FilterLogs[GET: Filtered logs]
    end
    
    subgraph "Query Parameters"
        GetAlerts --> TimeFilterParam[timeFilter]
        GetAlerts --> StatsOnlyParam[statsOnly]
        GetLogs --> PaginationParams[page, pageSize]
        GetLogs --> FilterParams[vehicleId, eventType, status]
        GetLogs --> SearchParam[search]
    end
```

### API Response Structure

```typescript
// Standard API Response
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// Paginated Response
interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
```

## 🎨 UI/UX Architecture

### Design System

```mermaid
graph TD
    subgraph "Design Tokens"
        Colors[Color Palette]
        Typography[Typography Scale]
        Spacing[Spacing System]
        Shadows[Shadow System]
    end
    
    subgraph "Component Library"
        Buttons[Button Components]
        Forms[Form Components]
        Tables[Table Components]
        Cards[Card Components]
    end
    
    subgraph "Layout System"
        Grid[Grid System]
        Containers[Container Layouts]
        Navigation[Navigation Patterns]
    end
    
    Colors --> Buttons
    Typography --> Forms
    Spacing --> Tables
    Shadows --> Cards
    
    Grid --> Navigation
    Containers --> Navigation
```

### Responsive Design Strategy

```mermaid
graph LR
    Mobile[Mobile: 320px-768px]
    Tablet[Tablet: 768px-1024px]
    Desktop[Desktop: 1024px+]
    
    Mobile --> Stack[Stacked Layout]
    Tablet --> Hybrid[Hybrid Layout]
    Desktop --> Grid[Grid Layout]
    
    Stack --> SingleColumn[Single Column]
    Hybrid --> TwoColumn[Two Column]
    Grid --> MultiColumn[Multi Column]
```

## 🔧 Technology Stack

### Frontend Technologies

```mermaid
graph TD
    subgraph "Core Framework"
        NextJS[Next.js 15]
        React[React 18]
        TypeScript[TypeScript]
    end
    
    subgraph "Styling"
        TailwindCSS[Tailwind CSS]
        PostCSS[PostCSS]
    end
    
    subgraph "Maps & Geolocation"
        GoogleMaps[Google Maps API]
        ReactGoogleMaps[@react-google-maps/api]
    end
    
    subgraph "Development Tools"
        ESLint[ESLint]
        Prettier[Prettier]
        TypeScriptCompiler[TypeScript Compiler]
    end
    
    NextJS --> React
    React --> TypeScript
    TailwindCSS --> PostCSS
    GoogleMaps --> ReactGoogleMaps
```

### Development Workflow

```mermaid
graph LR
    Development[Development]
    Build[Build Process]
    Testing[Testing]
    Deployment[Deployment]
    
    Development --> TypeCheck[Type Checking]
    Development --> Linting[ESLint]
    Development --> Formatting[Prettier]
    
    Build --> Compilation[TypeScript Compilation]
    Build --> Bundling[Next.js Bundling]
    Build --> Optimization[Asset Optimization]
    
    Testing --> UnitTests[Unit Tests]
    Testing --> IntegrationTests[Integration Tests]
    Testing --> E2ETests[E2E Tests]
    
    Deployment --> StaticGeneration[Static Generation]
    Deployment --> ServerSideRendering[SSR]
    Deployment --> CDNDeployment[CDN Deployment]
```

## 🚀 Performance Architecture

### Optimization Strategies

```mermaid
graph TD
    subgraph "Frontend Optimization"
        CodeSplitting[Code Splitting]
        LazyLoading[Lazy Loading]
        Memoization[React Memoization]
        ImageOptimization[Image Optimization]
    end
    
    subgraph "API Optimization"
        Pagination[Pagination]
        Caching[Response Caching]
        DataFiltering[Efficient Filtering]
        Compression[Response Compression]
    end
    
    subgraph "Map Optimization"
        MarkerClustering[Marker Clustering]
        ViewportOptimization[Viewport Optimization]
        TileOptimization[Tile Optimization]
    end
    
    CodeSplitting --> LazyLoading
    Pagination --> Caching
    MarkerClustering --> ViewportOptimization
```

### Loading Strategy

```mermaid
sequenceDiagram
    participant User
    participant App
    participant Components
    participant API
    participant GoogleMaps
    
    User->>App: Navigate to Page
    App->>Components: Load Critical Components
    Components->>User: Show Loading State
    
    par Load Map
        Components->>GoogleMaps: Initialize Map
        GoogleMaps->>Components: Map Ready
    and Load Data
        Components->>API: Fetch Initial Data
        API->>Components: Return Data
    end
    
    Components->>User: Show Complete Interface
```

This architecture documentation provides a comprehensive understanding of how the Vehicle Tracking Dashboard is structured and how its components interact with each other.
