# Vehicle Tracking Dashboard - Project Documentation

## Overview

The Vehicle Tracking Dashboard is a comprehensive Next.js 15 application that provides real-time vehicle monitoring, geofencing, and fleet management capabilities. Built with TypeScript, Google Maps API, and Tailwind CSS, it offers both live tracking and detailed analytics.

## 🏗️ Project Structure

```
poc_apps/interactive_map/
├── src/
│   ├── app/                          # Next.js App Router
│   │   ├── api/                      # API Routes
│   │   │   ├── alerts/route.ts       # Alert statistics API
│   │   │   └── logs/route.ts         # Vehicle logs API
│   │   ├── dashboard/
│   │   │   └── monitoring/page.tsx   # Monitoring dashboard page
│   │   ├── globals.css               # Global styles
│   │   ├── layout.tsx                # Root layout
│   │   └── page.tsx                  # Live tracking page
│   ├── components/                   # React Components
│   │   ├── AlertBanner.tsx           # Alert notifications
│   │   ├── AlertSummary.tsx          # Alert statistics dashboard
│   │   ├── ClientTimestamp.tsx       # Client-side timestamp component
│   │   ├── LogsTable.tsx             # Data table for vehicle logs
│   │   ├── Navigation.tsx            # Navigation component
│   │   ├── TrackingMap.tsx           # Google Maps integration
│   │   └── VehicleTracker.tsx        # Vehicle simulation logic
│   └── types/
│       └── index.ts                  # TypeScript interfaces
├── _docs/                            # Project documentation
├── .env.local                        # Environment variables
├── package.json                      # Dependencies
└── README.md                         # Setup instructions
```

## 🎯 Core Features

### 1. Live Vehicle Tracking
- **Real-time map visualization** using Google Maps API
- **Vehicle simulation** with animated movement along predefined routes
- **Geofencing** with 100-meter tolerance detection
- **Alert system** for route violations
- **Interactive controls** for simulation management

### 2. Monitoring Dashboard
- **Alert statistics** with time-based filtering
- **Comprehensive logs table** with pagination and search
- **Data filtering** by vehicle, event type, status, and alert level
- **Real-time data updates** via API endpoints

### 3. Mock API System
- **RESTful API endpoints** for alerts and logs
- **Realistic Saudi Arabia data** with Riyadh locations
- **Pagination support** for large datasets
- **Error handling** and loading states

## 🔧 Technical Architecture

### Frontend Architecture
- **Next.js 15** with App Router for modern React development
- **TypeScript** for type safety and better developer experience
- **Tailwind CSS** for responsive and consistent styling
- **Client-side state management** using React hooks

### API Architecture
- **Next.js API Routes** for backend functionality
- **Mock data generation** for development and testing
- **RESTful design** with proper HTTP status codes
- **Pagination and filtering** support

### Data Flow
1. **Live Tracking**: Vehicle simulation → Position updates → Map rendering → Geofence checking → Alert generation
2. **Monitoring**: API requests → Data filtering → Table rendering → User interactions

## 📊 Data Models

### Core Entities

#### Vehicle
```typescript
interface VehicleState {
  id: string;
  position: VehiclePosition;
  isOnRoute: boolean;
  currentWaypointIndex: number;
  distanceFromRoute: number;
  status: 'on-route' | 'off-route' | 'stopped';
}
```

#### Route
```typescript
interface Route {
  id: string;
  name: string;
  waypoints: Coordinate[];
  color?: string;
  strokeWeight?: number;
}
```

#### Alert
```typescript
interface Alert {
  id: string;
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: Date;
  vehicleId: string;
  isActive: boolean;
}
```

#### Vehicle Log
```typescript
interface VehicleLog {
  id: string;
  timestamp: Date;
  vehicleId: string;
  eventType: EventType;
  location: Coordinate;
  address?: string;
  status: VehicleState['status'];
  alertLevel?: AlertLevel;
  message: string;
  metadata?: Record<string, any>;
}
```

## 🗺️ Component Architecture

### Page Components
- **`page.tsx`**: Live tracking dashboard with map and vehicle simulation
- **`dashboard/monitoring/page.tsx`**: Analytics dashboard with alerts and logs

### Feature Components
- **`TrackingMap`**: Google Maps integration with vehicle markers and route visualization
- **`VehicleTracker`**: Vehicle simulation engine with position updates
- **`AlertSummary`**: Statistics cards and charts for alert data
- **`LogsTable`**: Data table with filtering, sorting, and pagination

### UI Components
- **`Navigation`**: App navigation with active state management
- **`AlertBanner`**: Notification system for real-time alerts
- **`ClientTimestamp`**: Hydration-safe timestamp display

## 🔄 Data Flow Diagrams

### Live Tracking Flow
```
Vehicle Simulation → Position Update → Geofence Check → Alert Generation → UI Update
     ↓                    ↓                ↓               ↓              ↓
VehicleTracker → TrackingMap → Distance Calc → AlertBanner → Map Markers
```

### Monitoring Dashboard Flow
```
User Interaction → API Request → Data Processing → UI Rendering
      ↓              ↓             ↓               ↓
Filter/Search → /api/logs → Pagination/Sort → LogsTable
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- Google Maps API key with Maps JavaScript API and Geometry API enabled

### Installation
```bash
cd poc_apps/interactive_map
npm install
```

### Configuration
1. Copy `.env.local.example` to `.env.local`
2. Add your Google Maps API key:
```
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key_here
```

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm start
```

## 🧪 Testing Strategy

### Manual Testing
1. **Live Tracking**: Verify vehicle movement, geofencing, and alerts
2. **Monitoring Dashboard**: Test filtering, pagination, and search functionality
3. **API Endpoints**: Validate data accuracy and error handling
4. **Responsive Design**: Test on mobile and desktop devices

### Automated Testing (Recommended)
- **Unit Tests**: Component logic and utility functions
- **Integration Tests**: API endpoints and data flow
- **E2E Tests**: User workflows and critical paths

## 🔒 Security Considerations

### API Key Security
- Environment variables for sensitive configuration
- HTTP referrer restrictions for development
- Separate keys for development and production

### Data Privacy
- Mock data only - no real vehicle information
- Client-side filtering to minimize data exposure
- Proper error handling to prevent information leakage

## 📈 Performance Optimization

### Frontend
- **Code splitting** with Next.js dynamic imports
- **Image optimization** with Next.js Image component
- **Lazy loading** for non-critical components
- **Memoization** for expensive calculations

### Backend
- **Pagination** to limit data transfer
- **Caching** for frequently accessed data
- **Efficient filtering** to reduce processing time

## 🔮 Future Enhancements

### Features
- **Real-time WebSocket updates** for live data
- **Advanced analytics** with charts and graphs
- **Export functionality** for reports
- **User authentication** and role-based access
- **Mobile app** with React Native

### Technical Improvements
- **Database integration** (PostgreSQL/MongoDB)
- **Redis caching** for improved performance
- **Docker containerization** for deployment
- **CI/CD pipeline** with automated testing
- **Monitoring and logging** with observability tools

## 🤝 Contributing

### Development Guidelines
1. Follow TypeScript best practices
2. Use consistent naming conventions
3. Write comprehensive documentation
4. Test all new features thoroughly
5. Maintain responsive design principles

### Code Style
- **ESLint** for code quality
- **Prettier** for formatting
- **TypeScript strict mode** for type safety
- **Tailwind CSS** for styling consistency

## 📚 Additional Documentation

- **[Architecture Guide](_docs/ARCHITECTURE.md)**: Detailed system architecture and component relationships
- **[API Documentation](_docs/API.md)**: Complete API endpoint documentation
- **[Development Guide](_docs/DEVELOPMENT.md)**: Development workflow and best practices

This documentation provides a comprehensive overview for junior developers to understand the project structure, architecture, and implementation details without needing to dive into the code immediately.
