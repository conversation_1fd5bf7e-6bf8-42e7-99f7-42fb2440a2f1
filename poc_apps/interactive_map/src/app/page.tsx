'use client';

import React, { useState, useCallback, useEffect } from 'react';
import TrackingMap from '@/components/TrackingMap';
import VehicleTracker from '@/components/VehicleTracker';
import AlertBanner from '@/components/AlertBanner';
import ClientTimestamp from '@/components/ClientTimestamp';
import Navigation from '@/components/Navigation';
import { Route, VehicleState, VehiclePosition, Alert, Coordinate } from '@/types';

// Sample route data (Riyadh City Route)
const sampleRoute: Route = {
  id: 'route-1',
  name: 'Riyadh City Route',
  waypoints: [
    { lat: 24.7136, lng: 46.6753 }, // King Fahd Road (Start)
    { lat: 24.7200, lng: 46.6800 }, // Olaya District
    { lat: 24.7280, lng: 46.6850 }, // Al Malaz
    { lat: 24.7350, lng: 46.6900 }, // King Abdulaziz Road
    { lat: 24.7420, lng: 46.6950 }, // Diplomatic Quarter
    { lat: 24.7480, lng: 46.7000 }, // Al Muhammadiya<PERSON>
    { lat: 24.7540, lng: 46.7050 }, // Al Naseem
    { lat: 24.7600, lng: 46.7100 }, // King Khalid Airport Road
    { lat: 24.7660, lng: 46.7150 }, // Al Rawdah
    { lat: 24.7720, lng: 46.7200 }, // Northern Ring Road (End)
  ],
  color: '#2563eb',
  strokeWeight: 4,
};

export default function VehicleTrackingDashboard() {
  const [vehicle, setVehicle] = useState<VehicleState>({
    id: 'vehicle-001',
    position: {
      lat: sampleRoute.waypoints[0].lat,
      lng: sampleRoute.waypoints[0].lng,
      timestamp: new Date(),
    },
    isOnRoute: true,
    currentWaypointIndex: 0,
    distanceFromRoute: 0,
    status: 'on-route',
  });

  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isSimulationActive, setIsSimulationActive] = useState(true);

  // Function to log events to terminal
  const logEventToTerminal = useCallback(async (eventType: string, vehicleId: string, position: VehiclePosition, distance: number, message: string) => {
    try {
      await fetch('/api/vehicle-events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventType,
          vehicleId,
          position,
          distance,
          timestamp: new Date().toISOString(),
          message
        })
      });
    } catch (error) {
      console.error('Failed to log event to terminal:', error);
    }
  }, []);

  // Calculate distance from route using proper point-to-line segment distance
  const calculateDistanceFromRoute = useCallback((position: VehiclePosition): number => {
    let minDistance = Infinity;

    // Helper function to calculate distance between two coordinates using Haversine formula
    const haversineDistance = (coord1: Coordinate, coord2: Coordinate): number => {
      const R = 6371000; // Earth's radius in meters
      const dLat = (coord2.lat - coord1.lat) * Math.PI / 180;
      const dLng = (coord2.lng - coord1.lng) * Math.PI / 180;
      const a =
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(coord1.lat * Math.PI / 180) * Math.cos(coord2.lat * Math.PI / 180) *
        Math.sin(dLng/2) * Math.sin(dLng/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      return R * c;
    };

    // Helper function to calculate distance from point to line segment
    const pointToLineDistance = (point: Coordinate, lineStart: Coordinate, lineEnd: Coordinate): number => {
      const A = point.lat - lineStart.lat;
      const B = point.lng - lineStart.lng;
      const C = lineEnd.lat - lineStart.lat;
      const D = lineEnd.lng - lineStart.lng;

      const dot = A * C + B * D;
      const lenSq = C * C + D * D;

      if (lenSq === 0) {
        // Line segment is actually a point
        return haversineDistance(point, lineStart);
      }

      const param = dot / lenSq;

      let closestPoint: Coordinate;
      if (param < 0) {
        closestPoint = lineStart;
      } else if (param > 1) {
        closestPoint = lineEnd;
      } else {
        closestPoint = {
          lat: lineStart.lat + param * C,
          lng: lineStart.lng + param * D
        };
      }

      return haversineDistance(point, closestPoint);
    };

    // Calculate minimum distance to any route segment
    for (let i = 0; i < sampleRoute.waypoints.length - 1; i++) {
      const start = sampleRoute.waypoints[i];
      const end = sampleRoute.waypoints[i + 1];

      const distance = pointToLineDistance(position, start, end);
      minDistance = Math.min(minDistance, distance);
    }

    return minDistance;
  }, []);

  // Handle vehicle position updates
  const handleVehicleUpdate = useCallback((newVehicle: VehicleState) => {
    setVehicle(newVehicle);
  }, []);

  // Handle position updates from tracker
  const handlePositionUpdate = useCallback((position: VehiclePosition) => {
    const distanceFromRoute = calculateDistanceFromRoute(position);
    const isOnRoute = distanceFromRoute <= 120; // Reduced tolerance to 120 meters for better testing

    // Enhanced debug logging
    console.log(`🚗 Vehicle position: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`);
    console.log(`📏 Distance from route: ${Math.round(distanceFromRoute)}m, Status: ${isOnRoute ? '✅ On Route' : '🔴 Off Route'}`);

    if (!isOnRoute) {
      console.log(`⚠️ ALERT: Vehicle is ${Math.round(distanceFromRoute)}m from route (tolerance: 120m)`);
    }

    // Log position updates to terminal (every update)
    logEventToTerminal('position-update', vehicle.id, position, distanceFromRoute,
      `Vehicle position update: ${isOnRoute ? 'On Route' : 'Off Route'}`);


    setVehicle(prev => {
      const wasOnRoute = prev.isOnRoute;
      const newVehicle = {
        ...prev,
        position,
        distanceFromRoute,
        isOnRoute,
      };

      // Create alert only when vehicle goes from on-route to off-route
      if (wasOnRoute && !isOnRoute) {
        console.log(`🚨 CREATING ALERT: Vehicle went off-route! Distance: ${Math.round(distanceFromRoute)}m`);

        // Log to terminal
        logEventToTerminal('off-route-start', prev.id, position, distanceFromRoute,
          `Vehicle went off-route! Distance: ${Math.round(distanceFromRoute)}m`);

        const newAlert: Alert = {
          id: `alert-${Date.now()}`,
          type: 'warning',
          message: `Vehicle is outside allowed route (${Math.round(distanceFromRoute)}m from route)`,
          timestamp: new Date(),
          vehicleId: prev.id,
          isActive: true,
        };

        setAlerts(prevAlerts => {
          console.log(`📢 Alert added to list. Total alerts: ${prevAlerts.length + 1}`);
          return [...prevAlerts, newAlert];
        });
      }

      // Log when vehicle returns to route
      if (!wasOnRoute && isOnRoute) {
        logEventToTerminal('back-on-route', prev.id, position, distanceFromRoute,
          `Vehicle returned to route! Distance: ${Math.round(distanceFromRoute)}m`);
      }

      // Log continued off-route status
      if (!wasOnRoute && !isOnRoute) {
        logEventToTerminal('off-route-continue', prev.id, position, distanceFromRoute,
          `Vehicle still off-route: ${Math.round(distanceFromRoute)}m`);
      }

      return newVehicle;
    });
  }, [calculateDistanceFromRoute, logEventToTerminal, vehicle.id]);

  // Handle alerts based on vehicle position (simplified)
  const handleStatusChange = useCallback(() => {
    // This function is called by VehicleTracker but we determine status based on distance only
    // Alerts are now handled in handlePositionUpdate when distance changes
  }, []);

  // Handle alert creation
  const handleAlert = useCallback((alert: Alert) => {
    setAlerts(prev => [...prev, alert]);
  }, []);

  // Handle alert dismissal
  const handleAlertDismiss = useCallback((alertId: string) => {
    setAlerts(prev =>
      prev.map(alert =>
        alert.id === alertId
          ? { ...alert, isActive: false }
          : alert
      )
    );
  }, []);

  // Auto-dismiss info alerts after 5 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setAlerts(prev =>
        prev.map(alert =>
          alert.type === 'info' && alert.isActive
            ? { ...alert, isActive: false }
            : alert
        )
      );
    }, 5000);

    return () => clearTimeout(timer);
  }, [alerts]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <Navigation />

      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Live Vehicle Tracking
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => {
                  const newState = !isSimulationActive;
                  setIsSimulationActive(newState);

                  // Log simulation state change to terminal
                  logEventToTerminal(
                    newState ? 'simulation-start' : 'simulation-stop',
                    vehicle.id,
                    vehicle.position,
                    vehicle.distanceFromRoute,
                    newState ? 'Vehicle simulation started' : 'Vehicle simulation stopped'
                  );
                }}
                className={`
                  px-4 py-2 rounded-md text-sm font-medium transition-colors
                  ${isSimulationActive
                    ? 'bg-red-600 text-white hover:bg-red-700'
                    : 'bg-green-600 text-white hover:bg-green-700'
                  }
                `}
              >
                {isSimulationActive ? 'Stop Simulation' : 'Start Simulation'}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Status Panel */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Vehicle Status
              </h2>

              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Vehicle ID</label>
                  <p className="text-lg font-mono">{vehicle.id}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div className="flex items-center space-x-2">
                    <div
                      className={`w-3 h-3 rounded-full ${
                        vehicle.isOnRoute ? 'bg-green-500' : 'bg-red-500'
                      }`}
                    />
                    <span className={`font-medium ${
                      vehicle.isOnRoute ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {vehicle.isOnRoute ? 'On Route' : 'Off Route'}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Distance from Route</label>
                  <p className="text-lg">{Math.round(vehicle.distanceFromRoute)}m</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Last Update</label>
                  <ClientTimestamp
                    timestamp={vehicle.position.timestamp}
                    className="text-sm text-gray-600"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Speed</label>
                  <p className="text-lg">
                    {vehicle.position.speed ? `${Math.round(vehicle.position.speed)} km/h` : 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Map */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow overflow-hidden" style={{ height: '600px' }}>
              <TrackingMap
                route={sampleRoute}
                vehicle={vehicle}
                onVehicleUpdate={handleVehicleUpdate}
                onAlert={handleAlert}
              />
            </div>
          </div>
        </div>
      </main>

      {/* Vehicle Tracker (invisible component) */}
      <VehicleTracker
        route={sampleRoute}
        onPositionUpdate={handlePositionUpdate}
        onStatusChange={handleStatusChange}
        isActive={isSimulationActive}
        speed={1}
      />

      {/* Alert Banner */}
      <AlertBanner
        alerts={alerts}
        onDismiss={handleAlertDismiss}
        maxVisible={3}
      />
    </div>
  );
}
