'use client';

import React, { useState, useCallback, useEffect } from 'react';
import TrackingMap from '@/components/TrackingMap';
import VehicleTracker from '@/components/VehicleTracker';
import AlertBanner from '@/components/AlertBanner';
import ClientTimestamp from '@/components/ClientTimestamp';
import { Route, VehicleState, VehiclePosition, Alert } from '@/types';

// Sample route data (Riyadh City Route)
const sampleRoute: Route = {
  id: 'route-1',
  name: 'Riyadh City Route',
  waypoints: [
    { lat: 24.7136, lng: 46.6753 }, // King Fahd Road (Start)
    { lat: 24.7200, lng: 46.6800 }, // Olaya District
    { lat: 24.7280, lng: 46.6850 }, // Al Malaz
    { lat: 24.7350, lng: 46.6900 }, // King Abdulaziz Road
    { lat: 24.7420, lng: 46.6950 }, // Diplomatic Quarter
    { lat: 24.7480, lng: 46.7000 }, // Al <PERSON>
    { lat: 24.7540, lng: 46.7050 }, // Al <PERSON>
    { lat: 24.7600, lng: 46.7100 }, // King Khalid Airport Road
    { lat: 24.7660, lng: 46.7150 }, // Al Rawdah
    { lat: 24.7720, lng: 46.7200 }, // Northern Ring Road (End)
  ],
  color: '#2563eb',
  strokeWeight: 4,
};

export default function VehicleTrackingDashboard() {
  const [vehicle, setVehicle] = useState<VehicleState>({
    id: 'vehicle-001',
    position: {
      lat: sampleRoute.waypoints[0].lat,
      lng: sampleRoute.waypoints[0].lng,
      timestamp: new Date(),
    },
    isOnRoute: true,
    currentWaypointIndex: 0,
    distanceFromRoute: 0,
    status: 'on-route',
  });

  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isSimulationActive, setIsSimulationActive] = useState(true);

  // Calculate distance from route using Google Maps geometry (simplified version)
  const calculateDistanceFromRoute = useCallback((position: VehiclePosition): number => {
    // Simplified distance calculation - in a real app, use Google Maps geometry library
    let minDistance = Infinity;

    for (let i = 0; i < sampleRoute.waypoints.length - 1; i++) {
      const start = sampleRoute.waypoints[i];
      const end = sampleRoute.waypoints[i + 1];

      // Calculate distance from point to line segment (simplified)
      const distance = Math.sqrt(
        Math.pow((position.lat - start.lat) * 111000, 2) +
        Math.pow((position.lng - start.lng) * 111000 * Math.cos(start.lat * Math.PI / 180), 2)
      );

      minDistance = Math.min(minDistance, distance);
    }

    return minDistance;
  }, []);

  // Handle vehicle position updates
  const handleVehicleUpdate = useCallback((newVehicle: VehicleState) => {
    setVehicle(newVehicle);
  }, []);

  // Handle position updates from tracker
  const handlePositionUpdate = useCallback((position: VehiclePosition) => {
    const distanceFromRoute = calculateDistanceFromRoute(position);
    const isOnRoute = distanceFromRoute <= 100; // 100 meter tolerance

    setVehicle(prev => ({
      ...prev,
      position,
      distanceFromRoute,
      isOnRoute,
    }));
  }, [calculateDistanceFromRoute]);

  // Handle status changes from tracker
  const handleStatusChange = useCallback((status: VehicleState['status']) => {
    setVehicle(prev => ({ ...prev, status }));

    // Create alert if vehicle goes off-route
    if (status === 'off-route') {
      const newAlert: Alert = {
        id: `alert-${Date.now()}`,
        type: 'warning',
        message: 'Vehicle is outside allowed route',
        timestamp: new Date(),
        vehicleId: vehicle.id,
        isActive: true,
      };

      setAlerts(prev => [...prev, newAlert]);
    }
  }, [vehicle.id]);

  // Handle alert creation
  const handleAlert = useCallback((alert: Alert) => {
    setAlerts(prev => [...prev, alert]);
  }, []);

  // Handle alert dismissal
  const handleAlertDismiss = useCallback((alertId: string) => {
    setAlerts(prev =>
      prev.map(alert =>
        alert.id === alertId
          ? { ...alert, isActive: false }
          : alert
      )
    );
  }, []);

  // Auto-dismiss info alerts after 5 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setAlerts(prev =>
        prev.map(alert =>
          alert.type === 'info' && alert.isActive
            ? { ...alert, isActive: false }
            : alert
        )
      );
    }, 5000);

    return () => clearTimeout(timer);
  }, [alerts]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Vehicle Tracking Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsSimulationActive(!isSimulationActive)}
                className={`
                  px-4 py-2 rounded-md text-sm font-medium transition-colors
                  ${isSimulationActive
                    ? 'bg-red-600 text-white hover:bg-red-700'
                    : 'bg-green-600 text-white hover:bg-green-700'
                  }
                `}
              >
                {isSimulationActive ? 'Stop Simulation' : 'Start Simulation'}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Status Panel */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Vehicle Status
              </h2>

              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Vehicle ID</label>
                  <p className="text-lg font-mono">{vehicle.id}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div className="flex items-center space-x-2">
                    <div
                      className={`w-3 h-3 rounded-full ${
                        vehicle.isOnRoute ? 'bg-green-500' : 'bg-red-500'
                      }`}
                    />
                    <span className={`font-medium ${
                      vehicle.isOnRoute ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {vehicle.isOnRoute ? 'On Route' : 'Off Route'}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Distance from Route</label>
                  <p className="text-lg">{Math.round(vehicle.distanceFromRoute)}m</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Last Update</label>
                  <ClientTimestamp
                    timestamp={vehicle.position.timestamp}
                    className="text-sm text-gray-600"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Speed</label>
                  <p className="text-lg">
                    {vehicle.position.speed ? `${Math.round(vehicle.position.speed)} km/h` : 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Map */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow overflow-hidden" style={{ height: '600px' }}>
              <TrackingMap
                route={sampleRoute}
                vehicle={vehicle}
                onVehicleUpdate={handleVehicleUpdate}
                onAlert={handleAlert}
              />
            </div>
          </div>
        </div>
      </main>

      {/* Vehicle Tracker (invisible component) */}
      <VehicleTracker
        route={sampleRoute}
        onPositionUpdate={handlePositionUpdate}
        onStatusChange={handleStatusChange}
        isActive={isSimulationActive}
        speed={1}
      />

      {/* Alert Banner */}
      <AlertBanner
        alerts={alerts}
        onDismiss={handleAlertDismiss}
        maxVisible={3}
      />
    </div>
  );
}
