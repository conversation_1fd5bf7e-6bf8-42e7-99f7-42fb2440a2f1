'use client';

import React, { useState } from 'react';
import Navigation from '@/components/Navigation';
import AlertSummary from '@/components/AlertSummary';
import LogsTable from '@/components/LogsTable';
import { TimeFilter } from '@/types';

export default function MonitoringDashboard() {
  const [timeFilter, setTimeFilter] = useState<TimeFilter>('7d');
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <Navigation />

      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Monitoring Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Real-time fleet monitoring and analytics
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Alert Summary Section */}
          <AlertSummary
            timeFilter={timeFilter}
            onTimeFilterChange={setTimeFilter}
          />

          {/* Logs Table Section */}
          <LogsTable
            timeFilter={timeFilter}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
          />
        </div>
      </main>
    </div>
  );
}
