'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Navigation from '@/components/Navigation';
import AlertTrendsChart from '@/components/charts/AlertTrendsChart';
import VehicleStatusChart from '@/components/charts/VehicleStatusChart';
import EventFrequencyChart from '@/components/charts/EventFrequencyChart';
import FleetMetrics from '@/components/FleetMetrics';
import { 
  TimeFilter, 
  AlertTrendData, 
  VehicleStatusData, 
  EventFrequencyData, 
  FleetPerformanceMetrics,
  AnalyticsApiResponse 
} from '@/types';

export default function AnalyticsDashboard() {
  const [timeFilter, setTimeFilter] = useState<TimeFilter>('7d');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Chart data states
  const [alertTrends, setAlertTrends] = useState<AlertTrendData[]>([]);
  const [vehicleStatus, setVehicleStatus] = useState<VehicleStatusData[]>([]);
  const [eventFrequency, setEventFrequency] = useState<EventFrequencyData[]>([]);
  const [fleetMetrics, setFleetMetrics] = useState<FleetPerformanceMetrics | null>(null);

  // Fetch analytics data
  const fetchAnalyticsData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/analytics?timeFilter=${timeFilter}&type=all`);
      const result: AnalyticsApiResponse<{
        alertTrends: AlertTrendData[];
        vehicleStatus: VehicleStatusData[];
        eventFrequency: EventFrequencyData[];
        fleetMetrics: FleetPerformanceMetrics;
      }> = await response.json();

      if (result.success) {
        setAlertTrends(result.data.alertTrends);
        setVehicleStatus(result.data.vehicleStatus);
        setEventFrequency(result.data.eventFrequency);
        setFleetMetrics(result.data.fleetMetrics);
      } else {
        setError(result.error || 'Failed to fetch analytics data');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  }, [timeFilter]);

  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData]);

  // Time filter options
  const timeFilterOptions: { value: TimeFilter; label: string }[] = [
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: 'all', label: 'All Time' },
  ];

  // Export data functionality
  const exportData = () => {
    const data = {
      timeFilter,
      generatedAt: new Date().toISOString(),
      alertTrends,
      vehicleStatus,
      eventFrequency,
      fleetMetrics
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `fleet-analytics-${timeFilter}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <Navigation />

      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                Analytics Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              {/* Time Filter */}
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">Time Period:</label>
                <select
                  value={timeFilter}
                  onChange={(e) => setTimeFilter(e.target.value as TimeFilter)}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {timeFilterOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Export Button */}
              <button
                onClick={exportData}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                📊 Export Data
              </button>

              {/* Refresh Button */}
              <button
                onClick={fetchAnalyticsData}
                disabled={loading}
                className="px-4 py-2 bg-gray-600 text-white rounded-md text-sm font-medium hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                🔄 Refresh
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Error State */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <span className="text-red-500 mr-2">❌</span>
              <p className="text-red-800">{error}</p>
              <button
                onClick={fetchAnalyticsData}
                className="ml-auto text-red-600 hover:text-red-800 text-sm font-medium"
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        <div className="space-y-8">
          {/* Fleet Performance Metrics */}
          <FleetMetrics
            metrics={fleetMetrics || {
              averageRouteCompletionTime: 0,
              totalDistanceTraveled: 0,
              fuelEfficiency: 0,
              onTimePerformance: 0,
              totalTrips: 0,
              averageSpeed: 0,
              maintenanceAlerts: 0,
              emergencyStops: 0
            }}
            loading={loading}
            error={error}
          />

          {/* Charts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Alert Trends Chart */}
            <div className="lg:col-span-2">
              <AlertTrendsChart
                data={alertTrends}
                loading={loading}
                error={error}
              />
            </div>

            {/* Vehicle Status Chart */}
            <VehicleStatusChart
              data={vehicleStatus}
              loading={loading}
              error={error}
            />

            {/* Event Frequency Chart */}
            <EventFrequencyChart
              data={eventFrequency}
              loading={loading}
              error={error}
            />
          </div>

          {/* Data Summary */}
          {!loading && !error && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Data Summary</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">
                    {alertTrends.reduce((sum, item) => sum + item.total, 0)}
                  </p>
                  <p className="text-sm text-gray-600">Total Alerts</p>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">
                    {vehicleStatus.reduce((sum, item) => sum + item.count, 0)}
                  </p>
                  <p className="text-sm text-gray-600">Active Vehicles</p>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <p className="text-2xl font-bold text-purple-600">
                    {eventFrequency.reduce((sum, item) => sum + item.count, 0)}
                  </p>
                  <p className="text-sm text-gray-600">Total Events</p>
                </div>
                <div className="p-4 bg-orange-50 rounded-lg">
                  <p className="text-2xl font-bold text-orange-600">
                    {fleetMetrics?.onTimePerformance || 0}%
                  </p>
                  <p className="text-sm text-gray-600">On-Time Performance</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
