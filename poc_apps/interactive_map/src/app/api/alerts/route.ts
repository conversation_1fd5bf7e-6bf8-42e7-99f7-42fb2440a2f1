import { NextRequest, NextResponse } from 'next/server';
import { Alert, AlertStats, ApiResponse, TimeFilter } from '@/types';

// Mock alert data generator
function generateMockAlerts(): Alert[] {
  const alerts: Alert[] = [];
  const vehicleIds = ['VH-001', 'VH-002', 'VH-003', 'VH-004', 'VH-005'];
  const alertTypes: Alert['type'][] = ['warning', 'error', 'info'];
  const messages = {
    warning: [
      'Vehicle is outside allowed route',
      'Speed limit exceeded',
      'Maintenance due soon',
      'Low fuel warning',
      'Route deviation detected'
    ],
    error: [
      'Emergency stop activated',
      'Critical system failure',
      'GPS signal lost',
      'Engine malfunction',
      'Unauthorized access attempt'
    ],
    info: [
      'Route completed successfully',
      'Waypoint reached',
      'Vehicle started journey',
      'Regular maintenance completed',
      'Driver shift change'
    ]
  };

  // Generate alerts for the past 7 days
  const now = new Date();
  for (let i = 0; i < 150; i++) {
    const daysAgo = Math.floor(Math.random() * 7);
    const hoursAgo = Math.floor(Math.random() * 24);
    const minutesAgo = Math.floor(Math.random() * 60);
    
    const timestamp = new Date(now);
    timestamp.setDate(timestamp.getDate() - daysAgo);
    timestamp.setHours(timestamp.getHours() - hoursAgo);
    timestamp.setMinutes(timestamp.getMinutes() - minutesAgo);

    const type = alertTypes[Math.floor(Math.random() * alertTypes.length)];
    const vehicleId = vehicleIds[Math.floor(Math.random() * vehicleIds.length)];
    const messageList = messages[type];
    const message = messageList[Math.floor(Math.random() * messageList.length)];
    
    // 70% chance of being resolved for older alerts
    const isActive = daysAgo === 0 ? Math.random() > 0.3 : Math.random() > 0.7;

    alerts.push({
      id: `alert-${i + 1}`,
      type,
      message,
      timestamp,
      vehicleId,
      isActive
    });
  }

  return alerts.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
}

// Filter alerts based on time filter
function filterAlertsByTime(alerts: Alert[], timeFilter: TimeFilter): Alert[] {
  if (timeFilter === 'all') return alerts;

  const now = new Date();
  const cutoffTime = new Date(now);

  switch (timeFilter) {
    case '24h':
      cutoffTime.setHours(cutoffTime.getHours() - 24);
      break;
    case '7d':
      cutoffTime.setDate(cutoffTime.getDate() - 7);
      break;
    case '30d':
      cutoffTime.setDate(cutoffTime.getDate() - 30);
      break;
  }

  return alerts.filter(alert => alert.timestamp >= cutoffTime);
}

// Calculate alert statistics
function calculateAlertStats(alerts: Alert[]): AlertStats {
  const stats: AlertStats = {
    total: alerts.length,
    byType: { warning: 0, error: 0, info: 0 },
    byStatus: { active: 0, resolved: 0 },
    byLevel: { low: 0, medium: 0, high: 0, critical: 0 }
  };

  alerts.forEach(alert => {
    // Count by type
    stats.byType[alert.type]++;
    
    // Count by status
    if (alert.isActive) {
      stats.byStatus.active++;
    } else {
      stats.byStatus.resolved++;
    }
    
    // Count by level (map type to level)
    if (alert.type === 'info') {
      stats.byLevel.low++;
    } else if (alert.type === 'warning') {
      stats.byLevel.medium++;
    } else if (alert.type === 'error') {
      stats.byLevel.high++;
    }
  });

  return stats;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeFilter = (searchParams.get('timeFilter') as TimeFilter) || '7d';
    const statsOnly = searchParams.get('statsOnly') === 'true';

    // Generate mock data
    const allAlerts = generateMockAlerts();
    const filteredAlerts = filterAlertsByTime(allAlerts, timeFilter);

    if (statsOnly) {
      const stats = calculateAlertStats(filteredAlerts);
      const response: ApiResponse<AlertStats> = {
        success: true,
        data: stats
      };
      return NextResponse.json(response);
    }

    const response: ApiResponse<Alert[]> = {
      success: true,
      data: filteredAlerts
    };

    return NextResponse.json(response);
  } catch (error) {
    const response: ApiResponse<null> = {
      success: false,
      data: null,
      error: 'Failed to fetch alerts'
    };
    return NextResponse.json(response, { status: 500 });
  }
}
