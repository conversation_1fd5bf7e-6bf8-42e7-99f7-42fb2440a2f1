import { NextRequest, NextResponse } from 'next/server';
import { 
  AlertTrendData, 
  VehicleStatusData, 
  EventFrequencyData, 
  FleetPerformanceMetrics,
  AnalyticsApiResponse,
  TimeFilter,
  EventType 
} from '@/types';

// Generate alert trends data for line chart
function generateAlertTrendsData(timeFilter: TimeFilter): AlertTrendData[] {
  const data: AlertTrendData[] = [];
  const days = timeFilter === '24h' ? 1 : timeFilter === '7d' ? 7 : timeFilter === '30d' ? 30 : 7;
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    // Generate realistic alert counts with some variation
    const baseWarning = Math.floor(Math.random() * 15) + 5;
    const baseError = Math.floor(Math.random() * 8) + 2;
    const baseInfo = Math.floor(Math.random() * 20) + 10;
    
    // Add some patterns - more alerts during weekdays
    const isWeekend = date.getDay() === 0 || date.getDay() === 6;
    const multiplier = isWeekend ? 0.6 : 1.2;
    
    const warning = Math.floor(baseWarning * multiplier);
    const error = Math.floor(baseError * multiplier);
    const info = Math.floor(baseInfo * multiplier);
    
    data.push({
      date: date.toISOString().split('T')[0],
      warning,
      error,
      info,
      total: warning + error + info
    });
  }
  
  return data;
}

// Generate vehicle status distribution data for pie chart
function generateVehicleStatusData(): VehicleStatusData[] {
  const totalVehicles = 5;
  const onRoute = Math.floor(Math.random() * 3) + 2; // 2-4 vehicles
  const offRoute = Math.floor(Math.random() * 2) + 1; // 1-2 vehicles
  const stopped = totalVehicles - onRoute - offRoute;
  
  return [
    {
      status: 'on-route',
      count: onRoute,
      percentage: (onRoute / totalVehicles) * 100,
      color: '#10b981'
    },
    {
      status: 'off-route',
      count: offRoute,
      percentage: (offRoute / totalVehicles) * 100,
      color: '#ef4444'
    },
    {
      status: 'stopped',
      count: stopped,
      percentage: (stopped / totalVehicles) * 100,
      color: '#f59e0b'
    }
  ];
}

// Generate event frequency data for bar chart
function generateEventFrequencyData(timeFilter: TimeFilter): EventFrequencyData[] {
  const eventTypes: { type: EventType; label: string; color: string }[] = [
    { type: 'position_update', label: 'Position Updates', color: '#3b82f6' },
    { type: 'route_started', label: 'Route Started', color: '#10b981' },
    { type: 'route_completed', label: 'Route Completed', color: '#059669' },
    { type: 'waypoint_reached', label: 'Waypoint Reached', color: '#06b6d4' },
    { type: 'alert_triggered', label: 'Alert Triggered', color: '#f59e0b' },
    { type: 'route_deviation', label: 'Route Deviation', color: '#ef4444' },
    { type: 'speed_violation', label: 'Speed Violation', color: '#dc2626' },
    { type: 'emergency_stop', label: 'Emergency Stop', color: '#991b1b' },
    { type: 'maintenance_due', label: 'Maintenance Due', color: '#7c3aed' },
    { type: 'vehicle_stopped', label: 'Vehicle Stopped', color: '#6b7280' },
    { type: 'vehicle_resumed', label: 'Vehicle Resumed', color: '#16a34a' }
  ];
  
  // Adjust counts based on time filter
  const multiplier = timeFilter === '24h' ? 0.1 : timeFilter === '7d' ? 1 : timeFilter === '30d' ? 4 : 1;
  
  return eventTypes.map(event => {
    let baseCount = 0;
    
    // Different base counts for different event types
    switch (event.type) {
      case 'position_update':
        baseCount = Math.floor((Math.random() * 100 + 200) * multiplier);
        break;
      case 'route_started':
      case 'route_completed':
        baseCount = Math.floor((Math.random() * 20 + 30) * multiplier);
        break;
      case 'waypoint_reached':
        baseCount = Math.floor((Math.random() * 50 + 80) * multiplier);
        break;
      case 'alert_triggered':
        baseCount = Math.floor((Math.random() * 15 + 25) * multiplier);
        break;
      case 'route_deviation':
      case 'speed_violation':
        baseCount = Math.floor((Math.random() * 8 + 12) * multiplier);
        break;
      case 'emergency_stop':
        baseCount = Math.floor((Math.random() * 3 + 2) * multiplier);
        break;
      case 'maintenance_due':
        baseCount = Math.floor((Math.random() * 5 + 5) * multiplier);
        break;
      case 'vehicle_stopped':
      case 'vehicle_resumed':
        baseCount = Math.floor((Math.random() * 25 + 35) * multiplier);
        break;
    }
    
    return {
      eventType: event.type,
      count: Math.max(1, baseCount),
      label: event.label,
      color: event.color
    };
  });
}

// Generate fleet performance metrics
function generateFleetPerformanceMetrics(timeFilter: TimeFilter): FleetPerformanceMetrics {
  const multiplier = timeFilter === '24h' ? 0.1 : timeFilter === '7d' ? 1 : timeFilter === '30d' ? 4 : 1;
  
  return {
    averageRouteCompletionTime: Math.floor(Math.random() * 30 + 45), // 45-75 minutes
    totalDistanceTraveled: Math.floor((Math.random() * 500 + 1000) * multiplier), // km
    fuelEfficiency: Math.round((Math.random() * 2 + 8) * 10) / 10, // 8-10 km/l
    onTimePerformance: Math.floor(Math.random() * 15 + 85), // 85-100%
    totalTrips: Math.floor((Math.random() * 20 + 50) * multiplier),
    averageSpeed: Math.floor(Math.random() * 20 + 45), // 45-65 km/h
    maintenanceAlerts: Math.floor((Math.random() * 5 + 2) * multiplier),
    emergencyStops: Math.floor((Math.random() * 3 + 1) * multiplier)
  };
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeFilter = (searchParams.get('timeFilter') as TimeFilter) || '7d';
    const dataType = searchParams.get('type') || 'all';

    let responseData: any = {};

    switch (dataType) {
      case 'trends':
        responseData = generateAlertTrendsData(timeFilter);
        break;
      case 'status':
        responseData = generateVehicleStatusData();
        break;
      case 'events':
        responseData = generateEventFrequencyData(timeFilter);
        break;
      case 'metrics':
        responseData = generateFleetPerformanceMetrics(timeFilter);
        break;
      case 'all':
      default:
        responseData = {
          alertTrends: generateAlertTrendsData(timeFilter),
          vehicleStatus: generateVehicleStatusData(),
          eventFrequency: generateEventFrequencyData(timeFilter),
          fleetMetrics: generateFleetPerformanceMetrics(timeFilter)
        };
        break;
    }

    const response: AnalyticsApiResponse<typeof responseData> = {
      success: true,
      data: responseData,
      timeFilter,
      generatedAt: new Date()
    };

    return NextResponse.json(response);
  } catch (error) {
    const response: AnalyticsApiResponse<null> = {
      success: false,
      data: null,
      error: 'Failed to fetch analytics data',
      timeFilter: '7d',
      generatedAt: new Date()
    };
    return NextResponse.json(response, { status: 500 });
  }
}
