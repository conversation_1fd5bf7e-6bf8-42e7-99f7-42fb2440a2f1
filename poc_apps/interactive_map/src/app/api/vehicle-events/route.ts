import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { eventType, vehicleId, position, distance, timestamp, message } = body;

    // Get current time for terminal logging
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    });

    // Log different types of events with colored output
    switch (eventType) {
      case 'off-route-start':
        console.log('\n' + '='.repeat(80));
        console.log(`🚨 [${timeString}] VEHICLE OFF-ROUTE ALERT!`);
        console.log(`   Vehicle ID: ${vehicleId}`);
        console.log(`   Position: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`);
        console.log(`   Distance from route: ${Math.round(distance)}m`);
        console.log(`   Message: ${message}`);
        console.log('='.repeat(80) + '\n');
        break;

      case 'off-route-continue':
        console.log(`🔴 [${timeString}] Vehicle ${vehicleId} still off-route: ${Math.round(distance)}m`);
        break;

      case 'back-on-route':
        console.log('\n' + '-'.repeat(80));
        console.log(`✅ [${timeString}] VEHICLE BACK ON ROUTE!`);
        console.log(`   Vehicle ID: ${vehicleId}`);
        console.log(`   Position: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`);
        console.log(`   Distance from route: ${Math.round(distance)}m`);
        console.log(`   Message: ${message}`);
        console.log('-'.repeat(80) + '\n');
        break;

      case 'simulation-start':
        console.log('\n' + '🚀 [' + timeString + '] Vehicle simulation started for ' + vehicleId);
        break;

      case 'simulation-stop':
        console.log('\n' + '⏹️  [' + timeString + '] Vehicle simulation stopped for ' + vehicleId);
        break;

      case 'position-update':
        // Only log every 10th position update to avoid spam
        if (Math.random() < 0.1) {
          console.log(`📍 [${timeString}] Vehicle ${vehicleId}: ${position.lat.toFixed(6)}, ${position.lng.toFixed(6)} (${Math.round(distance)}m from route)`);
        }
        break;

      default:
        console.log(`📝 [${timeString}] Vehicle Event: ${eventType} - ${vehicleId} - ${message}`);
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Event logged successfully',
      timestamp: now.toISOString()
    });

  } catch (error) {
    console.error('❌ Error logging vehicle event:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to log event' },
      { status: 500 }
    );
  }
}
