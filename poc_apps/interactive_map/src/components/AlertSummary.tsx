'use client';

import React, { useState, useEffect } from 'react';
import { AlertSummaryProps, AlertStats, TimeFilter, ApiResponse } from '@/types';

const AlertSummary: React.FC<AlertSummaryProps> = ({
  timeFilter,
  onTimeFilterChange,
}) => {
  const [stats, setStats] = useState<AlertStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch alert statistics
  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/alerts?timeFilter=${timeFilter}&statsOnly=true`);
        const result: ApiResponse<AlertStats> = await response.json();
        
        if (result.success) {
          setStats(result.data);
        } else {
          setError(result.error || 'Failed to fetch alert statistics');
        }
      } catch (err) {
        setError('Network error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [timeFilter]);

  // Time filter options
  const timeFilterOptions: { value: TimeFilter; label: string }[] = [
    { value: '24h', label: 'Last 24 Hours' },
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: 'all', label: 'All Time' },
  ];

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4 w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center text-red-600">
          <p>Error loading alert statistics: {error}</p>
        </div>
      </div>
    );
  }

  if (!stats) return null;

  return (
    <div className="bg-white rounded-lg shadow p-6">
      {/* Header with time filter */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 sm:mb-0">
          Alert Summary
        </h2>
        <div className="flex items-center space-x-2">
          <label className="text-sm font-medium text-gray-700">Time Period:</label>
          <select
            value={timeFilter}
            onChange={(e) => onTimeFilterChange(e.target.value as TimeFilter)}
            className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {timeFilterOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Total Alerts */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">📊</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-blue-800">Total Alerts</p>
              <p className="text-2xl font-bold text-blue-900">{stats.total}</p>
            </div>
          </div>
        </div>

        {/* Active Alerts */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">🚨</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">Active Alerts</p>
              <p className="text-2xl font-bold text-red-900">{stats.byStatus.active}</p>
            </div>
          </div>
        </div>

        {/* Resolved Alerts */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">✅</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">Resolved</p>
              <p className="text-2xl font-bold text-green-900">{stats.byStatus.resolved}</p>
            </div>
          </div>
        </div>

        {/* Critical Alerts */}
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">⚠️</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-orange-800">Critical</p>
              <p className="text-2xl font-bold text-orange-900">{stats.byLevel.critical}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Alert Type Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* By Type */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-3">By Type</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-700">Warnings</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{stats.byType.warning}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-700">Errors</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{stats.byType.error}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-700">Info</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{stats.byType.info}</span>
            </div>
          </div>
        </div>

        {/* By Level */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-3">By Level</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-700">Low</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{stats.byLevel.low}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-700">Medium</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{stats.byLevel.medium}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-700">High</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{stats.byLevel.high}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-700">Critical</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{stats.byLevel.critical}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AlertSummary;
