'use client';

import React, { useCallback, useRef, useState, useEffect } from 'react';
import { GoogleMap, LoadScript, <PERSON>yline, Marker } from '@react-google-maps/api';
import { TrackingMapProps, MapConfig, Coordinate } from '@/types';

// Default map configuration
const defaultMapConfig: MapConfig = {
  center: { lat: 24.7136, lng: 46.6753 }, // Riyadh, Saudi Arabia
  zoom: 12,
  disableDefaultUI: false,
  zoomControl: true,
  streetViewControl: false,
  fullscreenControl: true,
};

// Google Maps libraries to load
const libraries: ('geometry' | 'places' | 'drawing' | 'visualization')[] = ['geometry'];

// Map container style
const mapContainerStyle = {
  width: '100%',
  height: '100%',
  minHeight: '500px',
};

const TrackingMap: React.FC<TrackingMapProps> = ({
  route,
  vehicle,
  onVehicleUpdate,
  onAlert,
  mapConfig = {},
  geofenceConfig = {},
}) => {
  const mapRef = useRef<google.maps.Map | null>(null);
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  // Merge default config with provided config
  const finalMapConfig = { ...defaultMapConfig, ...mapConfig };

  // Get API key from environment
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

  // Ensure component only renders on client side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Handle map load
  const onMapLoad = useCallback((map: google.maps.Map) => {
    mapRef.current = map;
    setIsMapLoaded(true);

    // Fit map to show the entire route
    if (route.waypoints.length > 0 && window.google) {
      const bounds = new window.google.maps.LatLngBounds();
      route.waypoints.forEach(point => {
        bounds.extend(new window.google.maps.LatLng(point.lat, point.lng));
      });
      map.fitBounds(bounds);
    }
  }, [route.waypoints]);

  // Handle map unmount
  const onMapUnmount = useCallback(() => {
    mapRef.current = null;
    setIsMapLoaded(false);
  }, []);

  // Handle load error
  const onLoadError = useCallback((error: Error) => {
    console.error('Google Maps load error:', error);
    setLoadError(error.message);
  }, []);

  // Calculate route polyline options
  const routeOptions = {
    path: route.waypoints,
    geodesic: true,
    strokeColor: route.color || '#2563eb',
    strokeOpacity: 1.0,
    strokeWeight: route.strokeWeight || 4,
  };

  // Custom vehicle icon path (truck/car shape)
  const vehicleIconPath = "M12 2C13.1 2 14 2.9 14 4V6H18C19.1 6 20 6.9 20 8V14H18.5C18.5 15.9 16.9 17.5 15 17.5S11.5 15.9 11.5 14H8.5C8.5 15.9 6.9 17.5 5 17.5S1.5 15.9 1.5 14H0V8C0 6.9 0.9 6 2 6H6V4C6 2.9 6.9 2 8 2H12M8 4V6H12V4H8M5 12.5C6.4 12.5 7.5 13.6 7.5 15S6.4 17.5 5 17.5 2.5 16.4 2.5 15 3.6 12.5 5 12.5M15 12.5C16.4 12.5 17.5 13.6 17.5 15S16.4 17.5 15 17.5 12.5 16.4 12.5 15 13.6 12.5 15 12.5Z";

  // Vehicle marker options
  const vehicleIcon = isMounted && isMapLoaded && typeof window !== 'undefined' && window.google ? {
    path: vehicleIconPath,
    scale: 1.5,
    fillColor: vehicle.isOnRoute ? '#10b981' : '#ef4444',
    fillOpacity: 1,
    strokeColor: '#ffffff',
    strokeWeight: 2,
    rotation: vehicle.position.heading || 0,
    anchor: new window.google.maps.Point(10, 10), // Center the icon
  } : undefined;

  // Show loading state during hydration
  if (!isMounted) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-100 rounded-lg">
        <div className="text-center p-6">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-600">Initializing map...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (!apiKey) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-100 rounded-lg">
        <div className="text-center p-6">
          <div className="text-red-500 text-xl mb-2">⚠️</div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            Google Maps API Key Missing
          </h3>
          <p className="text-gray-600 text-sm">
            Please add your Google Maps API key to the .env.local file
          </p>
        </div>
      </div>
    );
  }

  if (loadError) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-100 rounded-lg">
        <div className="text-center p-6">
          <div className="text-red-500 text-xl mb-2">❌</div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            Map Load Error
          </h3>
          <p className="text-gray-600 text-sm">{loadError}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative">
      <LoadScript
        googleMapsApiKey={apiKey}
        libraries={libraries}
        onError={onLoadError}
        loadingElement={
          <div className="flex items-center justify-center h-full bg-gray-100 rounded-lg">
            <div className="text-center p-6">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p className="text-gray-600">Loading Google Maps...</p>
            </div>
          </div>
        }
      >
        <GoogleMap
          mapContainerStyle={mapContainerStyle}
          center={finalMapConfig.center}
          zoom={finalMapConfig.zoom}
          onLoad={onMapLoad}
          onUnmount={onMapUnmount}
          options={{
            disableDefaultUI: finalMapConfig.disableDefaultUI,
            zoomControl: finalMapConfig.zoomControl,
            streetViewControl: finalMapConfig.streetViewControl,
            fullscreenControl: finalMapConfig.fullscreenControl,
            mapTypeControl: false,
            scaleControl: true,
          }}
        >
          {/* Route polyline */}
          {route.waypoints.length > 1 && (
            <Polyline options={routeOptions} />
          )}

          {/* Vehicle marker */}
          {vehicleIcon && (
            <Marker
              position={vehicle.position}
              icon={vehicleIcon}
              title={`Vehicle ${vehicle.id} - ${vehicle.status}`}
            />
          )}

          {/* Route waypoint markers */}
          {isMounted && isMapLoaded && typeof window !== 'undefined' && window.google && route.waypoints.map((waypoint, index) => (
            <Marker
              key={`waypoint-${index}`}
              position={waypoint}
              icon={{
                path: window.google.maps.SymbolPath.CIRCLE,
                scale: 4,
                fillColor: index === 0 ? '#10b981' : index === route.waypoints.length - 1 ? '#ef4444' : '#6b7280',
                fillOpacity: 1,
                strokeColor: '#ffffff',
                strokeWeight: 2,
              }}
              title={
                index === 0
                  ? 'Start Point'
                  : index === route.waypoints.length - 1
                    ? 'End Point'
                    : `Waypoint ${index + 1}`
              }
            />
          ))}
        </GoogleMap>
      </LoadScript>

      {/* Map overlay with vehicle status */}
      <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 z-10">
        <div className="flex items-center space-x-2">
          <div 
            className={`w-3 h-3 rounded-full ${
              vehicle.isOnRoute ? 'bg-green-500' : 'bg-red-500'
            }`}
          />
          <span className="text-sm font-medium">
            {vehicle.isOnRoute ? 'On Route' : 'Off Route'}
          </span>
        </div>
        <div className="text-xs text-gray-500 mt-1">
          Distance from route: {Math.round(vehicle.distanceFromRoute)}m
        </div>
      </div>
    </div>
  );
};

export default TrackingMap;
