'use client';

import React, { useEffect, useRef, useCallback } from 'react';
import { VehicleTrackerProps, VehiclePosition, Coordinate } from '@/types';

const VehicleTracker: React.FC<VehicleTrackerProps> = ({
  route,
  onPositionUpdate,
  onStatusChange,
  isActive,
  speed = 1,
}) => {
  const currentWaypointIndex = useRef(0);
  const progress = useRef(0); // Progress between current and next waypoint (0-1)
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isOffRoute = useRef(false);
  const offRouteCounter = useRef(0);

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = useCallback((coord1: Coordinate, coord2: Coordinate): number => {
    const R = 6371000; // Earth's radius in meters
    const dLat = (coord2.lat - coord1.lat) * Math.PI / 180;
    const dLng = (coord2.lng - coord1.lng) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(coord1.lat * Math.PI / 180) * Math.cos(coord2.lat * Math.PI / 180) * 
      Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }, []);

  // Calculate bearing between two coordinates
  const calculateBearing = useCallback((coord1: Coordinate, coord2: Coordinate): number => {
    const dLng = (coord2.lng - coord1.lng) * Math.PI / 180;
    const lat1 = coord1.lat * Math.PI / 180;
    const lat2 = coord2.lat * Math.PI / 180;
    
    const y = Math.sin(dLng) * Math.cos(lat2);
    const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLng);
    
    const bearing = Math.atan2(y, x) * 180 / Math.PI;
    return (bearing + 360) % 360;
  }, []);

  // Interpolate between two coordinates
  const interpolatePosition = useCallback((
    start: Coordinate, 
    end: Coordinate, 
    progress: number
  ): Coordinate => {
    // Add some randomness for off-route simulation
    let lat = start.lat + (end.lat - start.lat) * progress;
    let lng = start.lng + (end.lng - start.lng) * progress;

    // Simulate going off-route occasionally (every 20-30 updates)
    if (offRouteCounter.current > 20 && Math.random() < 0.1) {
      const offsetDistance = 0.001; // Roughly 100 meters
      lat += (Math.random() - 0.5) * offsetDistance;
      lng += (Math.random() - 0.5) * offsetDistance;
      isOffRoute.current = true;
      offRouteCounter.current = 0;
    } else if (isOffRoute.current && offRouteCounter.current > 5) {
      // Return to route after being off-route for a while
      isOffRoute.current = false;
    }

    offRouteCounter.current++;

    return { lat, lng };
  }, []);

  // Update vehicle position
  const updatePosition = useCallback(() => {
    if (!route.waypoints || route.waypoints.length < 2) return;

    const currentWaypoint = route.waypoints[currentWaypointIndex.current];
    const nextWaypointIndex = currentWaypointIndex.current + 1;

    // Check if we've reached the end of the route
    if (nextWaypointIndex >= route.waypoints.length) {
      // Restart from the beginning
      currentWaypointIndex.current = 0;
      progress.current = 0;
      onStatusChange('on-route');
      return;
    }

    const nextWaypoint = route.waypoints[nextWaypointIndex];

    // Calculate new position
    const newPosition = interpolatePosition(currentWaypoint, nextWaypoint, progress.current);
    
    // Calculate bearing for vehicle orientation
    const bearing = calculateBearing(currentWaypoint, nextWaypoint);

    // Create vehicle position object
    const vehiclePosition: VehiclePosition = {
      lat: newPosition.lat,
      lng: newPosition.lng,
      timestamp: new Date(),
      speed: 50 + Math.random() * 20, // Random speed between 50-70 km/h
      heading: bearing,
    };

    // Update progress
    progress.current += 0.02 * speed; // Adjust speed multiplier

    // Check if we've reached the next waypoint
    if (progress.current >= 1) {
      currentWaypointIndex.current = nextWaypointIndex;
      progress.current = 0;
    }

    // Update status based on whether vehicle is off-route
    const status = isOffRoute.current ? 'off-route' : 'on-route';
    onStatusChange(status);

    // Notify parent component
    onPositionUpdate(vehiclePosition);
  }, [route.waypoints, speed, onPositionUpdate, onStatusChange, interpolatePosition, calculateBearing]);

  // Start/stop simulation
  useEffect(() => {
    if (isActive && route.waypoints.length >= 2) {
      // Start simulation
      intervalRef.current = setInterval(updatePosition, 2000); // Update every 2 seconds
    } else {
      // Stop simulation
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    // Cleanup on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isActive, route.waypoints.length, updatePosition]);

  // Reset simulation when route changes
  useEffect(() => {
    currentWaypointIndex.current = 0;
    progress.current = 0;
    isOffRoute.current = false;
    offRouteCounter.current = 0;
  }, [route]);

  // This component doesn't render anything visible
  return null;
};

export default VehicleTracker;
