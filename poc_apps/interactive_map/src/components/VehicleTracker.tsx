'use client';

import React, { useEffect, useRef, useCallback } from 'react';
import { VehicleTrackerProps, VehiclePosition, Coordinate } from '@/types';

const VehicleTracker: React.FC<VehicleTrackerProps> = ({
  route,
  onPositionUpdate,
  onStatusChange,
  isActive,
  speed = 1,
}) => {
  const currentWaypointIndex = useRef(0);
  const progress = useRef(0); // Progress between current and next waypoint (0-1)
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isOffRoute = useRef(false);
  const offRouteCounter = useRef(0);
  const totalUpdateCounter = useRef(0);
  const offRouteTestTriggered = useRef(false);



  // Calculate bearing between two coordinates
  const calculateBearing = useCallback((coord1: Coordinate, coord2: Coordinate): number => {
    const dLng = (coord2.lng - coord1.lng) * Math.PI / 180;
    const lat1 = coord1.lat * Math.PI / 180;
    const lat2 = coord2.lat * Math.PI / 180;
    
    const y = Math.sin(dLng) * Math.cos(lat2);
    const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLng);
    
    const bearing = Math.atan2(y, x) * 180 / Math.PI;
    return (bearing + 360) % 360;
  }, []);

  // Interpolate between two coordinates
  const interpolatePosition = useCallback((
    start: Coordinate,
    end: Coordinate,
    progress: number
  ): Coordinate => {
    // Calculate base position along the route
    let lat = start.lat + (end.lat - start.lat) * progress;
    let lng = start.lng + (end.lng - start.lng) * progress;

    // Increment counters
    totalUpdateCounter.current++;
    offRouteCounter.current++;

    // Controlled off-route testing: trigger once after 30 updates, then stay off-route for 10 updates
    if (!offRouteTestTriggered.current && totalUpdateCounter.current > 30 && totalUpdateCounter.current < 45) {
      if (totalUpdateCounter.current === 31) {
        // Start off-route test
        isOffRoute.current = true;
        offRouteTestTriggered.current = true;
        offRouteCounter.current = 0;
      }

      if (isOffRoute.current) {
        // Apply consistent offset while off-route
        const offsetDistance = 0.002; // Roughly 200 meters
        lat += offsetDistance; // Move north consistently
        lng += offsetDistance * 0.5; // Move slightly east
      }
    } else if (isOffRoute.current && offRouteCounter.current > 10) {
      // Return to route after being off-route for 10 updates
      isOffRoute.current = false;
      offRouteCounter.current = 0;
    }

    return { lat, lng };
  }, []);

  // Update vehicle position
  const updatePosition = useCallback(() => {
    if (!route.waypoints || route.waypoints.length < 2) return;

    const currentWaypoint = route.waypoints[currentWaypointIndex.current];
    const nextWaypointIndex = currentWaypointIndex.current + 1;

    // Check if we've reached the end of the route
    if (nextWaypointIndex >= route.waypoints.length) {
      // Restart from the beginning
      currentWaypointIndex.current = 0;
      progress.current = 0;
      onStatusChange('on-route');
      return;
    }

    const nextWaypoint = route.waypoints[nextWaypointIndex];

    // Calculate new position
    const newPosition = interpolatePosition(currentWaypoint, nextWaypoint, progress.current);
    
    // Calculate bearing for vehicle orientation
    const bearing = calculateBearing(currentWaypoint, nextWaypoint);

    // Create vehicle position object
    const vehiclePosition: VehiclePosition = {
      lat: newPosition.lat,
      lng: newPosition.lng,
      timestamp: new Date(),
      speed: 50 + Math.random() * 20, // Random speed between 50-70 km/h
      heading: bearing,
    };

    // Update progress
    progress.current += 0.02 * speed; // Adjust speed multiplier

    // Check if we've reached the next waypoint
    if (progress.current >= 1) {
      currentWaypointIndex.current = nextWaypointIndex;
      progress.current = 0;
    }

    // Update status based on whether vehicle is off-route
    // Note: The actual route status will be determined by the distance calculation in the parent component
    const status = isOffRoute.current ? 'off-route' : 'on-route';
    onStatusChange(status);

    // Notify parent component
    onPositionUpdate(vehiclePosition);
  }, [route.waypoints, speed, onPositionUpdate, onStatusChange, interpolatePosition, calculateBearing]);

  // Start/stop simulation
  useEffect(() => {
    if (isActive && route.waypoints.length >= 2) {
      // Start simulation
      intervalRef.current = setInterval(updatePosition, 2000); // Update every 2 seconds
    } else {
      // Stop simulation
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    // Cleanup on unmount
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isActive, route.waypoints.length, updatePosition]);

  // Reset simulation when route changes
  useEffect(() => {
    currentWaypointIndex.current = 0;
    progress.current = 0;
    isOffRoute.current = false;
    offRouteCounter.current = 0;
    totalUpdateCounter.current = 0;
    offRouteTestTriggered.current = false;
  }, [route]);

  // This component doesn't render anything visible
  return null;
};

export default VehicleTracker;
