'use client';

import React from 'react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { EventFrequencyChartProps } from '@/types';

const EventFrequencyChart: React.FC<EventFrequencyChartProps> = ({
  data,
  loading = false,
  error
}) => {
  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{data.label}</p>
          <p className="text-sm text-gray-600">Count: {data.count}</p>
          <p className="text-sm text-gray-600">Event Type: {data.eventType}</p>
        </div>
      );
    }
    return null;
  };

  // Loading state
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4 w-1/3"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Event Frequency</h3>
        <div className="text-center text-red-600">
          <p>Error loading chart data: {error}</p>
        </div>
      </div>
    );
  }

  // Empty data state
  if (!data || data.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Event Frequency</h3>
        <div className="text-center text-gray-500 py-8">
          <p>No event data available for the selected time period</p>
        </div>
      </div>
    );
  }

  // Sort data by count for better visualization
  const sortedData = [...data].sort((a, b) => b.count - a.count);

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Event Frequency by Type</h3>
        <div className="text-sm text-gray-600">
          Total Events: {data.reduce((sum, item) => sum + item.count, 0)}
        </div>
      </div>

      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={sortedData}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 60,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="label"
              stroke="#6b7280"
              fontSize={10}
              angle={-45}
              textAnchor="end"
              height={80}
              interval={0}
            />
            <YAxis 
              stroke="#6b7280"
              fontSize={12}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar 
              dataKey="count" 
              fill={(entry: any) => entry.color}
              radius={[4, 4, 0, 0]}
            >
              {sortedData.map((entry, index) => (
                <Bar key={`bar-${index}`} fill={entry.color} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Top events summary */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Top Event Types</h4>
        <div className="space-y-2">
          {sortedData.slice(0, 5).map((item, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-6 h-6 rounded text-white text-xs font-bold mr-3" style={{ backgroundColor: item.color }}>
                  {index + 1}
                </div>
                <span className="text-sm text-gray-900">{item.label}</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{item.count}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Event categories */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Operational Events */}
          <div className="text-center">
            <p className="text-sm font-medium text-gray-900 mb-1">Operational</p>
            <p className="text-2xl font-bold text-blue-600">
              {data.filter(item => 
                ['position_update', 'route_started', 'route_completed', 'waypoint_reached'].includes(item.eventType)
              ).reduce((sum, item) => sum + item.count, 0)}
            </p>
            <p className="text-xs text-gray-600">Normal operations</p>
          </div>

          {/* Alert Events */}
          <div className="text-center">
            <p className="text-sm font-medium text-gray-900 mb-1">Alerts</p>
            <p className="text-2xl font-bold text-yellow-600">
              {data.filter(item => 
                ['alert_triggered', 'route_deviation', 'speed_violation'].includes(item.eventType)
              ).reduce((sum, item) => sum + item.count, 0)}
            </p>
            <p className="text-xs text-gray-600">Warning events</p>
          </div>

          {/* Critical Events */}
          <div className="text-center">
            <p className="text-sm font-medium text-gray-900 mb-1">Critical</p>
            <p className="text-2xl font-bold text-red-600">
              {data.filter(item => 
                ['emergency_stop'].includes(item.eventType)
              ).reduce((sum, item) => sum + item.count, 0)}
            </p>
            <p className="text-xs text-gray-600">Emergency events</p>
          </div>

          {/* Maintenance Events */}
          <div className="text-center">
            <p className="text-sm font-medium text-gray-900 mb-1">Maintenance</p>
            <p className="text-2xl font-bold text-purple-600">
              {data.filter(item => 
                ['maintenance_due'].includes(item.eventType)
              ).reduce((sum, item) => sum + item.count, 0)}
            </p>
            <p className="text-xs text-gray-600">Service events</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventFrequencyChart;
