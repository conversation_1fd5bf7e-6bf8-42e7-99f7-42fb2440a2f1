'use client';

import React from 'react';
import { AlertBannerProps, Alert } from '@/types';

const AlertBanner: React.FC<AlertBannerProps> = ({
  alerts,
  onDismiss,
  maxVisible = 3,
}) => {
  // Filter active alerts and limit to maxVisible
  const activeAlerts = alerts
    .filter(alert => alert.isActive)
    .slice(0, maxVisible);

  if (activeAlerts.length === 0) {
    return null;
  }

  // Get alert styling based on type
  const getAlertStyles = (type: Alert['type']) => {
    switch (type) {
      case 'error':
        return {
          container: 'bg-red-50 border-red-200 text-red-800',
          icon: '🚨',
          iconBg: 'bg-red-100',
        };
      case 'warning':
        return {
          container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
          icon: '⚠️',
          iconBg: 'bg-yellow-100',
        };
      case 'info':
        return {
          container: 'bg-blue-50 border-blue-200 text-blue-800',
          icon: 'ℹ️',
          iconBg: 'bg-blue-100',
        };
      default:
        return {
          container: 'bg-gray-50 border-gray-200 text-gray-800',
          icon: '📢',
          iconBg: 'bg-gray-100',
        };
    }
  };

  // Format timestamp
  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-md">
      {activeAlerts.map((alert) => {
        const styles = getAlertStyles(alert.type);
        
        return (
          <div
            key={alert.id}
            className={`
              ${styles.container}
              border rounded-lg shadow-lg p-4 
              transform transition-all duration-300 ease-in-out
              animate-slide-in-right
            `}
            role="alert"
          >
            <div className="flex items-start space-x-3">
              {/* Alert Icon */}
              <div className={`
                ${styles.iconBg} 
                rounded-full p-1 flex-shrink-0 mt-0.5
              `}>
                <span className="text-sm">{styles.icon}</span>
              </div>

              {/* Alert Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium leading-5">
                      {alert.message}
                    </p>
                    <div className="mt-1 flex items-center space-x-2 text-xs opacity-75">
                      <span>Vehicle: {alert.vehicleId}</span>
                      <span>•</span>
                      <span>{formatTime(alert.timestamp)}</span>
                    </div>
                  </div>

                  {/* Dismiss Button */}
                  <button
                    onClick={() => onDismiss(alert.id)}
                    className="
                      ml-2 flex-shrink-0 rounded-md p-1.5 
                      hover:bg-black hover:bg-opacity-10 
                      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent
                      transition-colors duration-200
                    "
                    aria-label="Dismiss alert"
                  >
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            {/* Progress bar for auto-dismiss (optional) */}
            {alert.type === 'info' && (
              <div className="mt-3 w-full bg-black bg-opacity-10 rounded-full h-1">
                <div 
                  className="bg-current h-1 rounded-full transition-all duration-1000 ease-linear"
                  style={{ width: '100%' }}
                />
              </div>
            )}
          </div>
        );
      })}

      {/* Alert counter if there are more alerts */}
      {alerts.filter(a => a.isActive).length > maxVisible && (
        <div className="text-center">
          <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            +{alerts.filter(a => a.isActive).length - maxVisible} more alerts
          </div>
        </div>
      )}
    </div>
  );
};

export default AlertBanner;
