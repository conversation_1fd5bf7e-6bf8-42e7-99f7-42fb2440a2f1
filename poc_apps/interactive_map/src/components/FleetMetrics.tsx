'use client';

import React from 'react';
import { FleetMetricsProps } from '@/types';

const FleetMetrics: React.FC<FleetMetricsProps> = ({
  metrics,
  loading = false,
  error
}) => {
  // Loading state
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-2 w-3/4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center text-red-600">
          <p>Error loading fleet metrics: {error}</p>
        </div>
      </div>
    );
  }

  // Format time in hours and minutes
  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  // Format large numbers
  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  const metricCards = [
    {
      title: 'Avg Route Time',
      value: formatTime(metrics.averageRouteCompletionTime),
      icon: '⏱️',
      color: 'blue',
      description: 'Average completion time',
      trend: metrics.averageRouteCompletionTime < 60 ? 'good' : 'warning'
    },
    {
      title: 'Total Distance',
      value: `${formatNumber(metrics.totalDistanceTraveled)} km`,
      icon: '🛣️',
      color: 'green',
      description: 'Distance traveled',
      trend: 'neutral'
    },
    {
      title: 'Fuel Efficiency',
      value: `${metrics.fuelEfficiency} km/L`,
      icon: '⛽',
      color: 'yellow',
      description: 'Average fuel efficiency',
      trend: metrics.fuelEfficiency > 9 ? 'good' : 'warning'
    },
    {
      title: 'On-Time Performance',
      value: `${metrics.onTimePerformance}%`,
      icon: '🎯',
      color: 'purple',
      description: 'Routes completed on time',
      trend: metrics.onTimePerformance > 90 ? 'good' : metrics.onTimePerformance > 80 ? 'warning' : 'poor'
    },
    {
      title: 'Total Trips',
      value: formatNumber(metrics.totalTrips),
      icon: '🚛',
      color: 'indigo',
      description: 'Completed trips',
      trend: 'neutral'
    },
    {
      title: 'Average Speed',
      value: `${metrics.averageSpeed} km/h`,
      icon: '🏃',
      color: 'cyan',
      description: 'Average vehicle speed',
      trend: metrics.averageSpeed > 50 && metrics.averageSpeed < 70 ? 'good' : 'warning'
    },
    {
      title: 'Maintenance Alerts',
      value: metrics.maintenanceAlerts.toString(),
      icon: '🔧',
      color: 'orange',
      description: 'Maintenance required',
      trend: metrics.maintenanceAlerts < 3 ? 'good' : 'warning'
    },
    {
      title: 'Emergency Stops',
      value: metrics.emergencyStops.toString(),
      icon: '🚨',
      color: 'red',
      description: 'Emergency incidents',
      trend: metrics.emergencyStops === 0 ? 'good' : metrics.emergencyStops < 3 ? 'warning' : 'poor'
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-50 border-blue-200 text-blue-800',
      green: 'bg-green-50 border-green-200 text-green-800',
      yellow: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      purple: 'bg-purple-50 border-purple-200 text-purple-800',
      indigo: 'bg-indigo-50 border-indigo-200 text-indigo-800',
      cyan: 'bg-cyan-50 border-cyan-200 text-cyan-800',
      orange: 'bg-orange-50 border-orange-200 text-orange-800',
      red: 'bg-red-50 border-red-200 text-red-800'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'good':
        return <span className="text-green-500">↗️</span>;
      case 'warning':
        return <span className="text-yellow-500">➡️</span>;
      case 'poor':
        return <span className="text-red-500">↘️</span>;
      default:
        return <span className="text-gray-400">➡️</span>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Fleet Performance Metrics</h2>
        <div className="text-sm text-gray-600">
          Real-time fleet performance indicators
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metricCards.map((metric, index) => (
          <div
            key={index}
            className={`rounded-lg border p-6 ${getColorClasses(metric.color)}`}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="text-2xl">{metric.icon}</div>
              {getTrendIcon(metric.trend)}
            </div>
            
            <div className="mb-1">
              <h3 className="text-sm font-medium opacity-80">{metric.title}</h3>
            </div>
            
            <div className="mb-2">
              <p className="text-2xl font-bold">{metric.value}</p>
            </div>
            
            <div>
              <p className="text-xs opacity-70">{metric.description}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Performance Summary */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Summary</h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Efficiency Score */}
          <div className="text-center">
            <div className="mb-2">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full">
                <span className="text-2xl">🏆</span>
              </div>
            </div>
            <h4 className="text-lg font-semibold text-gray-900">Efficiency Score</h4>
            <p className="text-3xl font-bold text-green-600 mb-1">
              {Math.round((metrics.onTimePerformance + (metrics.fuelEfficiency * 10)) / 2)}%
            </p>
            <p className="text-sm text-gray-600">Overall fleet efficiency</p>
          </div>

          {/* Safety Rating */}
          <div className="text-center">
            <div className="mb-2">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full">
                <span className="text-2xl">🛡️</span>
              </div>
            </div>
            <h4 className="text-lg font-semibold text-gray-900">Safety Rating</h4>
            <p className="text-3xl font-bold text-blue-600 mb-1">
              {metrics.emergencyStops === 0 ? 'A+' : metrics.emergencyStops < 3 ? 'B+' : 'C'}
            </p>
            <p className="text-sm text-gray-600">Based on incidents</p>
          </div>

          {/* Maintenance Health */}
          <div className="text-center">
            <div className="mb-2">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full">
                <span className="text-2xl">🔧</span>
              </div>
            </div>
            <h4 className="text-lg font-semibold text-gray-900">Maintenance Health</h4>
            <p className="text-3xl font-bold text-orange-600 mb-1">
              {metrics.maintenanceAlerts < 3 ? 'Good' : metrics.maintenanceAlerts < 6 ? 'Fair' : 'Poor'}
            </p>
            <p className="text-sm text-gray-600">Fleet maintenance status</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FleetMetrics;
