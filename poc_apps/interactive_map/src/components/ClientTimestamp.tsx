'use client';

import React, { useState, useEffect } from 'react';

interface ClientTimestampProps {
  timestamp: Date;
  className?: string;
}

const ClientTimestamp: React.FC<ClientTimestampProps> = ({ timestamp, className }) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <span className={className}>Loading...</span>;
  }

  return (
    <span className={className}>
      {timestamp.toLocaleTimeString()}
    </span>
  );
};

export default ClientTimestamp;
