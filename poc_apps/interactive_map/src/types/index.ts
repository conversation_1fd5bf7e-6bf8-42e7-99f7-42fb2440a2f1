// Geographic coordinate interface
export interface Coordinate {
  lat: number;
  lng: number;
}

// Vehicle position with additional metadata
export interface VehiclePosition extends Coordinate {
  timestamp: Date;
  speed?: number;
  heading?: number;
}

// Route definition with waypoints
export interface Route {
  id: string;
  name: string;
  waypoints: Coordinate[];
  color?: string;
  strokeWeight?: number;
}

// Vehicle state for tracking
export interface VehicleState {
  id: string;
  position: VehiclePosition;
  isOnRoute: boolean;
  currentWaypointIndex: number;
  distanceFromRoute: number;
  status: 'on-route' | 'off-route' | 'stopped';
}

// Alert configuration
export interface Alert {
  id: string;
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: Date;
  vehicleId: string;
  isActive: boolean;
}

// Map configuration options
export interface MapConfig {
  center: Coordinate;
  zoom: number;
  mapTypeId?: google.maps.MapTypeId;
  disableDefaultUI?: boolean;
  zoomControl?: boolean;
  streetViewControl?: boolean;
  fullscreenControl?: boolean;
}

// Geofencing configuration
export interface GeofenceConfig {
  toleranceDistance: number; // in meters
  checkInterval: number; // in milliseconds
}

// Component props interfaces
export interface TrackingMapProps {
  route: Route;
  vehicle: VehicleState;
  onVehicleUpdate: (vehicle: VehicleState) => void;
  onAlert: (alert: Alert) => void;
  mapConfig?: Partial<MapConfig>;
  geofenceConfig?: Partial<GeofenceConfig>;
}

export interface VehicleTrackerProps {
  route: Route;
  onPositionUpdate: (position: VehiclePosition) => void;
  onStatusChange: (status: VehicleState['status']) => void;
  isActive: boolean;
  speed?: number; // movement speed multiplier
}

export interface AlertBannerProps {
  alerts: Alert[];
  onDismiss: (alertId: string) => void;
  maxVisible?: number;
}

// Google Maps specific types
export interface GoogleMapsContextType {
  map: google.maps.Map | null;
  isLoaded: boolean;
  loadError: Error | null;
}

// Simulation configuration
export interface SimulationConfig {
  updateInterval: number; // milliseconds between position updates
  movementSpeed: number; // speed multiplier for animation
  autoRestart: boolean; // restart simulation when route is complete
  enableOffRouteSimulation: boolean; // allow vehicle to go off-route for testing
}

// Extended interfaces for monitoring dashboard

// Event types for vehicle tracking logs
export type EventType =
  | 'position_update'
  | 'alert_triggered'
  | 'route_deviation'
  | 'route_started'
  | 'route_completed'
  | 'waypoint_reached'
  | 'emergency_stop'
  | 'speed_violation'
  | 'maintenance_due'
  | 'vehicle_stopped'
  | 'vehicle_resumed';

// Alert levels for monitoring
export type AlertLevel = 'low' | 'medium' | 'high' | 'critical';

// Vehicle tracking log entry
export interface VehicleLog {
  id: string;
  timestamp: Date;
  vehicleId: string;
  eventType: EventType;
  location: Coordinate;
  address?: string;
  status: VehicleState['status'];
  alertLevel?: AlertLevel;
  message: string;
  metadata?: Record<string, any>;
}

// Alert statistics for dashboard
export interface AlertStats {
  total: number;
  byType: {
    warning: number;
    error: number;
    info: number;
  };
  byStatus: {
    active: number;
    resolved: number;
  };
  byLevel: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
}

// Time filter options
export type TimeFilter = '24h' | '7d' | '30d' | 'all';

// API response interfaces
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Monitoring dashboard props
export interface MonitoringDashboardProps {
  initialTimeFilter?: TimeFilter;
}

// Alert summary component props
export interface AlertSummaryProps {
  timeFilter: TimeFilter;
  onTimeFilterChange: (filter: TimeFilter) => void;
}

// Logs table component props
export interface LogsTableProps {
  timeFilter: TimeFilter;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
}

// Table column definition
export interface TableColumn<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (value: any, item: T) => React.ReactNode;
  width?: string;
}

// Sort configuration
export interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

// Filter configuration for logs
export interface LogsFilter {
  vehicleId?: string;
  eventType?: EventType;
  status?: VehicleState['status'];
  alertLevel?: AlertLevel;
  timeFilter: TimeFilter;
  searchQuery?: string;
}

// Chart data interfaces
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

export interface TimeSeriesDataPoint {
  timestamp: Date;
  value: number;
  category?: string;
}

// Fleet overview data
export interface FleetOverview {
  totalVehicles: number;
  activeVehicles: number;
  onRouteVehicles: number;
  offRouteVehicles: number;
  stoppedVehicles: number;
  totalAlerts: number;
  criticalAlerts: number;
}
