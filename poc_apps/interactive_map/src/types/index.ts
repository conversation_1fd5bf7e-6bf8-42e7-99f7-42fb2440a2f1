// Geographic coordinate interface
export interface Coordinate {
  lat: number;
  lng: number;
}

// Vehicle position with additional metadata
export interface VehiclePosition extends Coordinate {
  timestamp: Date;
  speed?: number;
  heading?: number;
}

// Route definition with waypoints
export interface Route {
  id: string;
  name: string;
  waypoints: Coordinate[];
  color?: string;
  strokeWeight?: number;
}

// Vehicle state for tracking
export interface VehicleState {
  id: string;
  position: VehiclePosition;
  isOnRoute: boolean;
  currentWaypointIndex: number;
  distanceFromRoute: number;
  status: 'on-route' | 'off-route' | 'stopped';
}

// Alert configuration
export interface Alert {
  id: string;
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: Date;
  vehicleId: string;
  isActive: boolean;
}

// Map configuration options
export interface MapConfig {
  center: Coordinate;
  zoom: number;
  mapTypeId?: google.maps.MapTypeId;
  disableDefaultUI?: boolean;
  zoomControl?: boolean;
  streetViewControl?: boolean;
  fullscreenControl?: boolean;
}

// Geofencing configuration
export interface GeofenceConfig {
  toleranceDistance: number; // in meters
  checkInterval: number; // in milliseconds
}

// Component props interfaces
export interface TrackingMapProps {
  route: Route;
  vehicle: VehicleState;
  onVehicleUpdate: (vehicle: VehicleState) => void;
  onAlert: (alert: Alert) => void;
  mapConfig?: Partial<MapConfig>;
  geofenceConfig?: Partial<GeofenceConfig>;
}

export interface VehicleTrackerProps {
  route: Route;
  onPositionUpdate: (position: VehiclePosition) => void;
  onStatusChange: (status: VehicleState['status']) => void;
  isActive: boolean;
  speed?: number; // movement speed multiplier
}

export interface AlertBannerProps {
  alerts: Alert[];
  onDismiss: (alertId: string) => void;
  maxVisible?: number;
}

// Google Maps specific types
export interface GoogleMapsContextType {
  map: google.maps.Map | null;
  isLoaded: boolean;
  loadError: Error | null;
}

// Simulation configuration
export interface SimulationConfig {
  updateInterval: number; // milliseconds between position updates
  movementSpeed: number; // speed multiplier for animation
  autoRestart: boolean; // restart simulation when route is complete
  enableOffRouteSimulation: boolean; // allow vehicle to go off-route for testing
}
